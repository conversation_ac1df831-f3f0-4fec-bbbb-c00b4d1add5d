#pragma once

#include <memory>

#include "mediasdk/data_center/biz_center/end_to_end_capture.h"
#include "mediasdk/data_center/biz_center/end_to_end_delay.h"

namespace mediasdk {

class EndtoEndMgr {
 public:
  EndtoEndMgr() {}

  EndtoEndMgr(const EndtoEndMgr&);
  EndtoEndMgr(std::shared_ptr<EndtoEndDelayCaptureConst> captures,
              int64_t before_mix_ns);

  bool IsValid() const;

  // use encode thread
  void SetEncodeEndNS(int64_t encode_end_ns) const;

  int64_t GetPushCostUS(int64_t push_end_ns) const;

  int64_t GetTotalCostUS(int64_t push_end_ns) const;

  std::string GetEndtoEndDelaySEI() const;

  EndtoEndSEIforRTC GetEndtoEndDelaySEIforRTC() const;

 private:
  std::shared_ptr<EndtoEndDelayCaptureConst> end_to_end_delay_captures_;
  int64_t before_mix_ns_ = 0;
  mutable int64_t encode_end_ns_ = 0;
};

}  // namespace mediasdk
