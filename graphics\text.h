#pragma once
#include "device.h"
#include "texture.h"

namespace graphics {
class GRAPHICS_EXPORT Text {
 public:

  struct TextOPT {
    const wchar_t* text = nullptr;
    const wchar_t* text_face = nullptr;
    int32_t size = 20;
    int32_t text_color = RGB(255, 255, 255);
    uint8_t text_opacity = uint8_t(255);
    int32_t bk_color = RGB(0, 0, 0);
    uint8_t bk_opacity = uint8_t(0);
    bool bold = false;
    bool outline = false;
    int32_t outline_size = 1;
    int32_t outline_color = RGB(255, 255, 255);
    uint8_t outline_opacity = 255;
  };

  virtual std::shared_ptr<Texture> UpdateTextToTexture(const TextOPT& opt) = 0;

 public:

  virtual ~Text() {}
};

GRAPHICS_EXPORT std::shared_ptr<Text> CreateText(Device& inst);

}  // namespace graphics
