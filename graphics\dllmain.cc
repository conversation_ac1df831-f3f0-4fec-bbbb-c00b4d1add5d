#include <windows.h>

#include "base/at_exit.h"
#include "base/logging.h"
#include "base/win/win_util.h"
#ifdef _WIN64
#pragma comment(linker, "/INCLUDE:_tls_used")
#else  // _WIN64
#pragma comment(linker, "/INCLUDE:__tls_used")
#endif  // _WIN64

extern "C" bool DllMain(HINSTANCE hinstDLL,
                        DWORD fdwReason,
                        LPVOID lpvReserved) {
  switch (fdwReason) {
    case DLL_PROCESS_ATTACH:
    break;

    case DLL_PROCESS_DETACH:
      break;
  }

  return true;
}
