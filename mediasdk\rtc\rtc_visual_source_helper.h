#pragma once

#include <string>

namespace rtc_visual_source {

constexpr const char* kRTCVisualSourceID =
    R"([Guid("4B21309A-EF7A-4978-B71C-66AC7C30962F")])";

constexpr const char* kRTCVisualSourceName = "RTCVisualSource";

constexpr const char* kRTCVisualSourceDesc = "RTCVisual";

inline std::string_view GetPluginID() {
  return kRTCVisualSourceID;
}

inline std::string_view GetPluginName() {
  return kRTCVisualSourceName;
}

inline std::string_view GetPluginDesc() {
  return kRTCVisualSourceDesc;
}

};  // namespace rtc_visual_source
