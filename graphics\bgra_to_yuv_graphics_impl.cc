#include "bgra_to_yuv_graphics_impl.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include "shader_manager.h"
#include "texture_color_convert_matrix_builder.h"

namespace graphics {

bool HandlePlanerTextureCreate(
    graphics::Device& device,
    const int32_t width,
    const int32_t height,
    bool i420,
    bool only_y,
    BGRAToYUVGraphics::ConvertResourceType& planer_graphics);
bool HandleSingleTextureCreate(
    graphics::Device& device,
    const int32_t width,
    const int32_t height,
    bool only_y,
    bool shared,
    Texture* output_texture,
    BGRAToYUVGraphics::ConvertResourceType& planer_graphics);

BGRAToYUVGraphicsImpl::BGRAToYUVGraphicsImpl(Device& ins) : device_(ins) {}

bool BGRAToYUVGraphicsImpl::Init(BGRAToYUVConvertConfig conf) {
  switch (conf.cr) {
    case mediasdk::VideoRange::kVideoRangePartial:
    case mediasdk::VideoRange::kVideoRangeFull: {
      break;
    }
    default:
      assert(false);
      return false;
      break;
  }
  switch (conf.cs) {
    case mediasdk::ColorSpace::kColorSpaceBT601:
    case mediasdk::ColorSpace::kColorSpaceBT709: {
      break;
    }
    default:
      assert(false);
      return false;
      break;
  }
  config_ = conf;
  auto is_planer = [](ColorConvertType type) {
    switch (type) {
      case graphics::BGRAToYUVGraphics::kColorConvertTypeUnspecified:
        break;
      case graphics::BGRAToYUVGraphics::kColorConvertTypeNV12Single:
        break;
      case graphics::BGRAToYUVGraphics::kColorConvertTypeNV12Double:
        return true;
        break;
      case graphics::BGRAToYUVGraphics::kColorConvertTypeI420:
        return true;
        break;
      case graphics::BGRAToYUVGraphics::kColorConvertTypeNV12SingleShared:
        break;
      case graphics::BGRAToYUVGraphics::kColorConvertTypeY8:
        break;
      default:
        break;
    }
    return false;
  };

  return true;
}

bool BGRAToYUVGraphicsImpl::BGRAToYUV(Texture& bgra) {
  if (!planer_graphics_) {
    planer_graphics_ =
        std::make_unique<BGRAToYUVGraphics::ConvertResourceType>();

    switch (config_.type) {
      case ColorConvertType::kColorConvertTypeNV12Single: {
        InitYUVResources(device_, config_.cx, config_.cy, false, false, false,
                         false, nullptr, *planer_graphics_);
        break;
      }
      case ColorConvertType::kColorConvertTypeNV12Double: {
        InitYUVResources(device_, config_.cx, config_.cy, true, false, false,
                         false, nullptr, *planer_graphics_);
        break;
      }
      case ColorConvertType::kColorConvertTypeI420: {
        InitYUVResources(device_, config_.cx, config_.cy, true, false, true,
                         false, nullptr, *planer_graphics_);
        break;
      }
      case ColorConvertType::kColorConvertTypeNV12SingleShared: {
        InitYUVResources(device_, config_.cx, config_.cy, false, false, false,
                         true, nullptr, *planer_graphics_);
        break;
      }
      case ColorConvertType::kColorConvertTypeY8: {
        InitYUVResources(device_, config_.cx, config_.cy, true, true, false,
                         false, nullptr, *planer_graphics_);
        break;
      }
      default:
        break;
    }
  }
  return BGRAToYUVToTarget(bgra, *planer_graphics_);
}

bool BGRAToYUVGraphicsImpl::BGRAToYUVToTarget(Texture& bgra,
                                              ConvertResourceType& resource) {
  ColorConvertType type = config_.type;
  switch (type) {
    case graphics::BGRAToYUVGraphics::kColorConvertTypeUnspecified:
      DCHECK(false);
      return false;
      break;
    case graphics::BGRAToYUVGraphics::kColorConvertTypeNV12Single:
      return DoTextureConvert(bgra, resource, false, false, false, false);
      break;
    case graphics::BGRAToYUVGraphics::kColorConvertTypeNV12Double:
      return DoTextureConvert(bgra, resource, true, false, false, false);
      break;
    case graphics::BGRAToYUVGraphics::kColorConvertTypeI420:
      return DoTextureConvert(bgra, resource, true, false, true, false);
      break;
    case graphics::BGRAToYUVGraphics::kColorConvertTypeNV12SingleShared:
      return DoTextureConvert(bgra, resource, false, false, false, true);
      break;
    case graphics::BGRAToYUVGraphics::kColorConvertTypeY8:
      return DoTextureConvert(bgra, resource, true, true, false, false);
      break;
    default:
      break;
  }
  DCHECK(false);
  return false;
}

void InitTextureDesc(D3D11_TEXTURE2D_DESC& texture_desc,
                     D3D11_SHADER_RESOURCE_VIEW_DESC& shader_resource_view_desc,
                     D3D11_RENDER_TARGET_VIEW_DESC& render_target_view_desc,
                     int32_t width,
                     int32_t height,
                     DXGI_FORMAT format,
                     DXGI_FORMAT view_format) {
  texture_desc = {};
  shader_resource_view_desc = {};
  render_target_view_desc = {};

  texture_desc.Width = width;
  texture_desc.Height = height;
  texture_desc.MipLevels = 1;
  texture_desc.ArraySize = 1;
  texture_desc.Format = format;
  texture_desc.SampleDesc.Count = 1;
  texture_desc.SampleDesc.Quality = 0;
  texture_desc.BindFlags =
      D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_RENDER_TARGET;
  texture_desc.MiscFlags = 0;
  texture_desc.Usage = D3D11_USAGE_DEFAULT;

  render_target_view_desc.Format = view_format;
  render_target_view_desc.ViewDimension = D3D11_RTV_DIMENSION_TEXTURE2D;
  render_target_view_desc.Texture2D.MipSlice = 0;

  shader_resource_view_desc.Format = view_format;
  shader_resource_view_desc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
  shader_resource_view_desc.Texture2D.MipLevels = 1;
}

bool InitYUVResources(graphics::Device& device,
                      int32_t width,
                      int32_t height,
                      bool planer_texture,
                      bool only_y,
                      bool i420,
                      bool shared,
                      Texture* output_texture,
                      BGRAToYUVGraphics::ConvertResourceType& planer_graphics) {
  if (!planer_graphics[0] || planer_graphics[0]->IsEmpty() || output_texture) {
    planer_graphics[0] = CreateGraphics2D(device);
    if (!planer_graphics[0])
      return false;
    if (planer_texture) {
      if (!HandlePlanerTextureCreate(device, width, height, i420, only_y,
                                     planer_graphics)) {
        return false;
      }

    } else {
      if (!HandleSingleTextureCreate(device, width, height, only_y, shared,
                                     output_texture, planer_graphics)) {
        return false;
      }
    }
    for (int i = 0; i < graphics::kMaxVideoPlanes; i++) {
      if (planer_graphics[i] && !planer_graphics[i]->IsEmpty() &&
          planer_graphics[i]->GetOutputTexture()) {
        D3D11SetDebugObjectName(
            planer_graphics[i]->GetOutputTexture()->GetTexture(),
            (std::string("bgra to yuv graphics ") + std::to_string(i)).c_str());
      }
    }
  }
  return true;
}

bool HandlePlanerTextureCreate(
    graphics::Device& device,
    const int32_t width,
    const int32_t height,
    bool i420,
    bool only_y,
    BGRAToYUVGraphics::ConvertResourceType& planer_graphics) {
  D3D11_TEXTURE2D_DESC texture_desc = {};
  D3D11_SHADER_RESOURCE_VIEW_DESC shader_resource_view_desc = {};
  D3D11_RENDER_TARGET_VIEW_DESC render_target_view_desc = {};
  InitTextureDesc(texture_desc, shader_resource_view_desc,
                  render_target_view_desc, width, height,
                  DXGI_FORMAT::DXGI_FORMAT_R8_UNORM,
                  DXGI_FORMAT::DXGI_FORMAT_R8_UNORM);

  if (!planer_graphics[0]->CreateGraphics(texture_desc,
                                          &render_target_view_desc)) {
    return false;
  }

  if (i420) {
    InitTextureDesc(texture_desc, shader_resource_view_desc,
                    render_target_view_desc, width / 2, height / 2,
                    DXGI_FORMAT::DXGI_FORMAT_R8_UNORM,
                    DXGI_FORMAT::DXGI_FORMAT_R8_UNORM);
    planer_graphics[1] = CreateGraphics2D(device);
    if (!planer_graphics[1])
      return false;

    if (!planer_graphics[1]->CreateGraphics(texture_desc,
                                            &render_target_view_desc)) {
      return false;
    }
    planer_graphics[2] = CreateGraphics2D(device);
    if (!planer_graphics[2])
      return false;
    InitTextureDesc(texture_desc, shader_resource_view_desc,
                    render_target_view_desc, width / 2, height / 2,
                    DXGI_FORMAT::DXGI_FORMAT_R8_UNORM,
                    DXGI_FORMAT::DXGI_FORMAT_R8_UNORM);

    if (!planer_graphics[2]->CreateGraphics(texture_desc,
                                            &render_target_view_desc)) {
      return false;
    }
  } else {
    if (!only_y) {
      planer_graphics[1] = CreateGraphics2D(device);
      if (!planer_graphics[1])
        return false;

      InitTextureDesc(texture_desc, shader_resource_view_desc,
                      render_target_view_desc, width / 2, height / 2,
                      DXGI_FORMAT::DXGI_FORMAT_R8G8_UNORM,
                      DXGI_FORMAT::DXGI_FORMAT_R8G8_UNORM);

      if (!planer_graphics[1]->CreateGraphics(texture_desc,
                                              &render_target_view_desc)) {
        return false;
      }
    }
  }
  return true;
}

bool HandleSingleTextureCreate(
    graphics::Device& device,
    const int32_t width,
    const int32_t height,
    bool only_y,
    bool shared,
    Texture* output_texture,
    BGRAToYUVGraphics::ConvertResourceType& planer_graphics) {
  D3D11_TEXTURE2D_DESC texture_desc = {};
  D3D11_RENDER_TARGET_VIEW_DESC render_target_view_desc = {};
  texture_desc.Width = width;
  texture_desc.Height = height;
  texture_desc.MipLevels = 1;
  texture_desc.ArraySize = 1;
  texture_desc.Format = DXGI_FORMAT::DXGI_FORMAT_NV12;
  texture_desc.SampleDesc.Count = 1;
  texture_desc.SampleDesc.Quality = 0;
  texture_desc.BindFlags =
      D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_RENDER_TARGET;
  texture_desc.MiscFlags = shared ? D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX : 0;
  texture_desc.Usage = D3D11_USAGE_DEFAULT;

  render_target_view_desc.Format = DXGI_FORMAT_R8_UNORM;
  render_target_view_desc.ViewDimension = D3D11_RTV_DIMENSION_TEXTURE2D;
  render_target_view_desc.Texture2D.MipSlice = 0;

  if (output_texture) {
    // attach to texture
    if (!planer_graphics[0]->CreateGraphics(output_texture->GetTexture(),
                                            &render_target_view_desc)) {
      return false;
    }
  } else {
    if (!planer_graphics[0]->CreateGraphics(texture_desc,
                                            &render_target_view_desc)) {
      return false;
    }
  }

  planer_graphics[1] = CreateGraphics2D(device);
  if (!planer_graphics[1])
    return false;

  render_target_view_desc.Format = DXGI_FORMAT_R8G8_UNORM;
  bool ret = planer_graphics[1]->CreateGraphics(
      planer_graphics[0]->GetOutputTexture()->GetTexture(),
      &render_target_view_desc);
  if (!ret) {
    return false;
  }
  return true;
}

bool BGRAToYUVGraphicsImpl::TryUpdateShaderParameter(int32_t width,
                                                     int32_t height) {
  if (!vs_buffer_ || !ps_buffer_) {
    D3D11_BUFFER_DESC buffer_desc = {};
    buffer_desc.Usage = D3D11_USAGE_DYNAMIC;
    buffer_desc.ByteWidth = sizeof(BGRAToYUVShader::VS_CONSTANT_BUFFER);
    buffer_desc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
    buffer_desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
    buffer_desc.MiscFlags = 0;
    buffer_desc.StructureByteStride = 0;
    auto device = GetDevice_();
    auto hRes = device->CreateBuffer(&buffer_desc, NULL, &vs_buffer_);
    if (FAILED(hRes)) {
      LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                       GetErrorString(hRes).c_str());
      return false;
    }
    D3D11SetDebugObjectName(vs_buffer_.Get(), "bgra to yuv vs buffer");

    buffer_desc.Usage = D3D11_USAGE_DYNAMIC;
    buffer_desc.ByteWidth = sizeof(BGRAToYUVShader::PS_CONSTANT_BUFFER);
    buffer_desc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
    buffer_desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
    hRes = device->CreateBuffer(&buffer_desc, NULL, &ps_buffer_);
    if (FAILED(hRes)) {
      LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(0x%X)", hRes);
      return false;
    }
    D3D11SetDebugObjectName(ps_buffer_.Get(), "bgra to yuv ps buffer");
    BuildPlanesBGRAToYUV(config_.cs, config_.cr, ps_buffer_v_.Y, ps_buffer_v_.U,
                         ps_buffer_v_.V);

    vs_buffer_v_.cx = width;
    vs_buffer_v_.cy = height;
    hRes = S_OK;
    D3D11_MAPPED_SUBRESOURCE map = {};
    auto context = GetContext_();
    if (SUCCEEDED(hRes = context->Map(vs_buffer_.Get(), 0,
                                      D3D11_MAP_WRITE_DISCARD, 0, &map))) {
      auto dataPtr = (BGRAToYUVShader::VS_CONSTANT_BUFFER*)map.pData;
      memcpy_s(dataPtr, sizeof(BGRAToYUVShader::VS_CONSTANT_BUFFER),
               &vs_buffer_v_, sizeof(BGRAToYUVShader::VS_CONSTANT_BUFFER));
      context->Unmap(vs_buffer_.Get(), 0);
    } else {
      assert(false);
      LOG(ERROR) << "Failed to map[" << GetErrorString(hRes) << "]";
      return false;
    }

    map = {};
    if (SUCCEEDED(hRes = context->Map(ps_buffer_.Get(), 0,
                                      D3D11_MAP_WRITE_DISCARD, 0, &map))) {
      auto dataPtr = (BGRAToYUVShader::PS_CONSTANT_BUFFER*)map.pData;
      memcpy_s(dataPtr, sizeof(BGRAToYUVShader::PS_CONSTANT_BUFFER),
               &ps_buffer_v_, sizeof(BGRAToYUVShader::PS_CONSTANT_BUFFER));
      context->Unmap(ps_buffer_.Get(), 0);

    } else {
      assert(false);
      LOG(ERROR) << "Failed to map[" << GetErrorString(hRes) << "]";
      return false;
    }
  }
  return vs_buffer_ && ps_buffer_;
}

bool BGRAToYUVGraphicsImpl::DoConvertByShader(
    bool i420,
    bool only_y,
    Texture& texture,
    BGRAToYUVGraphics::ConvertResourceType& planer_graphics) {
  auto shader = GetShaderManager_()->GetOrCreateShader<BGRAToYUVShader>(
      device_.shared_from_this());
  if (!shader)
    return false;
  device_.AllowBlend(false);
  {
    if (!planer_graphics[0]->BeginDraw(false)) {
      return false;
    }
    graphics::ScopedEndDraw end_draw0(*planer_graphics[0]);

    shader->RenderY(vs_buffer_, ps_buffer_, texture.GetSRV());
  }
  if (i420) {
    {
      auto new_size = planer_graphics[0]->GetSize() / 2;
      if (!planer_graphics[1]->BeginDraw(false, &new_size)) {
        return false;
      }
      graphics::ScopedEndDraw end_draw1(*planer_graphics[1]);

      shader->RenderU(vs_buffer_, ps_buffer_, texture.GetSRV());
    }
    {
      auto new_size = planer_graphics[0]->GetSize() / 2;

      if (!planer_graphics[2]->BeginDraw(false, &new_size)) {
        return false;
      }

      graphics::ScopedEndDraw end_draw1(*planer_graphics[2]);

      shader->RenderV(vs_buffer_, ps_buffer_, texture.GetSRV());
    }

  } else {
    if (!only_y) {
      auto new_size = planer_graphics[0]->GetSize() / 2;
      if (!planer_graphics[1]->BeginDraw(
              false,
              &new_size))  // R8G8 texture only have half size
      {
        return false;
      }
      graphics::ScopedEndDraw end_draw1(*planer_graphics[1]);

      shader->RenderUV(vs_buffer_, ps_buffer_, texture.GetSRV());
    }
  }

  return true;
}

bool BGRAToYUVGraphicsImpl::DoTextureConvert(
    Texture& texture,
    BGRAToYUVGraphics::ConvertResourceType& planer_graphics,
    bool planer_texture,
    bool only_y,
    bool i420,
    bool shared,
    Texture* output_texture) {
  TryUpdateShaderParameter(config_.cx, config_.cy);

  const auto desc = texture.GetDesc();

  DCHECK(desc.Width == config_.cx);
  DCHECK(desc.Height == config_.cy);

  if (desc.Format != DXGI_FORMAT::DXGI_FORMAT_B8G8R8A8_UNORM) {
    assert(false);
    return false;
  }
  if (!desc.Width || !desc.Height) {
    assert(false);
    return false;
  }

  return DoConvertByShader(i420, only_y, texture, planer_graphics);
}

std::shared_ptr<Texture> BGRAToYUVGraphicsImpl::GetOutputTexture(int plane) {
  if (!planer_graphics_) {
    DCHECK(false);
    return nullptr;
  }
  return (*planer_graphics_)[plane]->GetOutputTexture();
}

std::shared_ptr<Texture> BGRAToYUVGraphicsImpl::MoveOutputTexture(int plane) {
  if (!planer_graphics_) {
    DCHECK(false);
    return nullptr;
  }

  return (*planer_graphics_)[plane]->MoveOutputTexture();
}

ID3D11Device* BGRAToYUVGraphicsImpl::GetDevice_() {
  return device_.GetDevice().Get();
}

ID3D11DeviceContext* BGRAToYUVGraphicsImpl::GetContext_() {
  return device_.GetContext().Get();
}

ShaderManager* BGRAToYUVGraphicsImpl::GetShaderManager_() {
  return device_.GetShaderManager();
}

BGRAToYUVGraphicsImpl ::~BGRAToYUVGraphicsImpl() {
  BGRAToYUVGraphicsImpl::Destroy();
}

void BGRAToYUVGraphicsImpl::Destroy() {
  if (planer_graphics_) {
    for (auto& planer : *planer_graphics_) {
      if (planer) {
        planer->Destroy();
        planer = nullptr;
      }
    }
  }

  if (ps_buffer_) {
    ps_buffer_.Reset();
  }

  if (vs_buffer_) {
    vs_buffer_.Reset();
  }
  if (planer_graphics_) {
    for (auto& planer : *planer_graphics_) {
      if (planer) {
        planer = nullptr;
      }
    }
  }
}
}  // namespace graphics