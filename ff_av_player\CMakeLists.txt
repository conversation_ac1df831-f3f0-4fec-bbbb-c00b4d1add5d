cmake_minimum_required(VERSION 3.20)

set(PROJECT_NAME ff_av_player)
file(GLOB SOURCES
        "*.h"
        "*.cpp"
        "*.cc"
        "avsyn/*.h"
        "avsyn/*.cc"
        "decoder/*.h"
        "decoder/*.cc"
        "demuxer/*.h"
        "demuxer/*.cc"
        "ff_swsscale/*.h"
        "ff_swsscale/*.cc"
)

add_library(${PROJECT_NAME} STATIC ${SOURCES})

target_link_libraries(${PROJECT_NAME} PUBLIC base ffmpeg mediasdk_utils)

target_include_directories(${PROJECT_NAME} PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})
