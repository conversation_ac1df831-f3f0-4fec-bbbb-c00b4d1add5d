﻿#include "device.h"

#include <SetupAPI.h>
#include <Windows.h>
#include <base/debug/stack_trace.h>
#include <base/logging.h>
#include <base/strings/safe_sprintf.h>
#include <base/strings/stringprintf.h>
#include <base/strings/sys_string_conversions.h>
#include <base/strings/utf_string_conversions.h>
#include <comdef.h>
#include <d3dcommon.h>
#include <d3dcompiler.h>
#include <devpropdef.h>
#include <dxgi.h>
#include <dxgitype.h>
#include <wrl/client.h>
#include "device_impl.h"

using Microsoft::WRL::ComPtr;

namespace graphics {

void LogAdapterMonitors(ComPtr<IDXGIAdapter1> adapter,
                        std::stringstream& stream) {
  if (!adapter)
    return;

  UINT i = 0;
  ComPtr<IDXGIOutput> output;
  while (adapter->EnumOutputs(i++, &output) == S_OK) {
    if (output != NULL) {
      DXGI_OUTPUT_DESC desc;
      if (FAILED(output->GetDesc(&desc)))
        continue;
      RECT rect = desc.DesktopCoordinates;
      stream << base::StringPrintf(
                    "\t\t  adapter monitor %d: "
                    "coordinates=%d, %d, "
                    "size=%d, %d, "
                    "attached to desktop=%s "
                    "rotation=%d ",
                    i, rect.left, rect.top, rect.right - rect.left,
                    rect.bottom - rect.top,
                    desc.AttachedToDesktop ? "true" : "false",
                    (INT32)desc.Rotation)
             << std::endl;
    }
  }
}

std::string GetAdapterName(ComPtr<IDXGIAdapter> adapter) {
  DXGI_ADAPTER_DESC desc = {};
  if (const auto r = adapter->GetDesc(&desc); SUCCEEDED(r)) {
    return base::WideToUTF8(desc.Description);
  }
  return {};
}

LUID GetAdapterLUID(ComPtr<IDXGIAdapter> adapter) {
  if (!adapter) {
    return LUID();
  }
  DXGI_ADAPTER_DESC desc = {};
  adapter->GetDesc(&desc);
  return desc.AdapterLuid;
}

std::uint32_t GetAdapterVendorId(ComPtr<IDXGIAdapter> adapter) {
  DXGI_ADAPTER_DESC desc = {};
  adapter->GetDesc(&desc);
  return desc.VendorId;
}

void LogAdapterInfo(int index, ComPtr<IDXGIAdapter1> adapter) {
  DXGI_ADAPTER_DESC desc = {};
  DXGI_ADAPTER_DESC1 desc1 = {};
  adapter->GetDesc(&desc);
  adapter->GetDesc1(&desc1);
  std::string str = GetAdapterName(adapter);
  std::stringstream output;
  output << base::StringPrintf("\nAdapter index %d", index) << std::endl;
  output << base::StringPrintf("\t Description %s", str.c_str()) << std::endl;
  output << base::StringPrintf("\t VendorId %u", desc.VendorId) << std::endl;
  output << base::StringPrintf("\t DeviceId %u", desc.DeviceId) << std::endl;
  output << base::StringPrintf("\t SubSysId %u", desc.SubSysId) << std::endl;
  output << base::StringPrintf("\t Revision %u", desc.Revision) << std::endl;
  output << base::StringPrintf("\t DedicatedVideoMemory %lld MB",
                               desc.DedicatedVideoMemory / 1024 / 1024)
         << std::endl;
  output << base::StringPrintf("\t SharedSystemMemory %lld MB",
                               desc.SharedSystemMemory / 1024 / 1024)
         << std::endl;
  output << base::StringPrintf("\t AdapterLuid %d.%d",
                               desc.AdapterLuid.HighPart,
                               desc.AdapterLuid.LowPart)
         << std::endl;

  output << base::StringPrintf("\t Flags %d", desc1.Flags) << std::endl;
  if (desc1.Flags & DXGI_ADAPTER_FLAG::DXGI_ADAPTER_FLAG_REMOTE) {
    output << "\t\t DXGI_ADAPTER_FLAG_REMOTE";
  }
  if (desc1.Flags & DXGI_ADAPTER_FLAG::DXGI_ADAPTER_FLAG_SOFTWARE) {
    output << "\t\t DXGI_ADAPTER_FLAG_SOFTWARE";
  }
  LogAdapterMonitors(adapter, output);
  LOG(INFO) << output.str();
}

void LogGraphicsEnvironment() {
  ComPtr<IDXGIFactory> pFactory;

  if (const auto hr =
          CreateDXGIFactory(__uuidof(IDXGIFactory), (void**)(&pFactory));
      FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateDXGIFactory [%s]",
                                     GetErrorString(hr).c_str());
    return;
  }
  ComPtr<IDXGIFactory1> pFactory1;

  if (const auto hr =
          CreateDXGIFactory1(__uuidof(IDXGIFactory1), (void**)(&pFactory1));
      FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateDXGIFactory1 [%s]",
                                     GetErrorString(hr).c_str());
    return;
  }
  int index = 0;
  while (true) {
    ComPtr<IDXGIAdapter1> adapter;

    if (const auto hr = pFactory1->EnumAdapters1(index, &adapter); FAILED(hr)) {
      break;
    }
    LogAdapterInfo(index, adapter);
    ++index;
  }
  LOG(INFO) << "Success Create Factory";
}

std::string GetErrorString(HRESULT hr) {
  const _com_error err(hr);

  if (const auto* err_msg = err.ErrorMessage()) {
    return base::SysWideToUTF8(err_msg);
  }
  return {};
}

std::shared_ptr<Device> CreateDevice(const CreateDeviceOption& opt) {
  return DeviceImpl::CreateDevice(opt);
}

void D3D11SetDebugObjectName(ID3D11DeviceChild* object, const char* name) {
#if (defined(DEBUG) || defined(_DEBUG)) && (GRAPHICS_DEBUGGER_OBJECT_NAME)
  object->SetPrivateData(WKPDID_D3DDebugObjectName, strlen(name), name);
#else
  UNREFERENCED_PARAMETER(object);
  UNREFERENCED_PARAMETER(name);
#endif  // defined(DEBUG) || defined(_DEBUG)) && (GRAPHICS_DEBUGGER_OBJECT_NAME
}

bool GetPCIDriverDate(uint32_t vendorId,
                      uint32_t deviceId,
                      uint32_t subSysId,
                      std::string& driver_date,
                      std::string& driver_version) {
  bool gotDate = false;
  bool gotVersion = false;
// These come from devpkey.h
#define MAKE_DEVPROPKEY(name, l, w1, w2, b1, b2, b3, b4, b5, b6, b7, b8, pid) \
  const DEVPROPKEY name = {{l, w1, w2, {b1, b2, b3, b4, b5, b6, b7, b8}}, pid}
  MAKE_DEVPROPKEY(DEVPKEY_Device_DriverDate, 0xa8b865dd, 0x2e3d, 0x4094, 0xad,
                  0x97, 0xe5, 0x93, 0xa7, 0xc, 0x75, 0xd6,
                  2);  // DEVPROP_TYPE_FILETIME
  MAKE_DEVPROPKEY(DEVPKEY_Device_DriverVersion, 0xa8b865dd, 0x2e3d, 0x4094,
                  0xad, 0x97, 0xe5, 0x93, 0xa7, 0xc, 0x75, 0xd6,
                  3);  // DEVPROP_TYPE_STRING

  wchar_t searchBuffer[128] = {};
  swprintf(searchBuffer, 128, L"PCI\\VEN_%04X&DEV_%04X&SUBSYS_%04X", vendorId,
           deviceId, subSysId);
  size_t searchBufferLen = wcslen(searchBuffer);

  wchar_t searchBuffer8[128] = {};
  swprintf(searchBuffer8, 128, L"PCI\\VEN_%04X&DEV_%04X&SUBSYS_%08X", vendorId,
           deviceId, subSysId);
  size_t searchBufferLen8 = wcslen(searchBuffer8);

  HDEVINFO deviceInfoList =
      SetupDiGetClassDevs(NULL, L"PCI", NULL, DIGCF_ALLCLASSES | DIGCF_PRESENT);
  if (deviceInfoList != INVALID_HANDLE_VALUE) {
    SP_DEVINFO_DATA deviceInfo;
    ZeroMemory(&deviceInfo, sizeof(deviceInfo));
    deviceInfo.cbSize = sizeof(SP_DEVINFO_DATA);

    DWORD deviceIndex = 0;
    while (SetupDiEnumDeviceInfo(deviceInfoList, deviceIndex, &deviceInfo)) {
      wchar_t deviceInstanceId[512] = {};
      DWORD deviceInstanceIdCharacters = 0;
      if (SetupDiGetDeviceInstanceId(deviceInfoList, &deviceInfo,
                                     deviceInstanceId,
                                     sizeof(deviceInstanceId) / sizeof(wchar_t),
                                     &deviceInstanceIdCharacters)) {
        if (wcsncmp(deviceInstanceId, searchBuffer, searchBufferLen) == 0 ||
            wcsncmp(deviceInstanceId, searchBuffer8, searchBufferLen8) == 0) {
          DEVPROPTYPE propertyType;
          FILETIME deviceDriverDate;
          wchar_t deviceDriverVersion[512];

          if (SetupDiGetDeviceProperty(deviceInfoList, &deviceInfo,
                                       &DEVPKEY_Device_DriverDate,
                                       &propertyType, (PBYTE)&deviceDriverDate,
                                       sizeof(deviceDriverDate), NULL, 0)) {
            if (propertyType == DEVPROP_TYPE_FILETIME) {
              SYSTEMTIME deviceDriverDateAsSystemTime;
              FileTimeToSystemTime(&deviceDriverDate,
                                   &deviceDriverDateAsSystemTime);
              driver_date = base::StringPrintf(
                  "%4d%02d%02d", deviceDriverDateAsSystemTime.wYear,
                  deviceDriverDateAsSystemTime.wMonth,
                  deviceDriverDateAsSystemTime.wDay);
              gotDate = true;
            }
          }
          if (SetupDiGetDeviceProperty(
                  deviceInfoList, &deviceInfo, &DEVPKEY_Device_DriverVersion,
                  &propertyType, (PBYTE)deviceDriverVersion,
                  sizeof(deviceDriverVersion), NULL, 0)) {
            if (propertyType == DEVPROP_TYPE_STRING) {
              driver_version = base::SysWideToMultiByte(
                  (wchar_t*)deviceDriverVersion, CP_UTF8);
              gotVersion = true;
            }
          }
        }
      }
      deviceIndex++;
    }

    SetupDiDestroyDeviceInfoList(deviceInfoList);
  } else {
    LOG(ERROR) << "Failed to SetupDiGetClassDevs [" << deviceInfoList << "]";
  }

  return gotDate || gotVersion;
}

}  // namespace graphics