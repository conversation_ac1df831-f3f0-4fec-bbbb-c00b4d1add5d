#include "ep_helper.h"

#ifdef _WIN32
#include <Windows.h>
#endif

#include "LokiPlatformConfig.h"
#include "base/logging.h"
#include "base/path_service.h"
#include "base/files/file_path.h"
#include "nlohmann/json.hpp"

namespace mediasdk::ep {

namespace {

std::string GetProcessorName() {
#ifdef _WIN32
  HKEY key;
  CHAR data[1024] = {0};
  DWORD size = 0;
  LSTATUS status;
  status =
      ::RegOpenKeyA(HKEY_LOCAL_MACHINE,
                    "HARDWARE\\DESCRIPTION\\System\\CentralProcessor\\0", &key);
  if (status != ERROR_SUCCESS)
    return "";

  size = sizeof(data);
  status = ::RegQueryValueExA(key, "ProcessorNameString", NULL, NULL,
                              (LPBYTE)data, &size);
  ::RegCloseKey(key);
  return data;
#else
  return std::string();
#endif
}

std::string GetHostABI() {
#ifdef _WIN32
#ifdef _WIN64
  return "win32-x64";
#else
  return "win32-x86";
#endif  //_WIN64
#else
  return "";
#endif  //_WIN32
}

}  // namespace

std::shared_ptr<davinci::effectplatform::loki::LokiPlatformConfig> CreateConfig(
    const std::string& json_param,
    const std::string& version,
    const std::shared_ptr<
        davinci::effectplatform::loki::LokiRequirementsPeeker>& peeker) {
  auto config =
      std::make_shared<davinci::effectplatform::loki::LokiPlatformConfig>();
  try {
    nlohmann::json root = nlohmann::json::parse(std::string(json_param));
    config->sdkVersion = version;
    config->appVersion = root.at("appVersion").get<std::string>();
    config->deviceType = root["deviceType"].get<std::string>();
    config->appID = root["appId"].get<std::string>();
    config->accessKey = root["accessKey"].get<std::string>();
    config->channel = root["channel"].get<std::string>();
    config->effectCacheDir = root["effectCacheDir"].get<std::string>();
    config->modelCacheDir = root["modelCacheDir"].get<std::string>();
    config->builtInModelDir = root["builtInModelDir"].get<std::string>();
    config->lokiHost = root["lokiHost"].get<std::string>();
    config->veCloudHost = root["veCloudHost"].get<std::string>();
    config->modelStatus = root["modelStatus"].get<std::string>();
    config->platform = "windows";
    config->region = root["region"].get<std::string>();
    if (root.contains("deviceId") && root.at("deviceId").is_string()) {
      auto devId = root.at("deviceId").get<std::string>();
      if (!devId.empty())
        config->deviceId = devId;
    }

    nlohmann::json deviceContent;
    deviceContent["CPU"] = GetProcessorName();
    deviceContent["GPU"] = config->gpu;
    deviceContent["host_abi"] = GetHostABI();

    config->deviceInfo = deviceContent.dump();
    config->requirementsPeeker = peeker;
  } catch (const std::exception& e) {
    LOG(ERROR) << "parse failed:" << e.what() << ", json:" << json_param;
    return nullptr;
  }
  return config;
}

std::optional<std::string> GetUserIdFromJson(const std::string& json_param) {
  try {
    nlohmann::json root = nlohmann::json::parse(std::string(json_param));
    if (root.contains("userId") && root["userId"].is_string()) {
      return std::make_optional(root["userId"].get<std::string>());
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "parse failed:" << e.what() << ", json:" << json_param;
  }
  return std::nullopt;
}

std::optional<std::string> GetTTLSHardwareLevel(const std::string& json_param) {
  try {
    nlohmann::json root = nlohmann::json::parse(std::string(json_param));
    if (root.contains("ttlsHardwareLevel") &&
        root["ttlsHardwareLevel"].is_string()) {
      return std::make_optional(root["ttlsHardwareLevel"].get<std::string>());
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "parse failed:" << e.what() << ", json:" << json_param;
  }
  return std::nullopt;
}

std::string CreateSuccessEvent(const std::string& event_name,
                               const std::string& requirement_id) {
  nlohmann::json data = {
      {"event", event_name}, {"success", true}, {"request_id", requirement_id}};
  return data.dump();
}

std::string CreateFailedEvent(const std::string& event_name,
                              const std::string& requirement_id,
                              const std::string& error) {
  nlohmann::json data = {{"event", event_name},
                         {"success", false},
                         {"error", error},
                         {"request_id", requirement_id}};
  return data.dump();
}

std::string CreateProgressEvent(const std::string& event_name,
                                const std::string& requirement_id,
                                int progress) {
  nlohmann::json data = {{"event", event_name},
                         {"success", true},
                         {"progress", progress},
                         {"request_id", requirement_id}};
  return data.dump();
}

std::string CreateNetEvent(const std::string& event_name,
                           const std::string& url,
                           const std::string& body,
                           int error_code) {
  nlohmann::json data = {
      {"event", event_name},
      {"url", url},
      {"body", body},
      {"error_code", error_code},
  };
  return data.dump();
}

}  // namespace mediasdk::ep