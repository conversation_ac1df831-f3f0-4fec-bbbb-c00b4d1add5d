#pragma once
#include <atomic>
#include <string>

namespace mediasdk {

class EncoderRateCalc {
 public:
  EncoderRateCalc() = default;

  ~EncoderRateCalc() = default;

  void StartEncode();

  void OnEncodeResult(bool success);

  void SetSinkId(const uint32_t sink_id) { sink_id_ = sink_id; }

  void SetCodecName(const std::string& name) { codec_name_ = name; }

 private:
  void OnEncodeSuccess();

  void OnEncodeError();

  void CalcAndReport();

  void Report(double fps, double err_fps, int cost);

 private:
  uint32_t sink_id_ = 0;
  std::string codec_name_;
  int64_t start_encode_time_ms_ = 0;
  int64_t max_encode_cost_ = 0;
  int64_t last_time_stamp_ms_ = 0;
  int64_t last_encode_num_ = 0;
  int64_t last_encode_err_num_ = 0;
  int64_t encode_num_ = 0;
  int64_t encode_err_num_ = 0;
};
}  // namespace mediasdk