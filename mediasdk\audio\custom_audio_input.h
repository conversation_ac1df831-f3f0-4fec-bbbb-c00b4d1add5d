#pragma once

#include "audio_input.h"
#include "base/notreached.h"
#include "mediasdk/public/hook_api/custom_audio_input_delegate.h"
#include "mediasdk/public/hook_api/custom_audio_input_proxy.h"
#include "mediasdk/utils/audio/hook_api_audio_dump_helper.h"

namespace mediasdk {

class CustomAudioInput : public AudioInput,
                         public hook_api::CustomAudioInputProxy {
 public:
  static std::shared_ptr<CustomAudioInput> Create(
      const std::string& id,
      const AudioFormat& output_format,
      hook_api::CustomAudioInputDelegate* delegate);

  // Destructor with same exception specification as base class
  ~CustomAudioInput() noexcept override;

  // AudioInput:
  void AttachSource(std::shared_ptr<AudioInputSource> source) override;

  std::shared_ptr<AudioInputSource> DetachSource() override;

  bool IsFromVisual() override { return false; }

  std::string GetDevName() override;

  std::string GetName() override;

  std::shared_ptr<AudioInputSource> source() override { return nullptr; }

 protected:
  // AudioInput:
  void OnSetMute(bool mute) override;

  void OnSetVolume(float volume) override;

  // CustomAudioInputProxy:
  void InputCustomAudioData(const AudioSourceFrame& frame) override;

 private:
#if ENABLE_HOOK_API_AUDIO_DUMP
  void DumpAudioToFile(const AudioFormat& format, const AudioFrame& frame);
#endif  // ENABLE_HOOK_API_AUDIO_DUMP

  CustomAudioInput(const std::string& id,
                   const AudioFormat& output_format,
                   hook_api::CustomAudioInputDelegate* delegate);

 private:
  hook_api::CustomAudioInputDelegate* delegate_ = nullptr;
#if ENABLE_HOOK_API_AUDIO_DUMP
  std::unique_ptr<HookApiAudioDumpHelper> hook_api_audio_dump_helper_;
#endif  // ENABLE_HOOK_API_AUDIO_DUMP
};

}  // namespace mediasdk