#include "bgra_to_rgba_shader.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>

using namespace Microsoft::WRL;

namespace graphics {

static const char* CONST_PIXEL_SHADER() {
  return R"(
Texture2D shaderTexture : register(t0);
SamplerState sampleType : register(s0);

struct PS_Pos
{
    float4 pos : SV_POSITION;
};

float4 PS_MAIN(PS_Pos input) : SV_TARGET
{
    float4 color = shaderTexture.Load(int3(input.pos.xy,0));
    // b g r a
    // r g b a
    return float4(color.b,color.g,color.r,color.a);
}
)";
}

static const char* CONST_VERTEX_SHADER() {
  return R"(

struct PS_Pos
{
    float4 pos : SV_POSITION;
};

PS_Pos VS_MAIN(uint vid : SV_VERTEXID)
{
	bool right = vid == 2;
	bool top = vid == 1;
	float x = - 1.0 + right * 4.f;
	float y = - 1.0 + top * 4.f;
	PS_Pos ret;
    ret.pos = float4(x,y,0.f,1.f);
    return ret;
}
)";
}

bool BGRAToRGBAShader::Init(const std::shared_ptr<Device>& ins) {
  if (!try_init_) {
    init_suc_ = _Init(ins);
    try_init_ = true;
  }

  return init_suc_;
}

bool BGRAToRGBAShader::_Init(const std::shared_ptr<Device>& ins) {
  device_ = ins;
  Device::CompileShaderParam param = {};
  param.ps = CONST_PIXEL_SHADER();
  param.vs = CONST_VERTEX_SHADER();
  param.ps_name = "PS_MAIN";
  param.vs_name = "VS_MAIN";

  if (!device_->CompileShader(param)) {
    return false;
  }
  vs_shader_ = param.vs_shader_;
  ps_shader_ = param.ps_shader_;
  D3D11SetDebugObjectName(vs_shader_.Get(), "bgra_to_rgba_shader_vs");
  D3D11SetDebugObjectName(ps_shader_.Get(), "bgra_to_rgba_shader_ps");
  D3D11_SAMPLER_DESC desc;
  desc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
  desc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.MipLODBias = 0.0F;
  desc.MaxAnisotropy = 1;
  desc.ComparisonFunc = D3D11_COMPARISON_ALWAYS;
  desc.BorderColor[0] = 0;
  desc.BorderColor[1] = 0;
  desc.BorderColor[2] = 0;
  desc.BorderColor[3] = 0;
  desc.MinLOD = 0;
  desc.MaxLOD = D3D11_FLOAT32_MAX;
  HRESULT hRes = GetDevice_()->CreateSamplerState(&desc, &sampler_);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateSamplerState(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  D3D11SetDebugObjectName(sampler_.Get(), "revert_shader_sampler");
  return true;
}

void BGRAToRGBAShader::RenderTexture(ID3D11ShaderResourceView* pSRView) {
  GetContext_()->PSSetShaderResources(0, 1, &pSRView);

  GetContext_()->IASetInputLayout(nullptr);

  GetContext_()->VSSetShader(vs_shader_.Get(), NULL, 0);
  GetContext_()->PSSetShader(ps_shader_.Get(), NULL, 0);
  GetContext_()->PSSetSamplers(0, 1, sampler_.GetAddressOf());
  GetContext_()->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);
  GetContext_()->Draw(3, 0);
}

ComPtr<ID3D11Device> BGRAToRGBAShader::GetDevice_() {
  return device_->GetDevice();
}

ComPtr<ID3D11DeviceContext> BGRAToRGBAShader::GetContext_() {
  return device_->GetContext();
}

void BGRAToRGBAShader::Destroy() {
  if (sampler_) {
    sampler_.Reset();
  }

  if (ps_shader_) {
    ps_shader_.Reset();
  }
  if (vs_shader_) {
    vs_shader_.Reset();
  }
}

BGRAToRGBAShader::~BGRAToRGBAShader() {
  BGRAToRGBAShader::Destroy();
}
}  // namespace graphics