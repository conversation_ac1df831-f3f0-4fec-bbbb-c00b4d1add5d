#pragma once
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include "shader.h"

// shader only init when first usage
namespace graphics {
class ShaderFactory {
 public:
  ShaderFactory();

 public:
  static void FactoryCallBack(void* param, ShaderItem factory);
  int RegisterInnerFactory();
  void RegisterFactory(EnumPluginsFunction pfn);
  ShaderItem GetFactoryByType(const char* type);
  std::shared_ptr<Shader> CreateShader(const char* type);

  template <typename T>
  std::shared_ptr<T> CreateShaderAS(const char* type) {
    std::shared_ptr<Shader> ret = CreateShader(type);
    return std::dynamic_pointer_cast<T>(ret);
  }

  template <typename T>
  T* GetOrCreateShader(const char* type) {
    auto it = buffered_shader_instance_.find(type);
    if (it != buffered_shader_instance_.end()) {
      return (T*)(it->second.get());
    }
    auto shader = CreateShaderAS<T>(type);

    if (!shader)
      return nullptr;
    buffered_shader_instance_[type] = shader;
    return shader.get();
  }

 private:
  std::map<std::string, ShaderItem> factory_map_;

  std::map<const char*, std::shared_ptr<Shader>> buffered_shader_instance_;
};
}  // namespace graphics
