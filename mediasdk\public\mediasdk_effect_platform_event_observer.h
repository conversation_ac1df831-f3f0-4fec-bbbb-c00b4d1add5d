#pragma once

#include "mediasdk_string.hpp"

#pragma pack(push, 1)

namespace mediasdk {

class MediaSDKEPEventObserver {
 public:
  virtual ~MediaSDKEPEventObserver() {}

  virtual void OnDownloadModelSuccess(MediaSDKString request_id) = 0;
  virtual void OnDownloadModelError(MediaSDKString request_id,
                                    MediaSDKString error) = 0;
  virtual void OnDownloadModelProgress(MediaSDKString request_id,
                                       int progress) = 0;
};

}  // namespace mediasdk

#pragma pack(pop)