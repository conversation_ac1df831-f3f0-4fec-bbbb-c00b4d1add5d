#pragma once

#include <map>
#include <mutex>
#include <string>
#include "mediasdk/public/mediasdk_defines.h"

namespace mediasdk {

class AudioPerformanceStoreCalc {
 public:
  AudioPerformanceStoreCalc(const std::string& audio_id);

  ~AudioPerformanceStoreCalc();

  virtual std::string GetAudioInputCalcId() { return audio_input_id_; };

  virtual MSAudioPerformance CalcPerformance() = 0;

 private:
  std::string audio_input_id_ = "";
};

class AudioPerformanceStore {
 public:
  AudioPerformanceStore() = default;

  ~AudioPerformanceStore() = default;

  // Register a AudioPerformanceStoreCalc instance
  void RegisterAudioPerformanceCalc(AudioPerformanceStoreCalc* calc);

  // Unregister a AudioPerformanceStoreCalc instance
  void UnRegisterAudioPerformanceCalc(AudioPerformanceStoreCalc* calc);

  // Get the Audio Performance for a given audio ID
  std::pair<bool, MSAudioPerformance> GetAudioPerformance(
      const std::string& audio_id);

 private:
  std::mutex calc_map_mtx_;
  std::map<std::string, AudioPerformanceStoreCalc*> calc_map_;
};

}  // namespace mediasdk