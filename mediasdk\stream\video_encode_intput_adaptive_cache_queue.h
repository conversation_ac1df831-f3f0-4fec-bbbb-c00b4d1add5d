#pragma once

#include <queue>
#include "video/video_texture.h"

namespace mediasdk {

class VideoEncodeInputAdaptiveCacheQueue {
 public:
  VideoEncodeInputAdaptiveCacheQueue() {}

  ~VideoEncodeInputAdaptiveCacheQueue() {}

  void Enqueue(const VideoTexture& texture) {
    // Copy the `texture` and save to `cache_`
    auto copy_tex = std::make_unique<VideoTexture>(texture);
    *copy_tex = texture;
    copy_tex->SetTexture(texture.GetTexture()->CopyTexture());
    cache_.push(std::move(copy_tex));
  }

  std::unique_ptr<VideoTexture> DeQueue() {
    if (IsEmpty()) {
      return nullptr;
    }

    auto tex = std::move(cache_.front());
    cache_.pop();
    return std::move(tex);
  }

  bool IsEmpty() { return cache_.empty(); }

 private:
  std::queue<std::unique_ptr<VideoTexture>> cache_;
};

}  // namespace mediasdk
