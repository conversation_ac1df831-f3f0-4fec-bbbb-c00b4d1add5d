#pragma once
#include <assert.h>
#include <base/strings/string_util.h>
#include <stdint.h>
#include <string>

namespace mediasdk {
namespace trace {

#define SMART_STRINGIFY_CASE(ENUM_CODE) \
  case ENUM_CODE:                       \
    return #ENUM_CODE

enum AUDIO_INNER_EVENT_TYPE : int32_t {
  EVENT_TYPE_UNKOWN = 0,
  PERFECT_CONSUME,
  INPUT_MIDDLLE_CONSUME,
  OUTPUT_MIDDLE_CONSUME,
  MIX_BUFFER_OVER_FLOW,
  REALLY_PUSH_BACK,
  A_LITTLE_EARLY,
  A_LITTLE_LATE,
  TOO_LATE,
  TOO_EARLY,
  EVENT_TYPE_MAX_VALUE = TOO_EARLY + 1
};

inline const char* TypeToString(AUDIO_INNER_EVENT_TYPE type) {
  switch (type) {
    SMART_STRINGIFY_CASE(PERFECT_CONSUME);
    SMART_STRINGIFY_CASE(INPUT_MIDDLLE_CONSUME);
    SMART_STRINGIFY_CASE(OUTPUT_MIDDLE_CONSUME);
    SMART_STRINGIFY_CASE(MIX_BUFFER_OVER_FLOW);
    SMART_STRINGIFY_CASE(REALLY_PUSH_BACK);
    SMART_STRINGIFY_CASE(A_LITTLE_EARLY);
    SMART_STRINGIFY_CASE(A_LITTLE_LATE);
    SMART_STRINGIFY_CASE(TOO_LATE);
    SMART_STRINGIFY_CASE(TOO_EARLY);
    default:
      DCHECK(false);
  }
  return "error";
}

inline AUDIO_INNER_EVENT_TYPE GetEventTypeFromStr(const std::string& str) {
  for (int i = PERFECT_CONSUME;
       i < AUDIO_INNER_EVENT_TYPE::EVENT_TYPE_MAX_VALUE; i++) {
    if (base::EndsWith(str,
                       TypeToString(static_cast<AUDIO_INNER_EVENT_TYPE>(i)))) {
      return static_cast<AUDIO_INNER_EVENT_TYPE>(i);
    }
  }
  return AUDIO_INNER_EVENT_TYPE::EVENT_TYPE_UNKOWN;
}

enum AUDIO_DURATION_TYPE : int32_t {
  AUDIO_DURATION_TYPE_UNKOWN = 0,
  FRAME,
  FIRST_FRAME,
  MIX_START,
  MIX_EMPTY,
  MIX_SUCCESS,
  BUFFER_CHANGE_FROM,
  BUFFER_CHANGE_TO,
  TRY_PULL_FROM,
  AUDIO_DURATION_TYPE_MAX_VALUE = TRY_PULL_FROM + 1
};

inline const char* TypeToString(AUDIO_DURATION_TYPE type) {
  switch (type) {
    SMART_STRINGIFY_CASE(FRAME);
    SMART_STRINGIFY_CASE(FIRST_FRAME);
    SMART_STRINGIFY_CASE(MIX_START);
    SMART_STRINGIFY_CASE(MIX_EMPTY);
    SMART_STRINGIFY_CASE(MIX_SUCCESS);
    SMART_STRINGIFY_CASE(BUFFER_CHANGE_FROM);
    SMART_STRINGIFY_CASE(BUFFER_CHANGE_TO);
    SMART_STRINGIFY_CASE(TRY_PULL_FROM);
    default:
      DCHECK(false);
  }
  return "error";
}

inline AUDIO_DURATION_TYPE GetDurationTypeFromStr(const std::string& str) {
  for (int i = AUDIO_DURATION_TYPE::FRAME;
       i < AUDIO_DURATION_TYPE::AUDIO_DURATION_TYPE_MAX_VALUE; i++) {
    if (base::EndsWith(str,
                       TypeToString(static_cast<AUDIO_DURATION_TYPE>(i)))) {
      return static_cast<AUDIO_DURATION_TYPE>(i);
    }
  }
  return AUDIO_DURATION_TYPE::AUDIO_DURATION_TYPE_UNKOWN;
}

enum TASK_COST_TYPE : int32_t {
  COST_TYPE_UNKOWN = 0,
  UserCall,
  Initialize,
  Uninitialize,
  WINDOW_MSG,
  RENDER_PUMP,
  AUDIO_PUMP,

  StartStream,
  StartStream_AudioEecoder,
  StartStream_AudioEecoder_AttachMix,
  StartStream_VideoEecoder,
  StartStream_VideoEecoder_AttachModel,
  StartStream_Stream_Connect,
  StartStream_Stream_MetaData,

  StreamPath_A_Frame,
  StreamPath_A_Packet,
  StreamPath_A,
  StreamPath_V_Frame,
  StreamPath_V_Packet,
  StreamPath_V,

  StopStream,
  StopStream_SignalStop,
  StopStream_StopConnect,

  StopStream_StreamDestroyed,
  StopStream_AudioEncoderDestroyed,
  StopStream_VideoEncoderDestroyed,

  CreateVisual,
  CreateVisual_CreateInputSource,
  CreateVisual_AddToVector,
  CreateVisual_AddAudioInput,
  CreateVisual_OnAudioInputCreated,

  DestroyVisual,
  DestroyVisual_RemoveAudioInput,
  DestroyVisual_OnCorrespondingAudioInputDestroyed,
  DestroyVisual_RemoveFromModel,
  DestroyVisual_DestroyVideoSource,
  DestroyVisual_OnVideoSourceDestroyed,

  CreateAudioInput,
  CreateAudioInput_CreateInputSource,
  CreateAudioInput_AddToTrack,

  DestroyAudioInput,
  DestroyAudioInput_RemoveFromTrack,
  DestroyAudioInput_DestroyAudioSource,
  DestroyAudioInput_OnAudioSourceDestroyed,

  kEnumCaptureAudios,
  kEnumRenderAudios,

  VIDEO_SIZE_CHANGE,

  EnqueVideoFrame,

  kAVSYN_AudioMix,
  kAVSYN_VideoMix,

  kAVSYN_AudioDTS,
  kAVSYN_VideoDTS,
  kAVSYN_VideoPTS,

  kAVSYN_AudioEncodeDTSMS,
  kAVSYN_VideoEncodeDTSMS,

  kAVSYN_MuxerADTSMS,
  kAVSYN_MuxerVDTSMS,

  COST_TYPE_MAX_VALUE
};

inline const char* TypeToString(TASK_COST_TYPE type) {
  switch (type) {
    SMART_STRINGIFY_CASE(UserCall);
    SMART_STRINGIFY_CASE(Initialize);
    SMART_STRINGIFY_CASE(Uninitialize);
    SMART_STRINGIFY_CASE(WINDOW_MSG);
    SMART_STRINGIFY_CASE(RENDER_PUMP);
    SMART_STRINGIFY_CASE(AUDIO_PUMP);

    SMART_STRINGIFY_CASE(StartStream);
    SMART_STRINGIFY_CASE(StartStream_AudioEecoder);
    SMART_STRINGIFY_CASE(StartStream_AudioEecoder_AttachMix);
    SMART_STRINGIFY_CASE(StartStream_VideoEecoder);
    SMART_STRINGIFY_CASE(StartStream_VideoEecoder_AttachModel);
    SMART_STRINGIFY_CASE(StartStream_Stream_Connect);
    SMART_STRINGIFY_CASE(StartStream_Stream_MetaData);

    SMART_STRINGIFY_CASE(StreamPath_A_Frame);
    SMART_STRINGIFY_CASE(StreamPath_A_Packet);
    SMART_STRINGIFY_CASE(StreamPath_A);

    SMART_STRINGIFY_CASE(StreamPath_V_Frame);
    SMART_STRINGIFY_CASE(StreamPath_V_Packet);
    SMART_STRINGIFY_CASE(StreamPath_V);

    SMART_STRINGIFY_CASE(StopStream);
    SMART_STRINGIFY_CASE(StopStream_SignalStop);
    SMART_STRINGIFY_CASE(StopStream_StopConnect);
    SMART_STRINGIFY_CASE(StopStream_StreamDestroyed);
    SMART_STRINGIFY_CASE(StopStream_AudioEncoderDestroyed);
    SMART_STRINGIFY_CASE(StopStream_VideoEncoderDestroyed);

    SMART_STRINGIFY_CASE(CreateVisual);
    SMART_STRINGIFY_CASE(CreateVisual_CreateInputSource);
    SMART_STRINGIFY_CASE(CreateVisual_AddToVector);
    SMART_STRINGIFY_CASE(CreateVisual_AddAudioInput);
    SMART_STRINGIFY_CASE(CreateVisual_OnAudioInputCreated);

    SMART_STRINGIFY_CASE(DestroyVisual);
    SMART_STRINGIFY_CASE(DestroyVisual_RemoveFromModel);
    SMART_STRINGIFY_CASE(DestroyVisual_DestroyVideoSource);
    SMART_STRINGIFY_CASE(DestroyVisual_OnCorrespondingAudioInputDestroyed);
    SMART_STRINGIFY_CASE(DestroyVisual_RemoveAudioInput);
    SMART_STRINGIFY_CASE(DestroyVisual_OnVideoSourceDestroyed);

    SMART_STRINGIFY_CASE(CreateAudioInput);
    SMART_STRINGIFY_CASE(CreateAudioInput_CreateInputSource);
    SMART_STRINGIFY_CASE(CreateAudioInput_AddToTrack);

    SMART_STRINGIFY_CASE(DestroyAudioInput);
    SMART_STRINGIFY_CASE(DestroyAudioInput_RemoveFromTrack);
    SMART_STRINGIFY_CASE(DestroyAudioInput_OnAudioSourceDestroyed);
    SMART_STRINGIFY_CASE(DestroyAudioInput_DestroyAudioSource);

    SMART_STRINGIFY_CASE(kEnumCaptureAudios);
    SMART_STRINGIFY_CASE(kEnumRenderAudios);
    SMART_STRINGIFY_CASE(VIDEO_SIZE_CHANGE);
    SMART_STRINGIFY_CASE(EnqueVideoFrame);
    SMART_STRINGIFY_CASE(kAVSYN_AudioMix);
    SMART_STRINGIFY_CASE(kAVSYN_VideoMix);
    SMART_STRINGIFY_CASE(kAVSYN_AudioDTS);
    SMART_STRINGIFY_CASE(kAVSYN_VideoDTS);
    SMART_STRINGIFY_CASE(kAVSYN_VideoPTS);
    SMART_STRINGIFY_CASE(kAVSYN_AudioEncodeDTSMS);
    SMART_STRINGIFY_CASE(kAVSYN_VideoEncodeDTSMS);
    SMART_STRINGIFY_CASE(kAVSYN_MuxerADTSMS);
    SMART_STRINGIFY_CASE(kAVSYN_MuxerVDTSMS);
    default:
      DCHECK(false);
  }
  return "error";
}

inline TASK_COST_TYPE GetTaskTypeFromStr(const std::string& str) {
  for (int i = TASK_COST_TYPE::COST_TYPE_UNKOWN + 1;
       i < TASK_COST_TYPE::COST_TYPE_MAX_VALUE; i++) {
    if (base::StartsWith(str, TypeToString(static_cast<TASK_COST_TYPE>(i)))) {
      return static_cast<TASK_COST_TYPE>(i);
    }
  }
  return TASK_COST_TYPE::COST_TYPE_UNKOWN;
}

}  // namespace trace

}  // namespace mediasdk
