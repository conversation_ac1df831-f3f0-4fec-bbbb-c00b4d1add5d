#include "lyrax_audio_input.h"

#include <mutex>
#include "audio_input.h"
#include "base/logging.h"
#include "base/mediasdk/thread_safe_deleter.h"
#include "component_proxy.h"
#include "frame_convert.h"
#include "mediasdk/audio/audio_controller.h"
#include "mediasdk/component_center.h"
#include "mediasdk/plugin_service/plugin_service.h"
#include "mediasdk/public/plugin/audio_input_source.h"
#include "mediasdk_thread.h"

namespace mediasdk {

std::shared_ptr<LyraxAudioInput> LyraxAudioInput::Create(
    const std::string& id,
    const AudioFormat& output_format) {
  LOG(INFO) << "CreateAudio Input lyrax[" << id;
  return std::shared_ptr<LyraxAudioInput>(
      new LyraxAudioInput(id, output_format),
      base::ThreadSafeDeleter<LyraxAudioInput>());
}

LyraxAudioInput::LyraxAudioInput(const std::string& id,
                                 const AudioFormat& output_format)
    : SourceAudioInput(id, output_format) {
  LOG(INFO) << "[LyraxAudioInput][LyraxAudioInput] construct";
}

LyraxAudioInput::~LyraxAudioInput() {
  LOG(INFO) << "[LyraxAudioInput][~LyraxAudioInput] destruct";
  capture_delegate_.reset();
}

void LyraxAudioInput::OnAudio(const AudioFormat& format,
                              const AudioSourceFrame& audio_frame) {
  auto processor = GetProcessor();
  if (!processor) {
    return;
  }
  AudioFrame frame = AudioSourceFrameToAudioFrame(audio_frame);
  processor->PushMicFrame(format, frame);
}

void LyraxAudioInput::SetMute(bool mute) {
  AudioInput::SetMute(mute);
  auto processor = GetProcessor();
  if (!processor) {
    DCHECK(false);
    LOG(ERROR) << "SetMute processor empty";
    return;
  }
  processor->SetMute(mute);
}

bool LyraxAudioInput::SetAECOption(const bool enable) {
  auto processor = GetProcessor();
  if (!processor) {
    DCHECK(false);
    LOG(ERROR) << "SetAECOption processor empty";
    return false;
  }
  bool pre_aec_enable = processor->IsAECEnable();
  bool curr_aec_enable = enable;
  bool pre_echo_detection_enable = processor->IsEchoDetectionEnable();
  bool curr_echo_detection_enable = pre_echo_detection_enable;
  CheckReferenceAudioInput(pre_aec_enable, curr_aec_enable,
                           pre_echo_detection_enable,
                           curr_echo_detection_enable);
  processor->SetAECOption(enable);
  return true;
}

bool LyraxAudioInput::SetANSOption(const int32_t level) {
  auto processor = GetProcessor();
  if (!processor) {
    DCHECK(false);
    LOG(ERROR) << "SetANSOption processor empty";
    return false;
  }
  processor->SetANSOption(level);
  return true;
}

bool LyraxAudioInput::SetRawDataOption(const int32_t mode) {
  raw_data_api_option_ = mode;
  auto processor = GetProcessor();
  if (!processor) {
    DCHECK(false);
    LOG(ERROR) << "SetRawDataOption processor empty";
    return false;
  }
  processor->SetRawDataOption(mode);
  return true;
}

bool LyraxAudioInput::EnableEchoDetection(const int32_t interval) {
  auto processor = GetProcessor();
  if (!processor) {
    DCHECK(false);
    LOG(ERROR) << "EnableEchoDetection processor empty";
    return false;
  }
  bool pre_aec_enable = processor->IsAECEnable();
  bool curr_aec_enable =
      pre_aec_enable;  // aec is not changed in this interface
  bool pre_echo_detection_enable = processor->IsEchoDetectionEnable();
  bool curr_echo_detection_enable = interval > 0;
  CheckReferenceAudioInput(pre_aec_enable, curr_aec_enable,
                           pre_echo_detection_enable,
                           curr_echo_detection_enable);
  return processor->EnableEchoDetection(interval);
}

void LyraxAudioInput::SetRefId(const std::string& id) {
  LOG(INFO) << "set ref[" << id << "]";
  ref_id_ = id;

  AudioController* audio = com::GetAudioController();
  if (!seted_ref_id_.empty()) {
    // remove pre ref
    LOG(INFO) << "RemoveInputAudioObserver [" << seted_ref_id_ << "]";
    audio->RemoveInputAudioObserver(seted_ref_id_, shared_from_this());
    seted_ref_id_ = "";
  }
  auto process = GetProcessor();
  if (process) {
    if (process->IsAECEnable() || process->IsEchoDetectionEnable()) {
      LOG(INFO) << "AddInputAudioObserver [" << id << "]";
      audio->AddInputAudioObserver(id, shared_from_this());
      seted_ref_id_ = id;
    }
  }
}

void LyraxAudioInput::OnLyraxPreAudioFrame(const AudioFormat& format,
                                           const AudioFrame& frame) {
  AudioInput::ComputePreAudioVolume(format, frame);
}

void LyraxAudioInput::OnLyraxPostAudioFrame(const AudioFormat& format,
                                            const AudioFrame& frame) {
  AudioInput::ProcessAudioFrame(format, frame);
}

void LyraxAudioInput::OnInputAudioFrame(const std::string& input_id,
                                        const AudioFrame& frame,
                                        const AudioFormat& format) {
  auto processor = GetProcessor();
  if (!processor) {
    return;
  }
  processor->PushRefFrame(format, frame);
}

void LyraxAudioInput::SetAudioProcessor(
    std::shared_ptr<LyraxAudioProcessor> processor) {
  if (!processor) {
    DCHECK(false);
    return;
  }

  std::lock_guard<std::mutex> lock(lock_processor_);
  processor_ = processor;
  if (!capture_delegate_) {
    capture_delegate_ =
        std::make_shared<LyraxAudioCaptureDelegate>(shared_from_this());
  }
  processor_->SetAudioDeviceDelegate(capture_delegate_);
}

std::shared_ptr<LyraxAudioProcessor> LyraxAudioInput::GetProcessor() {
  std::lock_guard<std::mutex> lock(lock_processor_);
  return processor_;
}

bool LyraxAudioInput::InterceptAudioEvent(const char* json_str) {
  try {
    nlohmann::json json_data = nlohmann::json::parse(json_str);
    std::string event_name = json_data["event_name"];
    // The frontend tracking needs to report raw_data_api_option (which is the
    // api value) in the WASAPIAudioRawDataModeUpdated event, this value is only
    // stored in lyrax audio input, so we intercept and convert it here
    if (event_name == "WASAPIAudioRawDataModeUpdated") {
      json_data["raw_data_api_option"] = raw_data_api_option_;
      json_data["event_name"] = "LyraxAudioRawDataModeUpdated";
      std::string json_str = json_data.dump();
      LOG(INFO) << "InterceptAudioEvent and send to super: " << json_str;
      SourceAudioInput::SignalSourceEvent(json_str.c_str());
      return true;
    }
  } catch (std::exception& e) {
    LOG(ERROR) << "InterceptAudioEvent parse json error: " << e.what();
  }
  return false;
}

void LyraxAudioInput::CheckReferenceAudioInput(
    bool pre_aec_enable,
    bool curr_aec_enable,
    bool pre_echo_detection_enable,
    bool curr_echo_detection_enable) {
  if ((pre_aec_enable == curr_aec_enable) &&
      (pre_echo_detection_enable == curr_echo_detection_enable)) {
    return;
  }
  AudioController* audio = com::GetAudioController();
  if (curr_aec_enable || curr_echo_detection_enable) {
    if (!seted_ref_id_.empty()) {
      if (seted_ref_id_ != ref_id_) {
        LOG(INFO) << "RemoveInputAudioObserver [" << seted_ref_id_ << "]";
        audio->RemoveInputAudioObserver(seted_ref_id_, shared_from_this());
        LOG(INFO) << "AddInputAudioObserver [" << ref_id_ << "]";
        if (!ref_id_.empty()) {
          audio->AddInputAudioObserver(ref_id_, shared_from_this());
        }
        seted_ref_id_ = ref_id_;
      }
    } else {
      LOG(INFO) << "AddInputAudioObserver [" << ref_id_ << "]";
      if (!ref_id_.empty()) {
        audio->AddInputAudioObserver(ref_id_, shared_from_this());
      }
      seted_ref_id_ = ref_id_;
    }
  } else {
    if (!seted_ref_id_.empty()) {
      LOG(INFO) << "RemoveInputAudioObserver [" << seted_ref_id_ << "]";
      audio->RemoveInputAudioObserver(seted_ref_id_, shared_from_this());
      seted_ref_id_ = "";
    }
  }
}

void LyraxAudioInput::SignalSourceEvent(const char* json_str) {
  if (InterceptAudioEvent(json_str)) {
    return;
  }
  SourceAudioInput::SignalSourceEvent(json_str);
  if (capture_delegate_) {
    capture_delegate_->SignalEvent(json_str);
  } else {
    LOG(ERROR) << "capture_delegate_ is_empty, id: " << GetId()
               << ", json_str: " << json_str;
  }
}

static void SendActionsToAudioSource(std::weak_ptr<AudioInputSource> source,
                                     const std::string& json_str) {
  if (auto s = source.lock()) {
    s->Action(json_str.c_str());
  }
}

static void MergeJsonString(std::string& lhs, const std::string& rhs) {
  if (rhs.empty()) {
    return;
  }
  if (lhs.empty()) {
    lhs = rhs;
    return;
  }
  try {
    nlohmann::json json_lhs = nlohmann::json::parse(lhs);
    nlohmann::json json_rhs = nlohmann::json::parse(rhs);
    for (auto it = json_rhs.begin(); it != json_rhs.end(); ++it) {
      json_lhs[it.key()] = it.value();
    }
    lhs = json_lhs.dump();
  } catch (std::exception& e) {
    LOG(ERROR) << "MergeJsonString parse json error: " << e.what();
  }
}

void LyraxAudioInput::AttachSource(std::shared_ptr<AudioInputSource> source) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  SourceAudioInput::AttachSource(source);
  std::string pending_action;
  {
    std::lock_guard<std::mutex> _(pending_action_lock_);
    pending_action = pending_action_;
    pending_action_ = "";
  }
  // post to plugin thread
  PostToPlugin(FROM_HERE, base::BindOnce(SendActionsToAudioSource, source_,
                                         pending_action));
}

MediaSDKString LyraxAudioInput::GetInitParams() {
  std::lock_guard<std::mutex> _(pending_action_lock_);
  // Initialization parameters for source. If this has a value, it means
  // SendActionToAudioSource was triggered before the source was created. This
  // effectively avoids restarting the audio client when parameters change after
  // source creation.
  return pending_action_;
}

// call on lyrax callback thread
bool LyraxAudioInput::SendActionToAudioSource(const std::string& json_str) {
  // update raw data mode to processor
  auto processor = GetProcessor();
  auto capture_delegate = capture_delegate_;
  if (processor && capture_delegate) {
    processor->SetRawDataMode(capture_delegate->IsRawDataModeEnabled());
  }

  // send action to audio source
  if (source_) {
    // post to plugin thread
    PostToPlugin(FROM_HERE,
                 base::BindOnce(SendActionsToAudioSource, source_, json_str));
  } else {
    // SendActionToAudioSource is triggered after setAudioDeviceDelegate, on the
    // lyrax callback thread It triggers quickly, so source_ might not be set
    // yet, so we need to cache it
    std::lock_guard<std::mutex> _(pending_action_lock_);
    MergeJsonString(pending_action_, json_str);
    LOG(ERROR) << "source_ is_empty, pending, id: " << GetId()
               << ", json_str: " << json_str;
  }
  return false;
}

}  // namespace mediasdk