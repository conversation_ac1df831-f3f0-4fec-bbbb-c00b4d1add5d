#pragma once

#include "base/synchronization/waitable_event.h"
#include "base/threading/thread.h"
#include "base/time/time.h"
#include "base/timer/timer.h"
#include "origin_stream_service.h"
#include "speed_test_raw_data.h"
#include "stream_rate_calc.h"

namespace mediasdk {

class SpeedTestStreamServiceReporter;

class SpeedTestStreamService : public OriginStreamService {
 public:
  SpeedTestStreamService(std::shared_ptr<StreamConfig> config,
                         std::shared_ptr<StreamServiceSource> source);

  ~SpeedTestStreamService();

  // StreamService:
  bool Start() override;

  void Stop() override;

 protected:
  bool PreprocessingBeforeDisconnected() override;

  bool PreprocessingAfterConnected() override;

  bool OnDropPacket(std::shared_ptr<PacketBase> packet) override;

  bool WritePacket(std::shared_ptr<PacketBase> packet) override;

  void EnqueuePacket(std::shared_ptr<PacketBase> packet,
                     bool drop = true) override;

 private:
  void OnReportTimer();

  void OnProducerPump();

  void WriteFlvMetaData();

  void WriteFlvVideoHeader();

  void WriteFlvVideoIdrFrame();

  void WriteFlvVideoBFrame();

  void WriteFlvAudioHeader();

  void WriteFlvAudioData();

  void BuildMetaData();

 private:
  std::unique_ptr<base::Thread> producer_thread_;
  base::WaitableEvent stop_event_;
  std::vector<uint8_t> meta_data_ = {};
  StreamRateCalc rate_calc_;

  std::atomic_int64_t last_net_time_stamp_ = 0;
  std::atomic_int64_t connect_time_stamp_ = 0;
  int dns_time_ = 0;
  int socket_connect_time_ = 0;
  int rtmp_connect_time_ = 0;
  std::vector<StreamServiceSourceBandwidthInfo> band_infos_ = {};

  base::RepeatingTimer report_timer_;
  friend class SpeedTestStreamServiceReporter;
  std::unique_ptr<SpeedTestStreamServiceReporter> reporter_ = nullptr;
};

}  // namespace mediasdk
