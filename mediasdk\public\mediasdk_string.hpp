#pragma once

#include <stdint.h>
#include <cassert>
#include <string>
#include <string_view>
#include "mediasdk_api_alloc.h"

namespace mediasdk {

#pragma pack(push, 1)

struct MediaSDKStringData {
  char* data;
  size_t size;
};

#pragma pack(pop)

class MediaSDKString {
 public:
  MediaSDKString() {
    data_.data = (char*)AllocBuffer(1);
    *data_.data = '\0';
    data_.size = 0;
  }

  MediaSDKString(const char* data) {
    assert(data);
    data_.size = strlen(data);
    data_.data = (char*)AllocBuffer(data_.size + 1);
    memcpy(data_.data, data, data_.size + 1);
  }

  MediaSDKString(const char* data, int size) {
    assert(data);
    data_.size = size;
    data_.data = (char*)AllocBuffer(data_.size + 1);
    memcpy(data_.data, data, data_.size + 1);
  }

  MediaSDKString(MediaSDKStringData&& data) noexcept {
    data_ = data;
    data.data = nullptr;
    data.size = 0;
  }

  MediaSDKString& operator=(MediaSDKStringData&& data) noexcept {
    FreeBuffer(data_.data);
    data_ = data;
    data.data = nullptr;
    data.size = 0;

    return *this;
  }

  MediaSDKString(const std::string& str)
      : MediaSDKString(str.data(), str.size()) {}

  MediaSDKString(const std::string_view& str)
      : MediaSDKString(str.data(), str.size()) {}

  MediaSDKString(const MediaSDKString& other) {
    data_.size = other.data_.size;
    data_.data = (char*)AllocBuffer(data_.size + 1);
    memcpy(data_.data, other.data_.data, data_.size + 1);
  }

  MediaSDKString& operator=(const MediaSDKString& other) {
    if (this != &other) {
      FreeBuffer(data_.data);
      data_.size = other.data_.size;
      data_.data = (char*)AllocBuffer(data_.size + 1);
      memcpy(data_.data, other.data_.data, data_.size + 1);
    }
    return *this;
  }

  MediaSDKString(MediaSDKString&& other) noexcept : data_(other.data_) {
    other.data_.data = nullptr;
    other.data_.size = 0;
  }

  MediaSDKString& operator=(MediaSDKString&& other) noexcept {
    if (this != &other) {
      FreeBuffer(data_.data);
      data_ = other.data_;
      other.data_.data = nullptr;
      other.data_.size = 0;
    }
    return *this;
  }

  MediaSDKStringData Detach() {
    MediaSDKStringData ret = data_;
    data_.data = nullptr;
    data_.size = 0;
    return ret;
  }

  ~MediaSDKString() { FreeBuffer(data_.data); }

  char* data() const { return data_.data; }

  size_t size() const { return data_.size; }

  bool empty() const { return size() == 0; }

  std::string ToString() const {
    if (data_.data) {
      return std::string(data_.data, data_.size);
    }
    return std::string();
  }

 private:
  MediaSDKStringData data_;
};

}  // namespace mediasdk
