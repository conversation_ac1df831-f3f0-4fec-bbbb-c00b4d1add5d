#include "audio_encoder_source_factory.h"

#include <base/logging.h>
#include <mediasdk/mediasdk_thread.h>
#include "base/mediasdk/thread_safe_deleter.h"

namespace mediasdk {

// static
std::shared_ptr<AudioEncoderSourceFactory> AudioEncoderSourceFactory::Create(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info) {
  auto factory = std::make_shared<AudioEncoderSourceFactory>(library, info);
  if (factory && factory->Load()) {
    return factory;
  }

  return nullptr;
}

AudioEncoderSourceFactory::AudioEncoderSourceFactory(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info)
    : SourceFactory(library, info) {}

AudioEncoderSourceFactory::~AudioEncoderSourceFactory() {}

std::shared_ptr<AudioEncoderSource> AudioEncoderSourceFactory::CreateSource(
    AudioEncoderProxy* proxy,
    const std::string& json_params) {
  if (create_func_) {
    auto ret = create_func_(proxy, json_params.c_str());
    if (ret) {
      std::shared_ptr<AudioEncoderSource> source(
          ret,
          base::ThreadSafeClosureDeleter<AudioEncoderSource>(base::BindOnce(
              &AudioEncoderSourceFactory::Destroy, AsWeakPtr())));
      return source;
    }
  }
  return nullptr;
}

bool AudioEncoderSourceFactory::Load() {
  create_func_ = reinterpret_cast<CreateAudioEncoderSourceFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "CreateAudioEncoderSource"));

  if (!create_func_) {
    LOG(ERROR)
        << info_->name.data()
        << ": Failed to get function pointer for CreateAudioEncoderSource";
    return false;
  }

  destroy_func_ = reinterpret_cast<DestroyAudioEncoderSourceFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "DestroyAudioEncoderSource"));

  if (!destroy_func_) {
    LOG(ERROR)
        << info_->name.data()
        << ": Failed to get function pointer for DestroyAudioEncoderSource";
    return false;
  }

  return true;
}

void AudioEncoderSourceFactory::Destroy(AudioEncoderSource* source) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (destroy_func_) {
    destroy_func_(source);
  }
}

}  // namespace mediasdk
