
#pragma once

#include <base/native_library.h>
#include <memory>
#include <set>
#include "mediasdk/public/mediasdk_defines.h"
#include "source_factory.h"

namespace mediasdk {

class AudioFilter;

class AudioFilterFactory : public SourceFactory {
 public:
  static std::shared_ptr<AudioFilterFactory> Create(
      base::NativeLibrary library,
      std::shared_ptr<PluginInfo> info);

  AudioFilterFactory(base::NativeLibrary library,
                     std::shared_ptr<PluginInfo> info);

  // SourceFactory:
  PluginType GetType() const override { return PluginType::kAudioFilter; }

  std::shared_ptr<AudioFilter> CreateFilter(const std::string& json_params);

  void Destroy(std::shared_ptr<AudioFilter> filter);

  void DestroyAll();

  std::shared_ptr<PluginInfo> GetInfo() const { return info_; }

 private:
  bool Load();

  typedef AudioFilter* (*CreateAudioFilterFunc)(const char*);
  typedef void (*DestroyAudioFilter)(AudioFilter*);

  CreateAudioFilterFunc create_func_ = nullptr;
  DestroyAudioFilter destroy_func_ = nullptr;

  std::set<std::shared_ptr<AudioFilter>> filters_;
};

}  // namespace mediasdk
