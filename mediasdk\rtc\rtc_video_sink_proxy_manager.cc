#include "rtc_video_sink_proxy_manager.h"

namespace mediasdk {

RTCVideoSinkProxyManager::RTCVideoSinkProxyManager(IRTCManager* manager)
    : rtc_manager_(manager) {}

int32_t RTCVideoSinkProxyManager::SetVideoRender(
    const std::string& user_id,
    int32_t stream_index,
    std::shared_ptr<RTCVideoRender> render) {
  if (render) {
    auto proxy = CreateProxy(user_id, stream_index);
    proxy->SetVideoRender(std::move(render));
    return RegisterProxyToRTC(proxy);
  } else {
    return UnRegisterProxyToRTC(user_id, stream_index);
  }
}

void RTCVideoSinkProxyManager::SetRTCRoomCreated(bool created) {
  LOG(INFO) << "SetRTCRoomCreated, from " << rtc_room_created_ << " to "
            << created;

  if (!rtc_room_created_ && created) {
    rtc_room_created_ = true;
    for (auto& key_proxy : video_sink_list_) {
      RegisterProxyToRTC(key_proxy.second);
    }
  } else if (rtc_room_created_ && !created) {
    for (auto& key_proxy : video_sink_list_) {
      auto proxy = key_proxy.second;
      if (proxy) {
        UnRegisterProxyToRTC(proxy->GetUserId(), proxy->GetStreamIndex());
      }
    }
    rtc_room_created_ = false;
  }
}

void RTCVideoSinkProxyManager::SetRTCEngineDestoryed() {
  for (const auto& sink_item : video_sink_list_) {
    if (sink_item.second) {
      sink_item.second->SetVideoRender(nullptr);
    }
  }
  video_sink_list_.clear();
}

std::shared_ptr<RTCVideoSinkProxy> RTCVideoSinkProxyManager::CreateProxy(
    const std::string& user_id,
    int32_t stream_index) {
  std::shared_ptr<RTCVideoSinkProxy> proxy = nullptr;
  std::string key = base::StringPrintf("%s#%d", user_id.c_str(), stream_index);
  LOG(INFO) << "CreateProxy, key: " << key;
  if (video_sink_list_.find(key) == video_sink_list_.end()) {
    proxy = std::make_shared<RTCVideoSinkProxy>(user_id, stream_index);
    video_sink_list_[key] = proxy;
  } else {
    proxy = video_sink_list_[key];
    LOG(INFO) << "Proxy repeate used.";
  }
  return proxy;
}

void RTCVideoSinkProxyManager::DestoryProxy(const std::string& user_id,
                                            int32_t stream_index) {
  std::string key = base::StringPrintf("%s#%d", user_id.c_str(), stream_index);
  LOG(INFO) << "DestoryProxy, key: " << key;
  auto iter = video_sink_list_.find(key);
  if (iter != video_sink_list_.end()) {
    video_sink_list_.erase(iter);
    LOG(INFO) << "Success to destory proxy, key: " << key;
  }
}

int32_t RTCVideoSinkProxyManager::RegisterProxyToRTC(
    std::shared_ptr<RTCVideoSinkProxy> proxy) {
  LOG(INFO) << "RegisterProxyToRTC, rtc_room_created_: " << rtc_room_created_;
  int32_t rst = 0;
  if (rtc_room_created_) {
    rst = rtc_manager_->SetRemoteVideoSink(
        proxy->GetUserId(), proxy->GetStreamIndex(), proxy.get());
  }
  return rst;
}

int32_t RTCVideoSinkProxyManager::UnRegisterProxyToRTC(
    const std::string& user_id,
    int32_t stream_index) {
  LOG(INFO) << "UnRegisterProxyToRTC, rtc_room_created_: " << rtc_room_created_;
  int32_t rst = 0;
  if (rtc_room_created_) {
    rst = rtc_manager_->SetRemoteVideoSink(user_id, stream_index, nullptr);
  }
  return rst;
}

}  // namespace mediasdk