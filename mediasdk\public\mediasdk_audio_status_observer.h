#pragma once

#include "mediasdk_string.hpp"

namespace mediasdk {

class MediaSDKAudioStatusObserver {
 public:
  virtual void OnAudioPeak(MediaSDKString id,
                           const float* peak_array,
                           uint32_t peak_array_size) = 0;

  virtual void OnAudioEvent(MediaSDKString id, MediaSDKString event) = 0;

  virtual void OnAudioPeakLR(MediaSDKString id,
                             const float left_dev,
                             const float right_dev,
                             const float left,
                             const float right) = 0;

  virtual void OnAudioTrackPeak(uint32_t track_id,
                                const float left,
                                const float right) = 0;

  virtual void OnEchoDetectionResult(const float probability) = 0;
};

}  // namespace mediasdk
