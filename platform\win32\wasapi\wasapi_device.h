#pragma once

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <devicetopology.h>
#include <guiddef.h>
#include <mmdeviceapi.h>
#include <mmreg.h>
#include <propsys.h>
#include <wrl/client.h>
#include <list>
#include <string>

#include <Functiondiscoverykeys_devpkey.h>
#include "audio/audio_format.h"

#ifndef KSAUDIO_SPEAKER_4POINT1
#define KSAUDIO_SPEAKER_4POINT1 (KSAUDIO_SPEAKER_QUAD | SPEAKER_LOW_FREQUENCY)
#endif  // !KSAUDIO_SPEAKER_4POINT1

namespace mediasdk {

class WASAPIDevice {
 public:
  struct DeviceName {
    std::wstring id;
    std::wstring name;
    GUID type;
    GUID sub_type;
  };

  static inline BOOL GetJackSubtype(IMMDevice* mmDevice, GUID& subType) {
    using Microsoft::WRL::ComPtr;

    HRESULT hRes = S_OK;
    if (mmDevice == NULL)
      return FALSE;
    ComPtr<IDeviceTopology> spEndpointTopology;
    ComPtr<IConnector> spPlug;
    ComPtr<IConnector> spJack;
    ComPtr<IPart> spJackAsPart;
    hRes = mmDevice->Activate(__uuidof(IDeviceTopology), CLSCTX_INPROC_SERVER,
                              NULL, (void**)&spEndpointTopology);
    if (hRes != S_OK)
      return FALSE;
    hRes = spEndpointTopology->GetConnector(0, &spPlug);
    if (hRes != S_OK)
      return FALSE;
    hRes = spPlug->GetConnectedTo(&spJack);
    if (hRes != S_OK)
      return FALSE;
    hRes = spJack->QueryInterface(__uuidof(IPart), (void**)&spJackAsPart);
    if (hRes != S_OK)
      return FALSE;
    hRes = spJackAsPart->GetSubType(&subType);
    if (hRes != S_OK)
      return FALSE;
    return TRUE;
  }

  static inline DeviceName GetDefaultDevice(EDataFlow dataFlow) {
    using Microsoft::WRL::ComPtr;

    DeviceName ret;
    ComPtr<IMMDeviceEnumerator> enumerator;
    HRESULT hRes =
        ::CoCreateInstance(__uuidof(MMDeviceEnumerator), NULL,
                           CLSCTX_INPROC_SERVER, IID_PPV_ARGS(&enumerator));
    if (hRes != S_OK) {
      if (hRes == CO_E_NOTINITIALIZED) {
        hRes = ::CoInitializeEx(0, COINIT_APARTMENTTHREADED);
        if (hRes == S_OK) {
          hRes = ::CoCreateInstance(__uuidof(MMDeviceEnumerator), NULL,
                                    CLSCTX_INPROC_SERVER,
                                    IID_PPV_ARGS(&enumerator));
        }
      }
      if (hRes != S_OK) {
        LOG(ERROR) << base::StringPrintf("Failed to CoCreateInstance(0x%X)",
                                         hRes);
        return ret;
      }
    }
    ComPtr<IMMDevice> device;
    hRes = enumerator->GetDefaultAudioEndpoint(dataFlow, eConsole, &device);
    if (hRes != S_OK) {
      LOG(ERROR) << base::StringPrintf(
          "Failed to GetDefaultAudioEndpoint(eRender, eConsole, 0x%X)", hRes);
      return ret;
    }
    std::wstring wszID;
    LPWSTR pwszVal = NULL;
    hRes = device->GetId(&pwszVal);
    if (SUCCEEDED(hRes) && pwszVal != NULL) {
      wszID = pwszVal;
      CoTaskMemFree(pwszVal);
      pwszVal = NULL;
    }
    std::string guid;
    std::wstring wszNAME;
    ComPtr<IPropertyStore> prop;
    hRes = device->OpenPropertyStore(STGM_READ, &prop);
    if (SUCCEEDED(hRes)) {
      PROPVARIANT varName;
      PropVariantInit(&varName);
      hRes = prop->GetValue(PKEY_Device_FriendlyName, &varName);
      if (SUCCEEDED(hRes) && varName.pwszVal != NULL) {
        wszNAME = varName.pwszVal;
        PropVariantClear(&varName);
        GUID type = {0};
        PROPVARIANT varGUID;
        PropVariantInit(&varGUID);
        PROPERTYKEY PKEY_AudioEndpoint_GUID = {
            {0x1da5d803,
             0xd492,
             0x4edd,
             {0x8c, 0x23, 0xe0, 0xc0, 0xff, 0xee, 0x7f, 0x0e}},
            4};
        hRes = prop->GetValue(PKEY_AudioEndpoint_GUID, &varGUID);
        if (SUCCEEDED(hRes) && varGUID.pwszVal != NULL) {
          CLSIDFromString(varGUID.pwszVal, &type);
          PropVariantClear(&varGUID);
          GUID subtype = {0};
          if (GetJackSubtype(device.Get(), subtype)) {
            ret =
                DeviceName{std::move(wszID), std::move(wszNAME), type, subtype};
          }
        }
      }
    }
    return ret;
  }

  static inline DeviceName GetDefaultOutput() {
    return GetDefaultDevice(EDataFlow::eRender);
  }

  static inline DeviceName GetDefaultInput() {
    return GetDefaultDevice(EDataFlow::eCapture);
  }

  static inline std::list<DeviceName> GetDevList(EDataFlow dataFlow) {
    std::list<DeviceName> ret;
    Microsoft::WRL::ComPtr<IMMDeviceEnumerator> enumerator;
    HRESULT hRes =
        ::CoCreateInstance(__uuidof(MMDeviceEnumerator), NULL,
                           CLSCTX_INPROC_SERVER, IID_PPV_ARGS(&enumerator));
    if (hRes != S_OK) {
      LOG(ERROR) << base::StringPrintf("Failed to CoCreateInstance(0x%X)",
                                       hRes);
      return ret;
    }
    Microsoft::WRL::ComPtr<IMMDeviceCollection> collection;
    hRes = enumerator->EnumAudioEndpoints(dataFlow, DEVICE_STATE_ACTIVE,
                                          &collection);
    if (hRes != S_OK) {
      LOG(ERROR) << base::StringPrintf("Failed to EnumAudioEndpoints 0x%X",
                                       hRes);
      return ret;
    }
    UINT count = 0;
    hRes = collection->GetCount(&count);
    if (hRes != S_OK)
      return ret;
    for (UINT i = 0; i < count; i++) {
      Microsoft::WRL::ComPtr<IMMDevice> device;
      hRes = collection->Item(i, &device);
      if (hRes != S_OK)
        continue;
      std::wstring wszID;
      LPWSTR pwszVal = NULL;
      hRes = device->GetId(&pwszVal);
      if (SUCCEEDED(hRes) && pwszVal != NULL) {
        wszID = pwszVal;
        CoTaskMemFree(pwszVal);
        pwszVal = NULL;
      }
      std::string guid;
      std::wstring wszNAME;
      Microsoft::WRL::ComPtr<IPropertyStore> prop;
      hRes = device->OpenPropertyStore(STGM_READ, &prop);
      if (SUCCEEDED(hRes)) {
        PROPVARIANT varName;
        PropVariantInit(&varName);
        hRes = prop->GetValue(PKEY_Device_FriendlyName, &varName);
        if (SUCCEEDED(hRes) && varName.pwszVal != NULL) {
          wszNAME = varName.pwszVal;
          PropVariantClear(&varName);
          GUID type = {0};
          PROPVARIANT varGUID;
          PropVariantInit(&varGUID);
          PROPERTYKEY PKEY_AudioEndpoint_GUID = {
              {0x1da5d803,
               0xd492,
               0x4edd,
               {0x8c, 0x23, 0xe0, 0xc0, 0xff, 0xee, 0x7f, 0x0e}},
              4};
          hRes = prop->GetValue(PKEY_AudioEndpoint_GUID, &varGUID);
          if (SUCCEEDED(hRes) && varGUID.pwszVal != NULL) {
            CLSIDFromString(varGUID.pwszVal, &type);
            PropVariantClear(&varGUID);
            GUID subtype = {0};
            if (GetJackSubtype(device.Get(), subtype)) {
              ret.push_back(DeviceName{std::move(wszID), std::move(wszNAME),
                                       type, subtype});
            }
          }
        }
      }
    }
    return ret;
  }

  static inline std::list<DeviceName> GetInput() {
    return GetDevList(EDataFlow::eCapture);
  }

  static inline std::list<DeviceName> GetOutput() {
    return GetDevList(EDataFlow::eRender);
  }

  static inline DeviceName GetSecondUSB() {
    auto ret = GetDevList(EDataFlow::eRender);
    int cnt = 0;
    for (auto dev : ret) {
      if (dev.name.find(L"USB") != std::string::npos) {
        ++cnt;
        if (cnt == 2) {
          return dev;
        }
      }
    }
    return {};
  }

  static inline DeviceName GetFirstUSB() {
    auto ret = GetDevList(EDataFlow::eRender);
    int cnt = 0;
    for (auto dev : ret) {
      if (dev.name.find(L"USB") != std::string::npos) {
        return dev;
      }
    }
    return {};
  }

  static inline DeviceName GetFirstHECATE() {
    auto ret = GetDevList(EDataFlow::eRender);
    int cnt = 0;
    for (auto dev : ret) {
      if (dev.name.find(L"HECATE") != std::string::npos) {
        return dev;
      }
    }
    return {};
  }

  static inline CHANNEL_LAYOUT ConvertChannelLayout(
      const WAVEFORMATEX* wave_format) {
    CHANNEL_LAYOUT ret = CHANNEL_UNKNOWN;
    if (wave_format->wFormatTag == WAVE_FORMAT_EXTENSIBLE) {
      WAVEFORMATEXTENSIBLE* s = (WAVEFORMATEXTENSIBLE*)wave_format;
      ret = ConvertTensibleChannelLayout(s->dwChannelMask);
    }
    if (ret == CHANNEL_UNKNOWN) {
      ret = ConvertChannelLayout(wave_format->nChannels);
    }
    return ret;
  }

  static inline CHANNEL_LAYOUT ConvertTensibleChannelLayout(DWORD channel) {
    switch (channel) {
      case KSAUDIO_SPEAKER_QUAD:
        return CHANNEL_QUAD;
      case KSAUDIO_SPEAKER_2POINT1:
        return CHANNEL_2POINT1;
      case KSAUDIO_SPEAKER_4POINT1:
        return CHANNEL_4POINT1;
      case KSAUDIO_SPEAKER_SURROUND:
        return CHANNEL_SURROUND;
      case KSAUDIO_SPEAKER_5POINT1:
        return CHANNEL_5POINT1;
      case KSAUDIO_SPEAKER_5POINT1_SURROUND:
        return CHANNEL_5POINT1_BACK;
      case KSAUDIO_SPEAKER_7POINT1:
        return CHANNEL_7POINT1;
      case KSAUDIO_SPEAKER_7POINT1_SURROUND:
        return CHANNEL_7POINT1_WIDE;
    }
    return CHANNEL_UNKNOWN;
  }

  static inline CHANNEL_LAYOUT ConvertChannelLayout(DWORD channel) {
    switch (channel) {
      case 1:
        return CHANNEL_MONO;
      case 2:
        return CHANNEL_STEREO;
      case 3:
        return CHANNEL_2POINT1;
      case 4:
        return CHANNEL_4POINT0;
      case 5:
        return CHANNEL_4POINT1;
      case 6:
        return CHANNEL_5POINT1;
      case 7:
        return CHANNEL_6POINT1;
      case 8:
        return CHANNEL_7POINT1;
    }
    return CHANNEL_UNKNOWN;
  }

  static inline AUDIO_FORMAT ConvertAudioFormat(const WAVEFORMATEX* wave_format,
                                                bool planer) {
    AUDIO_FORMAT audio_ret = AUDIO_FORMAT_UNKNOWN;
    switch (wave_format->wBitsPerSample) {
      case 8:
        audio_ret = planer ? AUDIO_FORMAT_U8_PLANAR : AUDIO_FORMAT_U8;
        break;
      case 16:
        audio_ret = planer ? AUDIO_FORMAT_S16_PLANAR : AUDIO_FORMAT_S16;
        break;
      case 32:
        audio_ret = planer ? AUDIO_FORMAT_S32_PLANAR : AUDIO_FORMAT_S32;
        if (wave_format->wFormatTag == WAVE_FORMAT_EXTENSIBLE) {
          WAVEFORMATEXTENSIBLE* s = (WAVEFORMATEXTENSIBLE*)wave_format;
          if (s->SubFormat == KSDATAFORMAT_SUBTYPE_IEEE_FLOAT) {
            audio_ret = planer ? AUDIO_FORMAT_FLOAT_PLANAR : AUDIO_FORMAT_FLOAT;
          }
        }
        if (wave_format->wFormatTag == WAVE_FORMAT_IEEE_FLOAT) {
          audio_ret = planer ? AUDIO_FORMAT_FLOAT_PLANAR : AUDIO_FORMAT_FLOAT;
        }
        break;
    }
    return audio_ret;
  }
};

}  // namespace mediasdk
