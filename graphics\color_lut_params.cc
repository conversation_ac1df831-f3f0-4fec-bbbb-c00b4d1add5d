#include "color_lut_params.h"
#include "graphics_utils.h"

namespace graphics {

ColorLutParams::ColorLutParams(const ColorLutParams& other) {
  Assign(other);
}

ColorLutParams& ColorLutParams::operator=(const ColorLutParams& other) {
  if (this != &other) {
    Assign(other);
  }

  return *this;
}

bool ColorLutParams::operator==(const ColorLutParams& other) const {
    return true;
}

bool ColorLutParams::operator!=(const ColorLutParams& other) const {
  return !(*this == other);
}

bool ColorLutParams::IsEmpty() const {
  return *this == ColorLutParams();
}

void ColorLutParams::Reset() {
  *this = ColorLutParams();
}
void ColorLutParams::SetAmount(float amount)
{
    amount_ = amount;
}

const float ColorLutParams::GetAmount() const
{
    return amount_;
}

void ColorLutParams::SetDomainMax(const std::vector<float>& domain_max)
{
    domain_max_ = domain_max;
}
std::vector<float> ColorLutParams::GetDomainMax() const
{
    return domain_max_;
}

void ColorLutParams::SetDomainMin(const std::vector<float>& domain_min)
{
    domain_min_ = domain_min;

}
std::vector<float>  ColorLutParams::GetDomainMin() const
{
    return domain_min_;
}

void ColorLutParams::SetPassThroughAlpha(bool pass_through_alpha)
{
    pass_through_alpha_ = pass_through_alpha;
}

bool ColorLutParams::GetPassThroughAlpha() const
{
    return pass_through_alpha_;
}

void ColorLutParams::Assign(const ColorLutParams& other) {

     amount_ = other.amount_;
     domain_min_ = other.domain_min_;
     domain_max_ = other.domain_max_;
     pass_through_alpha_ = other.pass_through_alpha_;
}

}  // namespace graphics