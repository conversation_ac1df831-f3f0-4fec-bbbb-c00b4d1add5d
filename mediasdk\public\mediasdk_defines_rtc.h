#ifndef MEDIASDK_MEDIASDK_DEFINES_RTC_H_
#define MEDIASDK_MEDIASDK_DEFINES_RTC_H_

#include "mediasdk_callback_defines.h"
#include "mediasdk_defines.h"

namespace mediasdk {

enum JoinRoomType {
  kJoinRoomTypeFirst = 0,
  kJoinRoomTypeReconnected = 1,
};

enum UserOfflineReason {
  kUserOfflineReasonQuit = 0,
  kUserOfflineReasonDropped = 1,
  kUserOfflineReasonSwitchToInvisible = 2,
  kUserOfflineReasonKickedByAdmin = 3,
};

enum ConnectionState {
  kConnectionStateDisconnected = 1,
  kConnectionStateConnecting = 2,
  kConnectionStateConnected = 3,
  kConnectionStateReconnecting = 4,
  kConnectionStateReconnected = 5,
  kConnectionStateLost = 6,
  kConnectionStateFailed = 7,
};

enum StreamIndex {
  kStreamIndexMain = 0,
  kStreamIndex2nd = 1,
  kStreamIndexScreen = kStreamIndex2nd,
  kStreamIndex3rd,
  kStreamIndex4th,
  kStreamIndex5th,
  kStreamIndex6th,
  kStreamIndex7th,
  kStreamIndexMax,
};

enum MediaStreamType {
  kMediaStreamTypeAudio = 1 << 0,
  kMediaStreamTypeVideo = 1 << 1,
  kMediaStreamTypeBoth = kMediaStreamTypeAudio | kMediaStreamTypeVideo
};

enum VideoEncodePreference {
  kVideoEncodePreferenceDisabled = 0,
  kVideoEncodePreferenceFramerate,
  kVideoEncodePreferenceQuality,
  kVideoEncodePreferenceBalance,
};

enum VideoCodecType {
  kVideoCodecTypeUnknown = 0,
  kVideoCodecTypeH264 = 1,
  kVideoCodecTypeByteVC1 = 2,
};

enum AudioProfileType {
  kAudioProfileTypeDefault = 0,
  kAudioProfileTypeFluent = 1,
  kAudioProfileTypeStandard = 2,
  kAudioProfileTypeHD = 3,
  kAudioProfileTypeStandardStereo = 4,
  kAudioProfileTypeHDMono = 5,
};

enum RTCAudioDeviceType {
  kRTCAudioDeviceTypeUnknown = -1,
  kRTCAudioDeviceTypeRenderDevice = 0,
  kRTCAudioDeviceTypeCaptureDevice = 1,
  kRTCAudioDeviceTypeScreenCaptureDevice = 2,
};

enum MediaDeviceState {
  kMediaDeviceStateStarted = 1,
  kMediaDeviceStateStopped = 2,
  kMediaDeviceStateRuntimeError = 3,
  kMediaDeviceStatePaused = 4,
  kMediaDeviceStateResumed = 5,
  kMediaDeviceStateAdded = 10,
  kMediaDeviceStateRemoved = 11,
  kMediaDeviceStateInterruptionBegan = 12,
  kMediaDeviceStateInterruptionEnded = 13,
  kMediaDeviceStateBecomeSystemDefault = 14,
  kMediaDeviceStateResignSystemDefault = 15,
  kMediaDeviceStateListUpdated = 16,
};

enum MediaDeviceError {
  kMediaDeviceErrorOK = 0,
  kMediaDeviceErrorDeviceNoPermission = 1,
  kMediaDeviceErrorDeviceBusy = 2,
  kMediaDeviceErrorDeviceFailure = 3,
  kMediaDeviceErrorDeviceNotFound = 4,
  kMediaDeviceErrorDeviceDisconnected = 5,
  kMediaDeviceErrorDeviceNoCallback = 6,
  kMediaDeviceErrorDeviceUNSupportFormat = 7,
  kMediaDeviceErrorDeviceNotFindGroupId = 8,
  kMediaDeviceErrorDeviceNotAvailableInBackground = 9,
  kMediaDeviceErrorDeviceVideoInUseByAnotherClient = 10,
  kMediaDeviceErrorDeviceNotAvailableWithMultipleForegroundApps = 11,
  kMediaDeviceErrorDeviceNotAvailableDueToSystemPressure = 12,
};

enum StreamMixingEvent {
  kStreamMixingEventBase = 0,
  kStreamMixingEventStart = 1,
  kStreamMixingEventStartSuccess = 2,
  kStreamMixingEventStartFailed = 3,
  kStreamMixingEventUpdate = 4,
  kStreamMixingEventUpdateSuccess = 5,
  kStreamMixingEventUpdateFailed = 6,
  kStreamMixingEventStop = 7,
  kStreamMixingEventStopSuccess = 8,
  kStreamMixingEventStopFailed = 9,
  kStreamMixingEventChangeMixType = 10,
  kStreamMixingEventFirstAudioFrameByClientMix = 11,
  kStreamMixingEventFirstVideoFrameByClientMix = 12,
  kStreamMixingEventUpdateTimeout = 13,
  kStreamMixingEventStartTimeout = 14,
  kStreamMixingEventRequestParamError = 15,
  kStreamMixingEventMixImageEvent = 16,
  kStreamMixingEventMixSingleWayChorusEvent = 17,
  kStreamMixingEventMixStreamMixingMax = 18,
  kStreamMixingEventAlternateImageSucceed = 19,
  kStreamMixingEventAlternateImageFailed = 20,
  kStreamMixingEventBackgroundUrlSucceed = 21,
  kStreamMixingEventBackgroundUrlFailed = 22
};

enum StreamMixingErrorCode {
  kStreamMixingErrorCodeOK = 0,
  kStreamMixingErrorCodeBase = 1090,
  kStreamMixingErrorCodeInvalidParam = 1091,
  kStreamMixingErrorCodeInvalidState = 1092,
  kStreamMixingErrorCodeInvalidOperator = 1093,
  kStreamMixingErrorCodeTimeout = 1094,
  kStreamMixingErrorCodeInvalidParamByServer = 1095,
  kStreamMixingErrorCodeSubTimeoutByServer = 1096,
  kStreamMixingErrorCodeInvalidStateByServer = 1097,
  kStreamMixingErrorCodeAuthenticationByCDN = 1098,
  kStreamMixingErrorCodeTimeoutBySignaling = 1099,
  kStreamMixingErrorCodeMixImageFail = 1100,
  kStreamMixingErrorCodeUnKnownByServer = 1101,
  kStreamMixingErrorCodeStreamSyncWorse = 1102,
  kStreamMixingErrorCodeUpdateRegionChanged = 1103,
  kStreamMixingErrorCodeAlternateImageSucceeded = 1105,
  kStreamMixingErrorCodeAlternateImageFailed = 1106,
  kStreamMixingErrorCodeBackgroundSucceeded = 1107,
  kStreamMixingErrorCodeBackgroundFailed = 1108,
  kStreamMixingErrorCodeMax = 1199,
};

enum MixedStreamType {
  kMixedStreamTypeByServer = 0,
  kMixedStreamTypeByClient = 1,
};

enum NetworkQuality {
  kNetworkQualityUnknown = 0,
  kNetworkQualityExcellent,
  kNetworkQualityGood,
  kNetworkQualityPoor,
  kNetworkQualityBad,
  kNetworkQualityVbad,
  kNetworkQualityDown,
};

enum StreamRemoveReason {
  kStreamRemoveReasonUnpublish = 0,
  kStreamRemoveReasonPublishFailed = 1,
  kStreamRemoveReasonKeepLiveFailed = 2,
  kStreamRemoveReasonClientDisconnected = 3,
  kStreamRemoveReasonRepublish = 4,
  kStreamRemoveReasonOther = 5,
  kStreamRemoveReasonPrivilegeTokenExpired = 6,
};

//#define ENABLE_RTC_VIDEO_SRC_DUMP
//#define ENABLE_RTC_VIDEO_ENC_DUMP
//#define ENABLE_RTC_VIDEO_DEC_DUMP
//#define ENABLE_RTC_VIDEO_SNK_DUMP

#define RTC_VFRAME_DUMP_INTERVAL		10 // in seconds			

}  // namespace mediasdk

#endif  // MEDIASDK_MEDIASDK_DEFINES_RTC_H_
