#pragma once
#include <DirectXMath.h>
#include <cmath>
#include <limits>
#include "DXSimpleMath.h"
#include "graphics_export.h"
#include "graphics_utils.h"

namespace graphics {

class GRAPHICS_EXPORT CornerBuffer {
 public:
  static CornerBuffer CreateCorner(const DirectX::XMFLOAT4& corner_radius,
                                   const DirectX::XMFLOAT4& corner_clip,
                                   const float corner_scale) {
    CornerBuffer corner;
    corner.SetRadius(corner_radius);
    corner.SetClip(corner_clip);
    corner.SetScale(corner_scale);
    return corner;
  }

  CornerBuffer(){};

  bool operator==(const CornerBuffer& other) const {
    if (!IsNearEqual(GetScale(), other.GetScale())) {
      return false;
    }
    if (!IsNearEqual(GetClip().x, other.GetClip().x) ||
        !IsNearEqual(GetClip().y, other.GetClip().y) ||
        !IsNearEqual(GetClip().z, other.GetClip().z) ||
        !IsNearEqual(GetClip().w, other.GetClip().w)) {
      return false;
    }
    if (!IsNearEqual(GetRadius().x, other.GetRadius().x) ||
        !IsNearEqual(GetRadius().y, other.GetRadius().y) ||
        !IsNearEqual(GetRadius().z, other.GetRadius().z) ||
        !IsNearEqual(GetRadius().w, other.GetRadius().w)) {
      return false;
    }
    return true;
  }

  const DirectX::XMFLOAT4 GetRadius() const { return corner_radius_; }

  void SetRadius(const DirectX::XMFLOAT4& corner_radius) {
    corner_radius_ = corner_radius;
  }

  const DirectX::XMFLOAT4 GetClip() const { return corner_clip_; }

  void SetClip(const DirectX::XMFLOAT4& corner_clip) {
    corner_clip_ = corner_clip;
  }

  const float GetScale() const { return corner_scale_; }

  void SetScale(const float scale) { corner_scale_ = scale; }

 private:
  DirectX::XMFLOAT4 corner_radius_ = XMFLOAT4_EMPTY;
  DirectX::XMFLOAT4 corner_clip_ = XMFLOAT4_EMPTY;
  float corner_scale_ = 0.0f;
};

}  // namespace graphics
