#pragma once

#include <array>
#include "texture.h"

namespace graphics {
class Graphics;

class GRAPHICS_EXPORT BGRAToYUVGraphics {
 public:
  enum ColorConvertType {
    kColorConvertTypeUnspecified,
    kColorConvertTypeNV12Single,
    kColorConvertTypeNV12Double,
    kColorConvertTypeI420,
    kColorConvertTypeNV12SingleShared,
    kColorConvertTypeY8
  };

  using ConvertResourceType =
      std::array<std::shared_ptr<Graphics>, kMaxVideoPlanes>;

  struct GRAPHICS_EXPORT BGRAToYUVConvertConfig {
    mediasdk::ColorSpace cs = mediasdk::ColorSpace::kColorSpaceUnspecified;
    mediasdk::VideoRange cr = mediasdk::VideoRange::kVideoRangeUnspecified;
    int32_t cx = 0;
    int32_t cy = 0;  // for init output texture,or 0 for delay texture create
    ColorConvertType type = kColorConvertTypeUnspecified;
  };

  virtual bool Init(BGRAToYUVConvertConfig conf) = 0;

  virtual bool BGRAToYUV(Texture&) = 0;

  virtual bool BGRAToYUVToTarget(Texture&, ConvertResourceType& resource) = 0;

  virtual std::shared_ptr<Texture> GetOutputTexture(int plane) = 0;

  virtual std::shared_ptr<Texture> MoveOutputTexture(int plane) = 0;

  virtual ~BGRAToYUVGraphics() = default;

  virtual void Destroy() = 0;
};

GRAPHICS_EXPORT std::shared_ptr<BGRAToYUVGraphics> CreateBGRAToYUVGraphics(
    Device& ins);
GRAPHICS_EXPORT bool NV12TextureConvertTest(Device& ins);
GRAPHICS_EXPORT bool InitYUVResources(
    graphics::Device& device,
    int32_t widht,
    int32_t height,
    bool planer_texture,
    bool only_y,
    bool i420,
    bool shared,
    Texture* output_texture,
    BGRAToYUVGraphics::ConvertResourceType& planer_graphics);

}  // namespace graphics
