#pragma once

#include <map>
#include <memory>
#include <string>
#include <vector>

#include "base/native_library.h"
#include "mediasdk/plugin_service/plugin_global_proxy_impl.h"
#include "mediasdk/public/mediasdk_defines.h"
#include "plugin_loader.h"
#include "source_factory.h"

namespace mediasdk {

class AudioInputSourceFactory;
class StreamServiceSourceFactory;
class VideoEncoderSourceFactory;
class AudioEncoderSourceFactory;
class VisualSourceFactory;
class AudioFilterFactory;
class VisualFilterFactory;

struct PluginInfo;
class VisualSource;
class VisualFilter;
class AudioInputSource;
class StreamServiceSource;
class AudioFilter;

class PluginManager : public PluginLoaderDelegate {
 public:
  PluginManager();

  ~PluginManager();

  void Load();

  bool IsLoading() { return !!loader_; }

  void UnLoadAll();

  void ReportLoadEvent();

  std::shared_ptr<VisualSourceFactory> GetVisualSourceFactory(
      const std::string& plugin_name);

  std::shared_ptr<AudioFilterFactory> GetAudioFilterFactory(
      const std::string& plugin_name);

  std::shared_ptr<VisualFilterFactory> GetVisualFilterFactory(
      const std::string& plugin_name);

  std::shared_ptr<AudioInputSourceFactory> GetAudioInputSourceFactory(
      const std::string& plugin_name);

  std::shared_ptr<StreamServiceSourceFactory> GetServiceSourceFactory(
      const std::string& plugin_name);

  std::shared_ptr<VideoEncoderSourceFactory> GetVideoEncoderSourceFactory(
      const std::string& plugin_name);

  std::shared_ptr<AudioEncoderSourceFactory> GetAudioEncoderSourceFactory(
      const std::string& plugin_name);

  std::shared_ptr<PluginInfoArray> EnumSource(PluginType type);

  void DestroyVisualSource(std::shared_ptr<VisualSource> source);

  void DestroyAllVisualSource();

  bool RegisterExternalVisualSourceFactory(
      const PluginInfo& info,
      std::shared_ptr<VisualSourceFactory> factory);

  bool RegisterExternalAudioInputSourceFactory(
      const PluginInfo& info,
      std::shared_ptr<AudioInputSourceFactory> factory);

  void DestroyAudioFilter(std::shared_ptr<AudioFilter> filter);

  void DestroyAllAudioFilter();

  void DestroyAudioInputSource(std::shared_ptr<AudioInputSource> source);

  void DestroyAllAudioInputSource();

  void DestroyStreamServiceSource(std::shared_ptr<StreamServiceSource> source);

  void DestroyAllStreamServiceSource();

 protected:
  // PluginLoaderDelegate:
  void OnPluginLoaded(std::shared_ptr<SourceFactory> factory) override;

  void OnPluginInitializeResult(
      std::shared_ptr<SourceFactory> factory) override;

  PluginGlobalProxy* GetPluginGlobalProxy() override;

  void OnLoadFinished() override;

  void OnSubTypePluginsLoadFinished(PluginType type) override;

 private:
  std::shared_ptr<SourceFactory> FindAndWaitFactoryReady(
      PluginType type,
      const std::string& plugin_name);

 private:
  std::map<PluginType, std::vector<std::shared_ptr<SourceFactory>>> factories_;
  std::unique_ptr<PluginGlobalProxy> plugin_global_proxy_;
  std::unique_ptr<PluginLoader> loader_;
};

}  // namespace mediasdk
