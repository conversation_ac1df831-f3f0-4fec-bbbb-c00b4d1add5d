﻿#include "lines_impl.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <dxgi.h>
#include <dxgitype.h>
#include <windows.h>
#include <wrl/client.h>
#include "DXSimpleMath.h"
#include "color_shader.h"

using namespace Microsoft::WRL;

namespace graphics {

int LinesImpl::GetIndexCnt() {
  if (cur_index_cnt_) {
    UINT stride = sizeof(ColorShader::VERTEXTYPE);
    UINT offset = 0;

    GetContext()->IASetVertexBuffers(0, 1, vs_vertex_buffer_.GetAddressOf(),
                                     &stride, &offset);
    GetContext()->IASetIndexBuffer(index_buffer_.Get(), DXGI_FORMAT_R32_UINT,
                                   0);
    GetContext()->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_LINESTRIP);

    GetContext()->VSSetConstantBuffers(0, 1, matrix_.GetAddressOf());
  }
  return cur_index_cnt_;
}

void LinesImpl::UpdateLineConf(const LinesConfig& conf) {
  { config_ = std::make_unique<LinesConfigInternal>(); }

  if (conf.count <= 0) {
    return;
  }
  auto config = std::make_unique<LinesConfigInternal>();

  config->points = std::vector<XMFLOAT2>(conf.count);
  memcpy(config->points.data(), conf.points, sizeof(XMFLOAT2) * conf.count);
  config->colors = std::vector<XMFLOAT4>(conf.color_cnt);
  memcpy(config->colors.data(), conf.colors, sizeof(XMFLOAT4) * conf.color_cnt);
  config->count = conf.count;
  config->cnt = conf.color_cnt;
  { std::swap(config_, config); }
}

bool LinesImpl::UpdateVertexBuffer(const LinesConfigInternal& config) {
  vertex_buffer_ = std::vector<ColorShader::VERTEXTYPE>(config.count);
  D3D11_BUFFER_DESC desc;
  ZeroMemory(&desc, sizeof(desc));
  desc.Usage = D3D11_USAGE_DYNAMIC;
  desc.ByteWidth = sizeof(ColorShader::VERTEXTYPE) * config.count;
  desc.BindFlags = D3D11_BIND_VERTEX_BUFFER;
  desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  desc.MiscFlags = 0;
  desc.StructureByteStride = 0;
  D3D11_SUBRESOURCE_DATA data;
  ZeroMemory(&data, sizeof(data));
  data.pSysMem = nullptr;
  data.SysMemPitch = 0;
  data.SysMemSlicePitch = 0;
  HRESULT hRes = GetDevice()->CreateBuffer(&desc, nullptr, &vs_vertex_buffer_);

  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  D3D11SetDebugObjectName(vs_vertex_buffer_.Get(), "lines_vertex_buffer");
  std::vector<ULONG> indices(config.count);
  for (INT32 i = 0; i < config.count; i++) {
    indices[i] = i;
  }
  desc.Usage = D3D11_USAGE_DEFAULT;
  desc.ByteWidth = sizeof(ULONG) * config.count;
  desc.BindFlags = D3D11_BIND_INDEX_BUFFER;
  desc.CPUAccessFlags = 0;
  data.pSysMem = indices.data();
  hRes = GetDevice()->CreateBuffer(&desc, &data, &index_buffer_);
  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  D3D11SetDebugObjectName(index_buffer_.Get(), "lines_index_buffer");
  D3D11_BUFFER_DESC buffer_desc = {};
  buffer_desc.Usage = D3D11_USAGE_DYNAMIC;
  buffer_desc.ByteWidth = sizeof(MATRIXBUFFER);
  buffer_desc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
  buffer_desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  hRes = GetDevice()->CreateBuffer(&buffer_desc, NULL, &matrix_);

  D3D11SetDebugObjectName(matrix_.Get(), "lines_matrix_buffer");

  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  return true;
}

bool LinesImpl::UpdateBufferWithVPSize(const XMFLOAT2& vp_size,
                                       const LinesConfigInternal& config) {
  cur_index_cnt_ = config.count;
  for (INT32 i = 0; i < config.count; i++) {
    vertex_buffer_[i].position =
        XMFLOAT3(config.points[i].x / vp_size.x,
                 (vp_size.y - config.points[i].y) / vp_size.y, 0.f);
    vertex_buffer_[i].color =
        (i >= config.cnt) ? config.colors[0] : config.colors[i];
  }
  D3D11_MAPPED_SUBRESOURCE mapped_resource;
  HRESULT hRes = GetContext()->Map(
      vs_vertex_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &mapped_resource);
  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to Map(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }

  std::memcpy(mapped_resource.pData, (void*)vertex_buffer_.data(),
              sizeof(ColorShader::VERTEXTYPE) * config.count);
  GetContext()->Unmap(vs_vertex_buffer_.Get(), 0);
  return true;
}

bool LinesImpl::DrawTo(const XMFLOAT2& vp,
                       const XMMATRIX& world,
                       const XMMATRIX& view,
                       const XMMATRIX& projection) {
  std::unique_ptr<LinesConfigInternal> config;

  { std::swap(config, config_); }
  if (config) {
    if (config->count == 0) {
      cur_index_cnt_ = 0;
      return false;
    }

    if (!vs_vertex_buffer_ || vertex_buffer_.size() < config->count) {
      if (!UpdateVertexBuffer(*config))
        return false;
    }

    if (!UpdateBufferWithVPSize(vp, *config))
      return false;

    if (!DoCopyMatrixBuffer(GetContext(), &world, &view, &projection, matrix_))
      return false;
  }
  return true;
}

LinesImpl::LinesImpl(Device& ins) : instance_(ins) {}

ID3D11DeviceContext* LinesImpl::GetContext() {
  return instance_.GetContext().Get();
}

ID3D11Device* LinesImpl::GetDevice() {
  return instance_.GetDevice().Get();
}

std::shared_ptr<LinesImpl> LinesImpl::CreateLines(Device& device_) {
  return std::make_shared<LinesImpl>(device_);
}

void LinesImpl::Destroy() {
  if (vs_vertex_buffer_) {
    vs_vertex_buffer_.Reset();
  }
  if (index_buffer_) {
    index_buffer_.Reset();
  }
  {
    if (config_) {
      config_ = nullptr;
    }
  }
}

LinesImpl::~LinesImpl() {
  Destroy();
}
}  // namespace graphics
