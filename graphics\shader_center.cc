#include "shader_center.h"
#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include "bgra_calc_shader.h"
#include "bgra_to_rgba_shader.h"
#include "bgra_to_yuv_shader.h"
#include "border_shader.h"
#include "clip_mask_shader.h"
#include "color_revert_shader.h"
#include "color_shader.h"
#include "corner_shader.h"
#include "point_nine_shader.h"
#include "texture_shader.h"
#include "y_xor_shader.h"
#include "yuv_color_shader.h"
#include "yuv_graphics_shader.h"
#include "color_lut_shader.h"
#include "partial_scale_shader.h"

namespace graphics {
EnumPluginsFunction g_inner_factorys[] = {
    TextureShader::EnumPluginsFunction,
    ColorShader::EnumPluginsFunction,
    PointNineShader::EnumPluginsFunction,
    ClipMaskShader::EnumPluginsFunction,
    YUVGraphicsShader::EnumPluginsFunction,
    BGRAToYUVShader::EnumPluginsFunction,
    ColorRevertShader::EnumPluginsFunction,
    YUVColorShader::EnumPluginsFunction,
    YXORShader::EnumPluginsFunction,
    BGRACalcShader::EnumPluginsFunction,
    CornerShader::EnumPluginsFunction,
    BorderShader::EnumPluginsFunction,
    BGRAToRGBAShader::EnumPluginsFunction,
    ColorLutShader::EnumPluginsFunction,
    PartialScaleShader::EnumPluginsFunction };

int ShaderFactory::RegisterInnerFactory() {
  int iSuc = 0;
  for (auto inner : g_inner_factorys) {
    RegisterFactory(inner);
    ++iSuc;
  }
  return iSuc;
}

void ShaderFactory::FactoryCallBack(void* param, ShaderItem factory) {
  auto pThis = static_cast<ShaderFactory*>(param);
  if (!pThis)
    return;

  auto it = pThis->factory_map_.find(factory.type);
  if (it != pThis->factory_map_.end()) {
    DCHECK(false);
    return;
  }
  pThis->factory_map_[factory.type] = factory;
}

void ShaderFactory::RegisterFactory(EnumPluginsFunction pfn) {
  pfn(this, FactoryCallBack);
}

ShaderItem ShaderFactory::GetFactoryByType(const char* type) {
  ShaderItem ret = {};
  auto it = factory_map_.find(type);
  if (it != factory_map_.end()) {
    ret = it->second;
  }

  return ret;
}

std::shared_ptr<Shader> ShaderFactory::CreateShader(const char* type) {
  ShaderItem factory = GetFactoryByType(type);
  if (!factory.factory)
    return nullptr;

  auto ret = factory.factory(type);
  if (!ret)
    return nullptr;

  return ret;
}

ShaderFactory::ShaderFactory() {}
}  // namespace graphics