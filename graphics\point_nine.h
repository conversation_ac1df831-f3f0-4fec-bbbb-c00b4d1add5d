#pragma once

#include <cmath>
#include "graphics_export.h"
#include "graphics_utils.h"
#include "DXSimpleMath.h"

namespace graphics {

class GRAPHICS_EXPORT PointNine {
 public:
  static PointNine CreatePointNine(const DirectX::XMFLOAT2& scale,
                                   const DirectX::XMFLOAT4& border,
                                   bool clear_border) {
    PointNine point_nine;
    point_nine.SetScale(scale);
    point_nine.SetBorder(border);
    point_nine.SetClearBorder(clear_border);
    return point_nine;
  }

  PointNine(){};

  bool operator==(const PointNine& other) const {
    if (GetClearBorder() != other.GetClearBorder()) {
      return false;
    }
    if (!IsNearEqual(GetScale().x, other.GetScale().x) ||
        !IsNearEqual(GetScale().y, other.GetScale().y)) {
      return false;
    }
    if (!IsNearEqual(GetBorder().x, other.GetBorder().x) ||
        !IsNearEqual(GetBorder().y, other.GetBorder().y) ||
        !IsNearEqual(GetBorder().z, other.GetBorder().z) ||
        !IsNearEqual(GetBorder().w, other.GetBorder().w)) {
      return false;
    }
    return true;
  }

  void SetScale(const DirectX::XMFLOAT2& scale) { scale_ = scale; }

  const DirectX::XMFLOAT2 GetScale() const { return scale_; }

  bool IsRatio() { return IsNearEqual(scale_.x, scale_.y); }

  bool StretchX() { return scale_.x > scale_.y; }

  bool StretchY() { return scale_.x < scale_.y; }

  void SetBorder(const DirectX::XMFLOAT4& border) { border_ = border; }

  const DirectX::XMFLOAT4 GetBorder() const { return border_; }

  void SetClearBorder(bool clear_border) { clear_border_ = clear_border; }

  const bool GetClearBorder() const { return clear_border_; }

 private:
  DirectX::XMFLOAT2 scale_ = XMFLOAT2_EMPTY;
  DirectX::XMFLOAT4 border_ = XMFLOAT4_EMPTY;
  bool clear_border_ = false;
};

}  // namespace graphics
