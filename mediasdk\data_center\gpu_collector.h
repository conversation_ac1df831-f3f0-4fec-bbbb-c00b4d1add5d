#pragma once
#include <memory>
#include <vector>

namespace gpu_collector {

struct GpuDeviceInfo {
  uint32_t id_1 = 0;
  uint32_t id_2 = 0;

  bool operator==(const GpuDeviceInfo& other) const {
    if (id_1 != other.id_1) {
      return false;
    }
    if (id_2 != other.id_2) {
      return false;
    }
    return true;
  }

  bool operator<(const GpuDeviceInfo& other) const {
    if (id_1 != other.id_1) {
      return id_1 < other.id_1;
    }
    if (id_2 != other.id_2) {
      return id_2 < other.id_2;
    }
    return false;
  }
};

struct GpuUsage {
  GpuDeviceInfo device_info = {};
  float usage_3d = 0.f;
  float usage_cuda = 0.f;
  float usage_copy = 0.f;
  float usage_video_decode = 0.f;
  float usage_video_encode = 0.f;
  float usage_video_processing = 0.f;
  double dedicate_usage = 0.0;
};

class GpuCollector {
 public:
  static std::shared_ptr<GpuCollector> CreateCollector();

  GpuCollector() = default;

  virtual ~GpuCollector() = default;

  virtual int GpuNumber(int pid) = 0;

  virtual bool ProcessUsage(uint64_t pid, std::vector<GpuUsage>& usage) = 0;

  virtual bool SystemUsage(std::vector<GpuUsage>& usage) = 0;
};
}  // namespace gpu_collector