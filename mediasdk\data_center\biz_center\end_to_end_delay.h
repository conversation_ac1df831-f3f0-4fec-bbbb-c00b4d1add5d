#pragma once

#include <map>
#include <memory>
#include <string>
#include "base/memory/weak_ptr.h"
#include "base/threading/thread.h"

namespace mediasdk {

struct EndtoEndDelayCostData {
  int64_t total_us = 0;
  int64_t cnt = 0;
};

struct EndtoEndSEIforRTC {
  int64_t camera_ntp_ms = 0;
  int64_t effect_ntp_ms = 0;
  std::string other_sei_json;
};

class EndtoEndDelay {
 public:
  EndtoEndDelay();

  virtual ~EndtoEndDelay();

  virtual bool FillCostData(std::map<std::string, int64_t>& cost_map) = 0;
};

}  // namespace mediasdk
