#include "dshow_helper.h"

#include <wmcodecdsp.h>
#include <algorithm>
#include "base/logging.h"
#include "base/strings/stringprintf.h"
#include "base/win/scoped_variant.h"
#include "baseclasses/scopedmtype.h"
#include "dshow_media_type.h"

namespace mediasdk {
namespace {

constexpr const WCHAR* PROPERTY_NAMES[] = {L"DevicePath", L"Description",
                                           L"FriendlyName"};

// 提取设备名称的核心部分
std::wstring ExtractCoreDeviceName(const std::wstring& device_name) {
  // 常见的摄像头型号
  std::vector<std::wstring> common_models = {
    L"C922", L"C920", L"C930", L"HD Pro", L"StreamCam",
    L"BRIO", L"EOS", L"Hero", L"Elgato", L"AVerMedia",
    L"Blackmagic", L"Magewell", L"HDMI", L"USB"
  };

  for (const auto& model : common_models) {
    size_t pos = device_name.find(model);
    if (pos != std::wstring::npos) {
      return model;
    }
  }

  // 如果没有匹配到常见型号，尝试提取第一个单词
  size_t space_pos = device_name.find(L' ');
  if (space_pos != std::wstring::npos && space_pos > 0) {
    return device_name.substr(0, space_pos);
  }

  return device_name;
}

// 提取USB路径信息
std::wstring ExtractUSBPath(const std::wstring& device_id) {
  // 解析设备ID中的USB路径
  // 例如: \\?\usb#vid_1234&pid_5678&mi_00#...
  size_t usb_pos = device_id.find(L"usb#");
  if (usb_pos == std::wstring::npos) {
    return L"";
  }

  size_t end_pos = device_id.find(L"#", usb_pos + 4);
  if (end_pos == std::wstring::npos) {
    return L"";
  }

  return device_id.substr(usb_pos, end_pos - usb_pos);
}

constexpr struct {
  const GUID& sub_type_st;
  mediasdk::PixelFormat format;
} FORMAT_MAP[] = {{MEDIASUBTYPE_I420, mediasdk::kPixelFormatI420},
                  {MEDIASUBTYPE_IYUV, mediasdk::kPixelFormatI420},
                  {MEDIASUBTYPE_NV12, mediasdk::kPixelFormatNV12},
                  {MEDIASUBTYPE_RGB24, mediasdk::kPixelFormatRGB24},
                  {MEDIASUBTYPE_RGB32, mediasdk::kPixelFormatRGBA},
                  {MEDIASUBTYPE_YUY2, mediasdk::kPixelFormatYUY2},
                  {MEDIASUBTYPE_MJPG, mediasdk::kPixelFormatMJPEG},
                  {MEDIASUBTYPE_UYVY, mediasdk::kPixelFormatUYVY},
                  {MEDIASUBTYPE_HDYC_MS, mediasdk::kPixelFormatHDYC}};
// 检查是否来自同一个USB设备
bool IsSameUSBDevice(const std::wstring& usb_path1, const std::wstring& usb_path2) {
  if (usb_path1.empty() || usb_path2.empty()) {
    return false;
  }

  // 提取VID和PID
  size_t vid_pos1 = usb_path1.find(L"vid_");
  size_t pid_pos1 = usb_path1.find(L"pid_");
  size_t vid_pos2 = usb_path2.find(L"vid_");
  size_t pid_pos2 = usb_path2.find(L"pid_");

  if (vid_pos1 == std::wstring::npos || pid_pos1 == std::wstring::npos ||
      vid_pos2 == std::wstring::npos || pid_pos2 == std::wstring::npos) {
    return false;
  }

  // 比较VID和PID
  std::wstring vid1 = usb_path1.substr(vid_pos1, 8);  // "vid_xxxx"
  std::wstring pid1 = usb_path1.substr(pid_pos1, 8);  // "pid_xxxx"
  std::wstring vid2 = usb_path2.substr(vid_pos2, 8);
  std::wstring pid2 = usb_path2.substr(pid_pos2, 8);

  return (vid1 == vid2) && (pid1 == pid2);
}

}  // namespace

// 计算设备匹配置信度
float CalculateDeviceMatchConfidence(const DShowDeviceName& video_device,
                                   const DShowDeviceName& audio_device,
                                   DeviceMatchStrategy strategy) {
  switch (strategy) {
    case DeviceMatchStrategy::EXACT_ID_MATCH:
      return (video_device.id == audio_device.id) ? 1.0f : 0.0f;

    case DeviceMatchStrategy::NAME_PATTERN_MATCH: {
      std::wstring video_core = ExtractCoreDeviceName(video_device.name);
      std::wstring audio_core = ExtractCoreDeviceName(audio_device.name);

      if (video_core == audio_core) {
        return 0.9f;  // 高置信度
      }

      // 检查是否包含相同的关键词
      if (!video_core.empty() &&
          (audio_device.name.find(video_core) != std::wstring::npos ||
           video_device.name.find(audio_core) != std::wstring::npos)) {
        return 0.7f;  // 中等置信度
      }

      return 0.0f;
    }

    case DeviceMatchStrategy::USB_PATH_MATCH: {
      std::wstring video_usb_path = ExtractUSBPath(video_device.id);
      std::wstring audio_usb_path = ExtractUSBPath(audio_device.id);

      if (video_usb_path.empty() || audio_usb_path.empty()) {
        return 0.0f;
      }

      // 检查是否来自同一个USB设备
      if (video_usb_path == audio_usb_path) {
        return 0.95f;  // 非常高的置信度
      }

      if (IsSameUSBDevice(video_usb_path, audio_usb_path)) {
        return 0.8f;
      }

      return 0.0f;
    }

    case DeviceMatchStrategy::FUZZY_NAME_MATCH: {
      // 模糊名称匹配，适用于"麦克风 (6- C922 Pro Stream Webcam)"这样的格式
      if (audio_device.name.find(video_device.name) != std::wstring::npos ||
          video_device.name.find(audio_device.name) != std::wstring::npos) {
        return 0.85f;
      }

      // 检查是否包含相同的核心设备名称
      std::wstring video_core = ExtractCoreDeviceName(video_device.name);
      if (!video_core.empty() &&
          audio_device.name.find(video_core) != std::wstring::npos) {
        return 0.6f;
      }

      return 0.0f;
    }

    default:
      return 0.0f;
  }
}

BOOL ValidVideoProcAmpStruct(const VideoProcAmpStruct& st) {
  if (st.default_value < st.min_value || st.default_value > st.max_value ||
      st.value < st.min_value || st.value > st.max_value) {
    return FALSE;
  }
  return TRUE;
}

BOOL ValidCameraControlStruct(const CameraControlStruct& st) {
  if (st.default_value < st.min_value || st.default_value > st.max_value ||
      st.value < st.min_value || st.value > st.max_value) {
    return FALSE;
  }
  return TRUE;
}

BOOL DShowEnumDevice(std::vector<DShowDeviceName>& names, const CLSID& clsid) {
  Microsoft::WRL::ComPtr<ICreateDevEnum> dev_enum;
  HRESULT h = ::CoCreateInstance(CLSID_SystemDeviceEnum, NULL, CLSCTX_INPROC,
                                 __uuidof(dev_enum), (void**)&dev_enum);
  if (h != S_OK) {
    LOG(ERROR) << "Failed to CoCreateInstance(CLSID_SystemDeviceEnum,0x"
               << std::hex << h << ")";
    return FALSE;
  }
  Microsoft::WRL::ComPtr<IEnumMoniker> enum_moniker;
  h = dev_enum->CreateClassEnumerator(clsid, &enum_moniker, 0);
  if (h != S_OK) {
    LOG(ERROR) << "Failed to CreateClassEnumerator(IEnumMoniker,0x" << std::hex
               << h << ")";
    return FALSE;
  }
  Microsoft::WRL::ComPtr<IMoniker> moniker;
  while (enum_moniker->Next(1, &moniker, NULL) == S_OK) {
    Microsoft::WRL::ComPtr<IPropertyBag> propertybag;
    h = moniker->BindToStorage(NULL, NULL, IID_IPropertyBag,
                               (void**)&propertybag);
    if (h != S_OK) {
      LOG(ERROR) << "Failed to BindToStorage(IPropertyBag,0x" << std::hex << h
                 << ")";
      moniker.Reset();
      continue;
    }
    base::win::ScopedVariant variant;
    h = propertybag->Read(L"Description", variant.Receive(), 0);
    if (h != S_OK) {
      h = propertybag->Read(L"FriendlyName", variant.Receive(), 0);
    }
    DShowDeviceName tmp;
    if (SUCCEEDED(h) && variant.type() == VT_BSTR) {
      tmp.name = V_BSTR(variant.ptr());
      variant.Reset();
      h = propertybag->Read(L"DevicePath", variant.Receive(), 0);
      if (FAILED(h) || variant.type() != VT_BSTR) {
        tmp.id = tmp.name;
      } else {
        tmp.id = V_BSTR(variant.ptr());
      }
      Microsoft::WRL::ComPtr<IBaseFilter> capture_filter;
      h = moniker->BindToObject(NULL, NULL, IID_IBaseFilter,
                                (void**)&capture_filter);
      if (SUCCEEDED(h)) {
        names.push_back(tmp);
      }
    }
    moniker.Reset();
  }
  return TRUE;
}

// 查找关联的音频设备
std::vector<DShowDeviceName> FindAssociatedAudioDevices(
    const DShowDeviceName& video_device,
    DeviceMatchStrategy strategy) {

  std::vector<DShowDeviceName> associated_devices;
  std::vector<DShowDeviceName> audio_devices;

  // 枚举所有音频设备
  if (!DShowEnumDevice(audio_devices, CLSID_AudioInputDeviceCategory)) {
    return associated_devices;
  }

  for (const auto& audio_device : audio_devices) {
    float confidence = CalculateDeviceMatchConfidence(
        video_device, audio_device, strategy);

    if (confidence > 0.5f) {  // 置信度阈值
      associated_devices.push_back(audio_device);
    }
  }

  return associated_devices;
}

BOOL IsPinCategory(IPin* pin, REFGUID category) {
  BOOL ret = FALSE;
  Microsoft::WRL::ComPtr<IKsPropertySet> ks_property;
  HRESULT h = pin->QueryInterface(__uuidof(ks_property), (void**)&ks_property);
  if (SUCCEEDED(h)) {
    GUID category_tmp = {0};
    DWORD size_st = 0;
    h = ks_property->Get(AMPROPSETID_Pin, AMPROPERTY_PIN_CATEGORY, NULL, 0,
                         &category_tmp, sizeof(category_tmp), &size_st);
    if (SUCCEEDED(h) && (size_st == sizeof(category_tmp))) {
      ret = (category_tmp == category);
    }
  }
  return ret;
}

BOOL IsPinStreamMajorType(IPin* pin, REFGUID majortype) {
  Microsoft::WRL::ComPtr<IAMStreamConfig> stream_config;
  if (FAILED(
          pin->QueryInterface(IID_IAMStreamConfig, (void**)&stream_config))) {
    return FALSE;
  }
  int32_t count = 0;
  int32_t size = 0;
  if (FAILED(stream_config->GetNumberOfCapabilities(&count, &size))) {
    return FALSE;
  }
  std::vector<uint8_t> caps(size, 0);
  for (int32_t i = 0; i < count; i++) {
    ScopedMediaType media_type;
    if (FAILED(stream_config->GetStreamCaps(i, media_type.Receive(),
                                            caps.data()))) {
      continue;
    }
    if (media_type.Ptr() != NULL && media_type->majortype == majortype) {
      return TRUE;
    }
  }
  return FALSE;
}

BOOL IsPinMajorType(IPin* pin, REFGUID majortype) {
  Microsoft::WRL::ComPtr<IEnumMediaTypes> enum_media_types;
  if (FAILED(pin->EnumMediaTypes(&enum_media_types))) {
    return FALSE;
  }
  enum_media_types->Reset();
  ULONG fetched = 0;
  ScopedMediaType media_type;
  while (enum_media_types->Next(1, media_type.Receive(), &fetched) == NOERROR) {
    if (media_type->majortype == majortype) {
      return TRUE;
    }
    media_type.Release();
  }
  return FALSE;
}

BOOL TranslateMediatypeToVideoCaptureFormat(
    VideoCaptureFormat& format,
    const GUID& sub_type,
    const VIDEOINFOHEADER* video_info_header,
    const VIDEO_STREAM_CONFIG_CAPS* video_stream_caps) {
  format.SetFormat(TranslateMediaSubtypeToPixelFormat(sub_type));
  if (format.GetFormat() == mediasdk::kPixelFormatUnspecified) {
    return false;
  }

  MSSize size{video_info_header->bmiHeader.biWidth,
              video_info_header->bmiHeader.biHeight};
  format.SetSize(size);
  format.SetMinSize(size);
  format.SetMaxSize(size);

  float rate = ((video_info_header->AvgTimePerFrame > 0)
                    ? 10000000.0 / video_info_header->AvgTimePerFrame
                    : 0.0F);
  format.SetRate(rate);
  format.SetMinRate(rate);
  format.SetMaxRate(rate);

  if (video_stream_caps) {
    if (video_stream_caps->MinOutputSize.cx &&
        video_stream_caps->MinOutputSize.cy &&
        video_stream_caps->MaxOutputSize.cx &&
        video_stream_caps->MaxOutputSize.cy) {
      format.SetMinSize(MSSize{video_stream_caps->MinOutputSize.cx,
                               video_stream_caps->MinOutputSize.cy});
      format.SetMaxSize(MSSize{video_stream_caps->MaxOutputSize.cx,
                               video_stream_caps->MaxOutputSize.cy});
    }
    if (video_stream_caps->MaxOutputSize.cx &&
        video_stream_caps->MaxOutputSize.cy) {
      format.SetMaxSize(MSSize{video_stream_caps->MaxOutputSize.cx,
                               video_stream_caps->MaxOutputSize.cy});
    }
    format.SetMinRate((video_stream_caps->MaxFrameInterval)
                          ? 10000000.0 / video_stream_caps->MaxFrameInterval
                          : format.GetRate());
    format.SetMaxRate((video_stream_caps->MinFrameInterval > 0)
                          ? 10000000.0 / video_stream_caps->MinFrameInterval
                          : format.GetRate());
  }
  return true;
}

mediasdk::PixelFormat TranslateMediaSubtypeToPixelFormat(const GUID& sub_type) {
  for (size_t i = 0; i < ARRAYSIZE(FORMAT_MAP); ++i) {
    if (sub_type == FORMAT_MAP[i].sub_type_st) {
      return FORMAT_MAP[i].format;
    }
  }
  return mediasdk::kPixelFormatUnspecified;
}

Microsoft::WRL::ComPtr<IPin> FindPinByName(IBaseFilter* filter,
                                           PIN_DIRECTION dest,
                                           LPCWSTR name) {
  Microsoft::WRL::ComPtr<IPin> pin;
  Microsoft::WRL::ComPtr<IEnumPins> enum_pins;
  if (FAILED(filter->EnumPins(&enum_pins)) || !enum_pins) {
    return pin;
  }
  enum_pins->Reset();
  while (enum_pins->Next(1, &pin, NULL) == S_OK) {
    PIN_DIRECTION src = static_cast<PIN_DIRECTION>(-1);
    PIN_INFO info = {0};
    if (SUCCEEDED(pin->QueryDirection(&src)) &&
        SUCCEEDED(pin->QueryPinInfo(&info))) {
      if (!name || wcscmp(info.achName, name) == 0) {
        return pin;
      }
    }
    pin.Reset();
  }
  return pin;
}

Microsoft::WRL::ComPtr<IPin> FindPinByMedium(IBaseFilter* filter,
                                             REGPINMEDIUM& medium) {
  Microsoft::WRL::ComPtr<IPin> pin;
  Microsoft::WRL::ComPtr<IEnumPins> enum_pins;
  if (FAILED(filter->EnumPins(&enum_pins)) || !enum_pins) {
    return pin;
  }
  enum_pins->Reset();
  while (enum_pins->Next(1, &pin, NULL) == S_OK) {
    REGPINMEDIUM cur_medium;
    if (GetPinMedium(pin.Get(), cur_medium) &&
        memcmp(&medium, &cur_medium, sizeof(medium)) == 0) {
      return pin;
    }
    pin.Reset();
  }
  return pin;
}

Microsoft::WRL::ComPtr<IPin> FindPinByCategoryAndStreamMajorType(
    IBaseFilter* filter,
    PIN_DIRECTION dest,
    REFGUID category,
    REFGUID majortype) {
  Microsoft::WRL::ComPtr<IPin> pin;
  Microsoft::WRL::ComPtr<IEnumPins> enum_pins;
  if (FAILED(filter->EnumPins(&enum_pins)) || !enum_pins) {
    return pin;
  }
  enum_pins->Reset();
  while (enum_pins->Next(1, &pin, NULL) == S_OK) {
    PIN_DIRECTION src = static_cast<PIN_DIRECTION>(-1);
    if (SUCCEEDED(pin->QueryDirection(&src)) && dest == src) {
      if ((category == GUID_NULL || IsPinCategory(pin.Get(), category)) &&
          (majortype == GUID_NULL ||
           IsPinStreamMajorType(pin.Get(), majortype))) {
        return pin;
      }
    }
    pin.Reset();
  }
  return pin;
}

Microsoft::WRL::ComPtr<IPin> FindPinByMajortype(IBaseFilter* filter,
                                                PIN_DIRECTION dest,
                                                REFGUID majortype) {
  Microsoft::WRL::ComPtr<IPin> pin;
  Microsoft::WRL::ComPtr<IEnumPins> enum_pins;
  if (FAILED(filter->EnumPins(&enum_pins)) || !enum_pins) {
    return pin;
  }
  enum_pins->Reset();
  while (enum_pins->Next(1, &pin, NULL) == S_OK) {
    PIN_DIRECTION src = static_cast<PIN_DIRECTION>(-1);
    if (SUCCEEDED(pin->QueryDirection(&src)) && dest == src) {
      if (majortype == GUID_NULL || IsPinMajorType(pin.Get(), majortype)) {
        return pin;
      }
    }
    pin.Reset();
  }
  return pin;
}

Microsoft::WRL::ComPtr<IBaseFilter> GetDeviceBaseFilter(
    const DShowDeviceName& name,
    const CLSID& id) {
  Microsoft::WRL::ComPtr<ICreateDevEnum> dev_enum;
  Microsoft::WRL::ComPtr<IBaseFilter> filter;
  HRESULT h = ::CoCreateInstance(CLSID_SystemDeviceEnum, NULL, CLSCTX_INPROC,
                                 __uuidof(dev_enum), (void**)&dev_enum);
  if (h != S_OK) {
    LOG(ERROR) << "Failed to CoCreateInstance(CLSID_SystemDeviceEnum,0x"
               << std::hex << h << ")";
    return filter;
  }
  Microsoft::WRL::ComPtr<IEnumMoniker> enum_moniker;
  h = dev_enum->CreateClassEnumerator(id, &enum_moniker, 0);
  if (h != S_OK) {
    LOG(ERROR) << "Failed to CreateClassEnumerator(IEnumMoniker,0x" << std::hex
               << h << ")";
    return filter;
  }
  Microsoft::WRL::ComPtr<IMoniker> moniker;
  DWORD fetched = 0;
  while (enum_moniker->Next(1, &moniker, &fetched) == S_OK) {
    Microsoft::WRL::ComPtr<IPropertyBag> propertybag;
    h = moniker->BindToStorage(NULL, NULL, IID_IPropertyBag,
                               (void**)&propertybag);
    if (h != S_OK) {
      moniker.Reset();
      continue;
    }
    base::win::ScopedVariant variant;
    for (int32_t i = 0;
         i < ARRAYSIZE(PROPERTY_NAMES) && variant.type() != VT_BSTR; ++i) {
      variant.Reset();
      propertybag->Read(PROPERTY_NAMES[i], variant.Receive(), 0);
    }
    if (variant.type() != VT_BSTR) {
      moniker.Reset();
      continue;
    }
    std::wstring sz_id = V_BSTR(variant.ptr());
    if (sz_id.compare(name.id) == 0) {
      h = moniker->BindToObject(NULL, NULL, IID_IBaseFilter, (void**)&filter);
      if (SUCCEEDED(h)) {
        return filter;
      }
      LOG(ERROR) << "Failed to BindToObject(IEnumMoniker,0x" << std::hex << h
                 << ")";
    }
    moniker.Reset();
  }
  return filter;
}

Microsoft::WRL::ComPtr<IBaseFilter> GetDeviceBaseFilter(const CLSID& id,
                                                        REGPINMEDIUM& medium) {
  Microsoft::WRL::ComPtr<ICreateDevEnum> dev_enum;
  Microsoft::WRL::ComPtr<IBaseFilter> filter;
  HRESULT h = ::CoCreateInstance(CLSID_SystemDeviceEnum, NULL, CLSCTX_INPROC,
                                 __uuidof(dev_enum), (void**)&dev_enum);
  if (h != S_OK) {
    LOG(ERROR) << "Failed to CoCreateInstance(CLSID_SystemDeviceEnum,0x"
               << std::hex << h << ")";
    return filter;
  }
  Microsoft::WRL::ComPtr<IEnumMoniker> enum_moniker;
  h = dev_enum->CreateClassEnumerator(id, &enum_moniker, 0);
  if (h != S_OK) {
    LOG(ERROR) << "Failed to CreateClassEnumerator(IEnumMoniker,0x" << std::hex
               << h << ")";
    return filter;
  }
  Microsoft::WRL::ComPtr<IMoniker> moniker;
  DWORD fetched = 0;
  while (enum_moniker->Next(1, &moniker, &fetched) == S_OK) {
    Microsoft::WRL::ComPtr<IPropertyBag> propertybag;
    h = moniker->BindToStorage(0, 0, IID_IPropertyBag, (void**)&propertybag);
    if (h != S_OK) {
      moniker.Reset();
      continue;
    }
    h = moniker->BindToObject(0, 0, IID_IBaseFilter, (void**)&filter);
    if (SUCCEEDED(h) && filter) {
      Microsoft::WRL::ComPtr<IPin> pin(FindPinByMedium(filter.Get(), medium));
      if (pin) {
        return filter;
      }
    }
  }
  return filter;
}

BOOL GetPinMedium(IPin* pin, REGPINMEDIUM& reg_medium) {
  Microsoft::WRL::ComPtr<IKsPin> ks_pin;
  pin->QueryInterface(__uuidof(ks_pin), (void**)&ks_pin);
  if (!ks_pin) {
    return FALSE;
  }
  PKSMULTIPLE_ITEM pmi = NULL;
  HRESULT hr = ks_pin->KsQueryMediums(&pmi);
  if (FAILED(hr)) {
    return FALSE;
  }
  REGPINMEDIUM* ptr_medium = (REGPINMEDIUM*)(pmi + 1);
  for (ULONG i = 0; i < pmi->Count; i++) {
    REGPINMEDIUM* ptr_medium_tmp = ptr_medium + i;
    if (!ptr_medium_tmp) {
      continue;
    }
    if (!::IsEqualGUID(ptr_medium_tmp->clsMedium, GUID_NULL) &&
        !::IsEqualGUID(ptr_medium_tmp->clsMedium, KSMEDIUMSETID_Standard)) {
      reg_medium = *ptr_medium_tmp;
      ::CoTaskMemFree(pmi);
      return TRUE;
    }
  }
  ::CoTaskMemFree(pmi);
  return FALSE;
}

BOOL GetDeviceFormats(const DShowDeviceName& name,
                      const CLSID& id,
                      std::vector<VideoCaptureFormat>& formats) {
  HRESULT h = S_OK;
  Microsoft::WRL::ComPtr<IBaseFilter> capture_filter =
      GetDeviceBaseFilter(name, id);
  if (!capture_filter) {
    LOG(ERROR) << "Failed to GetDeviceFilter";
    return FALSE;
  }
  Microsoft::WRL::ComPtr<IPin> output_pin = FindPinByCategoryAndStreamMajorType(
      capture_filter.Get(), PINDIR_OUTPUT, PIN_CATEGORY_CAPTURE,
      MEDIATYPE_Video);
  if (!output_pin) {
    LOG(ERROR) << "Failed to FindPinByCategoryAndStreamMajorType";
    return FALSE;
  }
  Microsoft::WRL::ComPtr<IAMStreamConfig> stream_config;
  h = output_pin->QueryInterface(__uuidof(stream_config),
                                 (void**)&stream_config);
  if (h != S_OK) {
    LOG(ERROR) << "Failed to QueryInterface(IAMStreamConfig,0x" << std::hex << h
               << ")";
    return FALSE;
  }
  int32_t counts = 0, size = 0;
  h = stream_config->GetNumberOfCapabilities(&counts, &size);
  if (h != S_OK) {
    LOG(ERROR) << "Failed to GetNumberOfCapabilities(0x" << std::hex << h
               << ")";
    return FALSE;
  }
  auto caps = std::shared_ptr<uint8_t>(new uint8_t[size],
                                       [](uint8_t* ptr) { delete[] ptr; });
  if (!caps) {
    LOG(ERROR) << "Failed to AllocMem";
    return FALSE;
  }
  LOG(INFO) << "GetNumberOfCapabilities counts=" << counts;
  for (int32_t i = 0; i < counts; ++i) {
    ScopedMediaType media_type;
    h = stream_config->GetStreamCaps(i, media_type.Receive(), caps.get());
    if (h != S_OK) {
      LOG(ERROR) << "Failed to GetStreamCaps(0x" << std::hex << h << ")";
      continue;
    }
    if (media_type.Ptr() && media_type->majortype == MEDIATYPE_Video &&
        media_type->formattype == FORMAT_VideoInfo) {
      const VIDEOINFOHEADER* video_info_header =
          reinterpret_cast<VIDEOINFOHEADER*>(media_type->pbFormat);
      if (video_info_header == NULL) {
        LOG(ERROR) << "mediaType->pbFormat cast error";
        continue;
      }
      VideoCaptureFormat format;
      const VIDEO_STREAM_CONFIG_CAPS* video_stream_caps =
          reinterpret_cast<const VIDEO_STREAM_CONFIG_CAPS*>(caps.get());
      if (TranslateMediatypeToVideoCaptureFormat(format, media_type->subtype,
                                                 video_info_header,
                                                 video_stream_caps)) {
        formats.push_back(format);
      }
    }
  }
  std::sort(formats.begin(), formats.end(),
            [](const VideoCaptureFormat& a, const VideoCaptureFormat& b) {
              MSSize size1 = a.GetSize();
              MSSize size2 = b.GetSize();
              return (a.GetFormat() == b.GetFormat()) &&
                     (size1.cx < size2.cx) && (size1.cy < size2.cy);
            });
  return !formats.empty();
}

BOOL GetDeviceFormats(const DShowDeviceName& name,
                      std::vector<AudioCaptureFormat>& formats) {
  HRESULT h = S_OK;
  Microsoft::WRL::ComPtr<IBaseFilter> capture_filter =
      GetDeviceBaseFilter(name, CLSID_AudioInputDeviceCategory);
  if (!capture_filter) {
    capture_filter = GetDeviceBaseFilter(name, CLSID_VideoInputDeviceCategory);
    if (!capture_filter) {
      LOG(WARNING) << "Audio Failed to GetDeviceFilter";
      return FALSE;
    }
  }
  Microsoft::WRL::ComPtr<IPin> output_pin = FindPinByCategoryAndStreamMajorType(
      capture_filter.Get(), PINDIR_OUTPUT, PIN_CATEGORY_CAPTURE,
      MEDIATYPE_Audio);
  if (!output_pin) {
    LOG(ERROR) << "Failed to FindPinByCategoryAndStreamMajorType";
    return FALSE;
  }
  Microsoft::WRL::ComPtr<IAMStreamConfig> stream_config;
  h = output_pin->QueryInterface(__uuidof(stream_config),
                                 (void**)&stream_config);
  if (h != S_OK) {
    LOG(ERROR) << "Failed to QueryInterface(IAMStreamConfig,0x" << std::hex << h
               << ")";
    return FALSE;
  }
  int32_t count = 0;
  int32_t size = 0;
  h = stream_config->GetNumberOfCapabilities(&count, &size);
  if (h != S_OK) {
    LOG(ERROR) << "Failed to GetNumberOfCapabilities(0x" << std::hex << h
               << ")";
    return FALSE;
  }
  std::vector<uint8_t> caps(size, 0);
  for (int32_t i = 0; i < count; ++i) {
    ScopedMediaType mediaType;
    h = stream_config->GetStreamCaps(i, mediaType.Receive(), caps.data());
    if (h != S_OK) {
      LOG(ERROR) << "Failed to GetStreamCaps(0x" << std::hex << h << ")";
      return FALSE;
    }
    if (mediaType.Ptr() && mediaType->majortype == MEDIATYPE_Audio &&
        mediaType->formattype == FORMAT_WaveFormatEx) {
      WAVEFORMATEX* wave_fmt =
          reinterpret_cast<WAVEFORMATEX*>(mediaType->pbFormat);
      if (!wave_fmt) {
        continue;
      }
      AudioCaptureFormat format;
      format.SetFormat(wave_fmt);
      formats.push_back(format);
    }
  }
  return TRUE;
}

// 扩展的音频格式枚举函数
BOOL GetAudioFormatsForVideoDevice(
    const DShowDeviceName& video_device,
    ExtendedAudioFormatResult& result) {

  result.source_video_device = video_device;
  result.has_integrated_audio = false;
  result.error_message.clear();
  result.suggestions.clear();
  result.audio_results.clear();

  // 策略1：尝试一体化设备查找（精确ID匹配）
  std::vector<AudioCaptureFormat> integrated_formats;
  if (GetDeviceFormats(video_device, integrated_formats)) {
    result.has_integrated_audio = true;

    AudioDeviceMatchResult integrated_result;
    integrated_result.audio_device = video_device;
    integrated_result.formats = integrated_formats;
    integrated_result.confidence_score = 1.0f;
    integrated_result.match_strategy = DeviceMatchStrategy::EXACT_ID_MATCH;
    integrated_result.match_details = "Integrated device with exact ID match";

    result.audio_results.push_back(integrated_result);
    return TRUE;
  }

  // 策略2：尝试关联设备查找
  std::vector<DeviceMatchStrategy> strategies = {
    DeviceMatchStrategy::USB_PATH_MATCH,
    DeviceMatchStrategy::FUZZY_NAME_MATCH,
    DeviceMatchStrategy::NAME_PATTERN_MATCH
  };

  for (const auto& strategy : strategies) {
    auto associated_devices = FindAssociatedAudioDevices(video_device, strategy);

    for (const auto& audio_device : associated_devices) {
      std::vector<AudioCaptureFormat> formats;
      if (GetDeviceFormats(audio_device, formats)) {
        AudioDeviceMatchResult audio_result;
        audio_result.audio_device = audio_device;
        audio_result.formats = formats;
        audio_result.confidence_score = CalculateDeviceMatchConfidence(
            video_device, audio_device, strategy);
        audio_result.match_strategy = strategy;

        // 生成匹配详情
        switch (strategy) {
          case DeviceMatchStrategy::USB_PATH_MATCH:
            audio_result.match_details = "Matched by USB device path";
            break;
          case DeviceMatchStrategy::FUZZY_NAME_MATCH:
            audio_result.match_details = "Matched by fuzzy name matching";
            break;
          case DeviceMatchStrategy::NAME_PATTERN_MATCH:
            audio_result.match_details = "Matched by name pattern";
            break;
          default:
            audio_result.match_details = "Unknown matching strategy";
            break;
        }

        result.audio_results.push_back(audio_result);
      }
    }

    // 如果找到了高置信度的匹配，就不再尝试其他策略
    if (!result.audio_results.empty() &&
        result.audio_results[0].confidence_score > 0.8f) {
      break;
    }
  }

  if (result.audio_results.empty()) {
    result.error_message = "No associated audio devices found for video device: " +
                          base::WideToUTF8(video_device.name);

    // 提供建议
    result.suggestions.push_back("Check if the device supports audio capture");
    result.suggestions.push_back("Try using a separate audio input device");
    result.suggestions.push_back("Verify device drivers are properly installed");
    result.suggestions.push_back("Check Windows audio device settings");

    return FALSE;
  }

  return TRUE;
}

BOOL GetPinMediaType(IPin* pin, REFGUID sub_type, AM_MEDIA_TYPE** pp_type) {
  HRESULT hr = S_OK;
  Microsoft::WRL::ComPtr<IEnumMediaTypes> enum_mtypes;
  hr = pin->EnumMediaTypes(&enum_mtypes);
  if (FAILED(hr)) {
    LOG(ERROR) << "Failed to EnumMediaTypes(IEnumMediaTypes,0x" << std::hex
               << hr << ")";
    return FALSE;
  }
  enum_mtypes->Reset();
  ULONG fetched = 0;
  ScopedMediaType media_type;
  while (enum_mtypes->Next(1, media_type.Receive(), &fetched) == NOERROR) {
    if (media_type->majortype == MEDIATYPE_Video &&
        media_type->formattype == FORMAT_VideoInfo &&
        media_type->subtype == sub_type) {
      if (*pp_type) {
        CopyMediaType(*pp_type, media_type.Ptr());
      } else {
        *pp_type = media_type.Ptr();
        *media_type.Receive() = NULL;
      }
      return TRUE;
    }
  }
  return FALSE;
}

}  // namespace mediasdk