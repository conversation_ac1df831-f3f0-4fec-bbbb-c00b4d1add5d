#pragma once

#include <cmath>
#include <limits>

namespace graphics {

template <typename T>
inline bool IsNearEqual(T a,
                        T b,
                        T epsilon = std::numeric_limits<T>::epsilon()) {
  return std::fabs(a - b) < epsilon;
}

template <typename T>
inline bool Is<PERSON>essThan(T a,
                       T b,
                       T epsilon = std::numeric_limits<T>::epsilon()) {
  return (b - a) > epsilon;
}

template <typename T>
inline bool IsGreaterThan(T a,
                          T b,
                          T epsilon = std::numeric_limits<T>::epsilon()) {
  return (a - b) > epsilon;
}

}  // namespace graphics
