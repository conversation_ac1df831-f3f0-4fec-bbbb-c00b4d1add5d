#include "speed_test_stream_service.h"
#include "stream_service.h"

#include "flv_meta_data_builder.h"
#include "mediasdk/component_center.h"
#include "mediasdk/public/plugin/stream_service_source.h"
#include "mediasdk/utils/time_helper.h"
#include "speed_test_stream_service_reporter.h"

namespace {
constexpr char kSpeedTestThread[] = "Speed_Test_Producer_Thread";

constexpr int kReportTimerIntervalS = 1;
constexpr int kInterval = 1000;
constexpr int kTimeBase = 60;
constexpr int kAudioBitrate = 128;
constexpr int kVideoBitrate = 3500;
constexpr int kGop = 2;
constexpr int kOutputWidth = 1080;
constexpr int kOutputHeight = 720;
constexpr int kAudioTimeBase = 1000;

int CalSleepInterval(int target_bitrate) {
  if (target_bitrate > 1000 && target_bitrate < 500000) {
    return 54000000 / target_bitrate - 300;
  }
  return 200;
}
}  // namespace

namespace mediasdk {

SpeedTestStreamService::SpeedTestStreamService(
    std::shared_ptr<StreamConfig> config,
    std::shared_ptr<StreamServiceSource> source)
    : OriginStreamService(config, source),
      rate_calc_(GetConfig() ? GetConfig()->GetStreamId() : ""),
      producer_thread_(std::make_unique<base::Thread>(kSpeedTestThread)),
      stop_event_(base::WaitableEvent::ResetPolicy::MANUAL,
                  base::WaitableEvent::InitialState::NOT_SIGNALED) {
  DCHECK(config_);
  DCHECK(source_);
  reporter_ = std::make_unique<SpeedTestStreamServiceReporter>();
  reporter_->SetService(this);
}

SpeedTestStreamService::~SpeedTestStreamService() {
  Stop();
}

bool SpeedTestStreamService::Start() {
  stop_event_.Reset();

  rate_calc_.Reset();
  BuildMetaData();
  if (meta_data_.empty()) {
    LOG(ERROR) << "FlvMetaDataBuilder build failed";
    return false;
  }

  StartOutputPump();

  if (!producer_thread_->IsRunning()) {
    producer_thread_->Start();
  }

  producer_thread_->task_runner()->PostTask(
      FROM_HERE, base::BindOnce(&SpeedTestStreamService::OnProducerPump,
                                base::Unretained(this)));

  report_timer_.Start(
      FROM_HERE, base::Seconds(kReportTimerIntervalS),
      base::BindRepeating(&SpeedTestStreamService::OnReportTimer,
                          base::Unretained(this)));
  return true;
}

void SpeedTestStreamService::Stop() {
  LOG(INFO) << "SpeedTestStreamService Stop";
  report_timer_.Stop();
  stop_event_.Signal();

  LOG(INFO) << "SpeedTestStreamService StopOutputPump";
  StopOutputPump();
  if (producer_thread_->IsRunning()) {
    LOG(INFO) << "SpeedTestStreamService Stop producer_thread";
    producer_thread_->Stop();
  }

  if (StreamIsClosed()) {
    LOG(INFO) << "StreamIsClosed";
    if (reporter_) {
      reporter_->ReportBandwidth();
      // Ensure that vqos will only report once
      reporter_ = nullptr;
    }
  }
}

bool SpeedTestStreamService::PreprocessingBeforeDisconnected() {
  if (StreamIsClosed()) {
    DCHECK(!config_->GetStreamId().empty());
  }
  return true;
}

bool SpeedTestStreamService::PreprocessingAfterConnected() {
  connect_time_stamp_ = nano_now();
  dns_time_ = source_->GetDnsResolveTime();
  socket_connect_time_ = source_->GetSocketConnectTime();
  rtmp_connect_time_ = source_->GetSocketConnectTime();
  return true;
}

bool SpeedTestStreamService::OnDropPacket(std::shared_ptr<PacketBase> packet) {
  DCHECK(packet);
  if (packet) {
    rate_calc_.DropPacket(packet->GetType(), packet->GetDataSize(),
                          packet->GetDTS());
  }
  return OriginStreamService::OnDropPacket(std::move(packet));
}

bool SpeedTestStreamService::WritePacket(std::shared_ptr<PacketBase> packet) {
  DCHECK(packet);
  if (!packet) {
    return false;
  }
  rate_calc_.SendPacket(packet->GetType(), packet->GetDataSize(),
                        packet->GetDTS());
  return OriginStreamService::WritePacket(packet);
}

void SpeedTestStreamService::EnqueuePacket(std::shared_ptr<PacketBase> packet,
                                           bool drop) {
  OriginStreamService::EnqueuePacket(packet, drop);
  DCHECK(packet);
  if (!packet) {
    return;
  }
  const auto packet_type = packet->GetType();
  const auto packet_size = packet->GetDataSize();
  const auto packet_dts = packet->GetDTS();
  rate_calc_.EnqueuePacket(packet_type, packet_size, packet_dts);
}

void SpeedTestStreamService::OnReportTimer() {
  if (reporter_) {
    reporter_->GetNetStatus();
  }
}

void SpeedTestStreamService::OnProducerPump() {
  DCHECK(reporter_);
  int target_bitrate = config_->GetSpeedTestTargetBitrate();
  int sleep_interval = CalSleepInterval(target_bitrate);
  WriteFlvMetaData();
  while (!stop_event_.IsSignaled()) {
    WriteFlvVideoHeader();
    WriteFlvAudioHeader();

    // Sleep for some time
    SleepByUS(sleep_interval);
    // remain responsive to the stop_event_
    if (stop_event_.IsSignaled()) {
      LOG(INFO) << "stop_event_ IsSignaled";
      break;
    }

    WriteFlvVideoIdrFrame();
    WriteFlvAudioData();

    SleepByUS((DWORD)sleep_interval);
    if (stop_event_.IsSignaled()) {
      LOG(INFO) << "stop_event_ IsSignaled";
      break;
    }

    for (int i = 0; i < 5; ++i) {
      WriteFlvVideoBFrame();
      WriteFlvAudioData();

      SleepByUS((DWORD)sleep_interval);
      if (stop_event_.IsSignaled()) {
        LOG(INFO) << "stop_event_ IsSignaled";
        break;
      }
    }
  }
}

void SpeedTestStreamService::WriteFlvMetaData() {
  auto meta_data = std::make_shared<VideoPacket>();
  meta_data->SetData(meta_data_.data(), meta_data_.size());
  meta_data->SetTimeBase(kTimeBase);

  // TODO(lgd): timestamp for packet, think of packet reuse
  meta_data->SetDTS(nano_now() / 1000L);
  EnqueuePacket(std::move(meta_data), false);
}

void SpeedTestStreamService::WriteFlvVideoHeader() {
  auto video_header = std::make_shared<VideoPacket>();
  video_header->SetData(kSpsPpsData, sizeof(kSpsPpsData));
  video_header->SetTimeBase(kTimeBase);
  video_header->SetDTS(nano_now() / 1000L);
  video_header->SetRefferred(true);
  EnqueuePacket(std::move(video_header), false);
}

void SpeedTestStreamService::WriteFlvVideoIdrFrame() {
  auto video_idr_frame = std::make_shared<VideoPacket>();
  video_idr_frame->SetData(kIdrData, sizeof(kIdrData));
  video_idr_frame->SetFrameType(mediasdk::kVideoFrameTypeI);
  video_idr_frame->SetKeyFrame(true);
  video_idr_frame->SetTimeBase(kTimeBase);
  video_idr_frame->SetDTS(nano_now() / 1000L);
  video_idr_frame->SetRefferred(true);
  EnqueuePacket(std::move(video_idr_frame), false);
}

void SpeedTestStreamService::WriteFlvVideoBFrame() {
  auto video_b_frame = std::make_shared<VideoPacket>();
  video_b_frame->SetData(kBFrameData, sizeof(kBFrameData));
  video_b_frame->SetFrameType(mediasdk::kVideoFrameTypeB);
  video_b_frame->SetTimeBase(kTimeBase);
  video_b_frame->SetDTS(nano_now() / 1000L);
  video_b_frame->SetRefferred(false);
  EnqueuePacket(std::move(video_b_frame), false);
}

void SpeedTestStreamService::WriteFlvAudioHeader() {
  auto audio_data = std::make_shared<AudioPacket>();
  audio_data->SetData(kAudioHeadData, sizeof(kAudioHeadData));
  auto time_stamp = nano_now() / 1000L;
  audio_data->SetPTS(time_stamp);
  audio_data->SetDTS(time_stamp);
  audio_data->SetTimeBase(kAudioTimeBase);
  EnqueuePacket(std::move(audio_data), false);
}

void SpeedTestStreamService::WriteFlvAudioData() {
  auto audio_data = std::make_shared<AudioPacket>();
  audio_data->SetData(kAudioData, sizeof(kAudioData));
  auto time_stamp = nano_now() / 1000L;
  audio_data->SetPTS(time_stamp);
  audio_data->SetDTS(time_stamp);
  audio_data->SetTimeBase(kAudioTimeBase);
  EnqueuePacket(std::move(audio_data), false);
}

void SpeedTestStreamService::BuildMetaData() {
  MetaDataField field;
  field.audio_bitrate = kAudioBitrate;
  field.cipher = config_->GetCipher();
  field.plain_text = config_->GetPlainText();
  field.frame_rate = kTimeBase;
  field.gop = kGop;
  field.is_h264 = true;
  field.output_height = kOutputHeight;
  field.output_width = kOutputWidth;
  field.video_bitrate = kVideoBitrate;
  FlvMetaDataBuilder builder;
  meta_data_ = builder.Build(field);
}
}  // namespace mediasdk