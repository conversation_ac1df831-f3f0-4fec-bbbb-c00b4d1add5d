#include <Windows.h>
#include <tchar.h>
#include "base/scoped_native_library.h"
#include "base/strings/stringprintf.h"
#include "os_version.h"

namespace {

typedef LONG(NTAPI* RTL_GETVRSION)(PRTL_OSVERSIONINFOW lpVersionInformation);

bool RtlGetVersion(RTL_OSVERSIONINFOEXW& ver) {
  ZeroMemory(&ver, sizeof(ver));
  ver.dwOSVersionInfoSize = sizeof(ver);
  static auto MS_RTL_GETVRSION = (RTL_GETVRSION)GetProcAddress(
      GetModuleHandleA("ntdll.dll"), "RtlGetVersion");
  if (MS_RTL_GETVRSION != 0) {
    if (MS_RTL_GETVRSION((PRTL_OSVERSIONINFOW)&ver) == 0) {
      if (ver.dwPlatformId != VER_PLATFORM_WIN32_NT) {
        return false;
      }
      return true;
    }
  }
  return false;
}

static decltype(::GetFileVersionInfoSizeW)* MS_GetFileVersionInfoSizeW = NULL;
static decltype(::GetFileVersionInfoW)* MS_GetFileVersionInfoW = NULL;
static decltype(::VerQueryValueW)* MS_VerQueryValueW = NULL;

bool NtoskrnlGetVersion(RTL_OSVERSIONINFOEXW& ver, DWORD& revision) {
  ZeroMemory(&ver, sizeof(ver));
  if (!MS_VerQueryValueW || !MS_GetFileVersionInfoW ||
      !MS_GetFileVersionInfoSizeW) {
    base::ScopedNativeLibrary version_lib(base::FilePath(L"version"));
    if (!version_lib.is_valid()) {
      return false;
    }
    MS_GetFileVersionInfoSizeW =
        reinterpret_cast<decltype(::GetFileVersionInfoSizeW)*>(
            version_lib.GetFunctionPointer("GetFileVersionInfoSizeW"));
    MS_GetFileVersionInfoW = reinterpret_cast<decltype(::GetFileVersionInfoW)*>(
        version_lib.GetFunctionPointer("GetFileVersionInfoW"));
    MS_VerQueryValueW = reinterpret_cast<decltype(::VerQueryValueW)*>(
        version_lib.GetFunctionPointer("VerQueryValueW"));
  }
  if (!MS_GetFileVersionInfoSizeW || !MS_GetFileVersionInfoW ||
      !MS_VerQueryValueW) {
    return false;
  }
  TCHAR ntoskrnl_path[MAX_PATH] = {0};
  _tcscat_s(ntoskrnl_path, _countof(ntoskrnl_path), TEXT("ntoskrnl.exe"));
  UINT size = MS_GetFileVersionInfoSizeW(ntoskrnl_path, NULL);
  if (!size) {
    return false;
  }
  std::vector<char> version_data;
  version_data.resize(size);
  if (!MS_GetFileVersionInfoW(ntoskrnl_path, 0, size,
                              (LPVOID)version_data.data())) {
    return false;
  }
  VS_FIXEDFILEINFO* pvs;
  if (!MS_VerQueryValueW((LPVOID)version_data.data(), L"\\", (LPVOID*)&pvs,
                         &size)) {
    return false;
  }
  ver.dwMajorVersion = (DWORD)HIWORD(pvs->dwFileVersionMS);
  ver.dwMinorVersion = (DWORD)LOWORD(pvs->dwFileVersionMS);
  ver.dwBuildNumber = (DWORD)HIWORD(pvs->dwFileVersionLS);
  revision = (DWORD)LOWORD(pvs->dwFileVersionLS);
  return true;
}

#define WINVER_REG_KEY "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion"

bool RegGetVersion(RTL_OSVERSIONINFOEXW& ver, DWORD& revision) {
  {
    ZeroMemory(&ver, sizeof(ver));
    HKEY key;
    DWORD size, value;
    LSTATUS status;
    WCHAR str[MAX_PATH];
    status = RegOpenKeyA(HKEY_LOCAL_MACHINE, WINVER_REG_KEY, &key);
    if (status != ERROR_SUCCESS) {
      return false;
    }
    size = sizeof(value);
    status = RegQueryValueExW(key, L"CurrentMajorVersionNumber", NULL, NULL,
                              (LPBYTE)&value, &size);
    if (status == ERROR_SUCCESS) {
      ver.dwMajorVersion = (INT32)value;
    }
    status = RegQueryValueExW(key, L"CurrentMinorVersionNumber", NULL, NULL,
                              (LPBYTE)&value, &size);
    if (status == ERROR_SUCCESS) {
      ver.dwMinorVersion = (INT32)value;
    }

    size = sizeof(str);
    if (auto status = RegQueryValueExW(key, L"CurrentBuildNumber", NULL, NULL,
                                       (LPBYTE)str, &size);
        ERROR_SUCCESS == status) {
      str[(sizeof(str) / sizeof(WCHAR)) - 1] = 0;
      ver.dwBuildNumber = wcstol(str, NULL, 10);
    }
    if (key != NULL) {
      RegCloseKey(key);
    }
  }
  {
    HKEY key = NULL;
    DWORD size;
    LSTATUS status = ERROR_SUCCESS;
    status = RegOpenKeyA(HKEY_LOCAL_MACHINE, WINVER_REG_KEY, &key);
    if (status == ERROR_SUCCESS) {
      size = sizeof(revision);
      status =
          RegQueryValueExA(key, "UBR", NULL, NULL, (LPBYTE)&revision, &size);
    }
    if (key != NULL) {
      RegCloseKey(key);
    }
  }

  return true;
}

bool GreaterVersion(const RTL_OSVERSIONINFOEXW& ver1,
                    const RTL_OSVERSIONINFOEXW& ver2) {
  if (ver2.dwMajorVersion > ver1.dwMajorVersion)
    return true;
  if (ver2.dwMajorVersion == ver1.dwMajorVersion) {
    if (ver2.dwMinorVersion > ver1.dwMinorVersion)
      return true;
    if (ver2.dwMinorVersion == ver1.dwMinorVersion) {
      if (ver2.dwBuildNumber > ver1.dwBuildNumber)
        return true;
    }
  }
  return false;
}

}  // namespace

namespace os_version {

std::string OsVersion::GetOsVersion() {
  static std::string os_version;
  if (os_version.empty()) {
    RTL_OSVERSIONINFOEXW os_ver;
    RtlGetVersion(os_ver);
    RTL_OSVERSIONINFOEXW tmp_os_ver;
    DWORD os_revision;
    if (NtoskrnlGetVersion(tmp_os_ver, os_revision)) {
      if (GreaterVersion(os_ver, tmp_os_ver)) {
        os_ver = tmp_os_ver;
      }
    }
    DWORD tmp_os_revision;
    if (RegGetVersion(tmp_os_ver, tmp_os_revision)) {
      if (GreaterVersion(os_ver, tmp_os_ver)) {
        os_ver = tmp_os_ver;
      }
      if (tmp_os_revision > os_revision) {
        os_revision = tmp_os_revision;
      }
    }
    os_version = base::StringPrintf("%d.%d.%d.%d", os_ver.dwMajorVersion,
                                    os_ver.dwMinorVersion, os_ver.dwBuildNumber,
                                    os_revision);
  }
  return os_version;
}
}  // namespace os_version