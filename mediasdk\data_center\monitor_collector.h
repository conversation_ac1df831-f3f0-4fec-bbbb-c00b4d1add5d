#pragma once
#include <dxgi.h>
#include <dxgi1_6.h>
#include <windows.h>
#include <wrl\client.h>
#include <vector>
#include "monitor_collector.h"

#pragma comment(lib, "DXGI.lib")

namespace monitor_collector {

struct MonitorInfo {
  void* monitor_handle = nullptr;
  int gpu_index = 0;
  int left = 0;
  int top = 0;
  int right = 0;
  int bottom = 0;
  int refresh = 0;
  bool hdr = false;
};

class MonitorCollector {
 public:
  static bool GetMonitorInfos(std::vector<MonitorInfo>& monitors) {
    monitors.clear();
    Microsoft::WRL::ComPtr<IDXGIFactory> factory;
    HRESULT hr = CreateDXGIFactory(IID_PPV_ARGS(&factory));
    if (!SUCCEEDED(hr)) {
      return false;
    }
    int gpu_index = 0;
    Microsoft::WRL::ComPtr<IDXGIAdapter> adapter;
    while (
        SUCCEEDED(factory->EnumAdapters(gpu_index, adapter.GetAddressOf())) &&
        adapter) {
      int monitor_index = 0;
      Microsoft::WRL::ComPtr<IDXGIOutput> output;
      while (SUCCEEDED(
                 adapter->EnumOutputs(monitor_index, output.GetAddressOf())) &&
             output) {
        MonitorInfo info{};
        info.gpu_index = gpu_index;
        DXGI_OUTPUT_DESC output_desc;
        hr = output->GetDesc(&output_desc);
        DEVMODEW mode;
        if (EnumDisplaySettingsW(output_desc.DeviceName, ENUM_CURRENT_SETTINGS,
                                 &mode)) {
          info.refresh = mode.dmDisplayFrequency;
        }

        MONITORINFO mi = {sizeof(mi)};
        GetMonitorInfo(output_desc.Monitor, &mi);
        info.monitor_handle = output_desc.Monitor;
        info.left = mi.rcMonitor.left;
        info.right = mi.rcMonitor.right;
        info.top = mi.rcMonitor.top;
        info.bottom = mi.rcMonitor.bottom;

        Microsoft::WRL::ComPtr<IDXGIOutput6> dxgi_output6;
        if (SUCCEEDED(output->QueryInterface(dxgi_output6.GetAddressOf()))) {
          DXGI_OUTPUT_DESC1 output_desc1;
          if (SUCCEEDED(dxgi_output6->GetDesc1(&output_desc1)) &&
              (output_desc1.Monitor == output_desc.Monitor)) {
            info.hdr = output_desc1.ColorSpace ==
                               DXGI_COLOR_SPACE_RGB_FULL_G2084_NONE_P2020
                           ? TRUE
                           : FALSE;
          }
        }
        monitors.emplace_back(info);
      }
    }

    return true;
  }

  static bool IsWindowCurrentMonitor(void* window, void* monitor) {
    if (window == nullptr) {
      return false;
    }
    if (!IsWindow((HWND)window)) {
      return false;
    }
    HMONITOR current_monitor =
        ::MonitorFromWindow((HWND)window, MONITOR_DEFAULTTONULL);
    return current_monitor == monitor;
  }
};
}  // namespace monitor_collector