if (CMAKE_SIZEOF_VOID_P EQUAL 8)
    set(_lib_suffix 64)
else ()
    set(_lib_suffix 32)
endif ()

get_property(IS_MULTI_CONFIG GLOBAL PROPERTY GENERATOR_IS_MULTI_CONFIG)

set(OUTPUT_DIRECTORY "${CMAKE_CURRENT_BINARY_DIR}/output")
if (CMAKE_SIZEOF_VOID_P EQUAL 8)
    set(PACKAGE_DIRECTORY "${CMAKE_SOURCE_DIR}/bins/exec64$<$<CONFIG:Debug>:d>")
elseif (CMAKE_SIZEOF_VOID_P EQUAL 4)
    set(PACKAGE_DIRECTORY "${CMAKE_SOURCE_DIR}/bins/exec32$<$<CONFIG:Debug>:d>")
endif ()

function(mediasdk_declare_resource target prefix)
    get_property(resource_count TARGET ${target} PROPERTY MEDIASDK_TARGET_RESOURCE_COUNT)
    if ("${resource_count}" STREQUAL "")
        set(resource_count "0")
    endif ()

    LIST(LENGTH ARGN list_length)
    if (list_length GREATER 0)
        set_property(TARGET ${target} PROPERTY "MEDIASDK_TARGET_RESOURCE_${resource_count}" "${ARGN}")
        set_property(TARGET ${target} PROPERTY "MEDIASDK_TARGET_RESOURCE_PREFIX_${resource_count}" "${prefix}")
        set_property(TARGET ${target} PROPERTY "MEDIASDK_TARGET_RESOURCE_DIR_${resource_count}" ${CMAKE_CURRENT_SOURCE_DIR})

        math(EXPR resource_count "${resource_count} + 1")
        set_property(TARGET ${target} PROPERTY MEDIASDK_TARGET_RESOURCE_COUNT ${resource_count})
    else ()
        message(FATAL_ERROR "unexpected type for arg resource")
    endif ()
endfunction()

function(mediasdk_copy_resource_after_build target dest_path)
    foreach (dependency_target IN LISTS ARGN)
        add_dependencies(${target} ${dependency_target})

        get_property(resource_count TARGET ${dependency_target} PROPERTY MEDIASDK_TARGET_RESOURCE_COUNT)
        if ("${resource_count}" STREQUAL "")
            message(WARNING "dependency_target ${dependency_target} do not have property MEDIASDK_TARGET_RESOURCE_COUNT")
            continue()
        endif ()

        math(EXPR max_resource_index "${resource_count} - 1")
        foreach (resource_index RANGE 0 ${max_resource_index})
            get_property(resource TARGET ${dependency_target} PROPERTY "MEDIASDK_TARGET_RESOURCE_${resource_index}")
            get_property(resource_prefix TARGET ${dependency_target} PROPERTY "MEDIASDK_TARGET_RESOURCE_PREFIX_${resource_index}")
            get_property(resource_dir TARGET ${dependency_target} PROPERTY "MEDIASDK_TARGET_RESOURCE_DIR_${resource_index}")
            if ("${resource_prefix}" STREQUAL "")
                set(resource_prefix ".")
            endif ()
            add_custom_command(
                    TARGET ${target}
                    POST_BUILD
                    COMMAND ${CMAKE_COMMAND} -E make_directory "${dest_path}/${resource_prefix}"
                    COMMAND_EXPAND_LISTS
                    COMMENT "make resource directory for ${target} from dependency_target ${dependency_target}"
                    WORKING_DIRECTORY "${resource_dir}"
                    VERBATIM
            )
            add_custom_command(
                    TARGET ${target}
                    POST_BUILD
                    COMMAND ${CMAKE_COMMAND} -E copy_if_different "${resource}" "${dest_path}/${resource_prefix}"
                    COMMAND_EXPAND_LISTS
                    COMMENT "copy resource ${p} for ${target} from dependency_target ${dependency_target}"
                    WORKING_DIRECTORY "${resource_dir}"
                    VERBATIM
            )
        endforeach ()
    endforeach ()
endfunction()

function(mediasdk_copy_target_file_after_build target dest_path)
    foreach (dependency_target IN LISTS ARGN)
        add_dependencies(${target} ${dependency_target})
        add_custom_command(
                TARGET ${target}
                POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy_if_different "$<TARGET_FILE:${dependency_target}>" "${dest_path}"
                COMMAND_EXPAND_LISTS
                COMMENT "copy target file ${target} from dependency_target ${dependency_target}"
                VERBATIM
        )
    endforeach ()
endfunction()

function(mediasdk_export_target target)
    # 1. copy target file to package dir
    get_target_property(target_type ${target} TYPE)
    if (target_type STREQUAL "EXECUTABLE")
        get_target_property(target_output_dir ${target} RUNTIME_OUTPUT_DIRECTORY)
    elseif (target_type STREQUAL "SHARED_LIBRARY")
        get_target_property(target_output_dir ${target} RUNTIME_OUTPUT_DIRECTORY)
    else ()
        message(FATAL_ERROR "unexpected target type: ${target_type}")
    endif ()

    file(RELATIVE_PATH rel_path_from_output_path "${OUTPUT_DIRECTORY}" ${target_output_dir})
    add_custom_command(TARGET ${target} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E make_directory
            "${PACKAGE_DIRECTORY}/${rel_path_from_output_path}/"
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "$<TARGET_FILE:${target}>"
            "${PACKAGE_DIRECTORY}/${rel_path_from_output_path}/$<TARGET_FILE_NAME:${target}>"
            COMMAND_EXPAND_LISTS
            COMMENT "copy target file of ${target} to package dir"
            VERBATIM
    )

endfunction()

function(mediasdk_set_rel_output_dir rel_path)
    set(output_path "${OUTPUT_DIRECTORY}/${rel_path}")
    set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${output_path} PARENT_SCOPE)
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${output_path} PARENT_SCOPE)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${output_path} PARENT_SCOPE)

    if (IS_MULTI_CONFIG)
        foreach (config IN LISTS CMAKE_CONFIGURATION_TYPES)
            string(TOUPPER ${config} upper_config)
            set(config_output_path "${OUTPUT_DIRECTORY}/${config}/${rel_path}")
            set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_${upper_config} ${config_output_path} PARENT_SCOPE)
            set(CMAKE_LIBRARY_OUTPUT_DIRECTORY_${upper_config} ${config_output_path} PARENT_SCOPE)
            set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_${upper_config} ${config_output_path} PARENT_SCOPE)
        endforeach ()
    endif ()
endfunction()
