#pragma once

#include <map>
#include <memory>
#include <optional>
#include <string>

namespace davinci::effectplatform::loki {
class LokiPlatformConfig;
class LokiRequirementsPeeker;
}  // namespace davinci::effectplatform::loki

namespace mediasdk::ep {

std::shared_ptr<davinci::effectplatform::loki::LokiPlatformConfig> CreateConfig(
    const std::string& json_param,
    const std::string& version,
    const std::shared_ptr<
        davinci::effectplatform::loki::LokiRequirementsPeeker>& peeker);

std::optional<std::string> GetUserIdFromJson(const std::string& json_param);

std::optional<std::string> GetTTLSHardwareLevel(const std::string& json_param);

std::string CreateSuccessEvent(const std::string& event_name,
                               const std::string& requirement_id);

std::string CreateFailedEvent(const std::string& event_name,
                              const std::string& requirement_id,
                              const std::string& error);

std::string CreateProgressEvent(const std::string& event_name,
                                const std::string& requirement_id,
                                int progress);

std::string CreateNetEvent(const std::string& event_name,
                           const std::string& url,
                           const std::string& body,
                           int error_code);

}  // namespace mediasdk::ep