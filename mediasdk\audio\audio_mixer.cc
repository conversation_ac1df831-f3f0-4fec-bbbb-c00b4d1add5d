#include "audio_mixer.h"

#include <audio/audio_common.h>
#include <base/time/time.h>
#include <mediasdk/audio/audio_frame_utils.h>
#include <mediasdk/public/mediasdk_trace_event.h>
#include "aligned_audio_frame.h"
#include "audio_mixer_input.h"
#include "audio_track.h"
#include "component_center.h"
#include "hook_api/hook_call.h"
#include "notify_center.h"

namespace mediasdk {

AudioMixer::AudioMixer() {}

AudioMixer::~AudioMixer() = default;

void AudioMixer::AddOutputObserver(
    uint32_t track_id,
    std::shared_ptr<MixedAudioObserver> observer) {
  observer_list_[track_id].AddObserver(observer);

  // Make sure track_id corresponded AudioTrack existed
  audio_input_manager_->CheckAudioTrackExisted(track_id);
}

void AudioMixer::RemoveOutputObserver(
    uint32_t track_id,
    std::shared_ptr<MixedAudioObserver> observer) {
  observer_list_[track_id].RemoveObserver(observer);
}

void AudioMixer::OnMixedAudioFrame(uint32_t track_id, const AudioFrame& frame) {
  observer_list_[track_id].Notify(&MixedAudioObserver::OnMixedAudioFrame,
                                  track_id, frame, kAudioOutputFormat);
}

void AudioMixer::OnPump(const mediasdk::TimeDuration& duration,
                        std::shared_ptr<AudioInputManager> manager) {
  if (!audio_input_manager_) {
    audio_input_manager_ = manager;
  }
  TimeDuration new_duration = duration;
  new_duration.SetBeginTimeStampNS(duration.GetBeginTimeStampNS() - 17000000);
  new_duration.SetEndTimeStampNS(duration.GetEndTimeStampNS() - 17000000);
  auto& tracks = audio_input_manager_->GetTracksRef();
  for (auto& track : tracks) {
    auto& audio_track = track.second;
    if (!audio_track)
      continue;
    bool have_observer = false;
    auto it = observer_list_.find(track.first);
    if (it != observer_list_.end()) {
      it->second.ForEachObserver(
          [&have_observer](const std::shared_ptr<MixedAudioObserver>& output) {
            have_observer = true;
          });
    }
    // audio mix task is running by audio track unit
    audio_track->OnAudioPump(new_duration, have_observer, this);
  }
}

}  // namespace mediasdk