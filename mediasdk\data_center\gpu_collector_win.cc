#include "gpu_collector_win.h"
#include <map>
#include <set>

using namespace gpu_collector;

std::shared_ptr<GpuCollector> GpuCollector::CreateCollector() {
  return std::make_shared<GpuCollectorImpl>();
}

GpuCollectorImpl::~GpuCollectorImpl() = default;

bool GpuCollectorImpl::IsInitialized() const {
  return pdh_wrapper_.IsInitialized();
}

int GpuCollectorImpl::GpuNumber(const int pid) {
  if (!pdh_wrapper_.CollectData(collect_items_)) {
    return 0;
  }
  std::set<GpuDeviceInfo> device_set = {};
  for (const auto& it : collect_items_) {
    if (pid == it.pid) {
      device_set.insert(it.device_info);
    }
  }
  return device_set.size();
}

bool GpuCollectorImpl::ProcessUsage(const uint64_t pid,
                                    std::vector<GpuUsage>& usage) {
  usage.clear();
  if (!pdh_wrapper_.CollectData(collect_items_)) {
    return false;
  }
  std::map<GpuDeviceInfo, GpuUsage> device_map = {};
  for (const auto& it : collect_items_) {
    if (it.pid != pid) {
      continue;
    }
    if (device_map.count(it.device_info) <= 0) {
      device_map[it.device_info] = {};
      device_map[it.device_info].device_info = it.device_info;
    }
    if (it.collect_type == DEDICATE) {
      device_map[it.device_info].dedicate_usage += it.value;
    } else if (it.collect_type == ENGINE_3D && it.engine_index == 0) {
      device_map[it.device_info].usage_3d += it.value;
    } else if (it.collect_type == ENGINE_COPY) {
      device_map[it.device_info].usage_copy += it.value;
    } else if (it.collect_type == ENGINE_CUDA) {
      device_map[it.device_info].usage_cuda += it.value;
    } else if (it.collect_type == ENGINE_ENCODE) {
      device_map[it.device_info].usage_video_encode += it.value;
    } else if (it.collect_type == ENGINE_DECODE) {
      device_map[it.device_info].usage_video_decode += it.value;
    } else if (it.collect_type == ENGINE_PROCESSING) {
      device_map[it.device_info].usage_video_processing += it.value;
    }
  }
  for (const auto& [device_info, device_usage] : device_map) {
    usage.emplace_back(device_usage);
  }
  return true;
}

bool GpuCollectorImpl::SystemUsage(std::vector<GpuUsage>& usage) {
  usage.clear();
  if (!pdh_wrapper_.CollectData(collect_items_)) {
    return false;
  }
  std::map<GpuDeviceInfo, GpuUsage> device_map = {};
  for (const auto& it : collect_items_) {
    if (device_map.count(it.device_info) <= 0) {
      device_map[it.device_info] = {};
      device_map[it.device_info].device_info = it.device_info;
    }
    if (it.collect_type == DEDICATE) {
      device_map[it.device_info].dedicate_usage += it.value;
    } else if (it.collect_type == ENGINE_3D && it.engine_index == 0) {
      device_map[it.device_info].usage_3d += it.value;
    } else if (it.collect_type == ENGINE_COPY) {
      device_map[it.device_info].usage_copy += it.value;
    } else if (it.collect_type == ENGINE_CUDA) {
      device_map[it.device_info].usage_cuda += it.value;
    } else if (it.collect_type == ENGINE_ENCODE) {
      device_map[it.device_info].usage_video_encode += it.value;
    } else if (it.collect_type == ENGINE_DECODE) {
      device_map[it.device_info].usage_video_decode += it.value;
    } else if (it.collect_type == ENGINE_PROCESSING) {
      device_map[it.device_info].usage_video_processing += it.value;
    }
  }
  for (const auto& [device_info, device_usage] : device_map) {
    usage.emplace_back(device_usage);
  }
  return true;
}
