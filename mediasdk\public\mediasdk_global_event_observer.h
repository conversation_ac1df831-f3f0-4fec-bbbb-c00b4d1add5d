#pragma once

#include "mediasdk_export.h"

#include "mediasdk_defines.h"

namespace mediasdk {

class MediaSDKGlobalEventObserver {
 public:
  virtual ~MediaSDKGlobalEventObserver() = default;

  virtual void OnPluginGlobalEvent(PluginInfo info, MediaSDKString event) = 0;

  // In order to monitor thread related events, `OnRenderThreadEvent` are
  // executed in the original thread
  virtual void OnRenderThreadEvent(RenderThreadEvent event_type) = 0;

  virtual void OnDeviceLostEvent(MSDevLostEvent event_type) = 0;

  virtual void OnTeaEvent(MediaSDKString id, MediaSDKString event) = 0;

  virtual void OnParfaitContextEvent(MediaSDKString key,
                                     MediaSDKString value) = 0;

  virtual void OnVqosDataReport(MediaSDKString data) = 0;
};

}  // namespace mediasdk
