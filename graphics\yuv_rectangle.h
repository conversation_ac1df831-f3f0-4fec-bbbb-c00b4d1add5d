#pragma once
#include "device.h"

namespace graphics {
// for yuv convert study and test
class GRAPHICS_EXPORT YUVRectangle {
 public:
  struct GRAPHICS_EXPORT YUVRectangleConfig {
    DirectX::XMFLOAT2 top_left;
    DirectX::XMFLOAT2 bottom_right;
    DirectX::XMFLOAT4 color;
    mediasdk::ColorSpace cs;
    mediasdk::VideoRange vr;
    float rotate = .0f;
    float shear_angle = 0.f;
    DirectX::XMFLOAT2 shear = DirectX::XMFLOAT2{1.f, 1.f};
    bool fill = false;  // fill rectangle
    float thinkness = 1.0f;
  };

 public:
  virtual bool DrawTo(const DirectX::XMFLOAT2& vpSize,
                      const DirectX::XMMATRIX& view,
                      const DirectX::XMMATRIX& projection) = 0;

  virtual void UpdateRectangleConf(
      const YUVRectangleConfig* rectangleConfig) = 0;
  virtual int32_t GetIndexCnt() = 0;
  virtual void Destroy() = 0;

  virtual ~YUVRectangle() {}
};

GRAPHICS_EXPORT std::shared_ptr<YUVRectangle> CreateYUVRectangle(Device& inst);

}  // namespace graphics

