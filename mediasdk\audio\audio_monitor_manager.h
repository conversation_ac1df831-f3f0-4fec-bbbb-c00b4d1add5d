#pragma once

#include <memory>
#include <mutex>
#include <string>

#include "audio/audio_format.h"
#include "audio/audio_frame.h"
#include "audio/audio_monitor.h"
#include "mediasdk/public/mediasdk_global_event_observer.h"

namespace mediasdk {

class AudioMonitorManager : public MediaSDKGlobalEventObserver {
 public:
  explicit AudioMonitorManager(const AudioFormat& output_format);
  ~AudioMonitorManager();

  void SetMonitorType(uint32_t monitor_type);

  uint32_t GetMonitorType() const { return monitor_type_; }

  void SetNeedSync(bool need_sync);

  void ChangeRenderTargetTo(const std::string& id);

  void PlayFrame(const AudioFrame& frame);

 protected:
  // MediaSDKGlobalEventObserver:
  void OnPluginGlobalEvent(PluginInfo info, MediaSDKString event) override;

  void OnRenderThreadEvent(RenderThreadEvent) override {}

  void OnDeviceLostEvent(MSDevLostEvent) override {}

  void OnTeaEvent(MediaSDKString id, MediaSDKString event) override {}

  void OnVqosDataReport(MediaSDKString data) override {}

  void OnParfaitContextEvent(MediaSDKString key,
                             MediaSDKString value) override {}

 private:
  void CreateMonitor();
  void CloseMonitor();

 private:
  AudioFormat output_format_;
  uint32_t monitor_type_ = AudioMonitorType::kAudioMonitorOutput;
  bool need_sync_ = false;
  bool monitor_changed_ = false;
  std::mutex lock_monitor_;
  std::shared_ptr<AudioMonitor> monitor_;
  std::string render_target_audio_dev_id_;
};

}  // namespace mediasdk