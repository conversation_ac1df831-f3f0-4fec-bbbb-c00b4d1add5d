#pragma once

#include <audio/audio_frame.h>
#include <memory>
#include <string>
#ifdef DEBUG_AUDIO_MIX
#define AUDIO_MIX_TRACE_VALUE TRACE_VALUE
#define AUDIO_MIX_TRACE_DURATION TRACE_DURATION
#else
#define AUDIO_MIX_TRACE_VALUE(n, v)
#define AUDIO_MIX_TRACE_DURATION(n, b, d)
#endif
namespace mediasdk {

struct TimeDurationFrame {
  mediasdk::AudioFrame frame;
  mediasdk::TimeDuration duration;
};

class AudioMixerInput {
 public:
  virtual ~AudioMixerInput() = default;

  virtual void OnData(const std::string_view& debug_str,
                      const AudioFrame& data,
                      int64_t syn_offset) = 0;
  virtual bool CheckCanConsumeInputBuffer(
      const std::string_view& debug_str,
      const mediasdk::TimeDuration& target_duration) const = 0;

  virtual TimeDurationFrame* GetAudioFrameDuration(
      const std::string_view& debug_str,
      const mediasdk::TimeDuration& ts) = 0;
  // return: false:audio not ready, caller should wait for a will

  virtual int64_t GetLastUpdateNS() = 0;

  virtual void GetErrorCnt(int32_t& too_late, int32_t& too_early) = 0;
};

std::shared_ptr<AudioMixerInput> CreateAudioMixerInput();

}  // namespace mediasdk
