#pragma once

#include <memory>

#include <audio/audio_format.h>
#include <audio/audio_frame.h>

namespace mediasdk {

class MixedAudioObserver {
 public:
  virtual ~MixedAudioObserver() = default;

  virtual void OnMixedAudioFrame(const uint32_t track_id,
                                 const AudioFrame& frame,
                                 const AudioFormat& format) = 0;
};

}  // namespace mediasdk
