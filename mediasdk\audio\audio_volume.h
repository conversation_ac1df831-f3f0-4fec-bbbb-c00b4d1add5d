#pragma once

#include <audio/audio_frame.h>

#include <array>

namespace mediasdk {

class AudioVolume {
 public:
  using ResultType = std::array<float, 8>;
  AudioVolume();
  ~AudioVolume();
  const ResultType& GetPeak();
  const ResultType& GetRMS();
  void SetCalcDelta(int32_t interval);
  int32_t GetCalcDelta() const;
  bool RMS(const AudioFrame& apk);
  bool RMS(const AudioFormat& format, const AudioFrame& apk);
  bool Peak(const AudioFrame& apk);
  void PeakForce(const AudioFrame& apk);
  void RMSForce(const mediasdk::AudioFrame& frame);
  void RMSForce(const AudioFormat& format, const mediasdk::AudioFrame& frame);

 private:
  bool CheckAudioFrame(const mediasdk::AudioFrame& frame);
 private:
  int32_t delta_ms_ = 0;
  int64_t last_ = 0;
  ResultType RMS_ = {};
  ResultType peak_ = {};
};

}  // namespace mediasdk
