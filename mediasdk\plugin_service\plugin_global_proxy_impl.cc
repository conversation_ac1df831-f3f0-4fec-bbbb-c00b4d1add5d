#include "plugin_global_proxy_impl.h"
#include "component_center.h"
#include "mediasdk/data_center/data_center.h"
#include "mediasdk/notify_center.h"

namespace mediasdk {

MSAdapterInfo PluginGlobalProxyImpl::GetCurrentAdapterInfo() {
  MSAdapterInfo adapter_info = {};
  if (auto dc = com::GetDataCenter(); dc) {
    auto current_adapter_info = com::GetDataCenter()->GetCurrentAdapterInfo();
    adapter_info.adapter_id = current_adapter_info.adapter_id;
    adapter_info.device_id = current_adapter_info.device_id;
    adapter_info.vendor_id = current_adapter_info.vendor_id;
    wcscpy_s(adapter_info.adapter_name,
             sizeof(adapter_info.adapter_name) / sizeof(wchar_t) - 1,
             current_adapter_info.adapter_name);
  }
  return adapter_info;
}

int32_t PluginGlobalProxyImpl::GetAllAdapterInfoCnt() {
  if (auto dc = com::GetDataCenter(); dc) {
    return dc->GetAdapters().size();
  }
  return 0;
}

MSAdapterInfo PluginGlobalProxyImpl::GetAdapterInfo(int32_t index) {
  if (auto dc = com::GetDataCenter(); dc) {
    auto adapters = dc->GetAdapters();
    if (adapters.size() > index) {
      MSAdapterInfo adapter_info = {};
      graphics::GpuAdapterInfo current_adapter_info = adapters[index];
      adapter_info.adapter_id.low = current_adapter_info.luid_low_;
      adapter_info.adapter_id.high = current_adapter_info.luid_high_;

      adapter_info.device_id = current_adapter_info.device_id_;
      adapter_info.vendor_id = current_adapter_info.vendor_id_;
      wcscpy_s(adapter_info.adapter_name,
               sizeof(adapter_info.adapter_name) / sizeof(wchar_t) - 1,
               current_adapter_info.driver_name_.c_str());
      return adapter_info;
    }
    return {};
  }
  return {};
}

void PluginGlobalProxyImpl::NotifyGlobalEvent(PluginInfo info,
                                              MediaSDKString notify_event) {
  // Prevent receiving callbacks before initialization is complete
  if (!ComponentCenter::IsInitialized()) {
    LOG(INFO) << "PluginGlobalEvent before Initialized: "
              << info.name.ToString();
    return;
  }

  auto nc = com::GetNotifyCenter();
  if (nc) {
    nc->GlobalEvent()->Notify(FROM_HERE,
                              &MediaSDKGlobalEventObserver::OnPluginGlobalEvent,
                              info, notify_event);
  }
}

}  // namespace mediasdk
