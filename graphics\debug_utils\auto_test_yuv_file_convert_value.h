#pragma once

#include <vector>

#include <Texture.h>
#include <base/check.h>
#include <graphics/debug_utils/video_frame_dump.h>
#include "auto_test_texture_value.h"
#include "media/video/yuv_to_bgra_by_cpu.h"

struct CheckYUVConvert {
  std::wstring path_;
  check_list check_list_;

  bool check(graphics::Device& device) {
    auto texture = graphics::GetTextureFromFile(device, path_);
    DCHECK(texture);
    BGRATextureFrame texture_frame =
        BGRATextureFrame::GetTextureFrameFromDXTexture(*texture);
    return check_values(texture_frame, check_list_) &&
           check_with_cpu(texture_frame);
  }

  bool check_with_cpu(BGRATextureFrame& gpu) {
    auto yuv_data = VideoFrame::GetVideoFrameFromFile(path_);
    if (IsRGBFormat(yuv_data->GetFormat()))
      return true;
    VideoFrame bgra;
    bgra.SetFormat(PIXEL_FORMAT::PIXEL_FORMAT_BGRA);
    bgra.ResizeBuffer(yuv_data->GetWidth(), yuv_data->GetHeight());
    YUVToBGRAConvert(*yuv_data, bgra);
    BGRATextureFrame bgra_from_cpu;
    bgra_from_cpu.ReverseData(yuv_data->GetWidth(), yuv_data->GetHeight());
    memcpy(bgra_from_cpu.data(), bgra.GetPlaneData(0),
           yuv_data->GetWidth() * yuv_data->GetHeight() * 4);

    return check_values(bgra_from_cpu, check_list_);
  }
};

using CheckYUVConverts = std::vector<CheckYUVConvert>;

inline bool DoCheckYUVConverts(graphics::Device& device,
                               CheckYUVConverts converts) {
  for (auto& convert : converts) {
    if (!convert.check(device)) {
      return false;
    }
  }
  return true;
}
