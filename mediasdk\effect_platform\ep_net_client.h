#pragma once

#include <LokiPlatform.h>
#include <PlatformHttpClientDelegate.h>
#include <base/logging.h>
#include <base/win/windows_types.h>
#include "ep_helper.h"
#include "ms_curl.h"

namespace mediasdk::ep {

struct NetClientDelegate {
  virtual ~NetClientDelegate() = default;
  virtual const std::map<std::string, std::string>& GetUrlParameter() = 0;
};

struct NetClient : public davinci::effectplatform::PlatformHttpClientDelegate {
  NetClient(NetClientDelegate* delegate) : delegate_(delegate) {}

  bool request(const std::string& raw,
               davinci::effectplatform::DAVNetItem* item,
               void* pHttpClient,
               davinci::effectplatform::HttpClientCallback callback) override {
    auto requestId = item->requestId;
    MSURL curl;
    if (!curl.Open()) {
      return false;
    }

    auto url = raw;
    if (!delegate_) {
      return false;
    }
    AddParamsToURL(url, delegate_->GetUrlParameter());
    LOG(INFO) << "NetClient request url is :" << url.c_str();
    auto content = std::string(item->body, item->bodyLength);

    curl.SetContentType(item->contentType);
    curl.SetHeaders(item->headerMap);
    curl.SetRedirect(true);

    bool ret = false;
    if (item->httpType == davinci::effectplatform::HTTP_TYPE::HTTP_GET) {
      ret = curl.Get(url, content);
    } else {
      ret = curl.Post(url, content);
    }

    davinci::effectplatform::MsgExtParam msgExtParam;
    msgExtParam.eHttpType = item->httpType;
    msgExtParam.uiReqId = item->requestId;

    auto rsp = curl.GetResponse();
    auto errorCode = -1 * curl.GetLastError();

    if (ret) {
      callback(pHttpClient,
               davinci::effectplatform::HttpClientCallbackAction::SUCCESS,
               rsp.data(), rsp.size(), msgExtParam);
    } else {
      callback(pHttpClient,
               davinci::effectplatform::HttpClientCallbackAction::FAIL,
               rsp.data(), rsp.size(), msgExtParam);
    }

    return true;
  }

 protected:
  // davinci::effectplatform::PlatformHttpClientDelegate:
  int64_t getContentLength(int64_t reqid) override { return 0; }

 private:
  void AddParamsToURL(std::string& url,
                      const std::map<std::string, std::string>& params) {
    if (params.empty()) {
      return;
    }

    size_t pos = url.find('?');
    bool firstParam = (pos == std::string::npos);
    if (firstParam) {
      url += '?';
    } else {
      url += '&';
    }

    for (auto it = params.begin(); it != params.end(); ++it) {
      if (it != params.begin() && !firstParam) {
        url += '&';
      }
      firstParam = false;
      url += it->first + "=" + it->second;
    }
    return;
  }

 private:
  NetClientDelegate* delegate_;
};

}  // namespace mediasdk::ep
