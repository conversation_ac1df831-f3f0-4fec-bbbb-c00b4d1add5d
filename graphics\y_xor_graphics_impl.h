#pragma once
#include "device.h"
#include "graphics.h"
#include "y_xor_graphics.h"
#include "y_xor_shader.h"

namespace graphics {

class YXORGraphicsImpl : public YXORGraphics {
 public:
  YXORGraphicsImpl(Device& ins);

 public:
  bool XOR(std::shared_ptr<Texture> left,
           std::shared_ptr<Texture> right) override;
  std::shared_ptr<Texture> GetOutputTexture() override;
  ~YXORGraphicsImpl() override;

 private:
  void Destroy();
  bool DoXOR(Texture& left, Texture& right);
  bool TryCreateResource(const Texture& texture);
  bool TryUpdateParam(const Texture& texutre);
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice();
  Microsoft::WRL::ComPtr<ID3D11DeviceContext> GetContext();

 private:
  std::shared_ptr<Graphics> graphics_;
  Device& device_;
};

}  // namespace graphics
