#pragma once

#include <cstdint>

#include "audio_input_proxy.h"
#include "graphics/texture.h"
#include "graphics/transform.h"
#include "mediasdk/public/mediasdk_string.hpp"
#include "visual_frame_queue_converter.h"

namespace mediasdk {

#pragma pack(push, 1)

constexpr uint32_t kVisualSourceMaxVideoPlane = kMaxVideoPlanes;

struct VisualSourceFrame {
  uint8_t* data[kVisualSourceMaxVideoPlane];
  uint32_t line_size[kVisualSourceMaxVideoPlane];
  uint32_t width;
  uint32_t height;
  PixelFormat format;
  ColorSpace color_space;
  VideoRange video_range;
  uint64_t timestamp;
};

enum VisualSourceEventType {
  kVisualSourceCommonEvent = 1024,
  kVisualSourceMaxDelayCountEvent = 1025,
  kVisualSourceOperationErrorEvent = 1026,
};

struct VideoSourceEvent {
  const char* source_name;
  int event_type;
  int extra_code;
  MediaSDKString extra_msg;
};

enum VisualFrameQueueConverterType {
  kVisualFrameQueueConverterTypeYuv = 0,
  kVisualFrameQueueConverterTypeCustom
};

#pragma pack(pop)

class VisualProxy : public AudioFrameHandler {
 public:
  ~VisualProxy() override = default;

  virtual void InitVisualFrameQueue(VisualFrameQueueConverterType type,
                                    uint32_t max_count) = 0;

  virtual void UinitVisualFrameQueue() = 0;

  // Enqueue video data, which can later be retrieved as a GPU texture during
  // the prepare phase by calling `GetLatestTextureFromQueue`. For the source,
  // once this method is called, it will enter the "queued data" state. In this
  // state, if there is no new data in the queue, `Prepare` will be determined
  // as false, and subsequent `Convert` and filter-related processing will not
  // be performed. The last enqueued image will be displayed. If you want the
  // next call to `VisualSource::GetTexture()` to be treated as a new frame, you
  // need to call `CleanVideoFrame`.
  virtual void EnqueueVideoFrame(const VisualSourceFrame& frame) = 0;

  virtual void CleanVideoFrame() = 0;

  virtual void SetVideoFrameQueueMaxCount(int max_count) = 0;

  virtual void SignalEvent(const char* event) = 0;

  virtual graphics::Texture* GetLatestTextureFromQueue() const = 0;

  virtual int64_t GetLatestTextureTimestampNSFromQueue() const = 0;

  virtual bool IsLastFrameConvertSuccess() const = 0;

  virtual graphics::Device* GetDevice() const = 0;

  virtual MSSize GetPreviewWindowSize() const = 0;

  virtual MediaSDKString GetABConfig(const MediaSDKString& key) const = 0;

  virtual void ReopenVisual(const char* json_params) = 0;

  virtual void ReportSourceEvent(const VideoSourceEvent& event) = 0;

  virtual int GetRenderFPS() = 0;

  virtual int GetConfiguredMaxFrameQueueCount(const char* section_name) = 0;
};

}  // namespace mediasdk
