#include "audio_input_performance_calc.h"
#include <time_helper.h>

namespace {
constexpr int kAudioInterval = 50;
}

namespace mediasdk {

AudioInputPerformanceCalc::AudioInputPerformanceCalc() = default;

AudioInputPerformanceCalc::~AudioInputPerformanceCalc() = default;

void AudioInputPerformanceCalc::OnAudioPacket() {
  if (audio_times_ < kAudioInterval) {
    audio_times_++;
    need_update_ = false;
  } else {
    audio_times_ = 0;
    need_update_ = true;
  }
  if (need_update_) {
    audio_ts_ = milli_now();
  }
}

void AudioInputPerformanceCalc::OnResampleFinished() {
  if (need_update_) {
    auto diff = milli_now() - audio_ts_;
    resample_cost_ = resample_cost_ < diff ? diff : resample_cost_;
  }
}

void AudioInputPerformanceCalc::SetSyncOffset(int offset) {
  offset_ = offset;
}

void AudioInputPerformanceCalc::GetPerformance(AudioPerformance& performance) {
  performance.cost = resample_cost_;
  performance.offset = offset_;
  performance.reset_times = reset_times_;
}

}  // namespace mediasdk