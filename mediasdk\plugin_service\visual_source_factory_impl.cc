#include "visual_source_factory_impl.h"

#include <base/logging.h>
#include <mediasdk/mediasdk_thread.h>
#include <mediasdk/public/plugin/visual_source.h>

namespace mediasdk {

// static:
std::shared_ptr<VisualSourceFactory> VisualSourceFactoryImpl::Create(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info) {
  auto factory = std::make_shared<VisualSourceFactoryImpl>(library, info);
  if (factory && factory->Load()) {
    return factory;
  }

  return nullptr;
}

VisualSourceFactoryImpl::VisualSourceFactoryImpl(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info)
    : VisualSourceFactory(library, info) {
  DCHECK(library_);
  DCHECK(info_);
}

mediasdk::MediaSDKString VisualSourceFactoryImpl::EnumVideoInput(
    const std::string& json) {
  if (enum_input_func_) {
    return enum_input_func_(json.c_str());
  }
  return {};
}

MediaSDKString VisualSourceFactoryImpl::EnumFormat(const std::string& device_id,
                                                   int32_t type) {
  if (enum_format_func_) {
    return enum_format_func_(device_id.c_str(), type);
  }
  return {};
}

MediaSDKString VisualSourceFactoryImpl::GetVisualSourceProperty(
    const std::string& json) {
  if (get_visual_source_property_func_) {
    return get_visual_source_property_func_(json.c_str());
  }
  return {};
}

bool VisualSourceFactoryImpl::SetVisualSourceProperty(const std::string& key,
                                                      const std::string& json) {
  if (set_visual_source_property_func_) {
    return set_visual_source_property_func_(key.c_str(), json.c_str());
  }
  return false;
}

VisualSourceFactoryImpl::~VisualSourceFactoryImpl() {
  DCHECK(sources_.empty());
}

std::function<void(VisualSource*)> VisualSourceFactoryImpl::DestroyWithCheck(
    DestroyVisualSourceFunc func) {
  return [func](VisualSource* input) {
    DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);
    return func(input);
  };
}

std::shared_ptr<VisualSource> VisualSourceFactoryImpl::CreateSource(
    VisualProxy* proxy,
    const std::string& json_params) {
  if (create_func_) {
    auto ret = create_func_(proxy, json_params.c_str());
    if (ret) {
      std::shared_ptr<VisualSource> source(ret,
                                           DestroyWithCheck(destroy_func_));
      sources_.insert(source);
      return source;
    }
  }
  return nullptr;
}

void VisualSourceFactoryImpl::Destroy(std::shared_ptr<VisualSource> source) {
  sources_.erase(source);
}

void VisualSourceFactoryImpl::DestroyAll() {
  sources_.clear();
}

bool VisualSourceFactoryImpl::Load() {
  create_func_ = reinterpret_cast<CreateVisualSourceFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "CreateVisualSource"));

  if (!create_func_) {
    LOG(ERROR) << info_->name.data()
               << ": Failed to get function pointer for CreateVisualSource";
    return false;
  }

  destroy_func_ = reinterpret_cast<DestroyVisualSourceFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "DestroyVisualSource"));

  if (!destroy_func_) {
    LOG(ERROR) << info_->name.data()
               << ": Failed to get function pointer for DestroyVisualSource";
    return false;
  }

  enum_input_func_ = reinterpret_cast<EnumVideoInputFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_, "EnumVideoInput"));

  if (!enum_input_func_) {
    LOG(INFO) << info_->name.data() << ": Has no interface for EnumVideoInput";
    // optional interface
  }

  enum_format_func_ = reinterpret_cast<EnumFormatFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_, "EnumFormat"));

  if (!enum_format_func_) {
    LOG(INFO) << info_->name.data() << ": Has no interface for EnumFormat";
    // optional interface
  }

  get_visual_source_property_func_ =
      reinterpret_cast<GetVisualPluginPropertyFunc>(
          base::GetFunctionPointerFromNativeLibrary(library_,
                                                    "GetVisualPluginProperty"));

  if (!get_visual_source_property_func_) {
    LOG(INFO) << info_->name.data()
              << ": Has no interface for GetVisualPluginProperty";
    // optional interface
  }

  set_visual_source_property_func_ =
      reinterpret_cast<SetVisualPluginPropertyFunc>(
          base::GetFunctionPointerFromNativeLibrary(library_,
                                                    "SetVisualPluginProperty"));

  if (!set_visual_source_property_func_) {
    LOG(INFO) << info_->name.data()
              << ": Has no interface for SetVisualPluginProperty";
    // optional interface
  }

  return true;
}

}  // namespace mediasdk
