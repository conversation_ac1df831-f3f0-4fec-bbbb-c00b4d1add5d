#pragma once

#include <mutex>
#include "shader.h"

namespace graphics {

class BGRACalcShader : public Shader {
 public:
  static inline const char* SHADER_ID_STRING = "bgra_calc_shader";

  static std::shared_ptr<Shader> CreateBGRACalcShader(const char* type) {
    if (strcmp(type, SHADER_ID_STRING) == 0) {
      return std::make_shared<BGRACalcShader>();
    }
    return nullptr;
  }

  static void EnumPluginsFunction(void* param, EnumPluginCallBack fpn) {
    fpn(param,
        ShaderItem{SHADER_ID_STRING, "shader for draw texture to graphics",
                   BGRACalcShader::CreateBGRACalcShader});
  }

 public:
  __declspec(align(16)) struct PS_BUFFER {
    DirectX::XMFLOAT2 texture_move;
    DirectX::XMFLOAT2 mask_move;
  };

  bool Init(const std::shared_ptr<Device>&) override;
  void Render(Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_buffer,
              ID3D11ShaderResourceView* texture,
              ID3D11ShaderResourceView* mask);
  ~BGRACalcShader() override;
  void Destroy() override;

 private:
  bool DoInit();

 protected:
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice();
  Microsoft::WRL::ComPtr<ID3D11DeviceContext> GetContext();
  bool try_init_ = false;
  bool init_suc_ = false;
  std::shared_ptr<Device> instance_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_;
  Microsoft::WRL::ComPtr<ID3D11VertexShader> vs_;
};
}  // namespace graphics
