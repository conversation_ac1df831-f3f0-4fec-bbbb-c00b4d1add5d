#include "video_encode_pts_generator.h"

#include <base/logging.h>
#include <base/time/time.h>
#include "component_center.h"
#include "data_center/data_center.h"
#include "data_center/vqos_data.h"
#include "time_helper.h"

namespace {
constexpr double drop_timestamp_threshold = 0.8;
}

namespace mediasdk {
VideoEncodePtsGenerator::VideoEncodePtsGenerator(const std::string codec_name,
                                                 const int64_t timebase)
    : timebase_(timebase), codec_name_(codec_name) {
  if (auto dc = com::GetDataCenter(); dc) {
    use_new_version_pushing_timestamp_ = dc->IsUseNewVersionPushingTimeStamp();
  }
}

int64_t VideoEncodePtsGenerator::Generate(
    const int64_t capture_begin_timestamp_ns,
    const int64_t capture_current_timestamp_ns) {
  int64_t new_pts_index =
      (capture_current_timestamp_ns - capture_begin_timestamp_ns) /
      (base::Time::kNanosecondsPerSecond / timebase_);
  std::lock_guard<std::mutex> lock(lock_);

  if (use_new_version_pushing_timestamp_) {
    if (new_pts_index == last_pts_index_) {
      int64_t timestamp_diff_ns =
          capture_current_timestamp_ns - last_capture_timestamp_ns_;
      int64_t pts_timestamp_diff_ns =
          base::Time::kNanosecondsPerSecond / timebase_;
      if (timestamp_diff_ns <=
          (int64_t)(drop_timestamp_threshold * pts_timestamp_diff_ns)) {
        LOG(WARNING) << " same pts, "
                     << "invalid [" << new_pts_index << "] ts ["
                     << capture_current_timestamp_ns << "]";
        return MEDIASDK_INVALID_DTS;
      }
      new_pts_index++;
    }
  } else if (new_pts_index == last_pts_index_) {
    LOG(WARNING) << " same pts, "
                 << "invalid [" << new_pts_index << "] ts ["
                 << capture_current_timestamp_ns << "]";
    return MEDIASDK_INVALID_DTS;
  }

  input_pts_record_.push_back(new_pts_index);
  last_pts_index_ = new_pts_index;
  if (use_new_version_pushing_timestamp_) {
    last_capture_timestamp_ns_ =
        capture_begin_timestamp_ns +
        new_pts_index * (base::Time::kNanosecondsPerSecond / timebase_);
  } else {
    last_capture_timestamp_ns_ = capture_current_timestamp_ns;
  }

  if (NeedReoprtFreezing()) {
    ReportFreezing();
  }

  return new_pts_index;
}

void VideoEncodePtsGenerator::UpdateTimeBase(const int64_t timebase) {
  timebase_ = timebase;
}

void VideoEncodePtsGenerator::PopBack() {
  std::lock_guard<std::mutex> lock(lock_);
  DCHECK(!input_pts_record_.empty());
  if (input_pts_record_.empty()) {
    LOG(ERROR) << "input_pts_record_ is empty";
  } else {
    input_pts_record_.pop_back();
  }
}

int64_t VideoEncodePtsGenerator::PopFront() {
  std::lock_guard<std::mutex> lock(lock_);
  DCHECK(!input_pts_record_.empty());
  if (input_pts_record_.empty()) {
    LOG(ERROR) << "input_pts_record_ is empty";
    return MEDIASDK_INVALID_DTS;
  }
  const int64_t pts = input_pts_record_.front();
  input_pts_record_.erase(input_pts_record_.begin());

  return pts;
}

bool VideoEncodePtsGenerator::NeedReoprtFreezing() {
  constexpr int report_threshold = 150;
  constexpr int64_t report_time_threshold = 5000;
  if (input_pts_record_.size() < report_threshold) {
    return false;
  }
  auto now = milli_now();
  if (now - last_report_time_ > report_time_threshold) {
    last_report_time_ = now;
    return true;
  }
  return false;
}

void VideoEncodePtsGenerator::ReportFreezing() {
  int queue_size = input_pts_record_.size();
  VqosData data;
  data.ReportFatalError(
      {static_cast<int>(mediasdk::FATAL_ERROR_TYPE::kPtsOverFlow), codec_name_,
       queue_size, 0});
}

}  // namespace mediasdk