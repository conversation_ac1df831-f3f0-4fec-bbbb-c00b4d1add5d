#pragma once

#include "hook_api/custom_audio_input_delegate.h"
#include "mediasdk_callback_defines.h"
#include "mediasdk_defines_audio.h"
#include "mediasdk_export.h"

#ifdef __cplusplus
extern "C" {
#endif  // __cplusplus

namespace mediasdk {

// Enum SDK current audio's plug ins can support input source/device
// Asynchronous callback result need cast to mediasdk::PluginInfoArray
MEDIASDK_EXPORT void MS_API EnumAudioInputSource(Closure closure);

// Get the default capture audio for the plugin named plugin_name
// Asynchronous callback result need cast to mediasdk::MediaSDKString is josn
MEDIASDK_EXPORT void MS_API GetDefaultCaptureAudio(const char* plugin_name,
                                                   Closure closure);

// Get the default render audio for the plugin named plugin_name
// Asynchronous callback result need cast to mediasdk::MediaSDKString is josn
MEDIASDK_EXPORT void MS_API GetDefaultRenderAudio(const char* plugin_name,
                                                  Closure closure);

// Get capture audios for the plugin named plugin_name
// Asynchronous callback result need cast to mediasdk::MediaSDKString is josn
MEDIASDK_EXPORT void MS_API EnumCaptureAudio(const char* plugin_name,
                                             Closure closure);

// Get render audios for the plugin named plugin_name
// Asynchronous callback result need cast to mediasdk::MediaSDKString is josn
MEDIASDK_EXPORT void MS_API EnumRenderAudio(const char* plugin_name,
                                            Closure closure);

// Set AudioParames
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API SetAudioInputParams(const char* audio_input_id,
                                                AudioInputParams param,
                                                Closure closure);

// Set the volume to audio_input_id
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API SetAudioVolume(const char* audio_input_id,
                                           float volume,
                                           Closure closure);

// Get the volume by audio_input_id
// Asynchronous callback result need cast to ResultBoolFloat
MEDIASDK_EXPORT void MS_API GetAudioVolume(const char* audio_input_id,
                                           Closure closure);

// Set the interval to audio_input_id
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API SetAudioInterval(const char* audio_input_id,
                                             int32_t interval,
                                             Closure closure);

// Set the sync offset to audio_input_id by sync_offset
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API SetAudioSyncOffset(const char* audio_input_id,
                                               int32_t sync_offset,
                                               Closure closure);

// Get the sync offset by audio_input_id
// Asynchronous callback result need cast to ResultBoolInt32
MEDIASDK_EXPORT void MS_API GetAudioSyncOffset(const char* audio_input_id,
                                               Closure closure);

// Set the monitor type to audio_input_id
// monitor_type(mediasdk::AudioMonitorType) Asynchronous callback result need
// cast to bool
MEDIASDK_EXPORT void MS_API SetAudioMonitorType(const char* audio_input_id,
                                                int32_t monitor_type,
                                                Closure closure);

// Get the monitor type by audio_input_id
// Asynchronous callback result need cast to ResultBoolUint32
MEDIASDK_EXPORT void MS_API GetAudioMonitorType(const char* audio_input_id,
                                                Closure closure);

// Set the audio balancing type to audio_input_id by balance
// monitor_type(mediasdk::AudioMonitorType) Asynchronous callback result need
// cast to bool
MEDIASDK_EXPORT void MS_API SetAudioBalancing(const char* audio_input_id,
                                              float balance,
                                              Closure closure);

// Set the audio render device id of concurrent audio input
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API
SetAudioInputRenderDeviceID(const char* audio_input_id,
                            const char* render_device_id,
                            Closure closure);

// Get the audio balancing type by audio_input_id
// Asynchronous callback result need cast to ResultBoolFloat
MEDIASDK_EXPORT void MS_API GetAudioBalancing(const char* audio_input_id,
                                              Closure closure);

// Test if audio_input_id is down mix mono
// Asynchronous callback result need cast to ResultBoolBool
MEDIASDK_EXPORT void MS_API IsAudioDownmixMono(const char* audio_input_id,
                                               Closure closure);

// Set the audio to down mix mono by audio_input_id
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API SetAudioDownmixMono(const char* audio_input_id,
                                                bool mono,
                                                Closure closure);

// Set the audio to mute by audio_input_id
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API SetAudioMute(const char* audio_input_id,
                                         bool mute,
                                         Closure closure);

// Test if audio_input_id is mute
// Asynchronous callback result need cast to ResultBoolBool
MEDIASDK_EXPORT void MS_API IsAudioMute(const char* audio_input_id,
                                        Closure closure);

// Set the audio to mute by audio_input_id
// This func is related to system device
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API SetAudioDeviceMute(const char* audio_input_id,
                                               bool mute,
                                               Closure closure);

// Test if audio_input_id is mute
// This func is related to system device
// Asynchronous callback result need cast to ResultBoolBool
MEDIASDK_EXPORT void MS_API IsAudioDeviceMute(const char* audio_input_id,
                                              Closure closure);

// Enum audio input devices by plugin_name
// Asynchronous callback result need cast to mediasdk::MediaSDKString value used
// utf8
MEDIASDK_EXPORT void MS_API EnumAudioInput(const char* plugin_name,
                                           Closure closure);

// Create audio input identifier with audio_input_id in
// More information refs to CreateAudioParams
// Asynchronous callback result need cast to bool
// If creating an RTC Audio Input, this interface needs to be called after the
// RTC engine is created.
MEDIASDK_EXPORT void MS_API CreateAudioInput(const char* audio_input_id,
                                             const CreateAudioParams& params,
                                             Closure closure);

// Create lyrax audio input identifier with audio_input_id in
// More information refs to CreateAudioParams
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API
CreateLyraxAudioInput(const char* audio_input_id,
                      const CreateAudioParams& params,
                      Closure closure);

// Create lyrax audio input identifier with audio_input_id in
// More information refs to CreateAudioParams
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API
CreateCustomAudioInput(const char* audio_input_id,
                       uint32_t track_id,
                       hook_api::CustomAudioInputDelegate* delegate,
                       Closure closure);

// Destroy audio input identifier with audio_input_id
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API DestroyAudioInput(const char* audio_input_id,
                                              Closure closure);

// Update PCMAudioSource's data
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API UpdatePCMAudioDatas(const char* audio_input_id,
                                                const char* pcm_audio_datas,
                                                Closure closure);

// Remove one audio input from track(track_id)
// If the audio input only exists on the track, it will be destroyed
// Asynchronous callback result need cast to ResultBoolBool
MEDIASDK_EXPORT void MS_API
RemoveAudioInputFromTrack(const char* audio_input_id,
                          uint32_t track_id,
                          Closure closure);

// Add one audio input into track(track_id)
// Asynchronous callback result need cast to ResultBoolBool
MEDIASDK_EXPORT void MS_API AddAudioInputToTrack(const char* audio_input_id,
                                                 uint32_t track_id,
                                                 Closure closure);

// Get AudioInput Performance
// This interface should not be called after calling unintialize, as there is a
// risk of crash. result is MSAudioPerformance, and return true if the
// performance is valid
MEDIASDK_EXPORT bool MS_API
GetAudioInputPerformance(const char* audio_input_id,
                         MSAudioPerformance& performance);

// Enum AudioInput in track_id
// Asynchronous callback result need cast to mediasdk::MediaSDKString
MEDIASDK_EXPORT void MS_API EnumAudioInputsInTrack(uint32_t track_id,
                                                   Closure closure);

// device_id null or empty for default render device
// device_id not empty for target render device
MEDIASDK_EXPORT void MS_API SetGlobalRenderDeviceID(const char* device_id);

MEDIASDK_EXPORT void MS_API
SetAudioInputReferenceId(const char* audio_input_id,
                         const char* ref_audio_input_id,
                         Closure closure);

MEDIASDK_EXPORT void MS_API SetAudioInputAECOption(const char* audio_input_id,
                                                   const bool enable,
                                                   Closure closure);

MEDIASDK_EXPORT void MS_API SetAudioInputANSOption(const char* audio_input_id,
                                                   const int level,
                                                   Closure closure);

MEDIASDK_EXPORT void MS_API
SetAudioInputRawDataOption(const char* audio_input_id,
                           const int mode,
                           Closure closure);

MEDIASDK_EXPORT void MS_API
EnableAudioInputEchoDetection(const char* audio_input_id,
                              const int interval,
                              Closure closure);

// Get info of all AudioInput as json string
// Asynchronous callback result need cast to mediasdk::MediaSDKString
// {"info" : [ {"id" : ""}, {"id" : ""}, {"id" : ""} ]}
MEDIASDK_EXPORT void MS_API GetAudioInputListInfo(Closure closure);

MEDIASDK_EXPORT void MS_API SetAudioTrackDelayMs(uint32_t track_id,
                                                 const int64_t ms);

}  // namespace mediasdk

#ifdef __cplusplus
}
#endif  // __cplusplus
