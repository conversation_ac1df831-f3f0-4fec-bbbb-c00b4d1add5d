#pragma once

#include "mediasdk/public/mediasdk_defines.h"
#include "mediasdk/public/plugin/plugin_global_proxy.h"
#include "plugin_export.h"

#pragma pack(push, 1)

namespace mediasdk {

enum PluginErrorCode {
  kPluginSuccess,
  kPluginFailed,
};

// The interface must implement to declare the basic information of the plugin
extern "C" PLUGIN_EXPORT const PluginInfo* GetPluginInfo();

// Optional
// If the plugin implements this function and returns true, it means that the
// plugin needs an asynchronous initialization process. PluginGlobalInit will be
// executed by a separate thread launched within MediaSDK. If this function is
// not implemented, it has the same meaning as the function returning false
extern "C" PLUGIN_EXPORT bool PluginNeedAsyncGlobalInit(
    PluginGlobalProxy* global_proxy);

// Optional
// The global initialization interface of the plugin. The MediaSDK is called
// after `GetPluginInfo`
// Give the plugin a chance to determine if its functionality is available in
// the current environment. If result is not `PluginErrorCode::kPluginSuccess`,
// it will not be added to the plugin list of MediaSDK. If it is not implemented
// by default, it indicates that the functionality is available
//
// `PluginGlobalProxy` is used for plugin to access the capabilities or
// resources of MediaSDK
extern "C" PLUGIN_EXPORT bool PluginGlobalInit(PluginGlobalProxy* global_proxy);

// Optional
// The global Uninitialize interface of the plugin and is called before
// unloading the plugin dynamic library
extern "C" PLUGIN_EXPORT void PluginGlobalUnInit();

}  // namespace mediasdk

#pragma pack(pop)
