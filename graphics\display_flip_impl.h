#pragma once
#include <dxgi1_2.h>
#include "display.h"
#include "mediasdk/utils/time_helper.h"

namespace graphics {

class DisplayImplFlipMode : public Display {
 public:
  static std::shared_ptr<DisplayImplFlipMode> CreateDisplay2D(Device& ins,
                                                              HWND hWnd,
                                                              int cx,
                                                              int cy);

  DisplayImplFlipMode(Device& ins);

  bool IsReady(uint32_t wait_time = 0) override;

  bool Present() override;

  Graphics& GetGraphics() override;
  void Destroy() override;
  bool Resize(uint32_t cx, uint32_t cy) override;
  virtual ~DisplayImplFlipMode() override;

 private:
  bool OpenWithHWND(HWND hWnd);
  bool CreateTexture();

 protected:
  std::shared_ptr<Graphics> graphics_;
  HWND hwnd_ = NULL;
  DXGI_SWAP_CHAIN_DESC swap_chain_desc_ = {};
  HANDLE waitable_event_ = NULL;
  base::TimeDelta last_present_ts_ = mediasdk::GetCurrentTimeDelta();

  Microsoft::WRL::ComPtr<IDXGISwapChain> swap_chain_;
  Device& device_;
  int32_t not_ready_cnt_ = 0;
};

}  // namespace graphics
