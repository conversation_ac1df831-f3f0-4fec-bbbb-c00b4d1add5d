import requests
import base64

url = 'https://code.byted.org/api/v4/projects/37106/trigger/pipeline'
params = {}
params['code_br'] = 'ydh_v2.0.5_gpufilter'                          ## 自动发包仓库分支【必须】
params['version'] = '9.0.0.11233'                                   ## 发版目标版本号【必须】
params['build'] = 'Build'                                           ## 是否重新生成【非必须】 Build / ReBuild
params['force'] = 'false'                                           ## 是否强制执行【非必须】true / false
params['target_br'] = 'v2.0.2.5'                                    ## C++代码生成文件仓库分支名称【非必须】
params['test'] = 'true'                                             ## 【非必须】
params['myparam'] = '123456789'                                     ## 自定义参数，可以有多个【非必须】

user_inpput = ''
for it in params :
    user_inpput += base64.b64encode(it.encode()).decode("utf-8") + ':' + base64.b64encode(params[it].encode()).decode("utf-8") + ' '
user_inpput = base64.b64encode(user_inpput.strip().encode()).decode("utf-8")

data = {}
data['token'] = 'e376ff93ae623e58610e6714503c43'                    ## trigger 自动生成【必须】
data['ref'] = params['code_br']                                     ## 自动发包仓库分支【必须】
data['variables[user_inpput]'] = user_inpput
request = requests.post(url, data)
print(request.headers)
print(request.text)