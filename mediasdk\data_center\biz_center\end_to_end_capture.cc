#include "end_to_end_capture.h"
#include <algorithm>
#include "base/check.h"

namespace mediasdk {
// static
std::shared_ptr<EndtoEndDelayCaptures> EndtoEndDelayCaptures::CreateShared() {
  return std::make_shared<EndtoEndDelayCaptures>();
};

void EndtoEndDelayCaptures::SetVisualCaptureTimestmapNS(const char* name,
                                                        int64_t timestamp) {
  if (0 == timestamp || !name) {
    return;
  }

  auto it = capture_ns_s_.find(name);
  if (it != capture_ns_s_.end()) {
    if (it->second > timestamp) {
      it->second = timestamp;
    }
  } else {
    capture_ns_s_.insert(std::make_pair(name, timestamp));
  }
}

std::shared_ptr<EndtoEndDelayCaptureConst>
EndtoEndDelayCaptures::MoveToConst() {
  std::map<std::string, int64_t> capture_timestamps;
  for (auto& [name, timestamp] : capture_ns_s_) {
    capture_timestamps.insert(std::make_pair(name, timestamp));
  }
  capture_ns_s_.clear();
  return std::make_shared<EndtoEndDelayCaptureConst>(capture_timestamps);
}

EndtoEndDelayCaptureConst::EndtoEndDelayCaptureConst(
    const std::map<std::string, int64_t>& capture_timestamps)
    : capture_timestamps_(capture_timestamps) {
  //DCHECK(!capture_timestamps.empty());
}

const std::map<std::string, int64_t>&
EndtoEndDelayCaptureConst::GetAllTimestmaps() {
  return capture_timestamps_;
}

int64_t EndtoEndDelayCaptureConst::GetMinCaptureTimestampNS() {
  auto it = std::min_element(
      capture_timestamps_.begin(), capture_timestamps_.end(),
      [](const auto& lhs, const auto& rhs) { return lhs.second < rhs.second; });
  return it == capture_timestamps_.end() ? 0 : it->second;
}

}  // namespace mediasdk