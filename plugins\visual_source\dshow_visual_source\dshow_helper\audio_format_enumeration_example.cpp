#include "dshow_helper.h"
#include "base/logging.h"
#include "base/strings/stringprintf.h"
#include "base/strings/utf_string_conversions.h"
#include <iostream>

namespace mediasdk {

// 获取匹配策略名称
std::string GetMatchStrategyName(DeviceMatchStrategy strategy) {
  switch (strategy) {
    case DeviceMatchStrategy::EXACT_ID_MATCH:
      return "精确ID匹配";
    case DeviceMatchStrategy::NAME_PATTERN_MATCH:
      return "名称模式匹配";
    case DeviceMatchStrategy::USB_PATH_MATCH:
      return "USB路径匹配";
    case DeviceMatchStrategy::FUZZY_NAME_MATCH:
      return "模糊名称匹配";
    default:
      return "未知策略";
  }
}

// 示例1：基本的音频格式枚举
void BasicAudioFormatEnumeration() {
  LOG(INFO) << "=== 基本音频格式枚举示例 ===";
  
  // 模拟从视频设备枚举获得的信息
  DShowDeviceName video_device;
  video_device.id = L"\\\\?\\usb#vid_046d&pid_085c&mi_00#6&348515e9&0&0000#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\\global";
  video_device.name = L"C922 Pro Stream Webcam";
  
  LOG(INFO) << "视频设备信息:";
  LOG(INFO) << "  ID: " << base::WideToUTF8(video_device.id);
  LOG(INFO) << "  Name: " << base::WideToUTF8(video_device.name);
  
  // 使用扩展函数查找音频格式
  ExtendedAudioFormatResult result;
  if (GetAudioFormatsForVideoDevice(video_device, result)) {
    LOG(INFO) << "✓ 成功找到音频格式信息";
    
    if (result.has_integrated_audio) {
      LOG(INFO) << "设备类型: 一体化设备";
    } else {
      LOG(INFO) << "设备类型: 非一体化设备";
      LOG(INFO) << "关联音频设备数量: " << result.audio_results.size();
    }
    
    for (const auto& audio_result : result.audio_results) {
      LOG(INFO) << "\n音频设备: " << base::WideToUTF8(audio_result.audio_device.name);
      LOG(INFO) << "匹配策略: " << GetMatchStrategyName(audio_result.match_strategy);
      LOG(INFO) << "置信度: " << audio_result.confidence_score;
      LOG(INFO) << "匹配详情: " << audio_result.match_details;
      
      LOG(INFO) << "支持的音频格式:";
      for (const auto& format : audio_result.formats) {
        LOG(INFO) << "  - " << format.GetChannels() << "声道, "
                  << format.GetSampleRate() << "Hz, "
                  << format.GetBitsPerSample() << "位";
      }
    }
  } else {
    LOG(ERROR) << "✗ 未能找到音频格式信息";
    LOG(ERROR) << "错误: " << result.error_message;
    
    LOG(INFO) << "建议:";
    for (const auto& suggestion : result.suggestions) {
      LOG(INFO) << "  - " << suggestion;
    }
  }
}

// 示例2：批量处理多个视频设备
void BatchProcessVideoDevices() {
  LOG(INFO) << "\n=== 批量处理视频设备示例 ===";
  
  // 枚举所有视频设备
  std::vector<DShowDeviceName> video_devices;
  if (!DShowEnumDevice(video_devices, CLSID_VideoInputDeviceCategory)) {
    LOG(ERROR) << "Failed to enumerate video devices";
    return;
  }
  
  LOG(INFO) << "Found " << video_devices.size() << " video devices";
  
  for (size_t i = 0; i < video_devices.size(); ++i) {
    const auto& video_device = video_devices[i];
    LOG(INFO) << "\n--- 处理视频设备 " << (i + 1) << "/" << video_devices.size() << " ---";
    LOG(INFO) << "设备名称: " << base::WideToUTF8(video_device.name);
    
    ExtendedAudioFormatResult result;
    if (GetAudioFormatsForVideoDevice(video_device, result)) {
      if (result.has_integrated_audio) {
        LOG(INFO) << "✓ 一体化设备，支持音频";
        LOG(INFO) << "  音频格式数量: " << result.audio_results[0].formats.size();
      } else {
        LOG(INFO) << "✓ 非一体化设备，找到 " << result.audio_results.size() << " 个关联音频设备";
        for (const auto& audio_result : result.audio_results) {
          LOG(INFO) << "  - " << base::WideToUTF8(audio_result.audio_device.name) 
                    << " (置信度: " << audio_result.confidence_score << ")";
        }
      }
    } else {
      LOG(INFO) << "✗ 未找到音频支持";
    }
  }
}

// 示例3：测试特定的设备匹配策略
void TestSpecificMatchingStrategies() {
  LOG(INFO) << "\n=== 测试特定匹配策略示例 ===";
  
  // 模拟不同类型的设备
  std::vector<std::pair<std::string, DShowDeviceName>> test_devices = {
    {"一体化USB摄像头", {L"C922 Pro Stream Webcam", L"\\\\?\\usb#vid_046d&pid_085c&mi_00#..."}},
    {"Elgato采集卡", {L"Elgato Game Capture HD60 S", L"\\\\?\\usb#vid_0fd9&pid_0063#..."}},
    {"AVerMedia采集卡", {L"AVerMedia Live Gamer Portable 2 Plus", L"\\\\?\\usb#vid_07ca&pid_0337#..."}}
  };
  
  std::vector<DeviceMatchStrategy> strategies = {
    DeviceMatchStrategy::EXACT_ID_MATCH,
    DeviceMatchStrategy::USB_PATH_MATCH,
    DeviceMatchStrategy::FUZZY_NAME_MATCH,
    DeviceMatchStrategy::NAME_PATTERN_MATCH
  };
  
  for (const auto& test_device : test_devices) {
    LOG(INFO) << "\n--- 测试设备: " << test_device.first << " ---";
    
    for (const auto& strategy : strategies) {
      auto associated_devices = FindAssociatedAudioDevices(test_device.second, strategy);
      LOG(INFO) << GetMatchStrategyName(strategy) << ": 找到 " 
                << associated_devices.size() << " 个关联设备";
      
      for (const auto& audio_device : associated_devices) {
        float confidence = CalculateDeviceMatchConfidence(
            test_device.second, audio_device, strategy);
        LOG(INFO) << "  - " << base::WideToUTF8(audio_device.name) 
                  << " (置信度: " << confidence << ")";
      }
    }
  }
}

// 示例4：错误处理和诊断
void ErrorHandlingAndDiagnostics() {
  LOG(INFO) << "\n=== 错误处理和诊断示例 ===";
  
  // 模拟一个不存在的设备
  DShowDeviceName fake_device;
  fake_device.id = L"fake_device_id";
  fake_device.name = L"Fake Device";
  
  ExtendedAudioFormatResult result;
  if (!GetAudioFormatsForVideoDevice(fake_device, result)) {
    LOG(INFO) << "预期的失败情况:";
    LOG(INFO) << "错误信息: " << result.error_message;
    
    LOG(INFO) << "系统建议:";
    for (const auto& suggestion : result.suggestions) {
      LOG(INFO) << "  - " << suggestion;
    }
  }
  
  // 模拟一个只有视频没有音频的设备
  DShowDeviceName video_only_device;
  video_only_device.id = L"video_only_device_id";
  video_only_device.name = L"Video Only Device";
  
  if (!GetAudioFormatsForVideoDevice(video_only_device, result)) {
    LOG(INFO) << "\n视频专用设备处理:";
    LOG(INFO) << "这种情况下，用户需要手动选择音频输入设备";
  }
}

// 示例5：实际应用场景
void PracticalUsageScenario() {
  LOG(INFO) << "\n=== 实际应用场景示例 ===";
  
  // 场景：用户选择了一个视频设备，系统自动查找对应的音频设备
  DShowDeviceName selected_video_device;
  selected_video_device.id = L"\\\\?\\usb#vid_046d&pid_085c&mi_00#...";
  selected_video_device.name = L"C922 Pro Stream Webcam";
  
  LOG(INFO) << "用户选择的视频设备: " << base::WideToUTF8(selected_video_device.name);
  
  ExtendedAudioFormatResult result;
  if (GetAudioFormatsForVideoDevice(selected_video_device, result)) {
    if (result.has_integrated_audio) {
      LOG(INFO) << "✓ 检测到一体化设备，自动配置音频";
      
      // 选择最佳音频格式
      const auto& formats = result.audio_results[0].formats;
      for (const auto& format : formats) {
        if (format.GetChannels() >= 2 && 
            format.GetSampleRate() == 48000 && 
            format.GetBitsPerSample() >= 16) {
          LOG(INFO) << "推荐音频格式: " << format.GetChannels() << "声道, "
                    << format.GetSampleRate() << "Hz, "
                    << format.GetBitsPerSample() << "位";
          break;
        }
      }
    } else {
      LOG(INFO) << "✓ 检测到非一体化设备，提供音频设备选项";
      
      // 按置信度排序，推荐最佳匹配
      if (!result.audio_results.empty()) {
        const auto& best_match = result.audio_results[0];
        LOG(INFO) << "推荐音频设备: " << base::WideToUTF8(best_match.audio_device.name);
        LOG(INFO) << "匹配置信度: " << best_match.confidence_score;
        
        if (best_match.confidence_score > 0.8f) {
          LOG(INFO) << "高置信度匹配，建议自动选择";
        } else {
          LOG(INFO) << "中等置信度匹配，建议用户确认";
        }
      }
    }
  } else {
    LOG(INFO) << "✗ 未找到关联音频设备";
    LOG(INFO) << "建议用户手动选择音频输入设备";
    
    // 列出所有可用的音频设备供用户选择
    std::vector<DShowDeviceName> audio_devices;
    if (DShowEnumDevice(audio_devices, CLSID_AudioInputDeviceCategory)) {
      LOG(INFO) << "可用的音频输入设备:";
      for (const auto& audio_device : audio_devices) {
        LOG(INFO) << "  - " << base::WideToUTF8(audio_device.name);
      }
    }
  }
}

}  // namespace mediasdk

// 主函数示例
int main() {
  // 初始化COM
  ::CoInitialize(NULL);
  
  try {
    mediasdk::BasicAudioFormatEnumeration();
    mediasdk::BatchProcessVideoDevices();
    mediasdk::TestSpecificMatchingStrategies();
    mediasdk::ErrorHandlingAndDiagnostics();
    mediasdk::PracticalUsageScenario();
  } catch (const std::exception& e) {
    LOG(ERROR) << "Exception: " << e.what();
  }
  
  // 清理COM
  ::CoUninitialize();
  
  return 0;
}
