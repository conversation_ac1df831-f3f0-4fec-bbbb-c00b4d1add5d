
#pragma once
#include <base/mediasdk/observer_list_thread_safe_weak.h>
#include <mediasdk/audio/audio_mixer_input.h>
#include <mediasdk/utils/time_duration.h>
#include <map>
#include <memory>
#include <string>
#include <vector>
#include "mediasdk/audio/audio_frame_observer.h"
#include "mediasdk/audio/audio_volume.h"
#include "mediasdk/audio/mixed_audio_observer.h"

namespace mediasdk {

class AudioInput;

class AudioTrackMixedFrameObserver {
 public:
  virtual void OnMixedAudioFrame(uint32_t track_id,
                                 const AudioFrame& frame) = 0;
};

class AudioTrack : public AudioInputFrameObserver,
                   public std::enable_shared_from_this<AudioTrack> {
 public:
  struct AudioInputWithAudioMixerInput {
    AudioInputWithAudioMixerInput(
        std::shared_ptr<AudioInput> audio_input,
        std::shared_ptr<AudioMixerInput> audio_mixer_input)
        : audio_input(audio_input), audio_mixer_input(audio_mixer_input) {}

    std::shared_ptr<AudioInput> audio_input;
    std::shared_ptr<AudioMixerInput> audio_mixer_input;
  };

  using AudioInputsType =
      std::map<std::string, std::shared_ptr<AudioInputWithAudioMixerInput>>;
  explicit AudioTrack(uint32_t track_id);

  ~AudioTrack();

  std::shared_ptr<AudioInput> GetAudioInput(const std::string& id);

  bool AddAudioInput(std::shared_ptr<AudioInput> input);

  bool RemoveAudioInput(std::shared_ptr<AudioInput> input);

  bool RemoveAudioInput(const std::string& id);

  void SetTrackDealy(int64_t delay_ns);

  int64_t GetTrackDelay() const;

  AudioInputsType& GetAudioInputs();

  void OnAudioPump(const mediasdk::TimeDuration& target,
                   const bool have_observer,
                   AudioTrackMixedFrameObserver* observer);

  void OnProcessedAudioFrame(const std::string& input_id,
                             const AudioFrame& frame,
                             const AudioFormat& format) override;
  std::string ToString();

 private:
  bool IsAllFramesReady(const TimeDuration& target);
  bool ConsumeAllMixerTask(const TimeDuration& target,
                           AudioTrackMixedFrameObserver* observer);

  void Notify(const mediasdk::AudioFrame& frames,
              AudioTrackMixedFrameObserver*);

 private:
  // audio input id - AudioInput
  std::map<std::string, std::shared_ptr<AudioInputWithAudioMixerInput>> inputs_;
  std::mutex lock_inputs_;

  AudioVolume volume_notify_;
  uint32_t track_id_ = -1;

  std::list<TimeDuration> pending_mixer_task_;
  size_t last_pending_mixer_task_size_ = 0;
  std::shared_ptr<mediasdk::AudioFrame> pump_frame_;

  std::atomic_bool have_observer_ = false;

  std::atomic_int64_t delay_ms_ = 0;
  int64_t pump_cnt_ = 0;
  int64_t last_log_ts_ = 0;
};

}  // namespace mediasdk
