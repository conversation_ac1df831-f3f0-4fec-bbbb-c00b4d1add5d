#include "buffered_texture_buffer.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include "border.h"
#include "shader.h"
#include "texture_shader.h"
#include "transform_calc.h"

using namespace DirectX;
using namespace Microsoft::WRL;

namespace graphics {

namespace {
XMFLOAT3 CalcClearColor(uint32_t bk_color) {
  int r = (bk_color >> 16) & 0xFF;
  int g = (bk_color >> 8) & 0xFF;
  int b = bk_color & 0xFF;
  return {r * 1.0f / 255, g * 1.0f / 255, b * 1.0f / 255};
}
}  // namespace

bool BufferedTextureBuffer::Equal(
    const Transform& trans,
    const XMFLOAT2 new_texture_size,
    const XMFLOAT2 new_vp_size,
    float tonemap_param,
    const PointNine& point_nine,
    const CornerBuffer& corner_buffer,
    const BorderBuffer& border_buffer,
    const DirectX::XMFLOAT4& clip_mask,
    bool is_force_alpha,
    const ColorParams& color_params,
    const ChromaKeyParams& chroma_key_params,
    const SharpnessParams& sharpness_params,
    const LuminanceMapParams& luminance_map_params) const {
  if (trans != pre_transform_) {
    return false;
  }

  if (pre_texture_size_ != new_texture_size) {
    return false;
  }

  if (pre_vp_size_ != new_vp_size) {
    return false;
  }

  if (pre_clip_mask_ != clip_mask) {
    return false;
  }

  if (is_force_alpha_ != is_force_alpha) {
    return false;
  }

  if (color_params_ != color_params) {
    return false;
  }

  if (chroma_key_params_ != chroma_key_params) {
    return false;
  }

  if (sharpness_params_.sharpness != sharpness_params.sharpness ||
      sharpness_params_.width != sharpness_params.width ||
      sharpness_params_.height != sharpness_params.height) {
    return false;
  }

  if (luminance_map_params_.luminance_map != luminance_map_params.luminance_map ||
      luminance_map_params_.is_luminace_wipe!= luminance_map_params.is_luminace_wipe ||
      luminance_map_params_.luminance_invert != luminance_map_params.luminance_invert ||
      luminance_map_params_.luminance_map_progress != luminance_map_params.luminance_map_progress ||
      luminance_map_params_.luminance_softness != luminance_map_params.luminance_softness) {
    return false;
  }

  if (std::abs(tonemap_param - pre_tonemap_param_) > 0.001f) {
    return false;
  }

  if (point_nine == pre_point_nine_ && corner_buffer == pre_corner_buffer_ &&
      border_buffer == pre_border_buffer_) {
    return true;
  } else {
    return false;
  }
}

void BufferedTextureBuffer::UpdateBuffers(
    ID3D11DeviceContext* context,
    const XMMATRIX& view_matrix,
    const XMMATRIX& projection_matrix,
    const float tonemap_param,
    const PointNine& point_nine,
    const CornerBuffer& corner_buffer,
    const BorderBuffer& border_buffer,
    const DirectX::XMFLOAT4& clip_mask,
    bool is_force_alpha,
    const ColorParams& color_params,
    const ChromaKeyParams& chroma_key_params,
    const SharpnessParams& sharpness_params,
    const LuminanceMapParams& luminance_map_params) {
  using namespace graphics;
  graphics::CROPBUFFER crop = {};
  graphics::PS_BUFFER ps_input = {};

  crop.translate = XMFLOAT2_EMPTY;
  crop.scale = XMFLOAT2_ONE;
  // https://bytedance.larkoffice.com/docx/O6aVd7OfooLnMlxSYorcbo1KnSb
  XMFLOAT4 clip = pre_transform_.GetClip();
  XMFLOAT2 old_size = pre_texture_size_;

  XMFLOAT2 new_size(clip.x + clip.z, clip.y + clip.w);
  new_size.x = (new_size.x > old_size.x) ? 2 : (old_size.x - new_size.x);
  new_size.y = (new_size.y > old_size.y) ? 2 : (old_size.y - new_size.y);

  if (new_size.x != old_size.x || new_size.y != old_size.y) {
    if (old_size.x > 0.0F) {
      crop.translate.x = clip.x / old_size.x;
      crop.scale.x = new_size.x / old_size.x;
    }
    if (old_size.y > 0.0F) {
      crop.translate.y = clip.y / old_size.y;
      crop.scale.y = new_size.y / old_size.y;
    }
  }
  XMMATRIX world = BuildMatrixToTextureRender(
      old_size, pre_vp_size_, pre_transform_.GetTranslate(),
      pre_transform_.GetScale(), pre_transform_.GetClip(),
      pre_transform_.GetRotate(), pre_transform_.GetShear(),
      pre_transform_.GetShearAngle());

  ps_input.tone_map_param = tonemap_param;

  ps_input.border = point_nine.GetBorder();
  ps_input.point_nine_scale =
      XMFLOAT4{point_nine.GetScale().x, point_nine.GetScale().y,
               point_nine.GetClearBorder() ? 1.0f : 0.0f, 0.0f};

  ps_input.clip_mask = clip_mask;

  ps_input.corner_radius = corner_buffer.GetRadius();
  ps_input.corner_clip = corner_buffer.GetClip();
  ps_input.corner_scale = XMFLOAT4{corner_buffer.GetScale(), 0.0f, 0.0f, 0.0f};

  ps_input.border_color = border_buffer.GetColor();
  ps_input.border_width =
      XMFLOAT4{border_buffer.GetWidth().x, border_buffer.GetWidth().y,
               border_buffer.GetScale().x, border_buffer.GetScale().y};

  ps_input.force_alpha = is_force_alpha ? 1.0f : -1.0f;

  if (!color_params.IsEmpty()) {
    ps_input.color_adjust_flag = 1.0f;
    if (!IsNearEqual(color_params.Opacity(), -1.0f)) {
      ps_input.opacity = color_params.Opacity();
    }
    ps_input.color_matrix = BuildColorCorrectionMatrix(color_params);
    ps_input.outside_gamma = GammaCorrection(color_params.Gamma());
  } else {
    ps_input.color_adjust_flag = -1.0f;
    // use identity matrix by default, in case of something error
    ps_input.color_matrix = XMMatrixIdentity();
  }

  ps_input.chroma_key_flags = chroma_key_params.IsEmpty() ? -1.0f : 1.0f;
  if (!chroma_key_params.IsEmpty()) {
    ps_input.pixel_size_x = chroma_key_params.PixelSizeX();
    ps_input.pixel_size_y = chroma_key_params.PixelSizeY();
    ps_input.similarity = chroma_key_params.Similarity();
    ps_input.smoothness = chroma_key_params.Smoothness();
    ps_input.spill = chroma_key_params.Spill();
    ps_input.extrude = chroma_key_params.Extrude();
    ps_input.chroma_key_gamma = GammaCorrection(chroma_key_params.Gamma());
    ps_input.chroma_key_opacity = chroma_key_params.Opacity();
    ColorParams chroma_key_color_params{};
    chroma_key_color_params.SetBrightness(chroma_key_params.Brightness());
    chroma_key_color_params.SetContrast(chroma_key_params.Contrast());
    ps_input.chroma_key_color_matrix =
        BuildBrightnessAndContrastMatrix(chroma_key_color_params);
    ps_input.chroma_key =
        CalcClearColor(static_cast<uint32_t>(chroma_key_params.ChromaKey()));
  } else {
    ps_input.chroma_key_color_matrix = XMMatrixIdentity();
  }

  if (!IsNearEqual(sharpness_params.sharpness, 0.0f)) {
    ps_input.sharpness_param =
        XMFLOAT4{sharpness_params.width, sharpness_params.height,
                 sharpness_params.sharpness, 1.0f};
  } else {
    ps_input.sharpness_param = XMFLOAT4{};
  }

  if (luminance_map_params.luminance_map) {
    ps_input.is_use_luminance_map_param = 1.0f;
    ps_input.luminance_map_param =
        XMFLOAT4{luminance_map_params.is_luminace_wipe ? 1.0f : -1.0f,
                 luminance_map_params.luminance_map_progress,
                 luminance_map_params.luminance_softness,
                 luminance_map_params.luminance_invert ? 1.0f : -1.0f};
  } else {
    ps_input.is_use_luminance_map_param = -1.0f;
    ps_input.luminance_map_param = XMFLOAT4{0.0f, 0.0f, 0.0f, 0.0f};
  }

  if (!DoCopyMatrixBuffer(context, &world, &view_matrix, &projection_matrix,
                          matrix_buffer_)) {
    DCHECK(false);
    return;
  }
  D3D11_MAPPED_SUBRESOURCE map = {};

  if (SUCCEEDED(context->Map(crop_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0,
                             &map))) {
    graphics::CROPBUFFER* ptr = (graphics::CROPBUFFER*)map.pData;
    memcpy_s(ptr, sizeof(graphics::CROPBUFFER), &crop,
             sizeof(graphics::CROPBUFFER));
    context->Unmap(crop_buffer_.Get(), 0);
  } else {
    DCHECK(false);
  }

  if (SUCCEEDED(context->Map(ps_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0,
                             &map))) {
    graphics::PS_BUFFER* dataPtr = (graphics::PS_BUFFER*)map.pData;
    memcpy_s(dataPtr, sizeof(graphics::PS_BUFFER), &ps_input,
             sizeof(graphics::PS_BUFFER));
    context->Unmap(ps_buffer_.Get(), 0);
  } else {
    DCHECK(false);
  }

  std::array<TextureShader::VERTEXTYPE, 4> vertex_types = {};

  vertex_types[0].position = XMFLOAT3(0.0F, 0.0F, 0.0F);
  vertex_types[0].texture = XMFLOAT2(pre_transform_.IsFlipH() ? 1.0F : 0.0F,
                                     pre_transform_.IsFlipV() ? 0.0F : 1.0F);
  vertex_types[0].color = XMFLOAT4(1.0F, 1.0F, 1.0F, 1.0F);
  vertex_types[1].position = XMFLOAT3(0.0F, 1.0F, 0.0F);
  vertex_types[1].texture = XMFLOAT2(pre_transform_.IsFlipH() ? 1.0F : 0.0F,
                                     pre_transform_.IsFlipV() ? 1.0F : 0.0F);
  vertex_types[1].color = XMFLOAT4(1.0F, 1.0F, 1.0F, 1.0F);
  vertex_types[2].position = XMFLOAT3(1.0F, 1.0F, 0.0F);
  vertex_types[2].texture = XMFLOAT2(pre_transform_.IsFlipH() ? 0.0F : 1.0F,
                                     pre_transform_.IsFlipV() ? 1.0F : 0.0F);
  vertex_types[2].color = XMFLOAT4(1.0F, 1.0F, 1.0F, 1.0F);
  vertex_types[3].position = XMFLOAT3(1.0F, 0.0F, 0.0F);
  vertex_types[3].texture = XMFLOAT2(pre_transform_.IsFlipH() ? 0.0F : 1.0F,
                                     pre_transform_.IsFlipV() ? 0.0F : 1.0F);
  vertex_types[3].color = XMFLOAT4(1.0F, 1.0F, 1.0F, 1.0F);
  if (!vertex_buffer_) {
    DCHECK(false);
    return;
  }

  D3D11_MAPPED_SUBRESOURCE ms = {};
  HRESULT hRes =
      context->Map(vertex_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &ms);
  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to Map(%s)",
                                     GetErrorString(hRes).c_str());
    DCHECK(false);
    return;
  }
  std::memcpy(ms.pData, (void*)vertex_types.data(),
              sizeof(TextureShader::VERTEXTYPE) * vertex_types.size());
  context->Unmap(vertex_buffer_.Get(), 0);
}

void BufferedTextureBuffer::UpdateLutBuffers(ID3D11DeviceContext* context,
    const DirectX::XMMATRIX& view_matrix,
    const DirectX::XMMATRIX& projection_matrix,
    const ColorLutParams& params)
{
    using namespace graphics;
    graphics::CROPBUFFER crop = {};
    graphics::LUT_PS_BUFFER ps_input = {};
    ps_input.amount = params.GetAmount();
    ps_input.domain_max = XMFLOAT4{ params.GetDomainMax()[0], params.GetDomainMax()[1],params.GetDomainMax()[2], 0 };
    ps_input.domain_min = XMFLOAT4{ params.GetDomainMin()[0], params.GetDomainMin()[1],params.GetDomainMin()[2], 0 };
    ps_input.pass_through_alpha = params.GetPassThroughAlpha() ? 1 : 0;

    crop.translate = XMFLOAT2_EMPTY;
    crop.scale = XMFLOAT2_ONE;
    XMFLOAT4 clip = pre_transform_.GetClip();
    XMFLOAT2 old_size = pre_texture_size_;

    XMFLOAT2 new_size(clip.x + clip.z, clip.y + clip.w);
    new_size.x = (new_size.x > old_size.x) ? 2 : (old_size.x - new_size.x);
    new_size.y = (new_size.y > old_size.y) ? 2 : (old_size.y - new_size.y);

    if (new_size.x != old_size.x || new_size.y != old_size.y) {
        if (old_size.x > 0.0F) {
            crop.translate.x = clip.x / old_size.x;
            crop.scale.x = new_size.x / old_size.x;
        }
        if (old_size.y > 0.0F) {
            crop.translate.y = clip.y / old_size.y;
            crop.scale.y = new_size.y / old_size.y;
        }
    }
    XMMATRIX world = BuildMatrixToTextureRender(
        old_size, pre_vp_size_, pre_transform_.GetTranslate(),
        pre_transform_.GetScale(), pre_transform_.GetClip(),
        pre_transform_.GetRotate(), pre_transform_.GetShear(),
        pre_transform_.GetShearAngle());


    if (!DoCopyMatrixBuffer(context, &world, &view_matrix, &projection_matrix,
        matrix_buffer_)) {
        DCHECK(false);
        return;
    }
    D3D11_MAPPED_SUBRESOURCE map = {};

    if (SUCCEEDED(context->Map(crop_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0,
        &map))) {
        graphics::CROPBUFFER* ptr = (graphics::CROPBUFFER*)map.pData;
        memcpy_s(ptr, sizeof(graphics::CROPBUFFER), &crop,
            sizeof(graphics::CROPBUFFER));
        context->Unmap(crop_buffer_.Get(), 0);
    }
    else {
        DCHECK(false);
    }

    if (SUCCEEDED(context->Map(ps_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0,
        &map))) {
        graphics::LUT_PS_BUFFER* dataPtr = (graphics::LUT_PS_BUFFER*)map.pData;
        memcpy_s(dataPtr, sizeof(graphics::LUT_PS_BUFFER), &ps_input,
            sizeof(graphics::LUT_PS_BUFFER));
        context->Unmap(ps_buffer_.Get(), 0);
    }
    else {
        DCHECK(false);
    }

    std::array<TextureShader::VERTEXTYPE, 4> vertex_types = {};

    vertex_types[0].position = XMFLOAT3(0.0F, 0.0F, 0.0F);
    vertex_types[0].texture = XMFLOAT2(pre_transform_.IsFlipH() ? 1.0F : 0.0F,
        pre_transform_.IsFlipV() ? 0.0F : 1.0F);
    vertex_types[0].color = XMFLOAT4(1.0F, 1.0F, 1.0F, 1.0F);
    vertex_types[1].position = XMFLOAT3(0.0F, 1.0F, 0.0F);
    vertex_types[1].texture = XMFLOAT2(pre_transform_.IsFlipH() ? 1.0F : 0.0F,
        pre_transform_.IsFlipV() ? 1.0F : 0.0F);
    vertex_types[1].color = XMFLOAT4(1.0F, 1.0F, 1.0F, 1.0F);
    vertex_types[2].position = XMFLOAT3(1.0F, 1.0F, 0.0F);
    vertex_types[2].texture = XMFLOAT2(pre_transform_.IsFlipH() ? 0.0F : 1.0F,
        pre_transform_.IsFlipV() ? 1.0F : 0.0F);
    vertex_types[2].color = XMFLOAT4(1.0F, 1.0F, 1.0F, 1.0F);
    vertex_types[3].position = XMFLOAT3(1.0F, 0.0F, 0.0F);
    vertex_types[3].texture = XMFLOAT2(pre_transform_.IsFlipH() ? 0.0F : 1.0F,
        pre_transform_.IsFlipV() ? 0.0F : 1.0F);
    vertex_types[3].color = XMFLOAT4(1.0F, 1.0F, 1.0F, 1.0F);
    if (!vertex_buffer_) {
        DCHECK(false);
        return;
    }

    D3D11_MAPPED_SUBRESOURCE ms = {};
    HRESULT hRes =
        context->Map(vertex_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &ms);
    if (hRes != S_OK) {
        LOG(ERROR) << base::StringPrintf("Failed to Map(%s)",
            GetErrorString(hRes).c_str());
        DCHECK(false);
        return;
    }
    std::memcpy(ms.pData, (void*)vertex_types.data(),
        sizeof(TextureShader::VERTEXTYPE) * vertex_types.size());
    context->Unmap(vertex_buffer_.Get(), 0);
}

void BufferedTextureBuffer::UpdateScaleBuffers(
    ID3D11DeviceContext* context,
    const DirectX::XMMATRIX& view_matrix,
    const DirectX::XMMATRIX& projection_matrix,
    const PartialScaleParams& params) {
      using namespace graphics;
      graphics::CROPBUFFER crop = {};
      graphics::SCALE_PS_BUFFER ps_input = {};
      ps_input.stretch_start_point = params.scale_starting_point;
      ps_input.stretch_ratio = params.scale_ratio;
      ps_input.stretch_direction = params.stretch_direction;
  
      crop.translate = XMFLOAT2_EMPTY;
      crop.scale = XMFLOAT2_ONE;
      XMFLOAT4 clip = pre_transform_.GetClip();
      XMFLOAT2 old_size = pre_texture_size_;
  
      XMFLOAT2 new_size(clip.x + clip.z, clip.y + clip.w);
      new_size.x = (new_size.x > old_size.x) ? 2 : (old_size.x - new_size.x);
      new_size.y = (new_size.y > old_size.y) ? 2 : (old_size.y - new_size.y);
  
      if (new_size.x != old_size.x || new_size.y != old_size.y) {
          if (old_size.x > 0.0F) {
              crop.translate.x = clip.x / old_size.x;
              crop.scale.x = new_size.x / old_size.x;
          }
          if (old_size.y > 0.0F) {
              crop.translate.y = clip.y / old_size.y;
              crop.scale.y = new_size.y / old_size.y;
          }
      }
      XMMATRIX world = BuildMatrixToTextureRender(
          old_size, pre_vp_size_, pre_transform_.GetTranslate(),
          pre_transform_.GetScale(), pre_transform_.GetClip(),
          pre_transform_.GetRotate(), pre_transform_.GetShear(),
          pre_transform_.GetShearAngle());
  
  
      if (!DoCopyMatrixBuffer(context, &world, &view_matrix, &projection_matrix,
          matrix_buffer_)) {
          DCHECK(false);
          return;
      }
      D3D11_MAPPED_SUBRESOURCE map = {};
  
      if (SUCCEEDED(context->Map(crop_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0,
          &map))) {
          graphics::CROPBUFFER* ptr = (graphics::CROPBUFFER*)map.pData;
          memcpy_s(ptr, sizeof(graphics::CROPBUFFER), &crop,
              sizeof(graphics::CROPBUFFER));
          context->Unmap(crop_buffer_.Get(), 0);
      }
      else {
          DCHECK(false);
      }
  
      if (SUCCEEDED(context->Map(ps_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0,
          &map))) {
          graphics::SCALE_PS_BUFFER* dataPtr =
              (graphics::SCALE_PS_BUFFER*)map.pData;
          memcpy_s(dataPtr, sizeof(graphics::SCALE_PS_BUFFER), &ps_input,
                   sizeof(graphics::SCALE_PS_BUFFER));
          context->Unmap(ps_buffer_.Get(), 0);
      }
      else {
          DCHECK(false);
      }
  
      std::array<TextureShader::VERTEXTYPE, 4> vertex_types = {};
  
      vertex_types[0].position = XMFLOAT3(0.0F, 0.0F, 0.0F);
      vertex_types[0].texture = XMFLOAT2(pre_transform_.IsFlipH() ? 1.0F : 0.0F,
          pre_transform_.IsFlipV() ? 0.0F : 1.0F);
      vertex_types[0].color = XMFLOAT4(1.0F, 1.0F, 1.0F, 1.0F);
      vertex_types[1].position = XMFLOAT3(0.0F, 1.0F, 0.0F);
      vertex_types[1].texture = XMFLOAT2(pre_transform_.IsFlipH() ? 1.0F : 0.0F,
          pre_transform_.IsFlipV() ? 1.0F : 0.0F);
      vertex_types[1].color = XMFLOAT4(1.0F, 1.0F, 1.0F, 1.0F);
      vertex_types[2].position = XMFLOAT3(1.0F, 1.0F, 0.0F);
      vertex_types[2].texture = XMFLOAT2(pre_transform_.IsFlipH() ? 0.0F : 1.0F,
          pre_transform_.IsFlipV() ? 1.0F : 0.0F);
      vertex_types[2].color = XMFLOAT4(1.0F, 1.0F, 1.0F, 1.0F);
      vertex_types[3].position = XMFLOAT3(1.0F, 0.0F, 0.0F);
      vertex_types[3].texture = XMFLOAT2(pre_transform_.IsFlipH() ? 0.0F : 1.0F,
          pre_transform_.IsFlipV() ? 0.0F : 1.0F);
      vertex_types[3].color = XMFLOAT4(1.0F, 1.0F, 1.0F, 1.0F);
      if (!vertex_buffer_) {
          DCHECK(false);
          return;
      }
  
      D3D11_MAPPED_SUBRESOURCE ms = {};
      HRESULT hRes =
          context->Map(vertex_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &ms);
      if (hRes != S_OK) {
          LOG(ERROR) << base::StringPrintf("Failed to Map(%s)",
              GetErrorString(hRes).c_str());
          DCHECK(false);
          return;
      }
      std::memcpy(ms.pData, (void*)vertex_types.data(),
          sizeof(TextureShader::VERTEXTYPE) * vertex_types.size());
      context->Unmap(vertex_buffer_.Get(), 0);
    }

bool BufferedTextureBuffer::TryUpdate(const Texture& texture,
                                      const XMFLOAT2& vp_size,
                                      ID3D11DeviceContext* context,
                                      const DirectX::XMMATRIX& view,
                                      const DirectX::XMMATRIX& project,
                                      const float tonemap_param,
                                      const Transform& trans,
                                      const PointNine& point_nine,
                                      const CornerBuffer& corner_buffer,
                                      const BorderBuffer& border_buffer,
                                      const DirectX::XMFLOAT4& clip_mask,
                                      bool force_tex_alpha,
                                      const ColorParams& color_params,
                                      const ChromaKeyParams& chroma_key_params,
                                      const SharpnessParams& sharpness_params) {
  return TryUpdate(texture.GetSize(), vp_size, context, view, project,
                   tonemap_param, trans, point_nine, corner_buffer,
                   border_buffer, clip_mask, force_tex_alpha, color_params,
                   chroma_key_params, sharpness_params);
}

bool BufferedTextureBuffer::TryUpdate(const DirectX::XMFLOAT2& texture_size,
                                      const DirectX::XMFLOAT2& vp_size,
                                      ID3D11DeviceContext* context,
                                      const DirectX::XMMATRIX& view,
                                      const DirectX::XMMATRIX& project,
                                      const float tonemap_param,
                                      const Transform& trans,
                                      const PointNine& point_nine,
                                      const CornerBuffer& corner_buffer,
                                      const BorderBuffer& border_buffer,
                                      const DirectX::XMFLOAT4& clip_mask,
                                      bool force_tex_alpha,
                                      const ColorParams& color_params,
                                      const ChromaKeyParams& chroma_key_params,
                                      const SharpnessParams& sharpness_params,
                                      const LuminanceMapParams& luminance_map_params) {
  Use();
  if (Equal(trans, texture_size, vp_size, tonemap_param, point_nine,
            corner_buffer, border_buffer, clip_mask, force_tex_alpha,
            color_params, chroma_key_params, sharpness_params,
            luminance_map_params)) {
    return true;
  }

  pre_transform_ = trans;
  pre_texture_size_ = texture_size;
  pre_vp_size_ = vp_size;
  pre_tonemap_param_ = tonemap_param;
  pre_point_nine_ = point_nine;
  pre_clip_mask_ = clip_mask;
  pre_corner_buffer_ = corner_buffer;
  pre_border_buffer_ = border_buffer;
  color_params_ = color_params;
  chroma_key_params_ = chroma_key_params;
  is_force_alpha_ = force_tex_alpha;
  luminance_map_params_ = luminance_map_params;

  UpdateBuffers(context, view, project, tonemap_param, point_nine,
                corner_buffer, border_buffer, clip_mask, force_tex_alpha,
                color_params, chroma_key_params, sharpness_params,
                luminance_map_params);
  return true;
}
bool BufferedTextureBuffer::TryUpdate(const DirectX::XMFLOAT2& texture_size,
    const DirectX::XMFLOAT2& vp_size,
    ID3D11DeviceContext* context,
    const DirectX::XMMATRIX& view,
    const DirectX::XMMATRIX& project,
    const Transform& trans,
    const ColorLutParams& params)
{
    Use();
    pre_transform_ = trans;
    pre_texture_size_ = texture_size;
    pre_vp_size_ = vp_size;
    UpdateLutBuffers(context, view, project, params);
    return true;
}

bool BufferedTextureBuffer::TryUpdate(const DirectX::XMFLOAT2& texture_size,
                                      const DirectX::XMFLOAT2& vp_size,
                                      ID3D11DeviceContext* context,
                                      const DirectX::XMMATRIX& view,
                                      const DirectX::XMMATRIX& project,
                                      const Transform& trans,
                                      const PartialScaleParams& params) {
                                        Use();
                                        pre_transform_ = trans;
                                        pre_texture_size_ = texture_size;
                                        pre_vp_size_ = vp_size;
                                        UpdateScaleBuffers(context, view, project, params);
                                        return true;
}

void BufferedTextureBuffer::Destroy() {
  if (vertex_buffer_) {
    vertex_buffer_.Reset();
  }
  if (index_buffer_) {
    index_buffer_.Reset();
  }
  if (matrix_buffer_) {
    matrix_buffer_.Reset();
  }
  if (crop_buffer_) {
    crop_buffer_.Reset();
  }
  if (ps_buffer_) {
    ps_buffer_.Reset();
  }
}
}  // namespace graphics