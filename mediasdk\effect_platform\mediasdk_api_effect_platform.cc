#include "public/mediasdk_api_effect_platform.h"

#include "mediasdk/component_proxy.h"
#include "mediasdk/effect_platform/effect_platform_controller.h"

namespace mediasdk {

void MS_API EffectPlatformInitialize(const char* json_param, Closure closure) {
  DCHECK(json_param);
  std::string jparam = json_param ? std::string(json_param) : std::string();
  EffectPlatformControllerProxy::Call(
      FROM_HERE, MSCallbackBool(closure, __FUNCDNAME__),
      &EffectPlatformController::EPInitialize, jparam);
}

void MS_API EffectPlatformUninitialize(Closure closure) {
  EffectPlatformControllerProxy::Call(
      FROM_HERE, MSCallbackBool(closure, __FUNCDNAME__),
      &EffectPlatformController::EPUninitialize);
}

void MS_API EffectPlatformRegistObserver(MediaSDKEPEventObserver* observer,
                                         Closure closure) {
  EffectPlatformControllerProxy::Call(
      FROM_HERE, MS<PERSON>allbackBool(closure, __FUNCDNAME__),
      &EffectPlatformController::EPRegistEventObserver,
      base::raw_ptr(observer));
}

void MS_API EffectPlatformUnregistObserver(MediaSDKEPEventObserver* observer,
                                           Closure closure) {
  EffectPlatformControllerProxy::Call(
      FROM_HERE, MSCallbackBool(closure, __FUNCDNAME__),
      &EffectPlatformController::EPUnregistEventObserver,
      base::raw_ptr(observer));
}

void MS_API EffectPlatformUpdateConfig(const char* user_id,
                                       const char* hardware_level,
                                       Closure closure) {
  DCHECK(user_id);
  DCHECK(hardware_level);
  std::string user_id_str = user_id ? std::string(user_id) : std::string();
  std::string hardware_str =
      hardware_level ? std::string(hardware_level) : std::string();
  EffectPlatformControllerProxy::Call(
      FROM_HERE, MSCallbackBool(closure, __FUNCDNAME__),
      &EffectPlatformController::EPUpdateConfig, user_id_str, hardware_str);
}

void MS_API EffectPlatformLoadModels(const char* request_id,
                                     const char* model_name,
                                     MediaSDKStringArray* requirments,
                                     Closure closure) {
  DCHECK(request_id);
  DCHECK(model_name);
  std::string rid = request_id ? std::string(request_id) : std::string();
  std::string name = model_name ? std::string(model_name) : std::string();
  std::vector<std::string> requirments_vector;
  requirments_vector.reserve(requirments->Size());
  for (size_t i = 0; i < requirments->Size(); i++) {
    requirments_vector.emplace_back(requirments->At(i).ToString());
  }
  EffectPlatformControllerProxy::Call(
      FROM_HERE, MSCallbackBool(closure, __FUNCDNAME__),
      &EffectPlatformController::EPLoadModels, requirments_vector, rid, name);
}

}  // namespace mediasdk
