#include "audio_dump_manager.h"

#include "audio/wav_writer.h"

namespace mediasdk {

AudioDumpManager::AudioDumpManager(const std::string& id) : id_(id) {
#ifdef ENABLE_AUDIO_DUMP
  InitDumpFileNames();
#endif  // ENABLE_AUDIO_DUMP
}

AudioDumpManager::~AudioDumpManager() = default;

void AudioDumpManager::InitDumpFileNames() {
  dump_file_name_in_ += "audio_input_in_";
  dump_file_name_in_ += id_;
  dump_file_name_in_ += ".wav";

  dump_file_name_after_mute_ += "audio_input_after_mute_";
  dump_file_name_after_mute_ += id_;
  dump_file_name_after_mute_ += ".wav";

  dump_file_name_after_balance_ += "audio_input_after_balance_";
  dump_file_name_after_balance_ += id_;
  dump_file_name_after_balance_ += ".wav";

  dump_file_name_after_mono_ += "audio_input_after_mono_";
  dump_file_name_after_mono_ += id_;
  dump_file_name_after_mono_ += ".wav";

  dump_file_name_after_filter_ += "audio_input_after_filter_";
  dump_file_name_after_filter_ += id_;
  dump_file_name_after_filter_ += ".wav";

  dump_file_name_after_volume_ += "audio_input_after_volume_";
  dump_file_name_after_volume_ += id_;
  dump_file_name_after_volume_ += ".wav";
}

void AudioDumpManager::DumpAudioToFile(const std::string& path,
                                       const AudioFormat& format,
                                       const AudioFrame& frame) {
#ifdef ENABLE_AUDIO_DUMP
  if (path.find("audio_input_in_") != std::string::npos) {
    if (!dump_audio_in_) {
      dump_audio_in_ = std::make_unique<WAVWriter>();
    }
    if (dump_audio_in_) {
      dump_audio_in_->Write(path, format, frame);
    }
  } else if (path.find("audio_input_after_mute_") != std::string::npos) {
    if (!dump_audio_after_mute_) {
      dump_audio_after_mute_ = std::make_unique<WAVWriter>();
    }
    if (dump_audio_after_mute_) {
      dump_audio_after_mute_->Write(path, format, frame);
    }
  } else if (path.find("audio_input_after_balance_") != std::string::npos) {
    if (!dump_audio_after_balance_) {
      dump_audio_after_balance_ = std::make_unique<WAVWriter>();
    }
    if (dump_audio_after_balance_) {
      dump_audio_after_balance_->Write(path, format, frame);
    }
  } else if (path.find("audio_input_after_mono_") != std::string::npos) {
    if (!dump_audio_after_mono_) {
      dump_audio_after_mono_ = std::make_unique<WAVWriter>();
    }
    if (dump_audio_after_mono_) {
      dump_audio_after_mono_->Write(path, format, frame);
    }
  } else if (path.find("audio_input_after_filter_") != std::string::npos) {
    if (!dump_audio_after_filter_) {
      dump_audio_after_filter_ = std::make_unique<WAVWriter>();
    }
    if (dump_audio_after_filter_) {
      dump_audio_after_filter_->Write(path, format, frame);
    }
  } else if (path.find("audio_input_after_volume_") != std::string::npos) {
    if (!dump_audio_after_volume_) {
      dump_audio_after_volume_ = std::make_unique<WAVWriter>();
    }
    if (dump_audio_after_volume_) {
      dump_audio_after_volume_->Write(path, format, frame);
    }
  }
#endif  // ENABLE_AUDIO_DUMP
}

void AudioDumpManager::DumpInputAudio(const AudioFormat& format,
                                      const AudioFrame& frame) {
#ifdef ENABLE_AUDIO_DUMP
  DumpAudioToFile(dump_file_name_in_, format, frame);
#endif  // ENABLE_AUDIO_DUMP
}

void AudioDumpManager::DumpAfterMute(const AudioFormat& format,
                                     const AudioFrame& frame) {
#ifdef ENABLE_AUDIO_DUMP
  DumpAudioToFile(dump_file_name_after_mute_, format, frame);
#endif  // ENABLE_AUDIO_DUMP
}

void AudioDumpManager::DumpAfterBalance(const AudioFormat& format,
                                        const AudioFrame& frame) {
#ifdef ENABLE_AUDIO_DUMP
  DumpAudioToFile(dump_file_name_after_balance_, format, frame);
#endif  // ENABLE_AUDIO_DUMP
}

void AudioDumpManager::DumpAfterMono(const AudioFormat& format,
                                     const AudioFrame& frame) {
#ifdef ENABLE_AUDIO_DUMP
  DumpAudioToFile(dump_file_name_after_mono_, format, frame);
#endif  // ENABLE_AUDIO_DUMP
}

void AudioDumpManager::DumpAfterFilter(const AudioFormat& format,
                                       const AudioFrame& frame) {
#ifdef ENABLE_AUDIO_DUMP
  DumpAudioToFile(dump_file_name_after_filter_, format, frame);
#endif  // ENABLE_AUDIO_DUMP
}

void AudioDumpManager::DumpAfterVolume(const AudioFormat& format,
                                       const AudioFrame& frame) {
#ifdef ENABLE_AUDIO_DUMP
  DumpAudioToFile(dump_file_name_after_volume_, format, frame);
#endif  // ENABLE_AUDIO_DUMP
}

}  // namespace mediasdk