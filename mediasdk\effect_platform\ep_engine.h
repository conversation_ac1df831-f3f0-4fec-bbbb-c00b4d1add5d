#pragma once

#include <memory>
#include "LokiPlatform.h"
#include "ep_engine_delegate.h"
#include "ep_net_client.h"
#include "mediasdk_thread.h"

namespace mediasdk::ep {

class Engine : public NetClientDelegate,
               public std::enable_shared_from_this<Engine> {
 public:
  typedef char* (*resource_finder)(void* effect_handle,
                                   const char* dirPath,
                                   const char* model_name);
  typedef void (*release_finder)(void* effect_handle);
  typedef void (*platform_finder_resource_releaser)(void* resource);

  typedef struct {
    resource_finder finder;
    release_finder finder_releaser;
    platform_finder_resource_releaser platform_resource_releaser;
  } effect_resource_finder;

  explicit Engine(EngineDelegate* delegate);

  ~Engine();

  Engine(const Engine&) = delete;

  Engine& operator=(const Engine&) = delete;

  bool Initialize(const std::string& json_params);

  void UnInitialize();

  void UpdateUrlParameter(const std::string& key, const std::string& value);

  void LoadModels(const std::vector<std::string>& requirements,
                  const std::string& model_name,
                  const std::string& request_id);

  Engine::effect_resource_finder GetResourceFinder();

 protected:
  struct ModelDownloadHandle
      : public davinci::effectplatform::ResourceFetchCallback<long> {
    explicit ModelDownloadHandle(const std::string& request_id,
                                 std::weak_ptr<ep::Engine> engine)
        : request_id_(request_id), engine_(engine) {}

    void onSuccess(std::shared_ptr<long> result) override;

    void onError(std::string error) override;

    void onProgress(long progress) override;

   private:
    std::string request_id_;
    std::weak_ptr<Engine> engine_;
  };

  // NetClientDelegate:
  const std::map<std::string, std::string>& GetUrlParameter() override;

 private:
  EngineDelegate* delegate_ = nullptr;
  std::map<std::string, std::string> param_map_;
  std::unique_ptr<davinci::effectplatform::loki::LokiPlatform> loki_platform_;
  std::shared_ptr<davinci::effectplatform::loki::LokiPlatformConfig> config_;
  effect_resource_finder finder_{nullptr, nullptr, nullptr};
};

}  // namespace mediasdk::ep
