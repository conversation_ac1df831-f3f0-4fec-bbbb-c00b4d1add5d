#pragma once
#include <string>
#include <unordered_map>
#include <vector>

#include "curl/curl.h"

namespace mediasdk {
class MSURL {
 public:
  MSURL();
  virtual ~MSURL();
  bool Open();
  void Close();
  bool Request(const std::string& url,
               const std::string& field,
               const std::string& method);
  bool Get(const std::string& url, const std::string& field);
  bool Post(const std::string& url, const std::string& field);
  void SetHeaders(const std::unordered_map<std::string, std::string>& headers);
  void SetContentType(const std::string& contentType);
  void SetRedirect(bool redirect);
  void SetHosts(const std::vector<std::string>& hosts);
  CURLcode GetLastError();
  std::vector<uint8_t> GetResponse();

  int GetErrorCode();
  int GetRecvPacketSize();

 private:
  std::string GetDomain(const std::string& uri);
  static size_t Callback(void* ptr, size_t size, size_t byteSize, void*);

 protected:
  std::vector<UINT8> m_response;
  CURL* m_curl;
  CURLcode m_errCode = CURLE_OK;
  std::unordered_map<std::string, std::string> m_headerMap;
  std::vector<std::string> m_hosts;
  std::string m_contentType;
  bool m_redirect = false;
};
}  // namespace mediasdk
