#include "visual_filter_factory.h"

#include <base/check.h>
#include <base/logging.h>
#include <base/mediasdk/thread_safe_deleter.h>
#include <mediasdk/mediasdk_thread.h>

namespace mediasdk {

// static:
std::shared_ptr<VisualFilterFactory> VisualFilterFactory::Create(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info) {
  if (auto factory =
          std::make_shared<VisualFilterFactory>(library, std::move(info));
      factory && factory->Load()) {
    return factory;
  }
  return nullptr;
}

VisualFilterFactory::VisualFilterFactory(base::NativeLibrary library,
                                         std::shared_ptr<PluginInfo> info)
    : SourceFactory(library, info) {}

VisualFilterFactory::~VisualFilterFactory() = default;

std::shared_ptr<VisualFilter> VisualFilterFactory::CreateFilter(
    const std::string& json_params) {
  if (create_func_) {
    if (auto* filter = create_func_(json_params.c_str())) {
      std::shared_ptr<VisualFilter> source(
          filter, base::ThreadSafeClosureDeleter<VisualFilter>(base::BindOnce(
                      &VisualFilterFactory::Destroy, AsWeakPtr())));
      return source;
    }
  }
  return nullptr;
}

void VisualFilterFactory::Destroy(VisualFilter* filter) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (destroy_func_) {
    destroy_func_(filter);
  }
}

bool VisualFilterFactory::Load() {
  create_func_ = reinterpret_cast<CreateVisualFilterFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "CreateVisualFilter"));

  if (!create_func_) {
    LOG(ERROR) << info_->name.data()
               << ": Failed to get function pointer for CreateVisualFilter";
    return false;
  }

  destroy_func_ = reinterpret_cast<DestroyVisualFilterFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "DestroyVisualFilter"));

  if (!destroy_func_) {
    LOG(ERROR) << info_->name.data()
               << ": Failed to get function pointer for DestroyVisualFilter";
    return false;
  }
  return true;
}

}  // namespace mediasdk