#pragma once
#include <memory>

namespace memory_collector {

class MemoryCollector {
 public:
  static std::shared_ptr<MemoryCollector> CreateCollector();

 public:
  MemoryCollector() = default;

  virtual ~MemoryCollector() = default;

  virtual bool TotalMemInMB(int& mem) = 0;

  virtual bool TotalMemUsedInMB(int& mem) = 0;

  virtual bool ProcessMemInMB(int& mem) = 0;

  virtual bool ProcessPageFaultNum(int& num) = 0;
};
}  // namespace memory_collector