#pragma once
#include <memory>
#include "cae_processor.h"
#include "public/plugin/video_encoder_source.h"

namespace mediasdk {

enum class ReconfigReasonType {
  kNet = 0,
  kCae = 1,
};

enum class ReconfigResult {
  kSuccess = 0,
  kInvalidBitrate = 1,
  kEncoderError = 2,
};

class EncoderOptimizerDelegate {
 public:
  virtual void OnTargetBitrate(
      const int bitrate_kbps,
      base::OnceCallback<void(uint32_t, bool)> callback) = 0;

  virtual void OnEncoderOptimizerEvent(const std::string& event) = 0;
};

class EncoderOptimizer : public CaeProcessorDelegate,
                         public base::SupportsWeakPtr<EncoderOptimizer> {
 public:
  EncoderOptimizer(EncoderOptimizerDelegate* delegate,
                   int sink_id,
                   PixelFormat pixel_format,
                   VideoEncoderSourceInputType input_type,
                   const std::string& codec_name,
                   const bool use_cae);

  ~EncoderOptimizer();

  void ResetBitrate();

  bool Start(int width, int height, int bitrate_kbps, int fps, int sink_id);

  bool Stop(int sink_id);

  void OnSharedTexture(std::shared_ptr<SharedVideoEncodeTexture> shared_tex);

  void OnFrame(std::shared_ptr<VideoFrame> frame);

  void ReconfigTargetBitrate(int bitrate_kbps,
                             ReconfigReasonType reason,
                             uint32_t& target_bitrate,
                             bool& need_reconfig);

  void ReCreateCae(int sink_id,
                   PixelFormat pixel_format,
                   VideoEncoderSourceInputType input_type,
                   const std::string& codec_name);

  void ResetCae(int sink_id);

  // CaeProcessorDelegate
  void OnCaeBitrate(int default_bitrate, int bitrate_kbps) override;

  void OnCaeCreated(CaeInitType type, int bitrate, int fps) override;

  void OnCaeStuck() override;

 private:
  void OnCaeReconfigResult(uint32_t bitrate, ReconfigResult result);

 private:
  EncoderOptimizerDelegate* delegate_ = nullptr;
  CaeProcessor* cae_processor_ = nullptr;
  std::atomic_int cae_bitrate_ = -1;
  std::atomic_int net_bitrate_ = -1;
  int default_bitrate_ = -1;
};
}  // namespace mediasdk