#pragma once

#include <stdint.h>
#include <memory>
#include "mediasdk/public/mediasdk_defines_video.h"
#include "mediasdk/public/mediasdk_string.hpp"
#include "mediasdk/public/plugin/plugin_export.h"
#include "mediasdk/public/plugin/plugin_global_proxy.h"
#include "mediasdk/public/video_encoder_config.hpp"
#include "video_encoder_proxy.h"

namespace mediasdk {

#pragma pack(push, 1)

struct VideoEncoderSourceFrame {
  uint8_t* data[4];
  uint32_t line_size[4];
  PixelFormat pixel_format;
  // Non-continuous(VFR), monotonically increasing index count
  int64_t pts_index;
  bool is_force_idr;
};

struct VideoEncoderSourceSharedTexture {
  // Shared texture handle of NV12
  void* shared_handle;
  // Non-continuous(VFR), monotonically increasing index count
  int64_t pts_index;
  bool is_force_idr;
};

enum VideoEncoderSourceInputType {
  kVideoEncoderSourceInputTypeBuffer,
  kVideoEncoderSourceInputTypeTexture
};

enum VideoEncoderSourceType {
  kVideoEncoderSourceTypeSoftWare,
  kVideoEncoderSourceTypeHardWare
};

#pragma pack(pop)

class VideoEncoderSource {
 public:
  virtual ~VideoEncoderSource() {}

  virtual VideoEncoderSourceInputType GetInputType() const = 0;

  virtual VideoEncoderSourceType GetEncoderType() const = 0;

  virtual PixelFormat GetRequestedPixelFormat() const {
    return PixelFormat::kPixelFormatNV12;
  }

  virtual bool Encode(VideoEncoderSourceFrame* frame) = 0;

  virtual bool EncodeTexture(VideoEncoderSourceSharedTexture* frame) = 0;

  virtual uint32_t CurrentTargetBitrate() = 0;

  virtual bool ReconfigBitrate(uint32_t bitrate_kbps) = 0;

  virtual bool SupportReconfigBaseInfo() { return false; }

  virtual bool ReconfigBaseInfo(MSSize size, uint32_t fps) { return false; }

  virtual bool RebuildEncoder(const MSSize& size, uint32_t fps) {
    return false;
  }

  virtual bool Flush() = 0;

  virtual const char* GetName() = 0;

  virtual StreamType GetStreamType() = 0;

  virtual uint32_t GetBFrameCount() = 0;

  virtual std::shared_ptr<VideoEncoderConfig> GetConfig() const = 0;

  virtual void UseGeneratedDts(bool use) { return; }

  virtual void ResetDtsGenerater() { return; }
};

extern "C" PLUGIN_EXPORT VideoEncoderSource* CreateVideoEncoderSource(
    VideoEncoderProxy* proxy,
    const char* json_params);

extern "C" PLUGIN_EXPORT void DestroyVideoEncoderSource(
    VideoEncoderSource* source);

extern "C" PLUGIN_EXPORT bool TestEncoderSessionCountSupported(
    uint32_t count,
    PluginGlobalProxy* global_proxy);
}  // namespace mediasdk
