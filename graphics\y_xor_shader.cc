#include "y_xor_shader.h"
#include <base/logging.h>
#include <array>

using namespace Microsoft::WRL;

namespace graphics {

// https://www.reddit.com/r/gamedev/comments/2j17wk/a_slightly_faster_bufferless_vertex_shader_trick/
static const char* CONST_VERTEX_SHADER() {
  return R"(
struct PS_Pos
{
	float4 pos : SV_POSITION;
};

// common get pos from vid logic

float4 VS_GET_POS_TEX_XY(uint vid)
{
	bool right = vid == 2;
	bool top = vid == 1;
	float x = - 1.0 + (right ? 4.0 : 0.0);
	float y = - 1.0 + (top ? 4.0 : 0.0);
	float u = right ? 2.0 : 0.0;
	float v =  1.0 - (top ? 2.0 : 0.0);
	return float4(x,y,u,v);
}

PS_Pos RAWCXCY_VS_Main(uint vid : SV_VERTEXID)
{
	float4 pos_tex_xy = VS_GET_POS_TEX_XY(vid);

	PS_Pos output;

	output.pos = float4(pos_tex_xy.x, pos_tex_xy.y, 0.0, 1.0);

	return output;
}

)";
}

static const char* CONST_PIXEL_SHADER() {
  return R"(
Texture2D texture0 : register(t0);
Texture2D texture1 : register(t1);
SamplerState tex_sampler : register(s0);

struct PS_Pos
{
	float4 pos : SV_POSITION;
};

float Y8_PS_MAIN(PS_Pos input) : SV_TARGET
{
	float left_y = texture0.Load(int3(input.pos.xy, 0)).x;
    float right_y = texture1.Load(int3(input.pos.xy, 0)).x;
    uint left_val = left_y * 255;
    uint right_val = right_y * 255;
    float ret = ((float)(left_val ^ right_val))/ 255.f;
    return ret;
}

)";
}

bool YXORShader::Init(const std::shared_ptr<Device>& ins) {
  instance_ = ins;
  if (!try_init_) {
    init_suc_ = DoInit();
    try_init_ = true;
  }

  return init_suc_;
  return true;
}

bool YXORShader::DoInit() {
  Device::CompileShaderParam param = {};
  param.ps = CONST_PIXEL_SHADER();
  param.vs = CONST_VERTEX_SHADER();
  param.ps_name = "Y8_PS_MAIN";
  param.vs_name = "RAWCXCY_VS_Main";

  if (!instance_->CompileShader(param)) {
    LOG(ERROR) << "Failed to Compile Shader";
    return false;
  }

  ps_ = param.ps_shader_;
  vs_ = param.vs_shader_;

  return true;
}

YXORShader::~YXORShader() {
  YXORShader::Destroy();
}

void YXORShader::Destroy() {
  if (vs_) {
    vs_.Reset();
  }
  if (ps_) {
    ps_.Reset();
  }
}

void YXORShader::Render(ID3D11ShaderResourceView* views[kMaxVideoPlanes]) {
  GetContext()->IASetVertexBuffers(0, 0, nullptr, nullptr, nullptr);
  GetContext()->IASetIndexBuffer(nullptr, DXGI_FORMAT::DXGI_FORMAT_UNKNOWN, 0);

  for (int32_t i = 0; i < kMaxVideoPlanes; i++) {
    if (!views[i])
      break;
    GetContext()->PSSetShaderResources(i, 1, &views[i]);
  }

  GetContext()->IASetInputLayout(nullptr);

  GetContext()->PSSetShader(ps_.Get(), NULL, 0);

  GetContext()->VSSetShader(vs_.Get(), NULL, 0);

  GetContext()->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);

  GetContext()->Draw(3, 0);  // input only VID
}

ComPtr<ID3D11Device> YXORShader::GetDevice() {
  return instance_->GetDevice();
}

ComPtr<ID3D11DeviceContext> YXORShader::GetContext() {
  return instance_->GetContext();
}
}  // namespace graphics