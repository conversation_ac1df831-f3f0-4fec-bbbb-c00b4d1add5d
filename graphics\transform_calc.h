#pragma once

#include <array>
#include <cmath>
#include "DXSimpleMath.h"
#include "device.h"
#include "transform.h"

namespace graphics {

inline void BuildMatrixs(FLOAT x,
                         FLOAT y,
                         FLOAT z,
                         const XMFLOAT3 lookAt,
                         const XMFLOAT2& vpSize,
                         XMMATRIX& world,
                         XMMATRIX& view,
                         XMMATRIX& projection) {
  XMFLOAT3 up;
  XMFLOAT3 position;
  XMVECTOR upVector;
  XMVECTOR positionVector;
  XMVECTOR lookAtVector;
  XMMATRIX rotationMatrix;
  up.x = 0.0F;
  up.y = 1.0F;
  up.z = 0.0F;
  upVector = XMLoadFloat3(&up);
  position.x = x;
  position.y = y;
  position.z = z;
  positionVector = XMLoadFloat3(&position);

  lookAtVector = XMLoadFloat3(&lookAt);
  rotationMatrix = XMMatrixRotationRollPitchYaw(0.0F, 0.0F, 0.0F);
  lookAtVector = XMVector3TransformCoord(lookAtVector, rotationMatrix);
  upVector = XMVector3TransformCoord(upVector, rotationMatrix);
  lookAtVector = XMVectorAdd(positionVector, lookAtVector);

  world = XMMatrixIdentity();  // WORLD
  projection =
      XMMatrixOrthographicLH(vpSize.x, vpSize.y, 0.1f, 1000.0F);    // ORTHO
  view = XMMatrixLookAtLH(positionVector, lookAtVector, upVector);  // VIEW
}

// trans texture pos to window pos by matrix
XMFLOAT3 ApplyMatrixToPos(FLOAT x, FLOAT y, const Matrix& matrix);

inline XMFLOAT2 GetClipAndScaledSize(const XMFLOAT2& pic_size,
                                     const XMFLOAT4& clip,
                                     const XMFLOAT2& scale) {
  XMFLOAT2 oldsize = pic_size;
  XMFLOAT2 marginSize(clip.x + clip.z, clip.y + clip.w), newSize;
  newSize.x = (marginSize.x > oldsize.x) ? 2 : (oldsize.x - marginSize.x);
  newSize.y = (marginSize.y > oldsize.y) ? 2 : (oldsize.y - marginSize.y);
  newSize *= scale;
  return newSize;
}

inline XMFLOAT4 ConvertRegionToClipRect(const XMFLOAT2& pic_size,
                                        const XMFLOAT4& region) {
  XMFLOAT4 clip, xmfloat4Null(0, 0, 0, 0);
  if (region == xmfloat4Null) {
    clip = xmfloat4Null;
  } else {
    float right, bottom;
    right = pic_size.x - region.x - region.z;
    right = right > 0 ? right : 0;
    bottom = pic_size.y - region.y - region.w;
    bottom = bottom > 0 ? bottom : 0;
    clip = DirectX::XMFLOAT4{region.x, region.y, right, bottom};
  }
  return clip;
}

// for render texture to graphics
inline XMMATRIX BuildMatrixToTextureRender(const XMFLOAT2& pic_size,
                                           const XMFLOAT2& vp_size,
                                           const XMFLOAT2& trans,
                                           const XMFLOAT2& scale,
                                           const XMFLOAT4& clip,
                                           float angle,
                                           const XMFLOAT2& shear = XMFLOAT2_ONE,
                                           const float shear_Angle = 0.f) {
  XMFLOAT2 newsize = GetClipAndScaledSize(pic_size, clip, scale);
  XMMATRIX toPicSize = XMMatrixScaling(newsize.x, newsize.y, 0.0F);
  XMMATRIX translationForRotate =
      XMMatrixTranslation(-newsize.x / 2.F, -newsize.y / 2.F, 0.0F);
  XMMATRIX rotation = XMMatrixRotationZ(
      static_cast<float>(((-angle) * (DirectX::XM_PI / 180.0f))));
  XMFLOAT2 center(vp_size.x / 2.0F, vp_size.y / 2.0F);

  XMMATRIX translationToTopLeft =
      XMMatrixTranslation(-center.x + newsize.x / 2.0F + trans.x,
                          center.y - newsize.y / 2.0F - trans.y, 0.0F);
  /*
  // viewport vertex buffer:
  // [-1,1]    [0,1]   [1,1]
  // [-1,0]    [0,0]   [1,0]
  // [-1,-1]  [0,-1]  [1,-1]
  //
  // texture input vertex
  //             [0,1]   [1,1]
  //             [0,0]   [1,0]
  // 1. to picture size[with clip]
  // 2. move picture center to [0,0]
  // 3. rotation angle
  // 4. shear scale[for group source]
  // 5. move picture to top left
  */

  XMMATRIX view = XMMatrixIdentity();
  XMMATRIX shearScale =
      XMMatrixScaling(shear.x, shear.y, 1.0F) *
      XMMatrixRotationZ(((-shear_Angle) * (DirectX::XM_PI / 180.0)));
  view *= toPicSize * translationForRotate * rotation * shearScale *
          translationToTopLeft;

  // XMFLOAT3 test_pos = ApplyMatrixToPos(0.5f, 0.5f, view);
  // assert(test_pos.x >= 0 && test_pos.x <= vp_size.x && 
  //         test_pos.y >= 0 && test_pos.y <= vp_size.y);
  return view;
}

// get texture rectangle pos
inline void GetWindowPosByTransform(const graphics::Transform& trans,
                                    const XMFLOAT2& pic_size,
                                    const XMFLOAT2& vp_size,
                                    XMFLOAT2& bottom_right_ret,
                                    XMFLOAT2& top_left_ret,
                                    XMFLOAT2& top_right_ret,
                                    XMFLOAT2& bottom_left_ret,
                                    bool rotate = false,
                                    bool shear = false) {
  XMMATRIX matrix = BuildMatrixToTextureRender(
      pic_size, vp_size, trans.GetTranslate(), trans.GetScale(),
      trans.GetClip(), rotate ? trans.GetRotate() : .0f,
      shear ? trans.GetShear() : XMFLOAT2_ONE,
      shear ? trans.GetShearAngle() : .0f);
  auto top_left = ApplyMatrixToPos(0.f, 1.f, matrix);
  auto top_right = ApplyMatrixToPos(1.f, 1.f, matrix);
  auto bottom_right = ApplyMatrixToPos(1.f, 0.f, matrix);
  auto bottom_left = ApplyMatrixToPos(0.f, 0.f, matrix);
  bottom_right_ret = XMFLOAT2{bottom_right.x + vp_size.x / 2.f,
                              vp_size.y / 2.f - bottom_right.y};
  top_left_ret =
      XMFLOAT2{top_left.x + vp_size.x / 2.f, vp_size.y / 2.f - top_left.y};

  top_right_ret =
      XMFLOAT2{top_right.x + vp_size.x / 2.f, vp_size.y / 2.f - top_right.y};
  bottom_left_ret = XMFLOAT2{bottom_left.x + vp_size.x / 2.f,
                             vp_size.y / 2.f - bottom_left.y};
}

inline void RegionOnPreviewWindow(const graphics::Transform& trans,
                                  const XMFLOAT2& pic_size,
                                  const XMFLOAT2& vp_size,
                                  XMFLOAT2& bottom_right_ret,
                                  XMFLOAT2& top_left_ret,
                                  XMFLOAT2& top_right_ret,
                                  XMFLOAT2& bottom_left_ret) {
  GetWindowPosByTransform(trans, pic_size, vp_size, bottom_right_ret,
                          top_left_ret, top_right_ret, bottom_left_ret, true,
                          true);
}

inline XMFLOAT2 GetTopLeftWindowPosByTransform(const graphics::Transform& trans,
                                               const XMFLOAT2& pic_size,
                                               const XMFLOAT2& vp_size,
                                               bool rotate = false,
                                               bool shear = false) {
  XMMATRIX matrix = BuildMatrixToTextureRender(
      pic_size, vp_size, trans.GetTranslate(), trans.GetScale(),
      trans.GetClip(), rotate ? trans.GetRotate() : .0f,
      shear ? trans.GetShear() : XMFLOAT2_ONE,
      shear ? trans.GetShearAngle() : .0f);
  auto top_left = ApplyMatrixToPos(0.f, 1.f, matrix);
  return XMFLOAT2{top_left.x + vp_size.x / 2.f, vp_size.y / 2.f - top_left.y};
}

inline XMFLOAT2 GetTopRightWindowPosByTransform(
    const graphics::Transform& trans,
    const XMFLOAT2& pic_size,
    const XMFLOAT2& vp_size,
    bool rotate = false,
    bool shear = false) {
  XMMATRIX matrix = BuildMatrixToTextureRender(
      pic_size, vp_size, trans.GetTranslate(), trans.GetScale(),
      trans.GetClip(), rotate ? trans.GetRotate() : .0f,
      shear ? trans.GetShear() : XMFLOAT2_ONE,
      shear ? trans.GetShearAngle() : .0f);
  auto top_right = ApplyMatrixToPos(1.f, 1.f, matrix);
  return XMFLOAT2{top_right.x + vp_size.x / 2.f, vp_size.y / 2.f - top_right.y};
}

inline XMFLOAT2 GetBottomLeftWindowPosByTransform(
    const graphics::Transform& trans,
    const XMFLOAT2& pic_size,
    const XMFLOAT2& vp_size,
    bool rotate = false,
    bool shear = false) {
  XMMATRIX matrix = BuildMatrixToTextureRender(
      pic_size, vp_size, trans.GetTranslate(), trans.GetScale(),
      trans.GetClip(), rotate ? trans.GetRotate() : .0f,
      shear ? trans.GetShear() : XMFLOAT2_ONE,
      shear ? trans.GetShearAngle() : .0f);
  auto bottom_left = ApplyMatrixToPos(0.f, 0.f, matrix);
  return XMFLOAT2{bottom_left.x + vp_size.x / 2.f,
                  vp_size.y / 2.f - bottom_left.y};
}

inline XMFLOAT2 GetBottomRightWindowPosByTransform(
    const graphics::Transform& trans,
    const XMFLOAT2& pic_size,
    const XMFLOAT2& vp_size,
    bool rotate = false,
    bool shear = false) {
  XMMATRIX matrix = BuildMatrixToTextureRender(
      pic_size, vp_size, trans.GetTranslate(), trans.GetScale(),
      trans.GetClip(), rotate ? trans.GetRotate() : .0f,
      shear ? trans.GetShear() : XMFLOAT2_ONE,
      shear ? trans.GetShearAngle() : .0f);
  auto bottom_right = ApplyMatrixToPos(1.f, 0.f, matrix);
  return XMFLOAT2{bottom_right.x + vp_size.x / 2.f,
                  vp_size.y / 2.f - bottom_right.y};
}

inline float GetCross(const XMFLOAT2& p1,
                      const XMFLOAT2& p2,
                      const XMFLOAT2& point) {
  return (p2.x - p1.x) * (point.y - p1.y) - (point.x - p1.x) * (p2.y - p1.y);
}

inline bool InRectangle(const XMFLOAT2& point,
                        const DirectX::XMFLOAT4& region) {
  return GetCross(/*top_left*/ XMFLOAT2{region.x, region.y},
                  /*bottom_left*/ XMFLOAT2{region.x, region.w}, point) *
                 GetCross(/*bottom_right*/ XMFLOAT2{region.z, region.w},
                          /*top_right*/ XMFLOAT2{region.z, region.y}, point) >
             0 &&
         GetCross(/*bottom_left*/ XMFLOAT2{region.x, region.w},
                  /*bottom_right*/ XMFLOAT2{region.z, region.w}, point) *
                 GetCross(/*top_right*/ XMFLOAT2{region.z, region.y},
                          /*top_left*/ XMFLOAT2{region.x, region.y}, point) >
             0;
}

inline void BuildCornerMatrix(const graphics::Transform& trans,
                              XMFLOAT2 picSize,
                              XMFLOAT2 vpSize,
                              XMMATRIX cornerMatrix[8],
                              const float corner_size) {
  XMMATRIX matrix = BuildMatrixToTextureRender(
      picSize, vpSize, trans.GetTranslate(), trans.GetScale(), trans.GetClip(),
      trans.GetRotate(), trans.GetShear(), trans.GetShearAngle());
  XMFLOAT2 scale_size = XMFLOAT2_EMPTY;
  auto clip = trans.GetClip();
  scale_size.x = clip.x + clip.z;
  scale_size.y = clip.y + clip.w;
  scale_size.x = (scale_size.x > picSize.x) ? 2 : (picSize.x - scale_size.x);
  scale_size.y = (scale_size.y > picSize.y) ? 2 : (picSize.y - scale_size.y);
  scale_size *= trans.GetScale();
  Matrix scale_matrix = Matrix::CreateScale(corner_size / scale_size.x,
                                            corner_size / scale_size.y, 1.0);
  cornerMatrix[0] =
      scale_matrix *
      Matrix::CreateTranslation(
          0.F, (scale_size.y - corner_size) / scale_size.y, 1.0F) *
      matrix;
  cornerMatrix[1] = scale_matrix *
                    Matrix::CreateTranslation(
                        (scale_size.x / 2 - corner_size / 2.0f) / scale_size.x,
                        (scale_size.y - corner_size) / scale_size.y, 1.0) *
                    matrix;
  cornerMatrix[2] = scale_matrix *
                    Matrix::CreateTranslation(
                        (scale_size.x - corner_size) / scale_size.x,
                        (scale_size.y - corner_size) / scale_size.y, 1.0) *
                    matrix;
  cornerMatrix[3] =
      scale_matrix *
      Matrix::CreateTranslation(
          (scale_size.x - corner_size) / scale_size.x,
          (scale_size.y / 2 - corner_size / 2.0f) / scale_size.y, 1.0) *
      matrix;
  cornerMatrix[4] = scale_matrix *
                    Matrix::CreateTranslation(
                        (scale_size.x - corner_size) / scale_size.x, 0, 1.0) *
                    matrix;
  cornerMatrix[5] =
      scale_matrix *
      Matrix::CreateTranslation(
          (scale_size.x / 2.F - corner_size / 2.0f) / scale_size.x, 0, 1.0) *
      matrix;
  cornerMatrix[6] =
      scale_matrix * Matrix::CreateTranslation(0, 0, 1.0) * matrix;
  cornerMatrix[7] =
      scale_matrix *
      Matrix::CreateTranslation(
          0, (scale_size.y / 2 - corner_size / 2.0f) / scale_size.y, 1.0) *
      matrix;
}

inline DirectX::XMFLOAT4 VertexToEdge(const XMFLOAT2& bottomRight,
                                      const XMFLOAT2& topLeft,
                                      const XMFLOAT2& topRight,
                                      const XMFLOAT2& bottomLeft) {
  DirectX::XMFLOAT2 vertexes[4] = {bottomRight, topLeft, topRight, bottomLeft};
  DirectX::XMFLOAT4 edge = {topLeft.x, topLeft.y, bottomRight.x, bottomRight.y};
  for (int i = 0; i < 4; ++i) {
    auto it = vertexes[i];
    if (edge.x > it.x) {
      edge.x = it.x;
    }
    if (edge.y > it.y) {
      edge.y = it.y;
    }
    if (edge.z < it.x) {
      edge.z = it.x;
    }
    if (edge.w < it.y) {
      edge.w = it.y;
    }
  }
  return edge;
}

inline DirectX::XMFLOAT4 GetWindowPosByTransform(
    const DirectX::XMFLOAT2& window_size,
    const DirectX::XMFLOAT2& size,
    const graphics::Transform& transform) {
  XMFLOAT2 bottomRight, topLeft, topRight, bottomLeft;
  GetWindowPosByTransform(transform, size, window_size, bottomRight, topLeft,
                          topRight, bottomLeft, true, true);
  return VertexToEdge(bottomRight, topLeft, topRight, bottomLeft);
}

constexpr const XMFLOAT2 kCanvasSize = {1920.f, 1080.f};

inline DirectX::XMFLOAT4 GetOriginPosByTransform(
    const DirectX::XMFLOAT2& size,
    const graphics::Transform& transform) {
  return GetWindowPosByTransform(kCanvasSize, size, transform);
}

inline void RegionCornerEdge(const DirectX::XMFLOAT4& region,
                             const float corner_size,
                             DirectX::XMFLOAT4 corners[8]) {
  corners[0] = XMFLOAT4{region.x, region.y, region.x + corner_size,
                        region.y + corner_size};
  corners[1] = XMFLOAT4{(region.x + region.z - corner_size) / 2.0f, region.y,
                        (region.x + region.z + corner_size) / 2.0f,
                        region.y + corner_size};
  corners[2] = XMFLOAT4{region.z - corner_size, region.y, region.z,
                        region.y + corner_size};
  corners[3] = XMFLOAT4{region.x, (region.y + region.w - corner_size) / 2.0f,
                        region.x + corner_size,
                        (region.y + region.w + corner_size) / 2.0f};
  corners[4] = XMFLOAT4{region.z - corner_size,
                        (region.y + region.w - corner_size) / 2.0f, region.z,
                        (region.y + region.w + corner_size) / 2.0f};
  corners[5] = XMFLOAT4{region.x, region.w - corner_size,
                        region.x + corner_size, region.w};
  corners[6] = XMFLOAT4{(region.x + region.z - corner_size) / 2.0f,
                        region.w - corner_size,
                        (region.x + region.z + corner_size) / 2.0f, region.w};
  corners[7] = XMFLOAT4{region.z - corner_size, region.w - corner_size,
                        region.z, region.w};
}

inline void RectangleCornerEdge(const DirectX::XMFLOAT2& window_size,
                                const DirectX::XMFLOAT2& size,
                                const graphics::Transform& trans,
                                const float corner_size,
                                DirectX::XMFLOAT4 corners[8]) {
  XMMATRIX cornerMatrix[8] = {};
  BuildCornerMatrix(trans, size, window_size, cornerMatrix, corner_size);
  for (int i = 0; i < 8; ++i) {
    auto matrix = cornerMatrix[i];
    auto _topLeft = ApplyMatrixToPos(0.f, 1.f, matrix);
    auto _topRight = ApplyMatrixToPos(1.f, 1.f, matrix);
    auto _bottomRight = ApplyMatrixToPos(1.f, 0.f, matrix);
    auto _bottomLeft = ApplyMatrixToPos(0.f, 0.f, matrix);
    auto bottomRight = XMFLOAT2{_bottomRight.x + window_size.x / 2.f,
                                window_size.y / 2.f - _bottomRight.y};
    auto topLeft = XMFLOAT2{_topLeft.x + window_size.x / 2.f,
                            window_size.y / 2.f - _topLeft.y};

    auto topRight = XMFLOAT2{_topRight.x + window_size.x / 2.f,
                             window_size.y / 2.f - _topRight.y};
    auto bottomLeft = XMFLOAT2{_bottomLeft.x + window_size.x / 2.f,
                               window_size.y / 2.f - _bottomLeft.y};
    corners[i] = VertexToEdge(bottomRight, topLeft, topRight, bottomLeft);
  }
  return;
}

// apply pos with matrix
inline static XMFLOAT3 ApplyMatrixToPos(const XMFLOAT3& pos,
                                        const Matrix& matrix) {
  XMFLOAT3 value;
  XMStoreFloat3(
      &value, XMVector3Transform(XMLoadFloat3(&pos), XMLoadFloat4x4(&matrix)));
  return value;
}

inline static XMFLOAT3 ApplyMatrixToPos(FLOAT x,
                                        FLOAT y,
                                        const Matrix& matrix) {
  XMFLOAT3 pos(x, y, 0.F);
  return ApplyMatrixToPos(pos, matrix);
}

}  // namespace graphics
