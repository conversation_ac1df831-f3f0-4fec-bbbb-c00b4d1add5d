#include "audio_performance_store.h"

#include "component_center.h"
#include "data_center/data_center.h"

namespace mediasdk {

AudioPerformanceStoreCalc::AudioPerformanceStoreCalc(
    const std::string& audio_id)
    : audio_input_id_(audio_id) {
  auto* dc = com::GetDataCenter();
  if (dc) {
    dc->GetAudioPerformanceStore().RegisterAudioPerformanceCalc(this);
  }
}

AudioPerformanceStoreCalc::~AudioPerformanceStoreCalc() {
  auto* dc = com::GetDataCenter();
  if (dc) {
    dc->GetAudioPerformanceStore().UnRegisterAudioPerformanceCalc(this);
  }
}

void AudioPerformanceStore::RegisterAudioPerformanceCalc(
    AudioPerformanceStoreCalc* calc) {
  std::lock_guard<std::mutex> lock(calc_map_mtx_);
  if (calc) {
    calc_map_[calc->GetAudioInputCalcId()] = calc;
  }
}

void AudioPerformanceStore::UnRegisterAudioPerformanceCalc(
    AudioPerformanceStoreCalc* calc) {
  std::lock_guard<std::mutex> lock(calc_map_mtx_);
  if (calc) {
    calc_map_.erase(calc->GetAudioInputCalcId());
  }
}

std::pair<bool, MSAudioPerformance> AudioPerformanceStore::GetAudioPerformance(
    const std::string& audio_id) {
  std::lock_guard<std::mutex> lock(calc_map_mtx_);
  auto it = calc_map_.find(audio_id);
  if (it != calc_map_.end() && it->second) {
    return {true, it->second->CalcPerformance()};
  }
  // Return false if the visual ID is not found
  return {false, {}};
}

}  // namespace mediasdk