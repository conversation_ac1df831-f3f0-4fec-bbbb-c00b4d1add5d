
#pragma once

#include <base/native_library.h>
#include <memory>
#include <set>

#include "visual_source_factory.h"

namespace mediasdk {

struct PluginInfo;

class VisualSourceFactoryImpl : public VisualSourceFactory {
 public:
  static std::shared_ptr<VisualSourceFactory> Create(
      base::NativeLibrary library,
      std::shared_ptr<PluginInfo> info);

  VisualSourceFactoryImpl(base::NativeLibrary library,
                          std::shared_ptr<PluginInfo> info);

  ~VisualSourceFactoryImpl();

  std::shared_ptr<VisualSource> CreateSource(
      VisualProxy* proxy,
      const std::string& json_params) override;

  void Destroy(std::shared_ptr<VisualSource> source) override;

  void DestroyAll() override;

  MediaSDKString EnumVideoInput(const std::string& json) override;

  MediaSDKString EnumFormat(const std::string& device_id,
                            int32_t type) override;

  MediaSDKString GetVisualSourceProperty(const std::string& json) override;

  bool SetVisualSourceProperty(const std::string& key,
                               const std::string& json) override;

  std::shared_ptr<PluginInfo> GetInfo() { return info_; }

 private:
  bool Load();

 private:
  typedef VisualSource* (*CreateVisualSourceFunc)(VisualProxy*, const char*);
  typedef void (*DestroyVisualSourceFunc)(VisualSource*);
  typedef MediaSDKStringData (*EnumVideoInputFunc)(const char*);
  typedef MediaSDKStringData (*EnumFormatFunc)(const char*, int32_t);
  typedef MediaSDKStringData (*GetVisualPluginPropertyFunc)(const char*);
  typedef bool (*SetVisualPluginPropertyFunc)(const char*, const char*);

  static std::function<void(VisualSource*)> DestroyWithCheck(
      DestroyVisualSourceFunc);

 private:
  CreateVisualSourceFunc create_func_ = nullptr;
  DestroyVisualSourceFunc destroy_func_ = nullptr;
  EnumVideoInputFunc enum_input_func_ = nullptr;
  EnumFormatFunc enum_format_func_ = nullptr;
  GetVisualPluginPropertyFunc get_visual_source_property_func_ = nullptr;
  SetVisualPluginPropertyFunc set_visual_source_property_func_ = nullptr;

  std::set<std::shared_ptr<VisualSource>> sources_;
};

}  // namespace mediasdk
