#pragma once

#include <DirectXMath.h>
#include <string.h>
#include <map>
#include <mutex>
#include "point_nine.h"
#include "shader.h"
#include "texture_shader.h"
#include "transform.h"

using namespace DirectX;

namespace graphics {
class PointNineShader : public TextureShader {
 public:
  static inline const char* SHADER_ID_STRING = "point_nine_shader";

  static std::shared_ptr<Shader> CreatePointNineShader(const char* type) {
    if (strcmp(type, SHADER_ID_STRING) == 0) {
      return std::make_shared<PointNineShader>();
    }
    return nullptr;
  }

  static void EnumPluginsFunction(void* param, EnumPluginCallBack fpn) {
    fpn(param, ShaderItem{SHADER_ID_STRING,
                          "shader for draw point nine texture to graphics",
                          PointNineShader::CreatePointNineShader});
  }

 public:
  bool Init(const std::shared_ptr<Device>&) override;

  void RenderPointNine(ID3D11ShaderResourceView* view,
                       Microsoft::WRL::ComPtr<ID3D11Buffer>& vertex_buffer,
                       Microsoft::WRL::ComPtr<ID3D11Buffer>& index_buffer,
                       Microsoft::WRL::ComPtr<ID3D11Buffer>& crop_buffer,
                       Microsoft::WRL::ComPtr<ID3D11Buffer>& matrix_buffer,
                       Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_buffer,
                       PointNine point_nine);
  void Destroy() override;
  ~PointNineShader() override;

 private:
  bool DoInit(std::shared_ptr<Device> ins);

 private:
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice_();
  ID3D11DeviceContext* GetContext_();

 private:
  bool try_init_ = false;
  bool init_suc_ = false;
  std::shared_ptr<Device> device_;

  Microsoft::WRL::ComPtr<ID3D11VertexShader> vs_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> remove_border_ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> scale_x_ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> scale_y_ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> remove_border_scale_x_ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> remove_border_scale_y_ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11InputLayout> layout_;
  Microsoft::WRL::ComPtr<ID3D11SamplerState> sampler_;
};
}  // namespace graphics
