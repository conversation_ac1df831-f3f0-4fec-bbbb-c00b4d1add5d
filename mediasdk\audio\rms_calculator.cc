#include "rms_calculator.h"

#include <cmath>
#include "component_center.h"
#include "notify_center.h"
#include "public/mediasdk_audio_status_observer.h"

namespace {

constexpr int64_t kLogVolumeAverageMS = 3000;
constexpr int64_t kLogVolumeMS = 60000;

bool FloatIsZero(float value, float tolerance = FLT_EPSILON) {
  return std::fabs(value) < tolerance;
}

int32_t mul_to_db(float mul) {
  return -(FloatIsZero(mul) ? -120 : (20.0f * log10f(mul)));
}

}  // namespace

namespace mediasdk {

void RMSCalculator::CalculateRMSRaw(const AudioFormat& format,
                                    const AudioFrame& frame) {
  volume_calculate_.RMSRaw(format, frame);
}

bool RMSCalculator::CalculateRMSHandled(const AudioFormat& format,
                                        const AudioFrame& frame) {
  return volume_calculate_.RMSHandled(format, frame);
}

void RMSCalculator::NotifyRMS(const std::string& id,
                              bool is_mute,
                              float volume) {
  auto& rms_dev_data = volume_calculate_.dev.GetRMS();
  auto& rms_handled_data = volume_calculate_.handled.GetRMS();

  auto log_volume = [this, &rms_dev_data, &rms_handled_data, id]() {
    auto now = low_precision_milli_now();
    if (now > last_log_ts_ms_ + kLogVolumeMS) {
      last_log_ts_ms_ = now;
      LOG(INFO) << "[" << id << "] v [" << log_volume_str_buffer_ << "]";
      log_volume_str_buffer_ = "";
    }
    if (now > last_log_buffer_ts_ms_ + kLogVolumeAverageMS) {
      last_log_buffer_ts_ms_ = now;
      if (log_volume_cnt_) {
        auto cur_volume_str = base::StringPrintf(
            "(%d,%d,%d,%d)", volume_buffer_[0] / log_volume_cnt_,
            volume_buffer_[1] / log_volume_cnt_,
            volume_buffer_[2] / log_volume_cnt_,
            volume_buffer_[3] / log_volume_cnt_);
        if (log_volume_str_buffer_.empty()) {
          log_volume_str_buffer_ = cur_volume_str;
        } else {
          log_volume_str_buffer_ += cur_volume_str;
        }
      }
      log_volume_cnt_ = 0;

      volume_buffer_[0] = 0;
      volume_buffer_[1] = 0;
      volume_buffer_[2] = 0;
      volume_buffer_[3] = 0;
    } else {
      log_volume_cnt_++;
      constexpr auto limit_range = [](const int32_t& value) {
        if (value > 60) {
          return 60;
        } else if (value < 0) {
          return 0;
        }
        return value;
      };
      volume_buffer_[0] += (60 - limit_range(mul_to_db(rms_dev_data[0])));
      volume_buffer_[1] += (60 - limit_range(mul_to_db(rms_dev_data[1])));
      volume_buffer_[2] += (60 - limit_range(mul_to_db(rms_handled_data[0])));
      volume_buffer_[3] += (60 - limit_range(mul_to_db(rms_handled_data[1])));
    }
  };

  if (!is_mute && volume > 0.f) {
    log_volume();
  }

  auto* nc = com::GetNotifyCenter();
  if (!nc)
    return;

  nc->AudioEvent()->Notify(FROM_HERE,
                           &MediaSDKAudioStatusObserver::OnAudioPeakLR, id,
                           rms_dev_data.at(0), rms_dev_data.at(1),
                           rms_handled_data.at(0), rms_handled_data.at(1));
}

}  // namespace mediasdk