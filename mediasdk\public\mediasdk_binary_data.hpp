#pragma once

#include <stdint.h>
#include <cstring>
#include "mediasdk_api_alloc.h"

namespace mediasdk {

class MediaSDKBinaryData {
 public:
  MediaSDKBinaryData() {}

  MediaSDKBinaryData(const uint8_t* data, uint32_t size) {
    size_ = size;
    data_ = static_cast<uint8_t*>(AllocBuffer(size_));
    if (data_) {
      memcpy(data_, data, size_);
    }
  }

  MediaSDKBinaryData(MediaSDKBinaryData&& other) noexcept
      : data_(other.data_), size_(other.size_) {
    other.data_ = nullptr;
    other.size_ = 0;
  }

  MediaSDKBinaryData& operator=(MediaSDKBinaryData&& other) noexcept {
    if (this != &other) {
      FreeBuffer(data_);
      data_ = other.data_;
      size_ = other.size_;
      other.data_ = nullptr;
      other.size_ = 0;
    }
    return *this;
  }

  MediaSDKBinaryData(const MediaSDKBinaryData& other) : size_(other.size_) {
    data_ = static_cast<uint8_t*>(AllocBuffer(size_));
    if (data_) {
      memcpy(data_, other.data_, size_);
    }
  }

  MediaSDKBinaryData& operator=(const MediaSDKBinaryData& other) {
    if (this != &other) {
      FreeBuffer(data_);
      size_ = other.size_;
      data_ = static_cast<uint8_t*>(AllocBuffer(size_));
      if (data_) {
        memcpy(data_, other.data_, size_);
      }
    }
    return *this;
  }

  ~MediaSDKBinaryData() {
    FreeBuffer(data_);
    data_ = nullptr;
    size_ = 0;
  }

  uint8_t* data() const { return data_; }

  size_t size() const { return size_; }

  bool empty() const { return size_ == 0; }

 private:
  uint8_t* data_ = nullptr;
  uint32_t size_ = 0;
};

}  // namespace mediasdk
