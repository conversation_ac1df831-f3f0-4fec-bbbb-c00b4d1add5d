#pragma once

#include "mediasdk_defines.h"
#include "mediasdk_defines_stream_error_code.h"
#include "mediasdk_export.h"
#include "mediasdk_string.hpp"

namespace mediasdk {

class MediaSDKStreamStatusObserver {
 public:
  virtual void OnStartStreamResult(MediaSDKString id,
                                   StreamErrorCode error_code) = 0;

  virtual void OnReconnecting(MediaSDKString id) = 0;

  virtual void OnConnected(MediaSDKString id) = 0;

  // Callback when the video config frame(SPS and PPS) is sent successfully,
  // and callback every time the reconnection is successful.
  virtual void OnFirstFrame(MediaSDKString id) = 0;

  virtual void OnStartFallback(MediaSDKString id) = 0;

  virtual void OnFallbackResult(MediaSDKString id, bool success) = 0;

  // error_code represents the reason why the stream is stopped. When it is
  // equal to kStreamSuccess, it means that the business party actively stopped
  // pushing the stream successfully. For other reference error codes
  virtual void OnStreamStopped(MediaSDKString id,
                               StreamErrorCode error_code) = 0;

  virtual void OnEncodeError(mediasdk::MediaSDKString id) = 0;

  virtual void OnEncodeEvent(mediasdk::MediaSDKString id,
                             mediasdk::MediaSDKString json_param) = 0;

  virtual void OnBitrateChange(MediaSDKString id,
                               uint32_t pre_bitrate_kbps,
                               uint32_t bitrate_kbps) = 0;

  // Callback of speed test results, the results are returned in json form.
  // {
  //    "stream_id":"123",
  //    "stream_type":"RTMPQStreamServiceSource",
  //    "result":0,
  //    "failed_reason":0,
  //    "average_transport_bitrate":1000.5,
  //    "prob_rtt":1500,
  //    "prob_bandwidth":1800,
  //    "total_sends":15000,
  //    "total_drops":0,
  //    "total_duration":15000,
  //    "total_send_duration":13000
  // }
  virtual void OnSpeedTestResult(MediaSDKString id,
                                 MediaSDKString speed_test_result) = 0;
};

}  // namespace mediasdk
