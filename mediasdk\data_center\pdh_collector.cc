#include "pdh_collector.h"
#include "base\logging.h"

#pragma comment(lib, "Pdh.lib")

namespace {
constexpr char kProcessorUtility[] =
    "\\Processor Information(_Total)\\% Processor Utility";

constexpr char kProcessorTime[] = "\\Processor(_Total)\\% Processor Time";

}  // namespace

namespace cpu_collector {

bool PdhCollector::Initialize() {
  if (initialized_) {
    return true;
  }
  do {
    if (PdhOpenQueryA(NULL, NULL, &query_) != ERROR_SUCCESS) {
      LOG(ERROR) << "[PdhCollector] PdhOpenQueryA Failed";
      break;
    }

    if (PdhAddEnglishCounterA(query_, kProcessorUtility, NULL,
                              &utility_counter_) != ERROR_SUCCESS ||
        PdhAddEnglishCounterA(query_, kProcessorTime, NULL, &time_counter_) !=
            ERROR_SUCCESS) {
      LOG(ERROR) << "[PdhCollector] PdhAddEnglishCounterA Failed";
      break;
    }

    if (PdhCollectQueryData(query_) != ERROR_SUCCESS) {
      LOG(ERROR) << "[PdhCollector] PdhCollectQueryData Failed";
      break;
    }
    initialized_ = true;
    return true;
  } while (0);
  return false;
}

bool PdhCollector::SystemUsage(double& system_usage, double& cpu_time) {
  if (!initialized_) {
    return false;
  }
  PDH_STATUS status = PdhCollectQueryData(query_);
  if (status != ERROR_SUCCESS) {
    return false;
  }

  bool utility_success = false;
  bool time_success = false;
  PDH_FMT_COUNTERVALUE utility_value;
  PDH_FMT_COUNTERVALUE time_value;
  do {
    status = PdhGetFormattedCounterValue(utility_counter_, PDH_FMT_DOUBLE, NULL,
                                         &utility_value);
    if (status != ERROR_SUCCESS) {
      break;
    }
    system_usage = utility_value.doubleValue;
    utility_success = true;
  } while (0);

  do {
    status = PdhGetFormattedCounterValue(time_counter_, PDH_FMT_DOUBLE, NULL,
                                         &time_value);
    if (status != ERROR_SUCCESS) {
      break;
    }
    cpu_time = time_value.doubleValue;
    time_success = TRUE;
  } while (0);

  return utility_success && time_success;
}

void PdhCollector::Destroy() {
  if (query_) {
    PdhCloseQuery(query_);
    query_ = NULL;
  }
  if (utility_counter_) {
    PdhRemoveCounter(utility_counter_);
    utility_counter_ = NULL;
  }
  if (time_counter_) {
    PdhRemoveCounter(time_counter_);
    time_counter_ = NULL;
  }
  initialized_ = false;
}
}  // namespace cpu_collector