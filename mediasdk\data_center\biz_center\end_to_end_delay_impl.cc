#include "end_to_end_delay_impl.h"
#include "base/logging.h"
#include "base/notreached.h"

namespace mediasdk {

EndtoEndDelayImpl::EndtoEndDelayImpl(const std::string& name) : name_(name) {
  LOG(INFO) << "[EndtoEnd] set e2e delay:" << name << "," << std::hex << this;
}

EndtoEndDelayImpl::~EndtoEndDelayImpl() {
  LOG(INFO) << "[EndtoEnd] rm e2e delay:" << name_ << "," << std::hex << this;
}

void EndtoEndDelayImpl::CollectCostUS(int64_t cost_us) {
  DCHECK(cost_us > 0);
  cost_data_.total_us += cost_us;
  cost_data_.cnt += 1;
}

bool EndtoEndDelayImpl::FillCostData(std::map<std::string, int64_t>& cost_map) {
  if (0 == cost_data_.cnt) {
    // NOTREACHED();
    return false;
  }

  int64_t cost_ms = cost_data_.total_us / 1000 / cost_data_.cnt;
  cost_map.insert_or_assign(name_, cost_ms);
  cost_data_ = EndtoEndDelayCostData();
  return true;
}

EndtoEndDelayImplThreadSafe::EndtoEndDelayImplThreadSafe(
    const std::string& name)
    : name_(name) {}

void EndtoEndDelayImplThreadSafe::CollectCostUS(int64_t cost_us) {
  DCHECK(cost_us > 0);
  std::lock_guard<std::mutex> lg(mutex_);
  cost_data_.total_us += cost_us;
  cost_data_.cnt += 1;
}

bool EndtoEndDelayImplThreadSafe::FillCostData(
    std::map<std::string, int64_t>& cost_map) {
  EndtoEndDelayCostData cost_data;
  {
    std::lock_guard<std::mutex> lg(mutex_);
    cost_data = cost_data_;
    cost_data_ = EndtoEndDelayCostData();
  }
  if (0 == cost_data.cnt) {
    return false;
  }
  int64_t cost_ms = cost_data.total_us / 1000 / cost_data.cnt;
  cost_map.insert_or_assign(name_, cost_ms);
  cost_data_ = EndtoEndDelayCostData();
  return true;
}

}  // namespace mediasdk