#include "texture_shader.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include "DXSimpleMath.h"
#include "pixel_shader.h"
#include "shader.h"
#include "vertex_shader.h"

using namespace Microsoft::WRL;

namespace graphics {

bool TextureShader::Init(const std::shared_ptr<Device>& ins) {
  if (!try_init_) {
    init_suc_ = DoInit(ins);
    try_init_ = true;
  }

  return init_suc_;
}

bool TextureShader::DoInit(const std::shared_ptr<Device>& ins) {
  context_ = ins->GetContext().Get();
  scrgb_ps_shaders_.emplace_back(TONEMAP_INFO{
      REINHARD, "HDR_Reinhard_PS_MAIN", "texture_shader_reinhard_ps", nullptr});
  scrgb_ps_shaders_.emplace_back(
      TONEMAP_INFO{UNCHARTED, "HDR_Uncharted_PS_MAIN",
                   "texture_shader_uncharted_ps", nullptr});
  scrgb_ps_shaders_.emplace_back(TONEMAP_INFO{
      ACES, "HDR_ACES_PS_MAIN", "texture_shader_aces_ps", nullptr});
  device_ = ins;
  Device::CompileShaderParam param = {};
  param.ps = TEXTURE_PIXEL_SHADER();
  param.vs = TEXTURE_VERTEX_SHADER();
  param.ps_name = "SRGB_PS_MAIN";
  param.vs_name = "VS_MAIN";
  D3D11_INPUT_ELEMENT_DESC layout[2];
  layout[0].SemanticName = "POSITION";
  layout[0].SemanticIndex = 0;
  layout[0].Format = DXGI_FORMAT_R32G32B32_FLOAT;
  layout[0].InputSlot = 0;
  layout[0].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
  layout[0].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
  layout[0].InstanceDataStepRate = 0;
  layout[1].SemanticName = "TEXCOORD";
  layout[1].SemanticIndex = 0;
  layout[1].Format = DXGI_FORMAT_R32G32_FLOAT;
  layout[1].InputSlot = 0;
  layout[1].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
  layout[1].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
  layout[1].InstanceDataStepRate = 0;
  param.layout_descs_ = layout;
  param.layout_cnt_ = 2;
  if (!device_->CompileShader(param)) {
    return false;
  }
  vs_shader_ = param.vs_shader_;
  srgb_ps_shader_ = param.ps_shader_;
  param.ps_name = "SRGB_GAMA_PS_MAIN";
  param.vs_name = nullptr;
  if (!device_->CompileShader(param)) {
    return false;
  }
  srgb_gama_ps_shader_ = param.ps_shader_;

  param.ps_name = "Y8_PS_MAIN";
  param.vs_name = nullptr;
  if (!device_->CompileShader(param)) {
    return false;
  }

  y8_ps_shader_ = param.ps_shader_;

  for (auto& it : scrgb_ps_shaders_) {
    param.ps_name = it.method_.c_str();
    param.vs_name = nullptr;

    if (!device_->CompileShader(param)) {
      return false;
    }
    it.ps_shader_ = param.ps_shader_;
  }

  layout_ = param.layout_;
  D3D11SetDebugObjectName(vs_shader_.Get(), "texture_shader_vs");
  for (const auto& it : scrgb_ps_shaders_) {
    if (it.ps_shader_) {
      D3D11SetDebugObjectName(it.ps_shader_.Get(), it.obj_name_.c_str());
    }
  }
  D3D11SetDebugObjectName(srgb_ps_shader_.Get(), "texture_shader_srgb_ps");
  D3D11SetDebugObjectName(layout_.Get(), "texture_shader_layout");
  D3D11_SAMPLER_DESC desc = {};
  desc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
  desc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.MipLODBias = 0.0F;
  desc.MaxAnisotropy = 1;
  desc.ComparisonFunc = D3D11_COMPARISON_ALWAYS;
  desc.BorderColor[0] = 0;
  desc.BorderColor[1] = 0;
  desc.BorderColor[2] = 0;
  desc.BorderColor[3] = 0;
  desc.MinLOD = 0;
  desc.MaxLOD = D3D11_FLOAT32_MAX;
  HRESULT hRes = GetDevice_()->CreateSamplerState(&desc, &sampler_);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateSamplerState(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  D3D11SetDebugObjectName(sampler_.Get(), "texture_shader_sampler");
  desc.Filter = D3D11_FILTER_ANISOTROPIC;
  hRes = GetDevice_()->CreateSamplerState(&desc, &sampler_state2_);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateSamplerState2(%s), retry again",
                                     GetErrorString(hRes).c_str());
    desc.Filter = D3D11_FILTER_ANISOTROPIC;
    hRes = GetDevice_()->CreateSamplerState(&desc, &sampler_state2_);
    if (FAILED(hRes)) {
        LOG(ERROR) << base::StringPrintf("Failed to CreateSamplerState2(%s)",
            GetErrorString(hRes).c_str());
        return false;
    }
  }
  D3D11SetDebugObjectName(sampler_.Get(), "texture_shader_sampler_state2");
  return true;
}

bool TextureShader::CreateInputBuffer(const ComPtr<ID3D11Device>& device,
                                      ComPtr<ID3D11Buffer>& crop_buffer,
                                      ComPtr<ID3D11Buffer>& matrix_buffer,
                                      ComPtr<ID3D11Buffer>& ps_buffer) {
  D3D11_BUFFER_DESC bufferDesc;
  ZeroMemory(&bufferDesc, sizeof(bufferDesc));
  bufferDesc.Usage = D3D11_USAGE_DYNAMIC;
  bufferDesc.ByteWidth = sizeof(MATRIXBUFFER);
  bufferDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
  bufferDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  bufferDesc.MiscFlags = 0;
  bufferDesc.StructureByteStride = 0;
  auto hRes = device->CreateBuffer(&bufferDesc, NULL, &matrix_buffer);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }

  D3D11SetDebugObjectName(matrix_buffer.Get(), "texture_shader_matrixbuffer");

  ZeroMemory(&bufferDesc, sizeof(bufferDesc));
  bufferDesc.Usage = D3D11_USAGE_DYNAMIC;
  bufferDesc.ByteWidth = sizeof(CROPBUFFER);
  bufferDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
  bufferDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  bufferDesc.MiscFlags = 0;
  bufferDesc.StructureByteStride = 0;
  hRes = device->CreateBuffer(&bufferDesc, NULL, &crop_buffer);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(0x%X)", hRes);
    return false;
  }
  D3D11SetDebugObjectName(crop_buffer.Get(), "texture_shader_CROP_INPUT");

  bufferDesc.Usage = D3D11_USAGE_DYNAMIC;
  bufferDesc.ByteWidth = sizeof(PS_BUFFER);
  bufferDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
  bufferDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  hRes = device->CreateBuffer(&bufferDesc, NULL, &ps_buffer);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(0x%X)", hRes);
    return false;
  }
  D3D11SetDebugObjectName(ps_buffer.Get(), "texture_shader_PS_BUFFER");

  return true;
}

void TextureShader::RenderBGRA(ID3D11ShaderResourceView* view,
                               ComPtr<ID3D11Buffer>& vertex_buffer,
                               ComPtr<ID3D11Buffer>& index_buffer,
                               ComPtr<ID3D11Buffer>& crop_input,
                               ComPtr<ID3D11Buffer>& matrix_buffer,
                               ComPtr<ID3D11Buffer>& ps_buffer,
                               DXGI_FORMAT format,
                               TONEMAP_TYPE tonemap_type,
                               bool tone_mapping,
                               SAMPLE_TYPE sample_type) {
  uint32_t stride = sizeof(TextureShader::VERTEXTYPE);
  uint32_t offset = 0;
  auto context = GetContext_();
  context->IASetVertexBuffers(0, 1, vertex_buffer.GetAddressOf(), &stride,
                              &offset);
  context->IASetIndexBuffer(index_buffer.Get(), DXGI_FORMAT_R32_UINT, 0);
  context->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);
  context->VSSetConstantBuffers(0, 1, matrix_buffer.GetAddressOf());
  context->VSSetConstantBuffers(1, 1, crop_input.GetAddressOf());
  context->PSSetConstantBuffers(0, 1, ps_buffer.GetAddressOf());
  context->PSSetShaderResources(0, 1, &view);
  context->IASetInputLayout(layout_.Get());
  context->VSSetShader(vs_shader_.Get(), NULL, 0);
  if (format == DXGI_FORMAT::DXGI_FORMAT_R8_UNORM) {
    context->PSSetShader(y8_ps_shader_.Get(), NULL, 0);
  } else if (format == DXGI_FORMAT::DXGI_FORMAT_R16G16B16A16_FLOAT ||
             format == DXGI_FORMAT_R16G16B16A16_UNORM) {
    if (tone_mapping) {
      for (const auto& it : scrgb_ps_shaders_) {
        if (it.type_ == tonemap_type && it.ps_shader_) {
          context->PSSetShader(it.ps_shader_.Get(), NULL, 0);
          break;
        }
      }
    } else {
      context->PSSetShader(srgb_ps_shader_.Get(), NULL, 0);
    }
  } else if (format == DXGI_FORMAT_R8G8B8A8_UNORM_SRGB ||
             format == DXGI_FORMAT_B8G8R8A8_UNORM_SRGB) {
    context->PSSetShader(srgb_gama_ps_shader_.Get(), NULL, 0);

  } else {
    context->PSSetShader(srgb_ps_shader_.Get(), NULL, 0);
  }
  if (sample_type == LINER) {
    context->PSSetSamplers(0, 1, sampler_.GetAddressOf());
  } else if (sample_type == ANISOTROPIC) {
    context->PSSetSamplers(0, 1, sampler_state2_.GetAddressOf());
  }

  context->DrawIndexed(6, 0, 0);
}

void TextureShader::RenderBGRAWithLuminanceMap(ID3D11ShaderResourceView* view,
                               ID3D11ShaderResourceView* luminanceMapSRV,
                               ComPtr<ID3D11Buffer>& vertex_buffer,
                               ComPtr<ID3D11Buffer>& index_buffer,
                               ComPtr<ID3D11Buffer>& crop_input,
                               ComPtr<ID3D11Buffer>& matrix_buffer,
                               ComPtr<ID3D11Buffer>& ps_buffer,
                               DXGI_FORMAT format,
                               TONEMAP_TYPE tonemap_type,
                               bool tone_mapping,
                               SAMPLE_TYPE sample_type) {
  uint32_t stride = sizeof(TextureShader::VERTEXTYPE);
  uint32_t offset = 0;
  auto context = GetContext_();
  context->IASetVertexBuffers(0, 1, vertex_buffer.GetAddressOf(), &stride,
                              &offset);
  context->IASetIndexBuffer(index_buffer.Get(), DXGI_FORMAT_R32_UINT, 0);
  context->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);
  context->VSSetConstantBuffers(0, 1, matrix_buffer.GetAddressOf());
  context->VSSetConstantBuffers(1, 1, crop_input.GetAddressOf());
  context->PSSetConstantBuffers(0, 1, ps_buffer.GetAddressOf());
  context->PSSetShaderResources(0, 1, &view);
  if (luminanceMapSRV)
  {
      context->PSSetShaderResources(1, 1, &luminanceMapSRV);
  }
  context->IASetInputLayout(layout_.Get());
  context->VSSetShader(vs_shader_.Get(), NULL, 0);
  if (format == DXGI_FORMAT::DXGI_FORMAT_R8_UNORM) {
    context->PSSetShader(y8_ps_shader_.Get(), NULL, 0);
  } else if (format == DXGI_FORMAT::DXGI_FORMAT_R16G16B16A16_FLOAT ||
             format == DXGI_FORMAT_R16G16B16A16_UNORM) {
    if (tone_mapping) {
      for (const auto& it : scrgb_ps_shaders_) {
        if (it.type_ == tonemap_type && it.ps_shader_) {
          context->PSSetShader(it.ps_shader_.Get(), NULL, 0);
          break;
        }
      }
    } else {
      context->PSSetShader(srgb_ps_shader_.Get(), NULL, 0);
    }
  } else if (format == DXGI_FORMAT_R8G8B8A8_UNORM_SRGB ||
             format == DXGI_FORMAT_B8G8R8A8_UNORM_SRGB) {
    context->PSSetShader(srgb_gama_ps_shader_.Get(), NULL, 0);

  } else {
    context->PSSetShader(srgb_ps_shader_.Get(), NULL, 0);
  }
  if (sample_type == LINER) {
    context->PSSetSamplers(0, 1, sampler_.GetAddressOf());
  } else if (sample_type == ANISOTROPIC) {
    context->PSSetSamplers(0, 1, sampler_state2_.GetAddressOf());
  }

  context->DrawIndexed(6, 0, 0);
}

ComPtr<ID3D11Device> TextureShader::GetDevice_() {
  return device_->GetDevice();
}

ID3D11DeviceContext* TextureShader::GetContext_() {
  return context_;
}

void TextureShader::Destroy() {
  if (sampler_) {
    sampler_.Reset();
  }

  if (layout_) {
    layout_.Reset();
  }
  for (auto& it : scrgb_ps_shaders_) {
    it.ps_shader_.Reset();
  }
  if (srgb_ps_shader_) {
    srgb_ps_shader_.Reset();
  }
  if (vs_shader_) {
    vs_shader_.Reset();
  }
  if (srgb_gama_ps_shader_) {
    srgb_gama_ps_shader_.Reset();
  }
}

TextureShader::~TextureShader() {
  TextureShader::Destroy();
}
}  // namespace graphics