#pragma once

#include <Pdh.h>
#include <mutex>
#include <vector>
#include "gpu_collector.h"

namespace gpu_collector {
enum COLLECT_TYPE {
  DEFAULT = 0,
  ENGINE_3D = 1,
  ENGINE_CUDA = 2,
  ENGINE_COPY = 3,
  ENGINE_DECODE = 4,
  ENGINE_ENCODE = 5,
  ENGINE_PROCESSING = 6,
  DEDICATE = 7
};

struct GpuCollectItem {
  COLLECT_TYPE collect_type = DEFAULT;
  int pid = 0;
  GpuDeviceInfo device_info = {};
  double value = 0.0;
  int engine_index = 0;
};

class PdhWrapper {
 public:
  PdhWrapper();

  ~PdhWrapper();

  bool IsInitialized() const { return initialized_; }

  bool CollectData(std::vector<GpuCollectItem>& collect_items);

 private:
  bool Initialize();

  bool ParseBuffer(std::vector<GpuCollectItem>& collect_items);

  HQUERY hquery_ = nullptr;
  HCOUNTER hcounter_ = nullptr;
  HQUERY hmem_query_ = nullptr;
  HCOUNTER hmem_counter_ = nullptr;
  std::vector<char> buffer_ = {};
  std::vector<char> mem_buffer_ = {};
  DWORD item_count_ = 0;
  DWORD mem_item_count_ = 0;
  bool initialized_ = false;
  int64_t last_ts_ = 0L;
  std::mutex collect_mutex_;
};
}  // namespace gpu_collector