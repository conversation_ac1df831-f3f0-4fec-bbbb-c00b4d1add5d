#pragma once
#include <base/logging.h>
#include <functional>
#include <map>
#include <mutex>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include "mediasdk/debug_helper.h"

constexpr size_t MAX_SINK_COUNT = 20;

template <std::size_t N>
inline auto InitName(const char* name) {
  std::array<std::string, N> ret;
  for (int i = 0; i < N; i++) {
    ret[i] = std::string(name) + "_" + std::to_string(i);
  }
  return ret;
}

#define G_CONST_STRING_CAT(a) CAT(CAT(a, _), STRINGS)
#define G_CONST_STRING(n, c) \
  static std::array<std::string, c> G_CONST_STRING_CAT(n) = InitName<c>(#n);

#define G_CONST_GET_STRING(n, i) \
  i < MAX_SINK_COUNT ? G_CONST_STRING_CAT(n)[i].c_str() : ""

#define PROFILER_RENDER(name) ScopedProfilerName profilter_render(#name);
#define PROFILER_COST(name) ScopedProfilerCostName profilter_cost(#name);
#define STRINGIZE(x) STRINGIZE2(x)
#define STRINGIZE2(x) #x
#define LINE_STRING STRINGIZE(__LINE__)
#define CAT_(a, b) a##b
#define CAT(a, b) CAT_(a, b)
#define VARNAME(Var) CAT(Var, __LINE__)

#define PROFILER_RENDER_LINE(name) \
  ScopedProfilerName VARNAME(a)(#name LINE_STRING);

namespace mediasdk {

struct ProfilerDataItem {
  int64_t total_us = 0;
  int64_t cnt = 0;
  int64_t min_ts_us = 0xffffffffll;
  int64_t max_ts_us = -1;
};

struct ProfilerCollectDataItem {
  int64_t median_cost_ = 0;
  int64_t percentile99_cost_ = 0;
  int64_t min_cost_ = 0;
  int64_t max_cost_ = 0;
  int64_t average_cost_ = 0;
};

struct PerfDataCollection {
  PerfDataCollection() = default;

  void Add(int64_t cost) {
    if (!collect_)
      return;
    ++call_cnt_;

    if (const auto it = map_cost_.find(cost); it == map_cost_.end()) {
      map_cost_.insert(std::make_pair(cost, 1));
    } else {
      ++it->second;
    }
  }

  ProfilerCollectDataItem GetOrdered() {
    ProfilerCollectDataItem ret;
    if (map_cost_.empty())
      return ret;

    ret.median_cost_ = -1;
    ret.percentile99_cost_ = -1;

    std::map<int64_t, int> ordered(map_cost_.begin(), map_cost_.end());
    map_cost_.clear();

    int32_t callCntPre = 0;
    int32_t midCnt = call_cnt_ / 2;
    int32_t percentile99 = call_cnt_ * 99 / 100;

    LOG(INFO) << "callCntPre [" << call_cnt_ << "]mid[" << midCnt << "]perc["
              << percentile99;

    ret.min_cost_ = ordered.begin()->first;
    ret.max_cost_ = ordered.rbegin()->first;
    int64_t totalCost = 0;
    for (auto it = ordered.begin(); it != ordered.end(); ++it) {
      totalCost += it->second * it->first;
      callCntPre += it->second;
      if (callCntPre >= midCnt && ret.median_cost_ < 0) {
        ret.median_cost_ = it->first;
      }
      if (callCntPre >= percentile99 && ret.percentile99_cost_ < 0) {
        ret.percentile99_cost_ = it->first;
      }
    }
    ret.average_cost_ = totalCost / call_cnt_;

    return ret;
  }

  bool collect_ = false;
  int32_t call_cnt_ = 0;
  std::unordered_map<int64_t, int32_t> map_cost_;
};
struct ProfilerData;
struct CostProfilerData;

struct CostThresholdParam {
  std::string name;
  int32_t threshold = 0;
};

struct CostProfilerDataItem {
  std::vector<int32_t> cost;
  int64_t last_begin_time_ms = 0;
};

class ProfilerConsumer {
 public:
  using ProcessProfilerData =
      std::function<void(const char*, const ProfilerDataItem&)>;
  using ProcessCostProfilerData =
      std::function<void(const char*, const CostProfilerDataItem&)>;

  ProfilerConsumer();

  static ProfilerConsumer& GetInstance();

  void SetHandler(ProcessProfilerData func);

  void SetCostProfilerHandler(ProcessCostProfilerData func);

  ProfilerData* GetProfiler(const char* name);
  CostProfilerData* GetCostProfiler(const char* name);
  void Calc();
  void CalcCollection();
  void CalcCost();
  void UpdateCostThreshold(const std::vector<CostThresholdParam>&);

  void OnProfilerData(const char* name, const ProfilerDataItem& data);
  void OnProfilerDataCollection(const char* name,
                                const ProfilerCollectDataItem& data);
  void OnCostProfilerData(const char*, const CostProfilerDataItem& data);

 private:
  std::shared_mutex process_profiler_data_mutex_;
  ProcessProfilerData process_profiler_data_;

  std::shared_mutex profiler_data_map_mutex_;
  std::unordered_map<const char*, std::unique_ptr<ProfilerData>>
      profiler_data_map_;

  std::shared_mutex process_cost_profiler_mutex_;
  ProcessCostProfilerData process_cost_profiler_;

  std::shared_mutex cost_profiler_data_map_mutex_;
  std::unordered_map<const char*, std::unique_ptr<CostProfilerData>>
      cost_profiler_data_map_;
  std::unique_ptr<ProfilerData> error_profiler_;
  std::unique_ptr<CostProfilerData> error_cost_profiler_;
};

struct CostProfilerData {
  explicit CostProfilerData(const char* name) : name_(name) {}

  CostProfilerData(const CostProfilerData& data)
      : data_(data.data_), name_(data.name_) {}

  CostProfilerData& operator=(const CostProfilerData& data) {
    if (this != &data) {
      name_ = data.name_;
      data_ = data.data_;
    }
    return *this;
  }

  void BeginTime(const int64_t begin_time_ms) {
    // for detect frozen
    std::lock_guard<std::mutex> lock(lock_data_);
    data_.last_begin_time_ms = begin_time_ms;
  }

  void AddTime(const int64_t time_ms) {
    std::lock_guard<std::mutex> lock(lock_data_);
    if (time_ms < 0)
      return;
    if (time_ms > threshold_ms_) {
      data_.cost.push_back(static_cast<int32_t>(time_ms));
    }
  }

  void Calc() {
    CostProfilerDataItem data;
    {
      std::lock_guard<std::mutex> lock(lock_data_);
      data = data_;
      data_ = {};
    }
    ProfilerConsumer::GetInstance().OnCostProfilerData(name_, data);
  }

  void UpdateThreadsHold(const int32_t val) {
    std::lock_guard<std::mutex> lock(lock_data_);
    threshold_ms_ = val;
  }

  // for test, default to 1000 * / (60 - 3);
  int32_t threshold_ms_ = 1000 / (60 - 3);
  CostProfilerDataItem data_;
  const char* name_;
  std::mutex lock_data_;
};

struct ProfilerData {
  ProfilerData(const ProfilerData& data)
      : data_(data.data_), name_(data.name_) {}

  ProfilerData& operator=(const ProfilerData& data) {
    if (this != &data) {
      name_ = data.name_;
      data_ = data.data_;
    }
    return *this;
  }

  explicit ProfilerData(const char* name) : name_(name) {}

  void AddTime(const int64_t timestamp_us) {
    if (timestamp_us > 10000000) {
      LOG(WARNING) << name_ << " cost [" << timestamp_us / 1000 << "]ms";
    }
    if (timestamp_us < 0)
      return;

    std::lock_guard<std::mutex> lock(lock_data_);

    data_.min_ts_us = std::min(data_.min_ts_us, timestamp_us);
    data_.max_ts_us = std::max(data_.max_ts_us, timestamp_us);

    data_.total_us += timestamp_us;
    ++data_.cnt;

    data_collection_.Add(timestamp_us);
  }

  void Calc() {
    ProfilerDataItem data;
    {
      std::lock_guard<std::mutex> lock(lock_data_);
      data = data_;
      data_ = {};
    }
    ProfilerConsumer::GetInstance().OnProfilerData(name_, data);
  }

  void CalcCollection() {
    ProfilerCollectDataItem data;
    {
      std::lock_guard<std::mutex> lock(lock_data_);
      data = data_collection_.GetOrdered();
    }

    ProfilerConsumer::GetInstance().OnProfilerDataCollection(name_, data);
  }

  PerfDataCollection data_collection_;
  ProfilerDataItem data_;
  const char* name_;
  std::mutex lock_data_;
};

class ScopedProfilerName {
 public:
  explicit ScopedProfilerName(const char* name)
      : name_(name), begin_ts_us_(micro_now()) {}

  ~ScopedProfilerName() {
    if (!name_ || !name_[0]) {
      return;
    }
    ProfilerConsumer::GetInstance().GetProfiler(name_)->AddTime(micro_now() -
                                                                begin_ts_us_);
  }

 private:
  const char* name_ = nullptr;
  const int64_t begin_ts_us_;
};

// collect count
class ScopedProfilerCostName {
 public:
  explicit ScopedProfilerCostName(const char* name)
      : name_(name), begin_ts_ms_(milli_now()) {
    if (!name_ || !name_[0]) {
      return;
    }
    ProfilerConsumer::GetInstance().GetCostProfiler(name_)->BeginTime(
        begin_ts_ms_);
  }

  ~ScopedProfilerCostName() {
    if (!name_ || !name_[0]) {
      return;
    }
    ProfilerConsumer::GetInstance().GetCostProfiler(name_)->AddTime(
        milli_now() - begin_ts_ms_);
  }

 private:
  const char* name_ = nullptr;
  const int64_t begin_ts_ms_;
};
}  // namespace mediasdk
