#include "cpu_collector_win.h"
#include "mediasdk/utils/time_helper.h"

namespace {
constexpr int64_t kQueryInterval = 5000;
}

namespace cpu_collector {

std::shared_ptr<CpuCollector> CpuCollector::CreateCollector() {
  return std::make_shared<CpuCollectorImpl>();
}

CpuCollectorImpl::CpuCollectorImpl()
    : cpu_information_(CpuInformation::Create()) {
  process_collector_.Initialize();
  pdh_collector_.Initialize();
}

double CpuCollectorImpl::ProcessUsage() {
  bool need_collect = false;
  {
    std::lock_guard<std::mutex> lck(mutex_);
    auto now = mediasdk::low_precision_milli_now();
    if (last_query_process_time_ <= 0 ||
        now - last_query_process_time_ > kQueryInterval) {
      last_query_process_time_ = now;
      need_collect = true;
    }
  }
  if (need_collect) {
    last_process_usage_ = process_collector_.ProcessUsage();
  }
  return last_process_usage_;
}

bool CpuCollectorImpl::SystemUsage(double& system_usage, double& cpu_time) {
  bool need_collect = false;
  {
    std::lock_guard<std::mutex> lck(mutex_);
    auto now = mediasdk::low_precision_milli_now();
    if (last_query_system_time_ <= 0 ||
        now - last_query_system_time_ > kQueryInterval) {
      last_query_system_time_ = now;
      need_collect = true;
    }
  }
  system_usage = 0.0;
  cpu_time = 0.0;
  if (need_collect) {
    pdh_collector_.SystemUsage(system_usage, cpu_time);
    last_system_usage_ = system_usage;
    last_cpu_time_ = cpu_time;
  }
  system_usage = last_system_usage_;
  cpu_time = last_cpu_time_;
  return true;
}

bool CpuCollectorImpl::CpuInfo(CpuHardwareInfo& info) {
  if (cpu_information_) {
    info.name = cpu_information_->GetCpuName();
    info.clock_speed = cpu_information_->GetClockSpeed();
    info.num = cpu_information_->GetProcessorNum();
  }
  return false;
}
}  // namespace cpu_collector
