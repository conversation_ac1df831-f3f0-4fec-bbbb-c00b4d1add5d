#include "memory_collector_win.h"

// clang-format off
#include <windows.h>
#include <Psapi.h>

// clang-format on

#include <base/logging.h>
#include "mediasdk/utils/time_helper.h"

namespace {
constexpr int64_t kQueryInterval = 5000;
}

namespace memory_collector {

std::shared_ptr<MemoryCollector> MemoryCollector::CreateCollector() {
  return std::make_shared<MemoryCollectorImpl>();
}

bool MemoryCollectorImpl::TotalMemInMB(int& mem) {
  mem = 0;
  bool need_update = false;
  {
    std::lock_guard<std::mutex> lck(collect_mutex_);
    const auto now = mediasdk::low_precision_milli_now();
    if (last_total_mem_time_ <= 0 ||
        now - last_total_mem_time_ > kQueryInterval) {
      last_total_mem_time_ = now;
      need_update = true;
    }
  }
  if (need_update) {
    MEMORYSTATUSEX memory_status;
    memset(&memory_status, 0, sizeof(MEMORYSTATUSEX));
    memory_status.dwLength = sizeof(MEMORYSTATUSEX);
    ::GlobalMemoryStatusEx(&memory_status);
    last_total_mem_ = static_cast<int>(memory_status.ullTotalPhys / 0x00100000);
  }
  mem = last_total_mem_;
  return true;
}

bool MemoryCollectorImpl::TotalMemUsedInMB(int& mem) {
  mem = 0;
  bool need_update = false;
  {
    std::lock_guard<std::mutex> lck(collect_mutex_);
    const auto now = mediasdk::low_precision_milli_now();
    if (last_total_mem_used_time_ <= 0 ||
        now - last_total_mem_used_time_ > kQueryInterval) {
      last_total_mem_used_time_ = now;
      need_update = true;
    }
  }
  if (need_update) {
    MEMORYSTATUSEX memory_status;
    memset(&memory_status, 0, sizeof(MEMORYSTATUSEX));
    memory_status.dwLength = sizeof(MEMORYSTATUSEX);
    ::GlobalMemoryStatusEx(&memory_status);

    last_total_used_mem_ = static_cast<int>(
        (memory_status.ullTotalPhys - memory_status.ullAvailPhys) / 0x00100000);
  }
  mem = last_total_used_mem_;
  return true;
}

bool MemoryCollectorImpl::ProcessMemInMB(int& mem) {
  mem = 0;
  ProcessQuery();
  mem = last_process_mem_;
  return mem;
}

bool MemoryCollectorImpl::ProcessPageFaultNum(int& num) {
  num = 0;
  ProcessQuery();
  num = last_page_fault_num_ - history_page_fault_num_;
  return true;
}

bool MemoryCollectorImpl::ProcessQuery() {
  bool need_update = false;
  {
    std::lock_guard<std::mutex> lck(collect_mutex_);
    const auto now = mediasdk::low_precision_milli_now();
    if (last_process_mem_time_ <= 0 ||
        now - last_process_mem_time_ > kQueryInterval) {
      last_process_mem_time_ = now;
      need_update = true;
    }
  }

  if (need_update) {
    PROCESS_MEMORY_COUNTERS_EX counters;

    memset(&counters, 0, sizeof(PROCESS_MEMORY_COUNTERS_EX));

    counters.cb = sizeof(PROCESS_MEMORY_COUNTERS_EX);
    ::GetProcessMemoryInfo(
        ::GetCurrentProcess(),
        reinterpret_cast<PPROCESS_MEMORY_COUNTERS>(&counters),
        sizeof(PROCESS_MEMORY_COUNTERS_EX));

    last_process_mem_ =
        static_cast<DWORD>(counters.WorkingSetSize / 0x00100000);
    if (last_page_fault_num_ <= 0) {
      history_page_fault_num_ = counters.PageFaultCount;
    } else {
      history_page_fault_num_ = last_page_fault_num_;
    }
    last_page_fault_num_ = counters.PageFaultCount;
  }
  return true;
}
}  // namespace memory_collector
