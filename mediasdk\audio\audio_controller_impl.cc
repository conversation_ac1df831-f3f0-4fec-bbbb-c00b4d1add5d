#include "audio_controller_impl.h"

#include <base/strings/string_number_conversions.h>
#include <base/strings/sys_string_conversions.h>
#include <mediasdk/audio/audio_frame_observer.h>
#include <mediasdk/audio/audio_input.h>
#include <mediasdk/audio/audio_input_manager.h>
#include <mediasdk/audio/audio_mixer.h>
#include <mediasdk/audio/audio_pump.h>
#include "custom_audio_input.h"
#include "mediasdk/audio/lyrax_audio_input.h"
#include "mediasdk/component_center.h"
#include "mediasdk/public/plugin/audio_input_source.h"
#include "mediasdk/rtc/rtc_controller.h"

namespace mediasdk {

AudioControllerImpl::AudioControllerImpl() = default;

AudioControllerImpl::~AudioControllerImpl() = default;

bool AudioControllerImpl::Initialize() {
  LOG(INFO) << "[AudioControllerImpl] begin to initialize.";
  audio_input_manager_ = std::make_shared<AudioInputManager>();
  audio_mixer_ = std::make_shared<AudioMixer>();
  audio_pump_ = std::make_shared<AudioPump>(audio_input_manager_);
  audio_pump_->AddObserver(audio_mixer_.get());
  audio_pump_->Start();
  LOG(INFO) << "[AudioControllerImpl] initialized.";
  return true;
}

void AudioControllerImpl::Uninitialize() {
  LOG(INFO) << "[AudioControllerImpl] begin to uninitialize.";
  audio_pump_->Stop();
  audio_pump_.reset();
  audio_mixer_.reset();
  audio_input_manager_.reset();
  LOG(INFO) << "[AudioControllerImpl] uninitialized";
}

void AudioControllerImpl::CreateAudioInput(const std::string& id,
                                           const CreateAudioParams& params,
                                           MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  LOG(INFO) << "CreateAudio Input [" << id << "] source ["
            << params.plugin_name.ToString() << "] param ["
            << params.json_params.ToString() << "] to track ["
            << params.track_id << "]";
  if (audio_input_manager_) {
    audio_input_manager_->CreateAudioInput(id, params, std::move(callback));
  }
}

void AudioControllerImpl::CreateLyraxAudioInput(const std::string& id,
                                                const CreateAudioParams& params,
                                                MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  LOG(INFO) << "CreateAudio Input lyrax [" << id << "] source ["
            << params.plugin_name.ToString() << "] param ["
            << params.json_params.ToString() << "] to track ["
            << params.track_id << "]";
  if (audio_input_manager_) {
    audio_input_manager_->CreateLyraxAudioInput(id, params,
                                                std::move(callback));
  }
}

ResultBoolBool AudioControllerImpl::AddAudioInputToTrack(const std::string& id,
                                                         uint32_t track_id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  LOG(INFO) << "AddAudioInputToTrack [" << id << "] to [" << track_id << "]";
  if (audio_input_manager_) {
    return audio_input_manager_->AddAudioInputToTrack(id, track_id);
  }
  return {false, false};
}

ResultBoolBool AudioControllerImpl::RemoveAudioInputFromTrack(
    const std::string& id,
    uint32_t track_id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  LOG(INFO) << "RemoveAudioInputFromTrack [" << id << "] from [" << track_id
            << "]";
  if (audio_input_manager_) {
    return audio_input_manager_->RemoveAudioInputFromTrack(id, track_id);
  }
  return {false, false};
}

std::shared_ptr<AudioInput> AudioControllerImpl::CreateAudioInputWithSource(
    const std::string& id,
    uint32_t track_id,
    std::shared_ptr<AudioInputSource> source) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  DCHECK(source);
  TRACE_ACTION_DURATION(
      TypeToString(trace::TASK_COST_TYPE::CreateVisual_AddAudioInput));
  if (audio_input_manager_) {
    auto audio_input =
        SourceAudioInput::Create(id, audio_input_manager_->GetOutputFormat());
    audio_input->AttachSource(source);
    if (audio_input_manager_->AddAudioInput(track_id, audio_input)) {
      return audio_input;
    }
  }

  return nullptr;
}

void AudioControllerImpl::DestroyAudioInput(const std::string& id,
                                            MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    audio_input_manager_->DestroyAudioInput(id, std::move(callback));
  }
}

bool AudioControllerImpl::SetAudioInputParams(const std::string& id,
                                              const AudioInputParams& param) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      audio_input->SetAudioInputParams(param);
      return true;
    }
  }
  return false;
}

void AudioControllerImpl::DestroyAudioInputFromVisual(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  if (audio_input_manager_) {
    audio_input_manager_->DestroyAudioInputFromVisual(id);
  }
}

void AudioControllerImpl::DestroyMultiAudioInputFromVisual(
    const std::vector<std::string>& ids) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  for (const auto& id : ids) {
    DestroyAudioInputFromVisual(id);
  }
}

void AudioControllerImpl::DestroyAllAudioInputFromVisual() {
  std::vector<std::string> ids;
  const auto audio_input_list = audio_input_manager_->GetAudioInputList();
  for (const auto& audio_input : audio_input_list) {
    if (audio_input && audio_input->IsFromVisual()) {
      ids.push_back(audio_input->GetId());
    }
  }
  DestroyMultiAudioInputFromVisual(ids);
}

bool AudioControllerImpl::SetVolume(const std::string& id, const float volume) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      audio_input->SetVolume(volume);
      return true;
    }
  }
  return false;
}

ResultBoolFloat AudioControllerImpl::GetVolume(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      return {true, audio_input->GetVolume()};
    }
  }
  return {false, 0};
}

bool AudioControllerImpl::SetMute(const std::string& id, const bool mute) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      audio_input->SetMute(mute);
      return true;
    }
  }
  return false;
}

ResultBoolBool AudioControllerImpl::GetMute(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      return {true, audio_input->IsMute()};
    }
  }
  return {false, false};
}

bool AudioControllerImpl::SetDeviceMute(const std::string& id,
                                        const bool mute) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id);
        audio_input && audio_input->source()) {
      audio_input->source()->SetDeviceMute(mute);
      return true;
    }
  }
  return false;
}

ResultBoolBool AudioControllerImpl::GetDeviceMute(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id);
        audio_input && audio_input->source()) {
      return {true, audio_input->source()->GetDeviceMute()};
    }
  }
  return {false, false};
}

bool AudioControllerImpl::SetBalance(const std::string& id, float balance) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      audio_input->SetBalance(balance);
      return true;
    }
  }
  return false;
}

bool AudioControllerImpl::SetAudioInputRenderDeviceID(
    const std::string& audio_input_id,
    const std::string& render_device_id) {
  if (audio_input_manager_) {
    if (const auto audio_input =
            audio_input_manager_->GetAudioInput(audio_input_id)) {
      audio_input->ChangeRenderTargetTo(render_device_id);
      return true;
    }
  }
  return false;
}

ResultBoolFloat AudioControllerImpl::GetBalance(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      return {true, audio_input->GetBalance()};
    }
  }
  return {false, 0};
}

ResultBoolInt32 AudioControllerImpl::GetSyncOffset(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      return {true, audio_input->GetSyncOffset()};
    }
  }
  return {false, 0};
}

bool AudioControllerImpl::SetSyncOffset(const std::string& id,
                                        int32_t sync_offset) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      audio_input->SetSyncOffset(sync_offset);
      return true;
    }
  }
  return false;
}

ResultBoolUint32 AudioControllerImpl::GetMonitorType(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      return {true, audio_input->GetMonitorType()};
    }
  }
  return {false, 0};
}

bool AudioControllerImpl::SetMonitorType(const std::string& id, int32_t type) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      audio_input->SetMonitorType(type);
      return true;
    }
  }
  return false;
}

bool AudioControllerImpl::SetMono(const std::string& id, bool mono) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      audio_input->SetMono(mono);
      return true;
    }
  }
  return false;
}

ResultBoolBool AudioControllerImpl::GetMono(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      return {true, audio_input->IsMono()};
    }
  }
  return {false, false};
}

bool AudioControllerImpl::SetInterval(const std::string& id, int32_t interval) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input = audio_input_manager_->GetAudioInput(id)) {
      audio_input->SetInterval(interval);
      return true;
    }
  }
  return false;
}

void AudioControllerImpl::DestroyAllAudioInput(MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    audio_input_manager_->DestroyAllAudioInput(std::move(callback));
  }
}

MediaSDKString AudioControllerImpl::GetFirstAudio() {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (!audio_input_manager_) {
    return {};
  }

  const auto ids = audio_input_manager_->GetAudioInputIds();
  if (ids.empty()) {
    return {};
  }

  return {ids[0]};
}

void AudioControllerImpl::CreateAudioFilter(
    const std::string& audio_filter_id,
    const std::string& audio_filter_name,
    const std::string& audio_input_id,
    const std::string& json_params,
    MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  if (!audio_input_manager_) {
    std::move(callback).Resolve(false);
    return;
  }
  const auto audio_input = audio_input_manager_->GetAudioInput(audio_input_id);
  if (!audio_input) {
    NOTREACHED()
        << "error audio input id, cannot find corresponding audio input";
    std::move(callback).Resolve(false);
    return;
  }
  LOG(INFO) << "add audio filter[" << audio_filter_name << "] ["
            << audio_filter_id << "] to [" << audio_input_id << "] param ["
            << json_params << "]";
  audio_input->GetAudioFilterChainRef().CreateFilter(
      audio_filter_id, audio_filter_name, json_params, std::move(callback));
}

void AudioControllerImpl::DestroyAudioFilter(const std::string& audio_filter_id,
                                             const std::string& audio_input_id,
                                             MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  LOG(INFO) << "remove filter [" << audio_filter_id << "] from ["
            << audio_input_id << "]";

  if (!audio_input_manager_) {
    std::move(callback).Resolve(false);
    return;
  }
  const auto audio_input = audio_input_manager_->GetAudioInput(audio_input_id);
  if (!audio_input) {
    std::move(callback).Resolve(false);
    return;
  }
  audio_input->GetAudioFilterChainRef().DestroyFilter(audio_filter_id,
                                                      std::move(callback));
}

void AudioControllerImpl::SetAudioFilterEnable(
    const std::string& audio_filter_id,
    const std::string& audio_input_id,
    const bool enable,
    MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input =
            audio_input_manager_->GetAudioInput(audio_input_id)) {
      audio_input->GetAudioFilterChainRef().SetFilterEnable(
          audio_filter_id, enable, std::move(callback));
      return;
    }
    NOTREACHED() << "invalid audio input id";
  }
  std::move(callback).Resolve(false);
}

bool AudioControllerImpl::UpdatePCMAudioDatas(
    const std::string& audio_input_id,
    const std::string& pcm_audio_datas) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input =
            audio_input_manager_->GetAudioInput(audio_input_id);
        audio_input && audio_input->source()) {
      audio_input->source()->Action(pcm_audio_datas.c_str());
      return true;
    }
  }
  return false;
}

void AudioControllerImpl::IsAudioFilterEnable(
    const std::string& audio_filter_id,
    const std::string& audio_input_id,
    MSCallback<ResultBoolBool> callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_input_manager_) {
    if (const auto audio_input =
            audio_input_manager_->GetAudioInput(audio_input_id)) {
      audio_input->GetAudioFilterChainRef().IsFilterEnable(audio_filter_id,
                                                           std::move(callback));
      return;
    }
    NOTREACHED() << "invalid audio input id";
  }
  std::move(callback).Resolve({false, false});
}

void AudioControllerImpl::SetAudioFilterProperty(
    const std::string& audio_filter_id,
    const std::string& audio_input_id,
    const std::string& key,
    const std::string& value,
    MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  if (!audio_input_manager_) {
    std::move(callback).Resolve(false);
    return;
  }
  const auto audio_input = audio_input_manager_->GetAudioInput(audio_input_id);
  if (!audio_input) {
    std::move(callback).Resolve(false);
    return;
  }

  audio_input->GetAudioFilterChainRef().SetFilterProperty(
      audio_filter_id, key, value, std::move(callback));
}

void AudioControllerImpl::GetAudioFilterProperty(
    const std::string& audio_filter_id,
    const std::string& audio_input_id,
    const std::string& key,
    MSCallback<ResultBoolString> callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  if (!audio_input_manager_) {
    std::move(callback).Resolve({false, {}});
    return;
  }
  const auto audio_input = audio_input_manager_->GetAudioInput(audio_input_id);
  if (!audio_input) {
    std::move(callback).Resolve({false, {}});
    return;
  }

  audio_input->GetAudioFilterChainRef().GetFilterProperty(audio_filter_id, key,
                                                          std::move(callback));
}

void AudioControllerImpl::AudioFilterAction(
    const std::string& audio_filter_id,
    const std::string& audio_input_id,
    const std::string& action,
    const std::string& param,
    MSCallback<ResultBoolString> callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  if (!audio_input_manager_) {
    std::move(callback).Resolve({false, {}});
    return;
  }
  const auto audio_input = audio_input_manager_->GetAudioInput(audio_input_id);
  if (!audio_input) {
    std::move(callback).Resolve({false, {}});
    return;
  }

  audio_input->GetAudioFilterChainRef().FilterAction(
      audio_filter_id, action, param, std::move(callback));
}

void AudioControllerImpl::AddMixedAudioObserver(
    uint32_t track_id,
    std::shared_ptr<MixedAudioObserver> observer) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_mixer_) {
    audio_mixer_->AddOutputObserver(track_id, observer);
  }
}

void AudioControllerImpl::RemoveMixedAudioObserver(
    uint32_t track_id,
    std::shared_ptr<MixedAudioObserver> observer) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (audio_mixer_) {
    audio_mixer_->RemoveOutputObserver(track_id, observer);
  }
}

void AudioControllerImpl::AddInputAudioObserver(
    std::string audio_input_id,
    std::shared_ptr<AudioInputFrameObserver> observer) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  const auto audio_input = audio_input_manager_->GetAudioInput(audio_input_id);
  if (!audio_input) {
    NOTREACHED() << "[AudioControllerImpl][AddInputAudioObserver]error audio "
                    "input id, cannot find corresponding audio input";
    return;
  }

  audio_input->AddObserver(observer);
}

void AudioControllerImpl::RemoveInputAudioObserver(
    std::string audio_input_id,
    std::shared_ptr<AudioInputFrameObserver> observer) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  const auto audio_input = audio_input_manager_->GetAudioInput(audio_input_id);
  if (!audio_input) {
    NOTREACHED() << "[AudioControllerImpl][RemoveInputAudioObserver]error "
                    "audio input id, cannot find corresponding audio input";
    return;
  }

  audio_input->RemoveObserver(observer);
}

MediaSDKString AudioControllerImpl::EnumAudioInputsInTrack(uint32_t track_id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  if (audio_input_manager_) {
    return audio_input_manager_->EnumAudioInputsInTrack(track_id);
  }
  return {};
}

std::string AudioControllerImpl::GetRenderTargetDeviceID() {
  std::lock_guard<std::mutex> lock(lock_render_target_);
  return render_target_audio_dev_id_;
}

void AudioControllerImpl::SetRenderTargetDeviceID(const std::string& id) {
  {
    LOG(INFO) << "SetRenderTargetDeviceID [" << id << "]";
    std::lock_guard<std::mutex> lock(lock_render_target_);
    render_target_audio_dev_id_ = id;
  }

  const auto audio_input_list = audio_input_manager_->GetAudioInputList();
  for (const auto& audio_input : audio_input_list) {
    audio_input->ChangeRenderTargetTo(id);
  }
}

bool AudioControllerImpl::SetAudioInputReferenceId(
    const std::string& audio_input_id,
    const std::string& audio_ref_input_id) {
  if (!audio_input_manager_) {
    DCHECK(false);
    LOG(ERROR) << "audio_input_manager empty";
    return false;
  }

  std::shared_ptr<AudioInput> audio =
      audio_input_manager_->GetAudioInput(audio_input_id);
  if (!audio) {
    DCHECK(false);
    LOG(ERROR) << "can not find audio of[" << audio_input_id << "]";
    return false;
  }
  auto lyrax_audio = std::dynamic_pointer_cast<LyraxAudioInput>(audio);
  if (!lyrax_audio) {
    DCHECK(false);
    LOG(ERROR) << "audio [" << audio_input_id << "] is not lyrax";
    return false;
  }
  lyrax_audio->SetRefId(audio_ref_input_id);
  return true;
}

bool AudioControllerImpl::SetAudioInputAECOption(
    const std::string& audio_input_id,
    const bool enable) {
  LOG(INFO) << "set aec[" << audio_input_id << "] enable [" << enable << "]";
  if (!audio_input_manager_) {
    DCHECK(false);
    LOG(ERROR) << "audio_input_manager empty";
    return false;
  }

  std::shared_ptr<AudioInput> audio =
      audio_input_manager_->GetAudioInput(audio_input_id);
  if (!audio) {
    DCHECK(false);
    LOG(ERROR) << "can not find audio of[" << audio_input_id << "]";
    return false;
  }

  auto lyrax_audio = std::dynamic_pointer_cast<LyraxAudioInput>(audio);
  if (!lyrax_audio) {
    DCHECK(false);
    LOG(ERROR) << "audio [" << audio_input_id << "] is not lyrax";
    return false;
  }
  return lyrax_audio->SetAECOption(enable);
}

bool AudioControllerImpl::SetAudioInputANSOption(
    const std::string& audio_input_id,
    const int32_t level) {
  LOG(INFO) << "set ans[" << audio_input_id << "] enable [" << level << "]";
  if (!audio_input_manager_) {
    DCHECK(false);
    LOG(ERROR) << "audio_input_manager empty";
    return false;
  }

  std::shared_ptr<AudioInput> audio =
      audio_input_manager_->GetAudioInput(audio_input_id);
  if (!audio) {
    DCHECK(false);
    LOG(ERROR) << "can not find audio of[" << audio_input_id << "]";
    return false;
  }

  auto lyrax_audio = std::dynamic_pointer_cast<LyraxAudioInput>(audio);
  if (!lyrax_audio) {
    DCHECK(false);
    LOG(ERROR) << "audio [" << audio_input_id << "] is not lyrax";
    return false;
  }
  return lyrax_audio->SetANSOption(level);
}

bool AudioControllerImpl::SetAudioInputRawDataOption(
    const std::string& audio_input_id,
    const int32_t mode) {
  LOG(INFO) << "set raw data option[" << audio_input_id << "] mode [" << mode
            << "]";
  if (!audio_input_manager_) {
    DCHECK(false);
    LOG(ERROR) << "audio_input_manager empty";
    return false;
  }
  if (const auto audio_input =
          audio_input_manager_->GetAudioInput(audio_input_id)) {
    auto lyrax_audio = std::dynamic_pointer_cast<LyraxAudioInput>(audio_input);
    if (!lyrax_audio) {
      DCHECK(false);
      LOG(ERROR) << "audio [" << audio_input_id << "] is not lyrax";
      return false;
    }
    return lyrax_audio->SetRawDataOption(mode);
  } else {
    LOG(ERROR) << "can not find audio of[" << audio_input_id << "]";
    return false;
  }
}

std::shared_ptr<MediaSDKString> AudioControllerImpl::GetAudioInputListInfo() {
  auto ret = std::make_shared<MediaSDKString>();

  nlohmann::json result;
  result["info"] = nlohmann::json::array();
  if (audio_input_manager_) {
    auto li = audio_input_manager_->GetAudioInputList();
    for (const auto& audio_input : li) {
      if (audio_input) {
        nlohmann::json entry;
        entry["id"] = audio_input->GetId();
        result["info"].push_back(entry);
      }
    }
  }
  *ret = result.dump();

  return ret;
}

void AudioControllerImpl::SetAudioTrackDelayMs(uint32_t track_id,
                                               const int64_t ms) {
  if (!audio_input_manager_) {
    DCHECK(false);
    return;
  }
  auto track = audio_input_manager_->GetAudioTrack(track_id);
  if (!track) {
    audio_input_manager_->CheckAudioTrackExisted(track_id);
    track = audio_input_manager_->GetAudioTrack(track_id);
    if (!track) {
      DCHECK(false);
      return;
    }
  }
  LOG(INFO) << "SetTrackDealy [" << track_id << "] delay [" << ms << "] ms";
  track->SetTrackDealy(ms);
}

bool AudioControllerImpl::EnableAudioInputEchoDetection(
    const std::string& audio_input_id,
    const int32_t interval) {
  LOG(INFO) << "enable echo detection[" << audio_input_id << "] interval ["
            << interval << "]";
  if (!audio_input_manager_) {
    DCHECK(false);
    LOG(ERROR) << "audio_input_manager empty";
    return false;
  }
  if (const auto audio_input =
          audio_input_manager_->GetAudioInput(audio_input_id)) {
    auto lyrax_audio = std::dynamic_pointer_cast<LyraxAudioInput>(audio_input);
    if (!lyrax_audio) {
      DCHECK(false);
      LOG(ERROR) << "audio [" << audio_input_id << "] is not lyrax";
      return false;
    }
    return lyrax_audio->EnableEchoDetection(interval);
  } else {
    LOG(ERROR) << "can not find audio of[" << audio_input_id << "]";
    return false;
  }
}

void AudioControllerImpl::CreateCustomAudioInput(
    const std::string& id,
    uint32_t track_id,
    hook_api::CustomAudioInputDelegate* delegate,
    MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  LOG(INFO) << "CreateCustomAudioInput [" << id << "] to track [" << track_id
            << "]";

  if (!audio_input_manager_) {
    std::move(callback).Resolve(false);
    return;
  }

  if (audio_input_manager_->IsAudioInputExist(id)) {
    LOG(ERROR) << "Audio input [" << id << "] already exists";
    std::move(callback).Resolve(false);
    return;
  }

  auto audio_input = CustomAudioInput::Create(
      id, audio_input_manager_->GetOutputFormat(), delegate);

  if (!audio_input) {
    LOG(ERROR) << "Failed to create CustomAudioInput [" << id << "]";
    std::move(callback).Resolve(false);
    return;
  }

  if (!audio_input_manager_->AddAudioInput(track_id, audio_input)) {
    LOG(ERROR) << "Failed to add CustomAudioInput [" << id << "] to track ["
               << track_id << "]";
    std::move(callback).Resolve(false);
    return;
  }

  std::move(callback).Resolve(true);
}

}  // namespace mediasdk
