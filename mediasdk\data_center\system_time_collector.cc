#include "system_time_collector.h"
#include <Windows.h>
#include <stdint.h>

namespace {
int64_t FiletimeToInt(const FILETIME& file_time) {
  return (int64_t)file_time.dwHighDateTime << 32 |
         (int64_t)file_time.dwHighDateTime;
}
}  // namespace

namespace cpu_collector {

double SystemTimeCollector::SystemUsage() {
  double percent = 0.0;
  FILETIME idle;
  FILETIME kernel;
  FILETIME user;
  GetSystemTimes(&idle, &kernel, &user);

  auto idle_time = FiletimeToInt(idle) - cache_time_.idle_time;
  auto kernel_time = FiletimeToInt(kernel) - cache_time_.kernel_time;
  auto user_time = FiletimeToInt(user) - cache_time_.user_time;

  if (kernel_time + user_time != 0) {
    percent = 100.0 * (kernel_time + user_time - idle_time) /
              (kernel_time + user_time);
  }
  cache_time_.idle_time = FiletimeToInt(idle);
  cache_time_.kernel_time = FiletimeToInt(kernel);
  cache_time_.user_time = FiletimeToInt(user);
  return percent;
}

bool SystemTimeCollector::Initialize() {
  FILETIME idle;
  FILETIME kernel;
  FILETIME user;
  GetSystemTimes(&idle, &kernel, &user);
  cache_time_.idle_time = FiletimeToInt(idle);
  cache_time_.kernel_time = FiletimeToInt(kernel);
  cache_time_.user_time = FiletimeToInt(user);
  return true;
}
}  // namespace cpu_collector