#pragma once

#include <stdint.h>
#include "audio_encoder_proxy.h"
#include "mediasdk/public/plugin/plugin_export.h"

namespace mediasdk {

#pragma pack(push, 1)

struct AudioEncoderSourceFrame {
  uint8_t* data[8];
  uint32_t channel_count;
  uint32_t count;
  uint32_t block_size;
  int64_t pts_index;
};

#pragma pack(pop)

class AudioEncoderSource {
 public:
  virtual ~AudioEncoderSource() {}

  virtual AudioFormat GetExpectFormat() = 0;
  virtual int64_t GetExpectFrameLength() = 0;
  virtual bool Encode(AudioEncoderSourceFrame* frame) = 0;

  virtual const char* GetName() = 0;
};

extern "C" PLUGIN_EXPORT AudioEncoderSource* CreateAudioEncoderSource(
    AudioEncoderProxy* proxy,
    const char* json_params);

extern "C" PLUGIN_EXPORT void DestroyAudioEncoderSource(
    AudioEncoderSource* source);

}  // namespace mediasdk
