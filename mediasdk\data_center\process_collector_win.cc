#include "process_collector_win.h"
#include <Windows.h>
#include "base\logging.h"

namespace {
int64_t FiletimeToInt(const FILETIME& file_time) {
  LARGE_INTEGER large_integer;
  large_integer.LowPart = file_time.dwLowDateTime;
  large_integer.HighPart = file_time.dwHighDateTime;
  return large_integer.QuadPart;
}
}  // namespace

namespace cpu_collector {

void ProcessCollector::Initialize() {
  SYSTEM_INFO si;
  GetSystemInfo(&si);
  if (si.dwNumberOfProcessors > 0) {
    processor_num_ = si.dwNumberOfProcessors;
  }

  FILETIME system_time;
  FILETIME user_time;
  FILETIME kernel_time;
  FILETIME dummy;
  GetSystemTimeAsFileTime(&system_time);
  GetProcessTimes(GetCurrentProcess(), &dummy, &dummy, &user_time,
                  &kernel_time);
  cache_time_.system_time = FiletimeToInt(system_time);
  cache_time_.user_time = FiletimeToInt(user_time);
  cache_time_.kernel_time = FiletimeToInt(kernel_time);
}

double ProcessCollector::ProcessUsage() {
  FILETIME system_time;
  FILETIME user_time;
  FILETIME kernel_time;
  FILETIME dummy;

  GetSystemTimeAsFileTime(&system_time);
  GetProcessTimes(GetCurrentProcess(), &dummy, &dummy, &user_time,
                  &kernel_time);

  int64_t system_ts = FiletimeToInt(system_time);
  int64_t user_ts = FiletimeToInt(user_time);
  int64_t kernel_ts = FiletimeToInt(kernel_time);
  double percent = 0.0;
  percent = (double)(user_ts - cache_time_.user_time +
                     (kernel_ts - cache_time_.kernel_time));
  percent /= (double)(system_ts - cache_time_.system_time);
  percent /= (double)processor_num_;

  cache_time_.system_time = system_ts;
  cache_time_.user_time = user_ts;
  cache_time_.kernel_time = kernel_ts;
  return percent * 100.0;
}
}  // namespace cpu_collector
