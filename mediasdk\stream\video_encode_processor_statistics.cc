#include "video_encode_processor_statistics.h"

#include <cmath>
#include "base/logging.h"

namespace mediasdk {

VideoEncodeProcessorStatistics::VideoEncodeProcessorStatistics() {
  Reset();
}

void VideoEncodeProcessorStatistics::OnInputFrame() {
  const int64_t current_time_us = micro_now();

  int64_t expected = 0;
  first_input_time_us_.compare_exchange_strong(expected, current_time_us);

  input_frame_count_.fetch_add(1);
}

void VideoEncodeProcessorStatistics::OnOutputFrame() {
  const int64_t current_time_us = micro_now();

  int64_t expected = 0;
  first_output_time_us_.compare_exchange_strong(expected, current_time_us);

  output_frame_count_.fetch_add(1);
}

std::pair<int, int> VideoEncodeProcessorStatistics::PopCurrentFrameRate() {
  std::lock_guard<std::mutex> lock(calculation_mutex_);

  const int64_t current_time_us = micro_now();

  int input_frame_rate = 0;
  const int64_t first_input = first_input_time_us_.load();
  if (first_input > 0) {
    const uint32_t input_count = input_frame_count_.load();
    const int64_t total_time_us = current_time_us - first_input;
    input_frame_rate = CalculateFrameRate(input_count, total_time_us);
  }

  int output_frame_rate = 0;
  const int64_t first_output = first_output_time_us_.load();
  if (first_output > 0) {
    const uint32_t output_count = output_frame_count_.load();
    const int64_t total_time_us = current_time_us - first_output;
    output_frame_rate = CalculateFrameRate(output_count, total_time_us);
  }

  // Reset counters for next calculation
  Reset();

  return std::make_pair(input_frame_rate, output_frame_rate);
}

void VideoEncodeProcessorStatistics::Reset() {
  input_frame_count_.store(0);
  output_frame_count_.store(0);
  first_input_time_us_.store(0);
  first_output_time_us_.store(0);
}

int VideoEncodeProcessorStatistics::CalculateFrameRate(uint32_t frame_count,
                                                       int64_t total_time_us) {
  if (total_time_us <= 0 || frame_count == 0) {
    return 0;
  }

  const float time_window_seconds =
      static_cast<float>(total_time_us) / 1000000.0f;

  const float frame_rate =
      static_cast<float>(frame_count) / time_window_seconds;

  return static_cast<int>(std::round(frame_rate));
}

}  // namespace mediasdk