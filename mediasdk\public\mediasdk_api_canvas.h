#pragma once
#include "mediasdk_callback_defines.h"
#include "mediasdk_canvas_item_event_observer.h"
#include "mediasdk_defines_canvas.h"
#include "mediasdk_defines_visual.h"
#include "mediasdk_defines_visual_filter.h"
#include "mediasdk_export.h"
#include "mediasdk_virtual_camera_event_observer.h"

#ifdef __cplusplus
extern "C" {
#endif

namespace mediasdk {

MEDIASDK_EXPORT void MS_API CreateCanvas(const char* canvas_id,
                                         uint32_t video_model_id,
                                         Closure closure);

MEDIASDK_EXPORT void MS_API DestroyCanvas(const char* canvas_id,
                                          Closure closure);

MEDIASDK_EXPORT void MS_API GetCurrentCanvas(uint32_t video_model_id,
                                             Closure closure);

// Current canvas_id -- transit to --> canvas_id
MEDIASDK_EXPORT void MS_API SetCurrentCanvas(uint32_t video_model_id,
                                             const char* canvas_id,
                                             const char* transition_id,
                                             Closure closure);

MEDIASDK_EXPORT void MS_API
CreateCanvasItem(const char* canvas_item_id,
                 const char* canvas_id,
                 const char* visual_id,
                 const CreateCanvasItemParams& canvas_params,
                 Closure closure);

MEDIASDK_EXPORT void MS_API
CreateCanvasItemWithFilter(const char* canvas_item_id,
                           const char* canvas_id,
                           const char* visual_id,
                           const CreateCanvasItemParams& params,
                           const char* filter_id,
                           const CreateVisualFilterParams& filter_params,
                           Closure closure);

MEDIASDK_EXPORT void MS_API CreateVisualThenCreateCanvasItemWithFilter(
    const char* visual_id,
    const CreateVisualParams& visual_params,
    const char* canvas_item_id,
    const char* canvas_id,
    const CreateCanvasItemParams& item_params,
    const char* filter_id,
    const CreateVisualFilterParams& filter_params,
    Closure closure);

MEDIASDK_EXPORT void MS_API DestroyCanvasItem(const char* canvas_item_id,
                                              Closure closure);

// Asynchronous callback result, MediaSDKString
MEDIASDK_EXPORT void MS_API GetVisualFromCanvasItem(const char* canvas_item_id,
                                                    Closure closure);

// Register an object named MediaSDKCanvasEventObserver to SDK, When event
// happened it will notify Ensure observer is valid before you call the
// UnregisterCanvasEventObserver return Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
RegisterCanvasEventObserver(MediaSDKCanvasEventObserver* observer,
                            Closure closure);

// Unregister observer to the SDK
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
UnregisterCanvasEventObserver(MediaSDKCanvasEventObserver* observer,
                              Closure closure);

// Change CanvasItem ref new Visual with new_transform
// if new_transform is null, use old transform
// canvas item id is null, will return false
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
CanvasItemChangeVisual(const char* canvas_item_id,
                       const char* visual_id,
                       const MSTransform* new_transform,
                       Closure closure);

// Set need draw border to canvas item by canvas_item_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
SetCanvasItemNeedDrawBorder(const char* canvas_item_id,
                            bool border,
                            Closure closure);

// Set always top of canvas items to one specific canvas item by canvas_item_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemAlwaysTop(const char* canvas_item_id,
                                                   bool always_top,
                                                   Closure closure);

MEDIASDK_EXPORT void MS_API SetCanvasItemAvoidOutput(const char* canvas_item_id,
                                                     bool avoid,
                                                     Closure closure);

// Begin to clip the canvas item by canvas_item_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API BeginCanvasItemClip(const char* canvas_item_id,
                                                Closure closure);

// Get the canvas item can flip_h by canvas_item_id
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API GetCanvasItemFlipH(const char* canvas_item_id,
                                               Closure closure);

// Set the canvas item flip_h by canvas_item_id, flip is flags need true or
// false Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemFlipH(const char* canvas_item_id,
                                               bool flip,
                                               Closure closure);

// Get the canvas item can flip_v by canvas_item_id
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API GetCanvasItemFlipV(const char* canvas_item_id,
                                               Closure closure);

// Set the canvas item flip_v by canvas_item_id, flip is flags need true or
// false Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemFlipV(const char* canvas_item_id,
                                               bool flip,
                                               Closure closure);

// Set the canvas item move range by canvas_item_id, MSClipF is range
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemMoveRange(const char* canvas_item_id,
                                                   const MSClipF& clip,
                                                   Closure closure);

// Get the canvas item move range by canvas_item_id
// Asynchronous callback result, ResultBoolMSClipF
MEDIASDK_EXPORT void MS_API GetCanvasItemMoveRange(const char* canvas_item_id,
                                                   Closure closure);

// Get the canvas item rotate by canvas_item_id
// Asynchronous callback result, ResultBoolFloat
MEDIASDK_EXPORT void MS_API GetCanvasItemRotate(const char* canvas_item_id,
                                                Closure closure);

// Set the canvas item rotate by canvas_item_id and rotate
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemRotate(const char* canvas_item_id,
                                                float rotate,
                                                Closure closure);

// Set the canvas item highlight by canvas_item_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemHighlight(uint32_t video_model_id,
                                                   const char* canvas_item_id,
                                                   Closure closure);

// Set the canvas item scale by canvas_item_id , MSScaleF is scale
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemScale(const char* canvas_item_id,
                                               const MSScaleF& scale,
                                               Closure closure);

// Get the canvas item scale by canvas_item_id
// Asynchronous callback result, ResultBoolMSScaleF
MEDIASDK_EXPORT void MS_API GetCanvasItemScale(const char* canvas_item_id,
                                               Closure closure);

// Get the canvas item min scale by canvas_item_id
// Asynchronous callback result, ResultBoolMSScaleF
MEDIASDK_EXPORT void MS_API GetCanvasItemMinScale(const char* canvas_item_id,
                                                  Closure closure);

// Set the canvas item min scale by canvas_item_id , MSScaleF is scale
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemMinScale(const char* canvas_item_id,
                                                  const MSScaleF& scale,
                                                  Closure closure);

// Get the canvas item max scale by canvas_item_id
// Asynchronous callback result, ResultBoolMSScaleF
MEDIASDK_EXPORT void MS_API GetCanvasItemMaxScale(const char* canvas_item_id,
                                                  Closure closure);

// Set the canvas item max scale by canvas_item_id , MSScaleF is scale
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemMaxScale(const char* canvas_item_id,
                                                  const MSScaleF& scale,
                                                  Closure closure);

// Lock the canvas item by canvas_item_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API LockCanvasItem(const char* canvas_item_id,
                                           Closure closure);

// Unlock the canvas item by canvas_item_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API UnLockCanvasItem(const char* canvas_item_id,
                                             Closure closure);

// Test if canvas item named canvas_item_id is locked
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API IsCanvasItemLocked(const char* canvas_item_id,
                                               Closure closure);

// Set the canvas item to enter or leave editable mode by canvas_item_id
// while canvas item enters editing mode, it no longer responds to dragging and
// stretching actions, and all mouse and keyboard events will be transmitted to
// the video source plugin of current canvas item until it leaves edit mode or
// is been removed. Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemEditable(const char* canvas_item_id,
                                                  bool editable,
                                                  Closure closure);

// Get the canvas item is editable by canvas_item_id
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API GetCanvasItemEditable(const char* canvas_item_id,
                                                  Closure closure);

// Set the canvas item translate by canvas_item_id , MSTranslateF is translate
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
SetCanvasItemTranslate(const char* canvas_item_id,
                       const MSTranslateF& translate,
                       Closure closure);

// Get the canvas item translate by canvas_item_id
// Asynchronous callback result, ResultBoolMSTranslateF
MEDIASDK_EXPORT void MS_API GetCanvasItemTranslate(const char* canvas_item_id,
                                                   Closure closure);

// Get the canvas item size by canvas_item_id
// Asynchronous callback result, ResultBoolMSSizeF
MEDIASDK_EXPORT void MS_API GetCanvasItemSizeF(const char* canvas_item_id,
                                               Closure closure);

// Get the canvas item clip by canvas_item_id
// Asynchronous callback result, ResultBoolMSClipF
MEDIASDK_EXPORT void MS_API GetCanvasItemClip(const char* canvas_item_id,
                                              Closure closure);

// Set the canvas item clip by canvas_item_id , MSClipF is clip
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemClip(const char* canvas_item_id,
                                              const MSClipF& clip,
                                              Closure closure);

// Test if canvas item named canvas_item_id is clipping
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API IsCanvasItemClip(const char* canvas_item_id,
                                             Closure closure);

// Enable the canvas item clip by canvas_item_id , bool is enable
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API EnableCanvasItemClip(const char* canvas_item_id,
                                                 bool enable,
                                                 Closure closure);

// Set the canvas item transform by canvas_item_id , MSTransform is transform
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemTransform(const char* canvas_item_id,
                                                   const MSTransform& transform,
                                                   Closure closure);

// Get the canvas item transform by canvas_item_id
// Asynchronous callback result, ResultBoolMSTransform
MEDIASDK_EXPORT void MS_API GetCanvasItemTransform(const char* canvas_item_id,
                                                   Closure closure);

// Move the canvas item z-order by canvas_item_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API MoveCanvasItemZOrder(const char* canvas_item_id,
                                                 MovePostion pos,
                                                 Closure closure);

// Get the order of canvas item in current showned canvas on video model
// Asynchronous callback result
// result is MediaSDKStringArray
MEDIASDK_EXPORT void MS_API
GetCanvasItemOrderIDSOnVideoModel(uint32_t video_model_id, Closure closure);

// Set the order of canvas item in current canvas showed on video model
// The order is MediaSDKStringArray
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
SetCanvasItemByOrderIDSOnVideoModel(uint32_t video_model_id,
                                    const MediaSDKStringArray& array,
                                    Closure closure);

// Get the order of canvas item in canvas of canvas_id
// Asynchronous callback result
// result is MediaSDKStringArray
MEDIASDK_EXPORT void MS_API GetCanvasItemOrderIDS(const char* canvas_id,
                                                  Closure closure);

// Set the order of canvas item in canvas of canvas_id and the order is
// MediaSDKStringArray
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
SetCanvasItemByOrderIDS(const char* canvas_id,
                        const MediaSDKStringArray& array,
                        Closure closure);

// Set the visual to visible by canvas_item_id , bool is visible
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemVisible(const char* canvas_item_id,
                                                 bool visible,
                                                 Closure closure);

// Get the visual is visible by canvas_item_id
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API GetCanvasItemVisible(const char* canvas_item_id,
                                                 Closure closure);

MEDIASDK_EXPORT void MS_API
SetCurrentCanvasItemOnVideoModel(uint32_t video_model_id,
                                 const char* canvas_item_id,
                                 Closure closure);

// Set current select visual in canvas_item_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCurrentCanvasItem(const char* canvas_id,
                                                 const char* canvas_item_id,
                                                 Closure closure);

// Get current select canvas item in canvas_item_id
// Asynchronous callback result, MediaSDKString
MEDIASDK_EXPORT void MS_API GetCurrentCanvasItem(const char* canvas_item_id,
                                                 Closure closure);

// Get current select canvas item of canvas using on VideoModel
// Asynchronous callback result, MediaSDKString
MEDIASDK_EXPORT void MS_API GetCurrentCanvasItemOnVideoModel(uint32_t sink_id,
                                                             Closure closure);

// Starts a canvas item preview by id
// |parent_wnd|: Handle to the parent window of the preview. The style of the
// parent window must exclude WS_CLIPSIBLINGS and include WS_CLIPCHILDREN
// |region|: Defines the region of the preview.
// |fill_type|: 0: kDisplayTypeRatioFill,1: kDisplayTypeTileFill, 2:
// kDisplayTypeClipFill |flip_h|: If set to true, the preview is horizontally
// flipped. |flip_v|: If set to true, the preview is vertically flipped.
// |angle|: The rotation angle of the preview in degrees.
// |bk_color|: Represents the background color of the preview. The color can be
//    calculated using:
//    function rgbToInt(r, g, b) { return ((r << 16) | (g << 8) | b); }.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API StartCanvasItemPreview(const char* canvas_item_id,
                                                   const char* preview_id,
                                                   uint64_t parent_wnd,
                                                   MSRect region,
                                                   int fill_type,
                                                   bool flip_h,
                                                   bool flip_v,
                                                   float angle,
                                                   int32_t bk_color,
                                                   const WndParams& params,
                                                   Closure closure);

// Stops the canvas item preview by id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API StopCanvasItemPreview(const char* preview_id,
                                                  Closure closure);

// Sets the position of the canvas item preview by id
// |rect|: New position for the preview.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemPreviewPos(const char* preview_id,
                                                    const MSRect& rect,
                                                    Closure closure);

// Flips the canvas item preview horizontally by id
// |flip_h|: If set to true, the preview is horizontally flipped.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemPreviewFlipH(const char* preview_id,
                                                      bool flip_h,
                                                      Closure closure);

// Flips the canvas item preview horizontally by id
// |flip_v|: If set to true, the preview is vertically flipped.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemPreviewFlipV(const char* preview_id,
                                                      bool flip_v,
                                                      Closure closure);

// Rotates the canvas item preview by id
// |rotate|: The rotation angle of the preview in degrees.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemPreviewRotate(const char* preview_id,
                                                       float rotate,
                                                       Closure closure);

// Set the WndParams for preview window by preview_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemPreviewParams(const char* preview_id,
                                                       const WndParams& params,
                                                       Closure closure);

// Register an object named MediaSDKCanvasEventObserver to SDK, When event
// happened it will notify Ensure observer is valid before you call the
// UnregisterCanvasEventObserver return Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
RegisterVirtualCameraEventObserver(MediaSDKVirtualCameraEventObserver* observer,
                                   Closure closure);

// Unregister observer to the SDK
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API UnregisterVirtualCameraEventObserver(
    MediaSDKVirtualCameraEventObserver* observer,
    Closure closure);

// Start virtual camera. The virtual camera is unique throughout the entire
// program.
// |canvas_item_id|: Output the frame corresponding to this canvas item to the
// virtual camera.
// |size|: The size of the virtual camera output frame
// |bg_color|: The background color of the virtual camera output frame
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
StartVirtualCamera(const char* canvas_item_id,
                   MSSize size,
                   uint32_t bg_color,
                   VirtualCameraObjectFitMode fit_mode,
                   Closure closure);

// Stop virtual camera output.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API StopVirtualCamera(Closure closure);

// |canvas_item_id|:  Switch the output to the virtual camera to this canvas
// item. Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SwitchVirtualCamera(const char* canvas_item_id,
                                                Closure closure);

// Update the virtual camera property.
// |size|: The size of the virtual camera output frame
// |bg_color|: The background color of the virtual camera output frame
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API UpdateVirtualCameraProperty(MSSize size,
                                                        uint32_t bg_color,
                                                        Closure closure);

// Set the virtual camera Rotate.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetVirtualCameraRotate(float rotate,
                                                   Closure closure);

// Set the virtual camera flip_v.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetVirtualCameraFlipV(bool flip_v, Closure closure);

// Set the virtual camera flip_h.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetVirtualCameraFlipH(bool flip_h, Closure closure);

// Set the enable for canvas_item pre process
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemPreprocess(const char* canvas_item_id,
                                                    bool enable,
                                                    Closure closure);

// Get the is enabled for canvas_item pre process
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API GetCanvasItemPreprocess(const char* canvas_item_id,
                                                    Closure closure);
// Set the new based size for canvas item pre process
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
SetCanvasItemPreprocessDefaultSize(const char* id,
                                   const MSSize& size,
                                   Closure closure);

// Remove the setted based size for canvas item pre process
MEDIASDK_EXPORT void MS_API
RemoveCanvasItemPreprocessDefaultSize(const char* id, Closure closure);

// Set prepare transform for canvas_item
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetCanvasItemSourceClip(const char* canvas_item_id,
                                                    const MSClipF& clip,
                                                    Closure closure);

// Save the canvas_item picture by canvas_item_id to file by file_path
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API CanvasItemSaveAsFile(const char* canvas_item_id,
                                                 const char* file_path,
                                                 ImageFileFormat format,
                                                 Closure closure);

MEDIASDK_EXPORT void MS_API
CreateTransition(const char* transition_id,
                 const CreateTransitionParams& transition_params,
                 Closure closure);

MEDIASDK_EXPORT void MS_API DestroyTransition(const char* transition_id,
                                              Closure closure);

MEDIASDK_EXPORT void MS_API SetTransitionProperty(const char* transition_id,
                                                  const char* property,
                                                  Closure closure);

}  // namespace mediasdk

#ifdef __cplusplus
}
#endif  // __cplusplus