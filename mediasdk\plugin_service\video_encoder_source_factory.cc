#include "video_encoder_source_factory.h"

#include <base/logging.h>
#include <mediasdk/mediasdk_thread.h>
#include "base/debug/stack_trace.h"
#include "base/mediasdk/thread_safe_deleter.h"

namespace mediasdk {

// static
std::shared_ptr<VideoEncoderSourceFactory> VideoEncoderSourceFactory::Create(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info,
    PluginGlobalProxy* plugin_globale_proxy) {
  auto factory = std::make_shared<VideoEncoderSourceFactory>(
      library, info, plugin_globale_proxy);
  if (factory && factory->Load()) {
    return factory;
  }

  return nullptr;
}

VideoEncoderSourceFactory::VideoEncoderSourceFactory(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info,
    PluginGlobalProxy* plugin_globale_proxy)
    : SourceFactory(library, info), plugin_global_proxy_(plugin_globale_proxy) {
  DCHECK(library_);
  DCHECK(info_);
}

VideoEncoderSourceFactory::~VideoEncoderSourceFactory() {}

std::shared_ptr<VideoEncoderSource> VideoEncoderSourceFactory::CreateSource(
    VideoEncoderProxy* proxy,
    const std::string& json_params) {
  if (create_func_) {
    auto ret = create_func_(proxy, json_params.c_str());
    if (ret) {
      std::shared_ptr<VideoEncoderSource> source(
          ret,
          base::ThreadSafeClosureDeleter<VideoEncoderSource>(base::BindOnce(
              &VideoEncoderSourceFactory::Destroy, AsWeakPtr())));
      return source;
    }
  }
  return nullptr;
}

bool VideoEncoderSourceFactory::TestEncoderSessionCountSupported(
    uint32_t count) {
  if (test_encoder_session_count_support_func_) {
    return test_encoder_session_count_support_func_(count,
                                                    plugin_global_proxy_);
  }
  return false;
}

bool VideoEncoderSourceFactory::Load() {
  create_func_ = reinterpret_cast<CreateVideoEncoderSourceFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "CreateVideoEncoderSource"));

  if (!create_func_) {
    LOG(ERROR)
        << info_->name.data()
        << ": Failed to get function pointer for CreateVideoEncoderSource";
    return false;
  }

  destroy_func_ = reinterpret_cast<DestroyVideoEncoderSourceFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "DestroyVideoEncoderSource"));

  if (!destroy_func_) {
    LOG(ERROR)
        << info_->name.data()
        << ": Failed to get function pointer for DestroyVideoEncoderSource";
    return false;
  }

  // optional interface
  test_encoder_session_count_support_func_ =
      reinterpret_cast<TestEncoderSessionCountSupportedFunc>(
          base::GetFunctionPointerFromNativeLibrary(
              library_, "TestEncoderSessionCountSupported"));
  if (!test_encoder_session_count_support_func_) {
    LOG(INFO) << info_->name.data()
              << ": Has no interface for TestEncoderSessionCountSupported";
    // optional interface
  }

  return true;
}

void VideoEncoderSourceFactory::Destroy(VideoEncoderSource* source) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);
  if (source) {
    LOG(INFO) << "video encode destroyed[" << source << "]";
  }
  if (destroy_func_) {
    destroy_func_(source);
  }
}

}  // namespace mediasdk