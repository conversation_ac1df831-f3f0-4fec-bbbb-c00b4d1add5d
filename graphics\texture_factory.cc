#include <atomic>
#include "texture.h"
#include "texture_impl.h"

namespace graphics {

std::shared_ptr<Texture> CreateTexture2D(Device& inst,
                                         HANDLE handle,
                                         uint64_t usage,
                                         const char* name) {
  auto ret = TextureImpl::CreateTexture2D(inst, handle, usage);
  if (ret && name) {
    ret->SetDebugName(name);
  }
  return ret;
}

std::shared_ptr<Texture> CreateTexture2D(Device& inst,
                                         ID3D11Texture2D* texture,
                                         ID3D11ShaderResourceView* view,
                                         uint64_t usage,
                                         const char* name) {
  auto ret = TextureImpl::CreateTexture2D(inst, texture, view, usage);
  if (ret && name) {
    ret->SetDebugName(name);
  }
  return ret;
}

std::shared_ptr<Texture> CreateTexture2D(Device& inst,
                                         ID3D11Texture2D* texture,
                                         uint64_t usage,
                                         const char* name) {
  auto ret = TextureImpl::CreateTexture2D(inst, texture, usage);
  if (ret && name) {
    ret->SetDebugName(name);
  }
  return ret;
}

std::shared_ptr<Texture> CreateTexture2D(Device& inst,
                                         int cx,
                                         int cy,
                                         DXGI_FORMAT format,
                                         uint64_t usage,
                                         const char* name) {
  auto ret = TextureImpl::CreateTexture2D(inst, cx, cy, format, usage);
  if (ret && name) {
    ret->SetDebugName(name);
  }
  return ret;
}

std::shared_ptr<Texture> CreateTexture2D(Device& inst,
                                         int cx,
                                         int cy,
                                         mediasdk::PixelFormat format,
                                         uint64_t usage,
                                         const char* name) {
  return CreateTexture2D(inst, cx, cy, PixelFormatToDXGIFormat(format), usage,
                         name);
}

std::shared_ptr<Texture> CreateTexture2D(Device& inst,
                                         const TextureFrame& frame,
                                         uint64_t usage,
                                         const char* name) {
  auto ret = TextureImpl::CreateTexture2D(inst, frame, usage);
  if (ret && name) {
    ret->SetDebugName(name);
  }
  return ret;
};

std::shared_ptr<Texture> CreateTexture2D(
    Device& inst,
    const wchar_t* file,
    uint64_t usage,
    const char* name)  // create texture from file
{
  auto ret = TextureImpl::CreateTexture2D(inst, file, usage);
  if (ret && name) {
    ret->SetDebugName(name);
  }
  return ret;
}

std::shared_ptr<Texture> CreateTexture2D(
    Device& inst,
    const uint8_t* file_mem_data,
    int32_t file_mem_data_size,
    uint64_t usage,
    const char* name)  // create texture from file memory
{
  auto ret = TextureImpl::CreateTexture2D(inst, file_mem_data,
                                          file_mem_data_size, usage);
  if (ret && name) {
    ret->SetDebugName(name);
  }
  return ret;
}

std::shared_ptr<Texture> CreateTexture3D(
    Device& inst,
    const uint8_t* buffer,
    int width,
    int height,
    int depth,
    mediasdk::PixelFormat format,
    uint64_t usage,
    const char* name)
{
    auto ret = TextureImpl::CreateTexture3D(inst, buffer,
        width, height, depth, PixelFormatToDXGIFormat(format), usage);
    if (ret && name) {
        ret->SetDebugName(name);
    }
    return ret;
}

std::shared_ptr<Texture> CreateTexture2D(
    Device& inst,
    const uint8_t* buffer,
    int width,
    int height,
    mediasdk::PixelFormat format,
    uint64_t usage,
    const char* name)
{
    auto ret = TextureImpl::CreateTexture2D(inst, buffer,
        width, height, PixelFormatToDXGIFormat(format), usage);
    if (ret && name) {
        ret->SetDebugName(name);
    }
    return ret;

}

}  // namespace graphics
