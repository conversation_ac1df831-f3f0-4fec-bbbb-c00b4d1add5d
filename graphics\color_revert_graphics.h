#pragma once

#include "texture.h"

namespace graphics {

class GRAPHICS_EXPORT ColorRevertGraphics {
 public:
  struct RevertParam {
    bool r;
    bool g;
    bool b;
    bool a;
  };

  virtual std::shared_ptr<Texture> Draw(Texture&, const RevertParam&) = 0;
  virtual void Destroy() = 0;
  virtual ~ColorRevertGraphics(){};
};

GRAPHICS_EXPORT std::shared_ptr<ColorRevertGraphics> CreateColorRevertGraphics(
    Device& ins);

}  // namespace graphics
