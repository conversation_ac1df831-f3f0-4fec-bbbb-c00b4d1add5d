
#pragma once

#include <base/memory/weak_ptr.h>
#include <mediasdk_json.hpp>
#include <optional>
#include <vector>
#include "graphics/adapter.h"
#include "mediasdk/component.h"
#include "mediasdk/data_center/audio_performance_store.h"
#include "mediasdk/data_center/biz_center/end_to_end_delay.h"
#include "mediasdk/data_center/profiler.h"
#include "mediasdk/public/mediasdk_callback_defines.h"
#include "visual_fps_store.h"

namespace mediasdk {
extern const char kCameraType[];
extern const char kWindowType[];
extern const char kMonitorType[];
extern const char kCastType[];
extern const char kGameType[];

struct EncoderStatistics {
  double encode_fps = 0.f;
  double error_fps = 0.f;
  int encode_process_time = 0;
  std::string name;
};

struct GpuInformation {
  std::string gpu_name;
  std::string driver_version;
  uint32_t vendor_id = 0;
  uint32_t device_id = 0;
  int id_low = 0;
  int id_high = 0;
  double gpu_3d_usage = 0.0f;
  double system_gpu_3d_usage = 0.0f;
  double dedicate_usage = 0.0;
  int64_t total_mem = 0;
  int64_t dedicate_mem = 0;
  int64_t shared_mem = 0;
};

struct AdaptiveGearStrategy {
  uint32_t up_limit = 0;
  uint32_t down_limit = 0;
  std::string strategy = "";
};

namespace config {
struct AbrConfig {
  bool disable_quic_strategy = false;
  std::optional<int> common_up_fast;
  std::optional<int> common_up_slow;
  std::optional<int> common_step;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(AbrConfig,
                                              disable_quic_strategy,
                                              common_up_fast,
                                              common_up_slow,
                                              common_step);
};

struct CameraConfig {
  struct Configs {
    bool drop_frame = false;
    bool use_pause = true;
    NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(Configs, drop_frame, use_pause);
  };

  Configs configs;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(CameraConfig, configs);
};

struct TraceConfig {
  bool report_performance = false;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(TraceConfig, report_performance);
};

struct DllLoadConfig {
  bool report_dll_failed_reason = false;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(DllLoadConfig,
                                              report_dll_failed_reason);
};

struct EffectFilter {
  int64_t place_holder = -1;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(EffectFilter, place_holder)
};

struct CaeConfig {
  bool enable = false;
  int process_sum = 25;
  int strategy_mode = 5;
  std::string params = "";
  std::string siti_type = "";
  int period_ms = 20 * 1000;
  int extract_duration = 5;
  std::string bitrate_ratios = "";
  std::string support_codec_name = "";
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(CaeConfig,
                                              enable,
                                              process_sum,
                                              strategy_mode,
                                              params,
                                              siti_type,
                                              period_ms,
                                              extract_duration,
                                              bitrate_ratios,
                                              support_codec_name);
};

struct TcpConfig {
  bool disable_buf_size = false;
  bool enable_time_out = false;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(TcpConfig,
                                              disable_buf_size,
                                              enable_time_out);
};

struct MjpegVideoRangeConfig {
  bool set_video_range = false;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(MjpegVideoRangeConfig,
                                              set_video_range);
};

struct QuicSleepConfig {
  bool enable = false;
  uint32_t sleep_ms = 1;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(QuicSleepConfig,
                                              enable,
                                              sleep_ms);
};

struct PushingTimeStamp {
  bool enable = false;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(PushingTimeStamp, enable);
};

struct QsvDtsConfig {
  bool use_encoder_dts = false;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(QsvDtsConfig, use_encoder_dts);
};

struct PushSessionIdConfig {
  bool concat_session_id = true;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(PushSessionIdConfig,
                                              concat_session_id);
};

struct RTCConfig {
  bool enable_pre_frame_drop = false;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(RTCConfig, enable_pre_frame_drop);
};

struct GlobalConfig {
  AbrConfig abr;
  CameraConfig camera;
  TraceConfig trace;
  DllLoadConfig dll_load;
  CaeConfig cae;
  EffectFilter effect_filter;
  TcpConfig tcp;
  MjpegVideoRangeConfig mjpeg_video_range;
  QuicSleepConfig quic_sleep;
  PushingTimeStamp new_version_pushing_time_stamp;
  QsvDtsConfig qsv_dts;
  PushSessionIdConfig session_id;
  RTCConfig rtc_config;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(GlobalConfig,
                                              abr,
                                              camera,
                                              trace,
                                              dll_load,
                                              effect_filter,
                                              cae,
                                              tcp,
                                              mjpeg_video_range,
                                              quic_sleep,
                                              new_version_pushing_time_stamp,
                                              qsv_dts,
                                              session_id,
                                              rtc_config);
};

struct LyraxAudioConfig {
  std::string abLabel;
  std::string appId;
  std::string appVersion;
  std::string logSdkWebsocketUrl;
  std::vector<std::string> accessHost;
  bool enable_specified_audio = false;
  NLOHMANN_DEFINE_TYPE_INTRUSIVE_WITH_DEFAULT(LyraxAudioConfig,
                                              abLabel,
                                              appId,
                                              appVersion,
                                              logSdkWebsocketUrl,
                                              accessHost,
                                              enable_specified_audio);
};
}  // namespace config

struct StreamStatistics {
  double send_fps = 0.0;
  double send_bitrate = 0.0;
  double send_audio_bitrate = 0.0;
  double send_video_bitrate = 0.0;
  double total_send_bitrate = 0.0;
  double enc_video_bitrate = 0.0;
  double enc_audio_bitrate = 0.0;
  double total_enc_bitrate = 0.0;
  double sei_bitrate = 0.0;
  double total_sei_bitrate = 0.0;
  int64_t total_drops = 0;
  int64_t total_video_packets = 0;
  int32_t dts_interval = 0;
  uint32_t sink_id = 0;
};

struct StreamStart {
  int64_t start_ms = 0;
  uint32_t sink_id = 0;
};

struct StreamAdaptiveStatistics {
  uint32_t count = 0;
  int64_t abnormal_duration = 0;
  int64_t abnormal_timestamp = 0;
  int64_t abnormal_up_duration = 0;
  int64_t abnormal_up_timestamp = 0;
  int64_t abnormal_down_duration = 0;
  int64_t abnormal_down_timestamp = 0;
};

struct SourceFPS {
  std::optional<std::vector<float>> camera_fps;
  std::optional<std::vector<float>> window_fps;
  std::optional<std::vector<float>> monitor_fps;
  std::optional<std::vector<float>> cast_fps;
  std::optional<std::vector<float>> game_fps;
};

struct StreamTransportReconnect {
  int32_t transport_reconnect_count = 0;
  int32_t transport_reconnect_time = 0;
};

struct SinkMapAndPresentStatistics {
  int64_t map_us = 0;
  int64_t present_us = 0;
};

namespace event_tracking_data {
struct PushStream;
}  // namespace event_tracking_data

struct TTNtp {
  int64_t tt_ntp_ms = 0;
  int64_t local_ms = 0;
  int64_t sdk_ns = 0;
};

struct RtcStatus {};

class DataCenter : public Component, public base::SupportsWeakPtr<DataCenter> {
 public:
  static constexpr char kComponentName[] = "DataCenter";

  // Component:
  ThreadID GetExecThreadId() override { return ThreadID::DATA; }

  virtual bool UpdateGlobalConfig(const std::string& json_info) = 0;

  virtual bool UpdateABConfig(const std::string& json_info) = 0;

  virtual std::string GetVersion() = 0;

  virtual std::string GetSdkVersion() = 0;

  virtual std::string GetParfaitHost() = 0;

  virtual std::string GetUserId() = 0;

  virtual config::LyraxAudioConfig GetLyraxConfig() = 0;

  virtual void UpdateRenderFPS(uint32_t fps) = 0;

  virtual void StartRenderProfiler() = 0;

  virtual void StartCollectPerformanceMatrics(
      const std::vector<CostThresholdParam>& params) = 0;

  virtual int GetAudioChannel() = 0;

  virtual int GetAudioSampleRate() = 0;

  virtual int GetMicAddCount() = 0;

  virtual int GetMicRemoveCount() = 0;

  virtual int GetRenderFPS() = 0;

  virtual CpuInfo GetCpuInfo() = 0;

  virtual bool GetGpuInfo(std::vector<GpuInformation>& infos) = 0;

  virtual MemoryInfo GetMemoryInfo() = 0;

  virtual bool GetDiskInfo(int& free_in_mb, int& total_in_mb) = 0;

  virtual EncoderStatisticInfo GetEncoderStatisticInfo(
      const std::string& stream_id) = 0;

  virtual StatisticInfo GetStatisticInfo(const std::string& stream_id) = 0;

  virtual void SetRealRenderFPS(float fps) = 0;

  virtual float GetRealRenderFPS() = 0;

  virtual void SetNoReadyFPS(float fps) = 0;

  virtual float GetNoReadyFPS() = 0;

  virtual void SetGpuTaskNum(int num) = 0;

  virtual int GetGpuTaskNum() = 0;

  virtual void SetStreamAdaptiveGearStrategy(
      const std::string& stream_id,
      const AdaptiveGearStrategy& strategy) = 0;

  virtual std::optional<AdaptiveGearStrategy> GetAdaptiveGearStrategy(
      const std::string& stream_id) = 0;

  virtual void OnAdaptiveSwitchSuccess(const std::string& stream_id) = 0;

  virtual int GetAdaptiveSwitchSuccessCount(const std::string& stream_id) = 0;

  virtual void OnAdaptiveAbnormalUp(const std::string& stream_id) = 0;

  virtual void OnAdaptiveAbnormalDown(const std::string& stream_id) = 0;

  virtual void OnAdaptiveNormal(const std::string& stream_id) = 0;

  virtual int64_t GetAdaptiveAbnormalDuration(const std::string& stream_id) = 0;

  virtual int64_t GetAdaptiveAbnormalUpDuration(
      const std::string& stream_id) = 0;

  virtual int64_t GetAdaptiveAbnormalDownDuration(
      const std::string& stream_id) = 0;

  virtual void AddStreamReconnectCnt(const std::string& stream_id) = 0;

  virtual int32_t GetStreamReconnectCnt(const std::string& stream_id) = 0;

  virtual void SetEncoderStatistics(uint32_t sink_id,
                                    const EncoderStatistics& info) = 0;

  virtual std::optional<EncoderStatistics> GetEncoderStatistics(
      uint32_t sink_id) = 0;

  virtual void SetStreamStatistics(const std::string& stream_id,
                                   const StreamStatistics& info) = 0;

  virtual std::optional<StreamStatistics> GetStreamStatistics(
      const std::string& stream_id) = 0;

  virtual void ResetStreamStatus(const std::string& stream_id) = 0;

  virtual void SetStreamStart(const std::string& stream_id,
                              const StreamStart& info) = 0;

  virtual std::optional<StreamStart> GetStreamStart(
      const std::string& stream_id) = 0;

  virtual std::optional<std::string> GetStreamPushSessionId(
      const std::string& stream_id) = 0;

  virtual std::optional<std::string> GetStreamConnectSessionId(
      const std::string& stream_id) = 0;

  virtual void UpdateStreamConnectSessionId(const std::string& stream_id) = 0;

  virtual bool UpdateStreamFirstConnectStartEventTimeStampMS(
      const std::string& stream_id,
      int64_t ms) = 0;

  virtual std::optional<int64_t> GetStreamFirstConnectStartEventTimeStampMS(
      const std::string& stream_id) = 0;

  virtual void OnFirstVideoFrame(const std::string& stream_id) = 0;

  virtual bool GetFirstVideoFrame(const std::string& stream_id) = 0;

  virtual void OnFirstAudioFrame(const std::string& stream_id) = 0;

  virtual bool GetFirstAudioFrame(const std::string& stream_id) = 0;

  virtual void OnFirstVideoPacket(const std::string& stream_id) = 0;

  virtual bool GetFirstVideoPacket(const std::string& stream_id) = 0;

  virtual void OnFirstAudioPacket(const std::string& stream_id) = 0;

  virtual bool GetFirstAudioPacket(const std::string& stream_id) = 0;

  virtual void OnFirstVideoSend(const std::string& stream_id) = 0;

  virtual bool GetFirstVideoSend(const std::string& stream_id) = 0;

  virtual void OnFirstAudioSend(const std::string& stream_id) = 0;

  virtual bool GetFirstAudioSend(const std::string& stream_id) = 0;

  virtual void SetSourceFPS(const SourceFPS& fps) = 0;

  virtual std::optional<SourceFPS> GetSourceFPS() = 0;

  virtual void SetVideoEncoderBw(const std::string& stream_id,
                                 const int32_t& v) = 0;

  virtual std::optional<int32_t> GetVideoEncoderBw(
      const std::string& stream_id) = 0;

  virtual void SetStreamSendDelay(const std::string& stream_id,
                                  const int64_t& v) = 0;

  virtual std::optional<int64_t> GetStreamSendDelay(
      const std::string& stream_id) = 0;

  virtual void AddStreamTransportReconnectDuration(const std::string& stream_id,
                                                   const int64_t duration) = 0;

  virtual void AddStreamTransportReconnectCount(
      const std::string& stream_id) = 0;

  virtual void SetStreamTransportReconnect(
      const std::string& stream_id,
      const StreamTransportReconnect& v) = 0;

  virtual std::optional<StreamTransportReconnect> GetStreamTransportReconnect(
      const std::string& stream_id) = 0;

  virtual void SetStreamSendPackageDelay(const std::string& stream_id,
                                         const int64_t& delay) = 0;

  virtual std::optional<int64_t> GetStreamSendPackageDelay(
      const std::string& stream_id) = 0;

  virtual void SetSinkMapAndPresentStatistics(
      const uint32_t& sink_id,
      const SinkMapAndPresentStatistics& v) = 0;

  virtual std::optional<SinkMapAndPresentStatistics>
  GetSinkMapAndPresentStatistics(const uint32_t& sink_id) = 0;

  virtual std::optional<RtcStatus> GetRtcStatus() = 0;

  virtual void SetRtcStatus(std::optional<RtcStatus> rtc_status) = 0;

  virtual std::shared_ptr<DeviceInfoArray> GetDeviceInfo() = 0;

  virtual config::AbrConfig GetAbrConfig() = 0;

  virtual nlohmann::json GetABConfig(const std::string& key) const = 0;

  virtual graphics::AdapterInfo GetCurrentAdapterInfo() const = 0;

  virtual std::vector<graphics::GpuAdapterInfo> GetAdapters() const = 0;

  virtual config::TraceConfig GetTraceConfig() = 0;

  virtual config::DllLoadConfig GetDllLoadConfig() = 0;

  virtual config::CaeConfig GetCaeConfig() = 0;

  virtual config::TcpConfig GetTcpConfig() = 0;

  virtual config::CameraConfig GetCameraConfig() = 0;

  virtual config::RTCConfig GetRTCConfig() = 0;

  virtual config::MjpegVideoRangeConfig GetMjpegVideoRangeConfig() = 0;

  virtual config::QuicSleepConfig GetQuicSleepConfig() = 0;

  virtual config::QsvDtsConfig GetQsvDtsConfig() = 0;

  virtual config::PushSessionIdConfig GetPushSessionIdConfig() = 0;

  virtual VisualFpsStore& GetVisualFpsStore() = 0;

  virtual AudioPerformanceStore& GetAudioPerformanceStore() = 0;

  virtual void SetTTNtp(const TTNtp& tt_ntp) = 0;

  virtual int64_t ChangeToTTNtpMS(int64_t sdk_ns) = 0;

  virtual int64_t ChangeToLocalMS(int64_t sdk_ns) = 0;

  virtual bool IsUseNewVersionPushingTimeStamp() = 0;

  // ttntp - local
  virtual std::optional<int64_t> GetLocaltoNtpDiffMS() = 0;

  virtual bool EnableEndtoEndDelay() = 0;

  virtual void RegistEndtoEndDelay(EndtoEndDelay* e2e_delay) = 0;

  virtual void UnregistEndtoEndDelay(EndtoEndDelay* e2e_delay) = 0;

  virtual void FillEndtoEndDelay(event_tracking_data::PushStream& data) = 0;
};

}  // namespace mediasdk
