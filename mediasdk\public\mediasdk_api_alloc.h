#pragma once
#include <stdint.h>

#include "mediasdk_export.h"

namespace mediasdk {

#ifdef __cplusplus
extern "C" {
#endif

// Allocate heap memory of the specified size in bytes.
// Synchronously return the starting address of the contiguous memory.
MEDIASDK_EXPORT void* MS_API AllocBuffer(size_t size);

// Release the heap memory allocated by 'AllocBuffer'.
MEDIASDK_EXPORT void MS_API FreeBuffer(void* p);

#ifdef __cplusplus
}
#endif
}  // namespace mediasdk
