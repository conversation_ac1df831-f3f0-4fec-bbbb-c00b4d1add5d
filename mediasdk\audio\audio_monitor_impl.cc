#include "audio_monitor_impl.h"

#include "time_helper.h"

namespace mediasdk {

AudioMonitorImpl::AudioMonitorImpl() {
  LOG(INFO) << "AudioMonitorImpl[" << this << "]";
  monitor_ = std::make_shared<WASAPIAudioMonitor>();
}

bool AudioMonitorImpl::Open(mediasdk::AudioFormat format,
                            const WASAPIDevice::DeviceName& dev) {
  LOG(INFO) << "try open device [" << this << "]";
  format_ = format;
  play_thread_ = std::make_shared<std::thread>(
      &AudioMonitorImpl::OpenDevInThread, this, format, dev);
  return true;
}

void AudioMonitorImpl::Close() {
  if (play_thread_) {
    auto handle = play_thread_->native_handle();
    auto native_id = play_thread_->get_id();
    auto ret = WaitForSingleObject(handle, 10000);
    play_thread_->detach();
    if (ret != WAIT_OBJECT_0) {
      LOG(WARNING) << "open device pending in [" << native_id << "]";
      return;
    }
    play_thread_ = nullptr;
  }
  if (monitor_) {
    monitor_->CloseAudioDevice();
  }
}

bool AudioMonitorImpl::Play(const mediasdk::AudioFrame& data, bool mute) {
  auto begin = milli_now();
  auto frame_duration =
      SamplesToNSDuration(format_.GetSampleRate(), data.GetCount()) / 1000;
  if (failed_) {
    LOG(WARNING) << "Open Failed, play by sleep";

    SleepByUS(frame_duration);
    return false;
  }

  if (!init_event_.TimedWait(base::Nanoseconds(
          SamplesToNSDuration(format_.GetSampleRate(), data.GetCount())))) {
    LOG(INFO) << "have not init success";
    return true;
  }

  if (failed_) {
    auto now = milli_now();
    if (now - begin < frame_duration / 1000) {
      SleepByUS(frame_duration - (now - begin) * 1000);
    }
    return false;
  }
  if (!monitor_)
    return false;
  return monitor_->Play(data, mute);
}

void AudioMonitorImpl::SignalStop() {
  if (monitor_) {
    monitor_->SignalStop();
  }
}

std::wstring AudioMonitorImpl::GetCurrentDevice() {
  if (!monitor_)
    return {};
  return monitor_->GetDeviceID();
}

AudioMonitorImpl::~AudioMonitorImpl() {
  SignalStop();
  LOG(INFO) << "~AudioMonitorImpl [" << this << "]";
  if (play_thread_) {
    auto handle = play_thread_->native_handle();
    auto native_id = play_thread_->get_id();
    auto ret = WaitForSingleObject(handle, 10000);
    play_thread_->detach();
    play_thread_ = nullptr;
    if (ret != WAIT_OBJECT_0) {
      LOG(WARNING) << "open device pending in [" << native_id << "]";
      return;
    }
  }
  {
    auto stop_thread =
        std::thread(&AudioMonitorImpl::CloseDevInThread, std::move(monitor_));
    auto native_id = stop_thread.get_id();
    auto handle = stop_thread.native_handle();

    auto ret = WaitForSingleObject(handle, 10000);
    if (ret != WAIT_OBJECT_0) {
      LOG(WARNING) << "close device pending in [" << native_id << "]";
    }
    stop_thread.detach();  // we do not need to wait for this thread to stop
  }
}

void AudioMonitorImpl::OpenDevInThread(mediasdk::AudioFormat format,
                                       const WASAPIDevice::DeviceName& dev) {
  LOG(INFO) << "open dev in current thread [" << this << "]";
  failed_ = !monitor_->Start(dev, format);  // this may cost more than 500 ms
  init_event_.Signal();
  LOG(INFO) << "open dev in current thread,end [" << this << "]";
}

void AudioMonitorImpl::CloseDevInThread(
    std::shared_ptr<WASAPIAudioMonitor> monitor) {
  monitor->CloseAudioDevice();
  monitor = nullptr;
}

}  // namespace mediasdk