﻿#include "display_impl.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <dxgi1_3.h>

namespace {
constexpr int kSwapCHainBufferCount = 3;
}

namespace graphics {

std::shared_ptr<DisplayImpl> DisplayImpl::CreateDisplay2D(Device& ins,
                                                          HWND hwnd,
                                                          int cx,
                                                          int cy) {
  auto ret = std::make_shared<DisplayImpl>(ins);
  if (!ret->_OpenWithHWND(hwnd, cx, cy)) {
    return nullptr;
  }

  return ret;
}

DisplayImpl::DisplayImpl(Device& ins) : device_(ins) {
  graphics_ = CreateGraphics2D(ins);
}

bool DisplayImpl::_OpenWithHWND(HWND hwnd, int cx, int cy) {
  RECT rect = {};
  GetClientRect(hwnd, &rect);
  if (rect.right <= rect.left || rect.bottom <= rect.top) {
    LOG(INFO) << base::StringPrintf("Window Size Error %ld %ld %ld %ld",
                                    rect.left, rect.top, rect.right,
                                    rect.bottom);
    return false;
  }

  // Create Device
  // default running on first adapter
  D3D_FEATURE_LEVEL features[] = {
      D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_1, D3D_FEATURE_LEVEL_10_0,
      D3D_FEATURE_LEVEL_9_3,  D3D_FEATURE_LEVEL_9_2,  D3D_FEATURE_LEVEL_9_1,
  };

  DXGI_MODE_DESC desc = {};

  desc.Width = cx;
  desc.Height = cy;
  desc.RefreshRate.Denominator = 60;
  desc.RefreshRate.Numerator = 1;
  desc.Format = DXGI_FORMAT::DXGI_FORMAT_B8G8R8A8_UNORM;
  desc.Scaling = DXGI_MODE_SCALING::DXGI_MODE_SCALING_UNSPECIFIED;
  desc.ScanlineOrdering =
      DXGI_MODE_SCANLINE_ORDER::DXGI_MODE_SCANLINE_ORDER_UNSPECIFIED;
  DXGI_SAMPLE_DESC sampleDesc = {};
  sampleDesc.Count = 1;
  swap_chain_desc_.BufferDesc = desc;
  swap_chain_desc_.SampleDesc = sampleDesc;
  swap_chain_desc_.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
  swap_chain_desc_.BufferCount = kSwapCHainBufferCount;
  swap_chain_desc_.OutputWindow = hwnd;
  swap_chain_desc_.Windowed = TRUE;
  swap_chain_desc_.Flags = 0;
  swap_chain_desc_.SwapEffect = DXGI_SWAP_EFFECT::DXGI_SWAP_EFFECT_DISCARD;
  // Create Swap Chains
  HRESULT hRes = device_.GetDXGIFactory()->CreateSwapChain(
      device_.GetDevice().Get(), &swap_chain_desc_, &swap_chain_);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateSwapChain %s",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  hRes = device_.GetDXGIFactory()->MakeWindowAssociation(hwnd,
                                                         DXGI_MWA_NO_ALT_ENTER);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to MakeWindowAssociation %s",
                                     GetErrorString(hRes).c_str());
    return false;
  }

  hWnd_ = hwnd;

  LOG(INFO) << base::StringPrintf("Success Create Swap Chain %u.%u",
                                  swap_chain_desc_.BufferDesc.Width,
                                  swap_chain_desc_.BufferDesc.Height);
  if (!_CreateTexture()) {
    return false;
  }

  device_.SetViewport(XMFLOAT2_EMPTY, graphics_->GetSize());
  return true;
}

bool DisplayImpl::_CreateTexture() {
  return graphics_->CreateWithSwapChain(swap_chain_.Get());
}

bool DisplayImpl::IsReady(uint32_t wait_time /*= 0*/) {
  return true;
}

bool DisplayImpl::Present() {
  ID3D11RenderTargetView* const rtv[D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT] = {
      NULL};
  device_.GetContext()->OMSetRenderTargets(
      D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT, rtv, NULL);
  HRESULT hr = swap_chain_->Present(0, 0u);
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to Present %s",
                                     GetErrorString(hr).c_str());
    return false;
  }
  return true;
}

Graphics& DisplayImpl::GetGraphics() {
  return *graphics_;
}

bool DisplayImpl::Resize(uint32_t cx, uint32_t cy) {
  if (!graphics_ || !swap_chain_)
    return false;
  DXGI_SWAP_CHAIN_DESC pre = swap_chain_desc_;
  graphics_->Destroy();
  HRESULT res = swap_chain_->ResizeBuffers(
      kSwapCHainBufferCount, cx, cy, DXGI_FORMAT::DXGI_FORMAT_B8G8R8A8_UNORM,
      pre.Flags);
  if (FAILED(res)) {
    LOG(ERROR) << "Failed to ResizeBuffers [" << GetErrorString(res) << "]";
    return false;
  }
  if (!graphics_->CreateWithSwapChain(swap_chain_.Get())) {
    return false;
  }
  swap_chain_->GetDesc(&swap_chain_desc_);

  return true;
}

void DisplayImpl::Destroy() {
  if (graphics_) {
    graphics_->Destroy();
    graphics_ = nullptr;
  }
  if (swap_chain_) {
    swap_chain_.Reset();
  }
}

DisplayImpl::~DisplayImpl() {
  Destroy();
}

}  // namespace graphics