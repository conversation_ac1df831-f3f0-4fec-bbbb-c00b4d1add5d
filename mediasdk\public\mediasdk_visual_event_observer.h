#pragma once

#include "mediasdk_defines.h"

namespace mediasdk {

class MediaSDKVisualEventObserver {
 public:
  virtual ~MediaSDKVisualEventObserver() = default;

  virtual void OnVisualSizeChanged(MediaSDKString id,
                                   const MSSize& new_size) = 0;

  virtual void OnVisualSourceEvent(MediaSDKString id, MediaSDKString event) = 0;

  virtual void OnVisualReopenResult(bool reopen_result, MediaSDKString id) = 0;

  virtual void OnVisualDestroyedWhenAllRefsRemoved(MediaSDKString id, bool remove_result) = 0;
};

}  // namespace mediasdk
