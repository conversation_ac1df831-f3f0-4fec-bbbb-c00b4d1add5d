#pragma once

#include <stdint.h>
#include <memory>
#include "device.h"
#include "public/graphics_texture_frame.h"
#include "shader.h"

namespace graphics {

enum ImageFileFormat {
  kImageFileFormatBMP,
  kImageFileFormatJPG,
  kImageFileFormatPNG,
  kImageFileFormatTIFF,
  kImageFileFormatGIF,
  kImageFileFormatWMP
};

class GRAPHICS_EXPORT Texture {
 public:
  enum TEXTURE_USAGE : uint64_t {
    TEXTURE_USAGE_COPY = 1,
    TEXTURE_USAGE_SHADER_RESOURCE = 2,
    TEXTURE_USAGE_CPU_MAP_READ = 4,
    TEXTURE_USAGE_CPU_MAP_WRITE = 8,
    TEXTURE_USAGE_RENDER_TARGET = 16,
    TEXTURE_USAGE_SHARED = 32,
    TEXTURE_USAGE_GDI = 64,
    TEXTURE_USAGE_KEYED_MUTEXED = 128
  };

  virtual ~Texture() = default;

  virtual Device& GetDevice() const = 0;

  virtual D3D11_TEXTURE2D_DESC GetDesc() const = 0;

  virtual ID3D11ShaderResourceView* GetSRV(int plane = 0) const = 0;

  virtual ID3D11Texture2D* GetTexture() const = 0;

  virtual bool SetupVertex() = 0;

  virtual HRESULT BuildFromTextureView(ID3D11Texture2D* texture,
                                       ID3D11ShaderResourceView* view,
                                       uint64_t usage) = 0;

  virtual HRESULT BuildFromTexture(ID3D11Texture2D* texture,
                                   uint64_t usage) = 0;

  virtual DirectX::XMFLOAT2 GetSize() const = 0;

  virtual bool IsEmpty() const = 0;

  virtual bool Map(D3D11_MAPPED_SUBRESOURCE& desc,
                   bool read_only,
                   unsigned flag = 0) = 0;

  virtual bool NeedUnMap() const = 0;

  virtual void UnMap() = 0;

  virtual bool CopyFrom(uint8_t* data, int32_t line_size, int cy) = 0;

  virtual bool CopyTo(uint8_t* data, int32_t line_size, int cy) = 0;

  virtual bool CopyTo(ID3D11Texture2D* texture) const = 0;

  virtual bool CopyTextureFrom(ID3D11Texture2D* texture) = 0;

  virtual bool CopySubFrom(ID3D11Texture2D* texture,
                           const DirectX::XMFLOAT2& start,
                           const DirectX::XMFLOAT2& end) = 0;

  virtual bool CopySubTo(ID3D11Texture2D* texture,
                         const DirectX::XMFLOAT2& start,
                         DirectX::XMFLOAT2& end) = 0;

  virtual bool IsKeyedMutex() const = 0;

  virtual bool AcquireKeyedAccess(uint64_t key = 0, uint32_t ms = 0) = 0;

  virtual void ReleaseKeyedAccess(uint64_t key = 0) = 0;

  virtual bool GetDC(bool discard, void** hdc) = 0;

  virtual bool ReleaseDC() = 0;

  virtual bool SaveAS(const char* path) const = 0;

  virtual std::shared_ptr<Texture> GenerateReadTexture() const = 0;

  virtual std::shared_ptr<Texture> CopyTexture() = 0;

  virtual HANDLE GetSharedHandle() const = 0;

  virtual void Destroy() = 0;

  virtual uint64_t GetUsage() const = 0;

  virtual void SetDebugName(const char* name) = 0;

  virtual TONEMAP_TYPE GetToneMapType() const = 0;

  virtual float GetToneMapParam() const = 0;

  virtual void SetToneMapType(const TONEMAP_TYPE& type) = 0;

  virtual void SetToneMapParam(const float param) = 0;
};

GRAPHICS_EXPORT std::shared_ptr<Texture> CreateTexture2D(
    Device& inst,
    void* handle,
    uint64_t usage,
    const char* name = nullptr);

GRAPHICS_EXPORT std::shared_ptr<Texture> CreateTexture2D(
    Device& inst,
    ID3D11Texture2D* texture,
    uint64_t usage,
    const char* name = nullptr);

GRAPHICS_EXPORT std::shared_ptr<Texture> CreateTexture2D(
    Device& inst,
    int cx,
    int cy,
    mediasdk::PixelFormat format,
    uint64_t usage,
    const char* name = nullptr);

GRAPHICS_EXPORT std::shared_ptr<Texture> CreateTexture2D(
    Device& inst,
    int cx,
    int cy,
    DXGI_FORMAT format,
    uint64_t usage,
    const char* name = nullptr);

GRAPHICS_EXPORT std::shared_ptr<Texture> CreateTexture2D(
    Device& inst,
    const TextureFrame& frame,
    uint64_t usage,
    const char* name =
        nullptr);  // create texture and initialize from CPU frame buffer

GRAPHICS_EXPORT std::shared_ptr<Texture> CreateTexture2D(
    Device& inst,
    const wchar_t* file,
    uint64_t usage,
    const char* name = nullptr);  // create texture from file

GRAPHICS_EXPORT std::shared_ptr<Texture> CreateTexture2D(
    Device& inst,
    const uint8_t* file_mem_data,
    int32_t file_mem_data_size,
    uint64_t usage,
    const char* name = nullptr);  // create texture from encoded file memory

GRAPHICS_EXPORT std::shared_ptr<Texture> CreateTexture3D(
    Device& inst,
    const uint8_t* buffer,
    int width,
    int height,
    int depth,
    mediasdk::PixelFormat format,
    uint64_t usage,
    const char* name = nullptr);  // create 3dtexture from  buffer

GRAPHICS_EXPORT std::shared_ptr<Texture> CreateTexture2D(
    Device& inst,
    const uint8_t* buffer,
    int width,
    int height,
    mediasdk::PixelFormat format,
    uint64_t usage,
    const char* name = nullptr);  // create 2dtexture from  buffer

GRAPHICS_EXPORT bool SaveBGRATextureToFile(ID3D11DeviceContext* pContext,
                                           ID3D11Resource* pSource,
                                           const wchar_t* fileName,
                                           ImageFileFormat targetFormat);
}  // namespace graphics
