#pragma once

#include <stdint.h>
#include <memory>
#include <set>
#include "mediasdk/plugin_service/visual_source_factory.h"

namespace mediasdk {

class VisualSource;

class RTCVisualSourceFactory final : public VisualSourceFactory {
 public:
  explicit RTCVisualSourceFactory(std::shared_ptr<PluginInfo> info);

  ~RTCVisualSourceFactory();

  // VisualSourceFactory:
  std::shared_ptr<VisualSource> CreateSource(
      VisualProxy* proxy,
      const std::string& json_params) override;

  void Destroy(std::shared_ptr<VisualSource> source) override;

  void DestroyAll() override;

  bool IsExternal() override { return true; }

 private:
  std::set<std::shared_ptr<VisualSource>> sources_;
};

}  // namespace mediasdk
