#include "d3d11_device_context_debug_layer.h"

#include <base/logging.h>

// LOG(INFO) << __FUNCTION__ << " called";
#define TRACE_DEVICE_CONTEXT_CALL

HRESULT STDMETHODCALLTYPE D3D11ContextDebugLayer::QueryInterface(
    /* [in] */ REFIID riid,
    /* [iid_is][out] */ _COM_Outptr_ void __RPC_FAR* __RPC_FAR* ppvObject) {
  return m_rawContext->QueryInterface(riid, ppvObject);
}

ULONG STDMETHODCALLTYPE D3D11ContextDebugLayer::AddRef(void) {
  return m_rawContext->AddRef();
}

ULONG STDMETHODCALLTYPE D3D11ContextDebugLayer::Release(void) {
  return m_rawContext->Release();
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::GetDevice(_Outptr_ ID3D11Device** ppDevice) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GetDevice(ppDevice);
}

HRESULT STDMETHODCALLTYPE D3D11ContextDebugLayer::GetPrivateData(
    _In_ REFGUID guid,
    _Inout_ UINT* pDataSize,
    _Out_writes_bytes_opt_(*pDataSize) void* pData) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GetPrivateData(guid, pDataSize, pData);
}

HRESULT STDMETHODCALLTYPE D3D11ContextDebugLayer::SetPrivateData(
    _In_ REFGUID guid,
    _In_ UINT DataSize,
    _In_reads_bytes_opt_(DataSize) const void* pData) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->SetPrivateData(guid, DataSize, pData);
}

HRESULT STDMETHODCALLTYPE D3D11ContextDebugLayer::SetPrivateDataInterface(
    _In_ REFGUID guid,
    _In_opt_ const IUnknown* pData) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->SetPrivateDataInterface(guid, pData);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::VSSetConstantBuffers(
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _In_reads_opt_(NumBuffers) ID3D11Buffer* const* ppConstantBuffers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->VSSetConstantBuffers(StartSlot, NumBuffers,
                                            ppConstantBuffers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::PSSetShaderResources(
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumViews,
    _In_reads_opt_(NumViews)
        ID3D11ShaderResourceView* const* ppShaderResourceViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->PSSetShaderResources(StartSlot, NumViews,
                                            ppShaderResourceViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::PSSetShader(
    _In_opt_ ID3D11PixelShader* pPixelShader,
    _In_reads_opt_(NumClassInstances)
        ID3D11ClassInstance* const* ppClassInstances,
    UINT NumClassInstances) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->PSSetShader(pPixelShader, ppClassInstances,
                                   NumClassInstances);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::PSSetSamplers(
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot)
        UINT NumSamplers,
    _In_reads_opt_(NumSamplers) ID3D11SamplerState* const* ppSamplers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->PSSetSamplers(StartSlot, NumSamplers, ppSamplers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::VSSetShader(
    _In_opt_ ID3D11VertexShader* pVertexShader,
    _In_reads_opt_(NumClassInstances)
        ID3D11ClassInstance* const* ppClassInstances,
    UINT NumClassInstances) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->VSSetShader(pVertexShader, ppClassInstances,
                                   NumClassInstances);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::DrawIndexed(_In_ UINT IndexCount,
                                    _In_ UINT StartIndexLocation,
                                    _In_ INT BaseVertexLocation) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DrawIndexed(IndexCount, StartIndexLocation,
                                   BaseVertexLocation);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::Draw(_In_ UINT VertexCount,
                             _In_ UINT StartVertexLocation) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->Draw(VertexCount, StartVertexLocation);
}

HRESULT STDMETHODCALLTYPE D3D11ContextDebugLayer::Map(
    _In_ ID3D11Resource* pResource,
    _In_ UINT Subresource,
    _In_ D3D11_MAP MapType,
    _In_ UINT MapFlags,
    _Out_opt_ D3D11_MAPPED_SUBRESOURCE* pMappedResource) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->Map(pResource, Subresource, MapType, MapFlags,
                           pMappedResource);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::Unmap(_In_ ID3D11Resource* pResource,
                              _In_ UINT Subresource) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->Unmap(pResource, Subresource);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::PSSetConstantBuffers(
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _In_reads_opt_(NumBuffers) ID3D11Buffer* const* ppConstantBuffers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->PSSetConstantBuffers(StartSlot, NumBuffers,
                                            ppConstantBuffers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::IASetInputLayout(
    _In_opt_ ID3D11InputLayout* pInputLayout) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->IASetInputLayout(pInputLayout);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::IASetVertexBuffers(
    _In_range_(0, D3D11_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _In_reads_opt_(NumBuffers) ID3D11Buffer* const* ppVertexBuffers,
    _In_reads_opt_(NumBuffers) const UINT* pStrides,
    _In_reads_opt_(NumBuffers) const UINT* pOffsets) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->IASetVertexBuffers(StartSlot, NumBuffers,
                                          ppVertexBuffers, pStrides, pOffsets);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::IASetIndexBuffer(_In_opt_ ID3D11Buffer* pIndexBuffer,
                                         _In_ DXGI_FORMAT Format,
                                         _In_ UINT Offset) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->IASetIndexBuffer(pIndexBuffer, Format, Offset);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::DrawIndexedInstanced(_In_ UINT IndexCountPerInstance,
                                             _In_ UINT InstanceCount,
                                             _In_ UINT StartIndexLocation,
                                             _In_ INT BaseVertexLocation,
                                             _In_ UINT StartInstanceLocation) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DrawIndexedInstanced(
      IndexCountPerInstance, InstanceCount, StartIndexLocation,
      BaseVertexLocation, StartInstanceLocation);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::DrawInstanced(_In_ UINT VertexCountPerInstance,
                                      _In_ UINT InstanceCount,
                                      _In_ UINT StartVertexLocation,
                                      _In_ UINT StartInstanceLocation) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DrawInstanced(VertexCountPerInstance, InstanceCount,
                                     StartVertexLocation,
                                     StartInstanceLocation);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::GSSetConstantBuffers(
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _In_reads_opt_(NumBuffers) ID3D11Buffer* const* ppConstantBuffers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GSSetConstantBuffers(StartSlot, NumBuffers,
                                            ppConstantBuffers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::GSSetShader(
    _In_opt_ ID3D11GeometryShader* pShader,
    _In_reads_opt_(NumClassInstances)
        ID3D11ClassInstance* const* ppClassInstances,
    UINT NumClassInstances) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GSSetShader(pShader, ppClassInstances,
                                   NumClassInstances);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::IASetPrimitiveTopology(
    _In_ D3D11_PRIMITIVE_TOPOLOGY Topology) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->IASetPrimitiveTopology(Topology);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::VSSetShaderResources(
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumViews,
    _In_reads_opt_(NumViews)
        ID3D11ShaderResourceView* const* ppShaderResourceViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->VSSetShaderResources(StartSlot, NumViews,
                                            ppShaderResourceViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::VSSetSamplers(
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot)
        UINT NumSamplers,
    _In_reads_opt_(NumSamplers) ID3D11SamplerState* const* ppSamplers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->VSSetSamplers(StartSlot, NumSamplers, ppSamplers);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::Begin(_In_ ID3D11Asynchronous* pAsync) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->Begin(pAsync);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::End(_In_ ID3D11Asynchronous* pAsync) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->End(pAsync);
}

HRESULT STDMETHODCALLTYPE
D3D11ContextDebugLayer::GetData(_In_ ID3D11Asynchronous* pAsync,
                                _Out_writes_bytes_opt_(DataSize) void* pData,
                                _In_ UINT DataSize,
                                _In_ UINT GetDataFlags) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GetData(pAsync, pData, DataSize, GetDataFlags);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::SetPredication(_In_opt_ ID3D11Predicate* pPredicate,
                                       _In_ BOOL PredicateValue) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->SetPredication(pPredicate, PredicateValue);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::GSSetShaderResources(
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumViews,
    _In_reads_opt_(NumViews)
        ID3D11ShaderResourceView* const* ppShaderResourceViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GSSetShaderResources(StartSlot, NumViews,
                                            ppShaderResourceViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::GSSetSamplers(
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot)
        UINT NumSamplers,
    _In_reads_opt_(NumSamplers) ID3D11SamplerState* const* ppSamplers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GSSetSamplers(StartSlot, NumSamplers, ppSamplers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::OMSetRenderTargets(
    _In_range_(0, D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT) UINT NumViews,
    _In_reads_opt_(NumViews) ID3D11RenderTargetView* const* ppRenderTargetViews,
    _In_opt_ ID3D11DepthStencilView* pDepthStencilView) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->OMSetRenderTargets(NumViews, ppRenderTargetViews,
                                          pDepthStencilView);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::OMSetRenderTargetsAndUnorderedAccessViews(
    _In_ UINT NumRTVs,
    _In_reads_opt_(NumRTVs) ID3D11RenderTargetView* const* ppRenderTargetViews,
    _In_opt_ ID3D11DepthStencilView* pDepthStencilView,
    _In_range_(0, D3D11_1_UAV_SLOT_COUNT - 1) UINT UAVStartSlot,
    _In_ UINT NumUAVs,
    _In_reads_opt_(NumUAVs)
        ID3D11UnorderedAccessView* const* ppUnorderedAccessViews,
    _In_reads_opt_(NumUAVs) const UINT* pUAVInitialCounts) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->OMSetRenderTargetsAndUnorderedAccessViews(
      NumRTVs, ppRenderTargetViews, pDepthStencilView, UAVStartSlot, NumUAVs,
      ppUnorderedAccessViews, pUAVInitialCounts);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::OMSetBlendState(_In_opt_ ID3D11BlendState* pBlendState,
                                        _In_opt_ const FLOAT BlendFactor[4],
                                        _In_ UINT SampleMask) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->OMSetBlendState(pBlendState, BlendFactor, SampleMask);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::OMSetDepthStencilState(
    _In_opt_ ID3D11DepthStencilState* pDepthStencilState,
    _In_ UINT StencilRef) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->OMSetDepthStencilState(pDepthStencilState, StencilRef);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::SOSetTargets(
    _In_range_(0, D3D11_SO_BUFFER_SLOT_COUNT) UINT NumBuffers,
    _In_reads_opt_(NumBuffers) ID3D11Buffer* const* ppSOTargets,
    _In_reads_opt_(NumBuffers) const UINT* pOffsets) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->SOSetTargets(NumBuffers, ppSOTargets, pOffsets);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::DrawAuto(void) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DrawAuto();
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::DrawIndexedInstancedIndirect(
    _In_ ID3D11Buffer* pBufferForArgs,
    _In_ UINT AlignedByteOffsetForArgs) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DrawIndexedInstancedIndirect(pBufferForArgs,
                                                    AlignedByteOffsetForArgs);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::DrawInstancedIndirect(
    _In_ ID3D11Buffer* pBufferForArgs,
    _In_ UINT AlignedByteOffsetForArgs) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DrawInstancedIndirect(pBufferForArgs,
                                             AlignedByteOffsetForArgs);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::Dispatch(_In_ UINT ThreadGroupCountX,
                                 _In_ UINT ThreadGroupCountY,
                                 _In_ UINT ThreadGroupCountZ) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->Dispatch(ThreadGroupCountX, ThreadGroupCountY,
                                ThreadGroupCountZ);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::DispatchIndirect(_In_ ID3D11Buffer* pBufferForArgs,
                                         _In_ UINT AlignedByteOffsetForArgs) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DispatchIndirect(pBufferForArgs,
                                        AlignedByteOffsetForArgs);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::RSSetState(
    _In_opt_ ID3D11RasterizerState* pRasterizerState) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->RSSetState(pRasterizerState);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::RSSetViewports(
    _In_range_(0, D3D11_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE)
        UINT NumViewports,
    _In_reads_opt_(NumViewports) const D3D11_VIEWPORT* pViewports) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->RSSetViewports(NumViewports, pViewports);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::RSSetScissorRects(
    _In_range_(0, D3D11_VIEWPORT_AND_SCISSORRECT_OBJECT_COUNT_PER_PIPELINE)
        UINT NumRects,
    _In_reads_opt_(NumRects) const D3D11_RECT* pRects) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->RSSetScissorRects(NumRects, pRects);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::CopySubresourceRegion(
    _In_ ID3D11Resource* pDstResource,
    _In_ UINT DstSubresource,
    _In_ UINT DstX,
    _In_ UINT DstY,
    _In_ UINT DstZ,
    _In_ ID3D11Resource* pSrcResource,
    _In_ UINT SrcSubresource,
    _In_opt_ const D3D11_BOX* pSrcBox) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CopySubresourceRegion(pDstResource, DstSubresource, DstX,
                                             DstY, DstZ, pSrcResource,
                                             SrcSubresource, pSrcBox);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::CopyResource(_In_ ID3D11Resource* pDstResource,
                                     _In_ ID3D11Resource* pSrcResource) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CopyResource(pDstResource, pSrcResource);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::UpdateSubresource(_In_ ID3D11Resource* pDstResource,
                                          _In_ UINT DstSubresource,
                                          _In_opt_ const D3D11_BOX* pDstBox,
                                          _In_ const void* pSrcData,
                                          _In_ UINT SrcRowPitch,
                                          _In_ UINT SrcDepthPitch) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->UpdateSubresource(pDstResource, DstSubresource, pDstBox,
                                         pSrcData, SrcRowPitch, SrcDepthPitch);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::CopyStructureCount(
    _In_ ID3D11Buffer* pDstBuffer,
    _In_ UINT DstAlignedByteOffset,
    _In_ ID3D11UnorderedAccessView* pSrcView) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CopyStructureCount(pDstBuffer, DstAlignedByteOffset,
                                          pSrcView);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::ClearRenderTargetView(
    _In_ ID3D11RenderTargetView* pRenderTargetView,
    _In_ const FLOAT ColorRGBA[4]) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->ClearRenderTargetView(pRenderTargetView, ColorRGBA);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::ClearUnorderedAccessViewUint(
    _In_ ID3D11UnorderedAccessView* pUnorderedAccessView,
    _In_ const UINT Values[4]) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->ClearUnorderedAccessViewUint(pUnorderedAccessView,
                                                    Values);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::ClearUnorderedAccessViewFloat(
    _In_ ID3D11UnorderedAccessView* pUnorderedAccessView,
    _In_ const FLOAT Values[4]) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->ClearUnorderedAccessViewFloat(pUnorderedAccessView,
                                                     Values);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::ClearDepthStencilView(
    _In_ ID3D11DepthStencilView* pDepthStencilView,
    _In_ UINT ClearFlags,
    _In_ FLOAT Depth,
    _In_ UINT8 Stencil) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->ClearDepthStencilView(pDepthStencilView, ClearFlags,
                                             Depth, Stencil);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::GenerateMips(
    _In_ ID3D11ShaderResourceView* pShaderResourceView) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GenerateMips(pShaderResourceView);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::SetResourceMinLOD(_In_ ID3D11Resource* pResource,
                                          FLOAT MinLOD) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->SetResourceMinLOD(pResource, MinLOD);
}

FLOAT STDMETHODCALLTYPE
D3D11ContextDebugLayer::GetResourceMinLOD(_In_ ID3D11Resource* pResource) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GetResourceMinLOD(pResource);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::ResolveSubresource(_In_ ID3D11Resource* pDstResource,
                                           _In_ UINT DstSubresource,
                                           _In_ ID3D11Resource* pSrcResource,
                                           _In_ UINT SrcSubresource,
                                           _In_ DXGI_FORMAT Format) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->ResolveSubresource(pDstResource, DstSubresource,
                                          pSrcResource, SrcSubresource, Format);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::ExecuteCommandList(_In_ ID3D11CommandList* pCommandList,
                                           BOOL RestoreContextState) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->ExecuteCommandList(pCommandList, RestoreContextState);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::HSSetShaderResources(
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumViews,
    _In_reads_opt_(NumViews)
        ID3D11ShaderResourceView* const* ppShaderResourceViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->HSSetShaderResources(StartSlot, NumViews,
                                            ppShaderResourceViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::HSSetShader(
    _In_opt_ ID3D11HullShader* pHullShader,
    _In_reads_opt_(NumClassInstances)
        ID3D11ClassInstance* const* ppClassInstances,
    UINT NumClassInstances) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->HSSetShader(pHullShader, ppClassInstances,
                                   NumClassInstances);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::HSSetSamplers(
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot)
        UINT NumSamplers,
    _In_reads_opt_(NumSamplers) ID3D11SamplerState* const* ppSamplers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->HSSetSamplers(StartSlot, NumSamplers, ppSamplers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::HSSetConstantBuffers(
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _In_reads_opt_(NumBuffers) ID3D11Buffer* const* ppConstantBuffers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->HSSetConstantBuffers(StartSlot, NumBuffers,
                                            ppConstantBuffers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::DSSetShaderResources(
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumViews,
    _In_reads_opt_(NumViews)
        ID3D11ShaderResourceView* const* ppShaderResourceViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DSSetShaderResources(StartSlot, NumViews,
                                            ppShaderResourceViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::DSSetShader(
    _In_opt_ ID3D11DomainShader* pDomainShader,
    _In_reads_opt_(NumClassInstances)
        ID3D11ClassInstance* const* ppClassInstances,
    UINT NumClassInstances) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DSSetShader(pDomainShader, ppClassInstances,
                                   NumClassInstances);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::DSSetSamplers(
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot)
        UINT NumSamplers,
    _In_reads_opt_(NumSamplers) ID3D11SamplerState* const* ppSamplers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DSSetSamplers(StartSlot, NumSamplers, ppSamplers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::DSSetConstantBuffers(
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _In_reads_opt_(NumBuffers) ID3D11Buffer* const* ppConstantBuffers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DSSetConstantBuffers(StartSlot, NumBuffers,
                                            ppConstantBuffers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::CSSetShaderResources(
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumViews,
    _In_reads_opt_(NumViews)
        ID3D11ShaderResourceView* const* ppShaderResourceViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CSSetShaderResources(StartSlot, NumViews,
                                            ppShaderResourceViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::CSSetUnorderedAccessViews(
    _In_range_(0, D3D11_1_UAV_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_1_UAV_SLOT_COUNT - StartSlot) UINT NumUAVs,
    _In_reads_opt_(NumUAVs)
        ID3D11UnorderedAccessView* const* ppUnorderedAccessViews,
    _In_reads_opt_(NumUAVs) const UINT* pUAVInitialCounts) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CSSetUnorderedAccessViews(
      StartSlot, NumUAVs, ppUnorderedAccessViews, pUAVInitialCounts);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::CSSetShader(
    _In_opt_ ID3D11ComputeShader* pComputeShader,
    _In_reads_opt_(NumClassInstances)
        ID3D11ClassInstance* const* ppClassInstances,
    UINT NumClassInstances) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CSSetShader(pComputeShader, ppClassInstances,
                                   NumClassInstances);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::CSSetSamplers(
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot)
        UINT NumSamplers,
    _In_reads_opt_(NumSamplers) ID3D11SamplerState* const* ppSamplers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CSSetSamplers(StartSlot, NumSamplers, ppSamplers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::CSSetConstantBuffers(
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _In_reads_opt_(NumBuffers) ID3D11Buffer* const* ppConstantBuffers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CSSetConstantBuffers(StartSlot, NumBuffers,
                                            ppConstantBuffers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::VSGetConstantBuffers(
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _Out_writes_opt_(NumBuffers) ID3D11Buffer** ppConstantBuffers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->VSGetConstantBuffers(StartSlot, NumBuffers,
                                            ppConstantBuffers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::PSGetShaderResources(
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumViews,
    _Out_writes_opt_(NumViews)
        ID3D11ShaderResourceView** ppShaderResourceViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->PSGetShaderResources(StartSlot, NumViews,
                                            ppShaderResourceViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::PSGetShader(
    _Outptr_result_maybenull_ ID3D11PixelShader** ppPixelShader,
    _Out_writes_opt_(*pNumClassInstances)
        ID3D11ClassInstance** ppClassInstances,
    _Inout_opt_ UINT* pNumClassInstances) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->PSGetShader(ppPixelShader, ppClassInstances,
                                   pNumClassInstances);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::PSGetSamplers(
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot)
        UINT NumSamplers,
    _Out_writes_opt_(NumSamplers) ID3D11SamplerState** ppSamplers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->PSGetSamplers(StartSlot, NumSamplers, ppSamplers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::VSGetShader(
    _Outptr_result_maybenull_ ID3D11VertexShader** ppVertexShader,
    _Out_writes_opt_(*pNumClassInstances)
        ID3D11ClassInstance** ppClassInstances,
    _Inout_opt_ UINT* pNumClassInstances) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->VSGetShader(ppVertexShader, ppClassInstances,
                                   pNumClassInstances);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::PSGetConstantBuffers(
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _Out_writes_opt_(NumBuffers) ID3D11Buffer** ppConstantBuffers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->PSGetConstantBuffers(StartSlot, NumBuffers,
                                            ppConstantBuffers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::IAGetInputLayout(
    _Outptr_result_maybenull_ ID3D11InputLayout** ppInputLayout) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->IAGetInputLayout(ppInputLayout);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::IAGetVertexBuffers(
    _In_range_(0, D3D11_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_IA_VERTEX_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _Out_writes_opt_(NumBuffers) ID3D11Buffer** ppVertexBuffers,
    _Out_writes_opt_(NumBuffers) UINT* pStrides,
    _Out_writes_opt_(NumBuffers) UINT* pOffsets) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->IAGetVertexBuffers(StartSlot, NumBuffers,
                                          ppVertexBuffers, pStrides, pOffsets);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::IAGetIndexBuffer(
    _Outptr_opt_result_maybenull_ ID3D11Buffer** pIndexBuffer,
    _Out_opt_ DXGI_FORMAT* Format,
    _Out_opt_ UINT* Offset) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->IAGetIndexBuffer(pIndexBuffer, Format, Offset);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::GSGetConstantBuffers(
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _Out_writes_opt_(NumBuffers) ID3D11Buffer** ppConstantBuffers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GSGetConstantBuffers(StartSlot, NumBuffers,
                                            ppConstantBuffers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::GSGetShader(
    _Outptr_result_maybenull_ ID3D11GeometryShader** ppGeometryShader,
    _Out_writes_opt_(*pNumClassInstances)
        ID3D11ClassInstance** ppClassInstances,
    _Inout_opt_ UINT* pNumClassInstances) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GSGetShader(ppGeometryShader, ppClassInstances,
                                   pNumClassInstances);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::IAGetPrimitiveTopology(
    _Out_ D3D11_PRIMITIVE_TOPOLOGY* pTopology) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->IAGetPrimitiveTopology(pTopology);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::VSGetShaderResources(
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumViews,
    _Out_writes_opt_(NumViews)
        ID3D11ShaderResourceView** ppShaderResourceViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->VSGetShaderResources(StartSlot, NumViews,
                                            ppShaderResourceViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::VSGetSamplers(
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot)
        UINT NumSamplers,
    _Out_writes_opt_(NumSamplers) ID3D11SamplerState** ppSamplers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->VSGetSamplers(StartSlot, NumSamplers, ppSamplers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::GetPredication(
    _Outptr_opt_result_maybenull_ ID3D11Predicate** ppPredicate,
    _Out_opt_ BOOL* pPredicateValue) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GetPredication(ppPredicate, pPredicateValue);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::GSGetShaderResources(
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumViews,
    _Out_writes_opt_(NumViews)
        ID3D11ShaderResourceView** ppShaderResourceViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GSGetShaderResources(StartSlot, NumViews,
                                            ppShaderResourceViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::GSGetSamplers(
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot)
        UINT NumSamplers,
    _Out_writes_opt_(NumSamplers) ID3D11SamplerState** ppSamplers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GSGetSamplers(StartSlot, NumSamplers, ppSamplers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::OMGetRenderTargets(
    _In_range_(0, D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT) UINT NumViews,
    _Out_writes_opt_(NumViews) ID3D11RenderTargetView** ppRenderTargetViews,
    _Outptr_opt_result_maybenull_ ID3D11DepthStencilView** ppDepthStencilView) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->OMGetRenderTargets(NumViews, ppRenderTargetViews,
                                          ppDepthStencilView);
}

void STDMETHODCALLTYPE
D3D11ContextDebugLayer::OMGetRenderTargetsAndUnorderedAccessViews(
    _In_range_(0, D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT) UINT NumRTVs,
    _Out_writes_opt_(NumRTVs) ID3D11RenderTargetView** ppRenderTargetViews,
    _Outptr_opt_result_maybenull_ ID3D11DepthStencilView** ppDepthStencilView,
    _In_range_(0, D3D11_PS_CS_UAV_REGISTER_COUNT - 1) UINT UAVStartSlot,
    _In_range_(0, D3D11_PS_CS_UAV_REGISTER_COUNT - UAVStartSlot) UINT NumUAVs,
    _Out_writes_opt_(NumUAVs)
        ID3D11UnorderedAccessView** ppUnorderedAccessViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->OMGetRenderTargetsAndUnorderedAccessViews(
      NumRTVs, ppRenderTargetViews, ppDepthStencilView, UAVStartSlot, NumUAVs,
      ppUnorderedAccessViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::OMGetBlendState(
    _Outptr_opt_result_maybenull_ ID3D11BlendState** ppBlendState,
    _Out_opt_ FLOAT BlendFactor[4],
    _Out_opt_ UINT* pSampleMask) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->OMGetBlendState(ppBlendState, BlendFactor, pSampleMask);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::OMGetDepthStencilState(
    _Outptr_opt_result_maybenull_ ID3D11DepthStencilState** ppDepthStencilState,
    _Out_opt_ UINT* pStencilRef) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->OMGetDepthStencilState(ppDepthStencilState, pStencilRef);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::SOGetTargets(
    _In_range_(0, D3D11_SO_BUFFER_SLOT_COUNT) UINT NumBuffers,
    _Out_writes_opt_(NumBuffers) ID3D11Buffer** ppSOTargets) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->SOGetTargets(NumBuffers, ppSOTargets);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::RSGetState(
    _Outptr_result_maybenull_ ID3D11RasterizerState** ppRasterizerState) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->RSGetState(ppRasterizerState);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::RSGetViewports(
    _Inout_ UINT* pNumViewports,
    _Out_writes_opt_(*pNumViewports) D3D11_VIEWPORT* pViewports) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->RSGetViewports(pNumViewports, pViewports);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::RSGetScissorRects(
    _Inout_ UINT* pNumRects,
    _Out_writes_opt_(*pNumRects) D3D11_RECT* pRects) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->RSGetScissorRects(pNumRects, pRects);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::HSGetShaderResources(
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumViews,
    _Out_writes_opt_(NumViews)
        ID3D11ShaderResourceView** ppShaderResourceViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->HSGetShaderResources(StartSlot, NumViews,
                                            ppShaderResourceViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::HSGetShader(
    _Outptr_result_maybenull_ ID3D11HullShader** ppHullShader,
    _Out_writes_opt_(*pNumClassInstances)
        ID3D11ClassInstance** ppClassInstances,
    _Inout_opt_ UINT* pNumClassInstances) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->HSGetShader(ppHullShader, ppClassInstances,
                                   pNumClassInstances);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::HSGetSamplers(
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot)
        UINT NumSamplers,
    _Out_writes_opt_(NumSamplers) ID3D11SamplerState** ppSamplers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->HSGetSamplers(StartSlot, NumSamplers, ppSamplers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::HSGetConstantBuffers(
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _Out_writes_opt_(NumBuffers) ID3D11Buffer** ppConstantBuffers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->HSGetConstantBuffers(StartSlot, NumBuffers,
                                            ppConstantBuffers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::DSGetShaderResources(
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumViews,
    _Out_writes_opt_(NumViews)
        ID3D11ShaderResourceView** ppShaderResourceViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DSGetShaderResources(StartSlot, NumViews,
                                            ppShaderResourceViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::DSGetShader(
    _Outptr_result_maybenull_ ID3D11DomainShader** ppDomainShader,
    _Out_writes_opt_(*pNumClassInstances)
        ID3D11ClassInstance** ppClassInstances,
    _Inout_opt_ UINT* pNumClassInstances) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DSGetShader(ppDomainShader, ppClassInstances,
                                   pNumClassInstances);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::DSGetSamplers(
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot)
        UINT NumSamplers,
    _Out_writes_opt_(NumSamplers) ID3D11SamplerState** ppSamplers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DSGetSamplers(StartSlot, NumSamplers, ppSamplers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::DSGetConstantBuffers(
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _Out_writes_opt_(NumBuffers) ID3D11Buffer** ppConstantBuffers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->DSGetConstantBuffers(StartSlot, NumBuffers,
                                            ppConstantBuffers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::CSGetShaderResources(
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - StartSlot)
        UINT NumViews,
    _Out_writes_opt_(NumViews)
        ID3D11ShaderResourceView** ppShaderResourceViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CSGetShaderResources(StartSlot, NumViews,
                                            ppShaderResourceViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::CSGetUnorderedAccessViews(
    _In_range_(0, D3D11_1_UAV_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_1_UAV_SLOT_COUNT - StartSlot) UINT NumUAVs,
    _Out_writes_opt_(NumUAVs)
        ID3D11UnorderedAccessView** ppUnorderedAccessViews) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CSGetUnorderedAccessViews(StartSlot, NumUAVs,
                                                 ppUnorderedAccessViews);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::CSGetShader(
    _Outptr_result_maybenull_ ID3D11ComputeShader** ppComputeShader,
    _Out_writes_opt_(*pNumClassInstances)
        ID3D11ClassInstance** ppClassInstances,
    _Inout_opt_ UINT* pNumClassInstances) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CSGetShader(ppComputeShader, ppClassInstances,
                                   pNumClassInstances);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::CSGetSamplers(
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1) UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - StartSlot)
        UINT NumSamplers,
    _Out_writes_opt_(NumSamplers) ID3D11SamplerState** ppSamplers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CSGetSamplers(StartSlot, NumSamplers, ppSamplers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::CSGetConstantBuffers(
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1)
        UINT StartSlot,
    _In_range_(0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - StartSlot)
        UINT NumBuffers,
    _Out_writes_opt_(NumBuffers) ID3D11Buffer** ppConstantBuffers) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->CSGetConstantBuffers(StartSlot, NumBuffers,
                                            ppConstantBuffers);
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::ClearState(void) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->ClearState();
}

void STDMETHODCALLTYPE D3D11ContextDebugLayer::Flush(void) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->Flush();
}

D3D11_DEVICE_CONTEXT_TYPE STDMETHODCALLTYPE
D3D11ContextDebugLayer::GetType(void) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GetType();
}

UINT STDMETHODCALLTYPE D3D11ContextDebugLayer::GetContextFlags(void) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->GetContextFlags();
}

HRESULT STDMETHODCALLTYPE D3D11ContextDebugLayer::FinishCommandList(
    BOOL RestoreDeferredContextState,
    _COM_Outptr_opt_ ID3D11CommandList** ppCommandList) {
  TRACE_DEVICE_CONTEXT_CALL
  return m_rawContext->FinishCommandList(RestoreDeferredContextState,
                                         ppCommandList);
}
