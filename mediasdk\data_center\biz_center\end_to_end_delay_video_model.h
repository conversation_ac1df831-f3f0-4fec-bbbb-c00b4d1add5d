#pragma once

#include <memory>
#include <mutex>
#include "mediasdk/data_center/biz_center/end_to_end_capture.h"
#include "mediasdk/data_center/biz_center/end_to_end_delay.h"

namespace mediasdk {

class EndtoEndDelayVideoModel : public EndtoEndDelay {
 public:
  EndtoEndDelayVideoModel();

  ~EndtoEndDelayVideoModel() override;

  void CollectCaptureTimestmapNS(const char* cap_name, int64_t timestamp);

  std::shared_ptr<EndtoEndDelayCaptureConst> CompleteCollectCaptures();

  void OutputMixBegin();

  void OutputMixEnd();

  inline int64_t GetMixBeginNS() { return mix_begin_ns_; }

  // EndtoEndDelay
  bool FillCostData(std::map<std::string, int64_t>& cost_map) override;

 private:
  std::mutex mutex_;
  std::shared_ptr<EndtoEndDelayCaptures> captures_;
  std::map<const char*, EndtoEndDelayCostData> caputure_cost_map_;
  EndtoEndDelayCostData mix_cost_data_;
  int64_t mix_begin_ns_ = 0;
};

}  // namespace mediasdk
