#pragma once
#include <memory>
#include <string>
#include "cpu_information.h"

namespace cpu_collector {

class CpuInformationImpl final : public CpuInformation {
 public:
  std::string GetCpuName() override;

  uint32_t GetClockSpeed() override;

  uint32_t GetProcessorNum() override;

  void SetCpuName(const std::string& name) { cpu_name_ = name; }

  void SetClockSpeed(uint32_t speed) { clock_speed_ = speed; }

  void SetNum(uint32_t num) { num_ = num; }

 private:
  std::string cpu_name_;
  uint32_t clock_speed_ = 0;
  uint32_t num_ = 0;
};
}  // namespace cpu_collector
