#pragma once

#include <Shlwapi.h>
#include <gdiplus.h>
#include <gdiplusgraphics.h>
#include <memory>
#include <string>
#include "DXSimpleMath.h"
#include "base/memory/aligned_memory.h"
#include "text.h"

namespace graphics {

class GDIFontRender {
 public:
  GDIFontRender();

  bool UpdateSetting(Text::TextOPT opt);
  ~GDIFontRender();

  DirectX::XMFLOAT2 GetSize() const { return size_; }

  int32_t GetBufferSize() const {
    return ((int32_t)size_.x) * ((int32_t)size_.y) * 4;
  }

  uint8_t* GetBuffer() { return bitmap_buffer_.get(); }

 private:
  bool MeasureString();
  bool MeasureString(const std::wstring& szTEXT,
                     const Gdiplus::StringFormat& sFormat,
                     const LOGFONTW& s,
                     Gdiplus::RectF& box,
                     DirectX::XMFLOAT2& size,
                     bool outline,
                     int32_t outline_size);
  void GetStringFormat(Gdiplus::StringFormat& sFormat, bool vertical);
  bool DrawStringWithOutline(const std::wstring& text,
                             const Gdiplus::StringFormat& string_format,
                             const Gdiplus::SolidBrush& brush,
                             Gdiplus::RectF& rect,
                             Gdiplus::Graphics& graphics_for_draw);

 private:
  HDC hdc_;
  HFONT font_;
  Gdiplus::Graphics gdi_graphics_;
  DirectX::XMFLOAT2 size_;
  std::unique_ptr<uint8_t[], base::AlignedFreeDeleter> bitmap_buffer_;
  std::unique_ptr<Gdiplus::Font> font_ptr_;
  Text::TextOPT opt_;
};

}  // namespace graphics
