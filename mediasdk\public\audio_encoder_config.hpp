#pragma once

#include <audio/audio_format.h>
#include <memory>
#include <nlohmann/json.hpp>
#include <string>

namespace mediasdk {
struct AudioEncoderConfigStorage {
  std::string id;
  std::string name;
  uint32_t track_id = 0;
  uint32_t bitrate = 0;
  std::string profile;
  int32_t format = AUDIO_FORMAT::AUDIO_FORMAT_FLOAT_PLANAR;
  int32_t channel_layout = CHANNEL_LAYOUT::CHANNEL_STEREO;
  uint32_t channel_count = 2;
  uint32_t sample_rate = 48000;
  uint32_t packet_size = 1024;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_WITH_DEFAULT(AudioEncoderConfigStorage,
                                                id,
                                                name,
                                                track_id,
                                                bitrate,
                                                profile,
                                                format,
                                                channel_layout,
                                                channel_count,
                                                sample_rate,
                                                packet_size);

class AudioEncoderConfig {
 public:
  AudioEncoderConfig() = default;

  ~AudioEncoderConfig() = default;

  explicit AudioEncoderConfig(AudioEncoderConfigStorage storage)
      : storage_(std::move(storage)) {}

  bool FromJsonString(const std::string_view json_str) {
    try {
      storage_ = nlohmann::json::parse(json_str);
      return true;
    } catch (const std::exception& e) {
      return false;
    }
  }

  std::string GetId() const { return storage_.id; }

  std::string GetEncoderName() const { return storage_.name; }

  uint32_t GetTrackId() const { return storage_.track_id; }

  uint32_t GetBitRateKbps() const { return storage_.bitrate; }

  std::string GetProfile() const { return storage_.profile; }

  // TODO(lgd): fix with kAudioOutputFormat
  AUDIO_FORMAT GetFormat() const {
    return static_cast<AUDIO_FORMAT>(storage_.format);
  }

  CHANNEL_LAYOUT GetChannelLayout() const {
    return static_cast<CHANNEL_LAYOUT>(storage_.channel_layout);
  }

  uint32_t GetChannelCount() const { return storage_.channel_count; }

  uint32_t GetSampleRate() const { return storage_.sample_rate; }

  uint32_t GetFixedPacketSize() const { return storage_.packet_size; }

  std::string ToString() const {
    std::string result;
    try {
      const nlohmann::json json = storage_;
      result = json.dump();
    } catch (const std::exception& e) {
    }
    return result;
  }

  AudioEncoderConfigStorage GetStorage() const { return storage_; }

 private:
  AudioEncoderConfigStorage storage_;
};

}  // namespace mediasdk
