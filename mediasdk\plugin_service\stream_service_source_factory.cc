#include "stream_service_source_factory.h"

#include "base/logging.h"
#include "mediasdk/mediasdk_thread.h"
#include "mediasdk/public/mediasdk_defines.h"
#include "mediasdk/public/plugin/stream_service_proxy.h"
#include "mediasdk/public/plugin/stream_service_source.h"

namespace mediasdk {

// static
std::shared_ptr<StreamServiceSourceFactory> StreamServiceSourceFactory::Create(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info) {
  auto factory = std::make_shared<StreamServiceSourceFactory>(library, info);
  if (factory && factory->Load()) {
    return factory;
  }

  return nullptr;
}

StreamServiceSourceFactory::StreamServiceSourceFactory(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info)
    : SourceFactory(library, info) {}

std::shared_ptr<StreamServiceSource> StreamServiceSourceFactory::CreateSource(
    std::shared_ptr<StreamServiceProxy> proxy,
    const std::string& json_params) {
  if (create_func_) {
    auto* ret = create_func_(proxy.get(), json_params.c_str());
    if (ret) {
      auto destroy_func = destroy_func_;
      std::shared_ptr<StreamServiceSource> source(
          ret, [destroy_func](StreamServiceSource* input) {
            DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);
            destroy_func(input);
          });
      sources_.insert(source);
      return source;
    }
  }
  return nullptr;
}

void StreamServiceSourceFactory::Destroy(
    std::shared_ptr<StreamServiceSource> source) {
  sources_.erase(source);
}

void StreamServiceSourceFactory::DestroyAll() {
  sources_.clear();
}

bool StreamServiceSourceFactory::Load() {
  create_func_ = reinterpret_cast<CreateStreamServiceSourceFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "CreateStreamServiceSource"));

  if (!create_func_) {
    LOG(ERROR)
        << "Failed to get function pointer for CreateStreamServiceSource";
    return false;
  }

  destroy_func_ = reinterpret_cast<DestroyStreamServiceSource>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "DestroyStreamServiceSource"));

  if (!destroy_func_) {
    LOG(ERROR) << "Failed to get function pointer for DestroyStreamService";
    return false;
  }

  return true;
}

}  // namespace mediasdk
