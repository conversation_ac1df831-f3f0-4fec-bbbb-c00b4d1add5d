#include "lyrax_audio_capture_delegate.h"
#include "base/check.h"
#include "lyrax/lyrax_audio_device_delegate.h"
#include "lyrax_audio_input.h"
#include "nlohmann/json.hpp"

namespace mediasdk {

// sync from platform\win32\wasapi\wasapi_audio.h
constexpr int DEVICE_STATE_START = 0;
constexpr int DEVICE_STATE_STOP = 1;
constexpr int DEVICE_ERROR_CODE_SUCCESS = 0;

LyraxAudioCaptureDelegate::LyraxAudioCaptureDelegate(
    std::weak_ptr<LyraxAudioInput> input)
    : input_(std::move(input)) {
  state_ = AudioDeviceState::kStop;
}

LyraxAudioCaptureDelegate::~LyraxAudioCaptureDelegate() {}

// call on setAudioDeviceDelegate Thread, handler_ set is before audio source
// attach
void LyraxAudioCaptureDelegate::onAttach(
    std::shared_ptr<lyrax::ILyraxAudioDeviceDelegateHandler> handler) {
  handler_ = handler;
}

bool LyraxAudioCaptureDelegate::IsRawDataModeEnabled() const {
  return raw_data_mode_enabled_;
}

// call on wasapi audio thread, audio_input will destroy after audio source
void LyraxAudioCaptureDelegate::SignalEvent(const std::string& json_str) {
  auto handler = handler_;
  if (!handler) {
    // abort when debug
    DCHECK(false) << "handler_ not set";
    return;
  }

  // try to parse json
  try {
    nlohmann::json json_data = nlohmann::json::parse(json_str);
    std::string event_name = json_data["event_name"];
    // convert to lyrax event
    std::optional<lyrax::LyraxAudioDeviceDelegateEvent> event = {};
    if (event_name == "OnAudioDeviceStateChange") {
      // device state change event
      int new_state = json_data["new_state"];
      int error_code = json_data["error_code"];
      std::string msg = json_data["msg"];
      LOG(INFO) << "SignalEvent OnAudioDeviceStateChange new_state: "
                << new_state << " error_code: " << error_code
                << " msg: " << msg;

      event = lyrax::LyraxAudioDeviceDelegateEvent();
      if (new_state == DEVICE_STATE_START) {
        event->key =
            lyrax::LyraxAudioDeviceDelegateEvent::EventKey::kStartDevice;
        // update state
        state_ = error_code == DEVICE_ERROR_CODE_SUCCESS
                     ? AudioDeviceState::kStart
                     : AudioDeviceState::kStop;
      } else if (new_state == DEVICE_STATE_STOP) {
        event->key =
            lyrax::LyraxAudioDeviceDelegateEvent::EventKey::kStopDevice;
        state_ = AudioDeviceState::kStop;
      } else {
        LOG(ERROR) << "SignalEvent unknown state: " << new_state;
        return;
      }

      event->error_code = error_code == DEVICE_ERROR_CODE_SUCCESS
                              ? lyrax::LyraxErrorCode::kSuccess
                              : lyrax::LyraxErrorCode::kFailure;
    } else if (event_name == "OnAudioDeviceInfoUpdate") {
      // device info update event
      std::string device_id = json_data["device_id"];
      std::string device_name = json_data["device_name"];
      LOG(INFO) << "SignalEvent OnAudioDeviceInfoUpdate device_id: "
                << device_id << " device_name: " << device_name;
      if (handler_) {
        lyrax::LyraxAudioDeviceDelegateInfo info;
        info.id = device_id;
        info.name = device_name;
        // set as default category
        info.category = lyrax::LyraxAudioDeviceDelegateCategory::kDefault;
        handler_->onAudioDeviceInfoUpdate(info);
      }
    } else if (event_name == "WASAPIAudioSetRawDataModeResult") {
      // raw data set result event
      bool success = json_data["success"];
      std::string msg = json_data["msg"];
      LOG(INFO) << "SignalEvent WASAPIAudioSetRawDataModeResult success: "
                << success << " msg: " << msg;
      event = lyrax::LyraxAudioDeviceDelegateEvent();
      event->key = lyrax::LyraxAudioDeviceDelegateEvent::EventKey::kSetRawData;
      event->error_code = success ? lyrax::LyraxErrorCode::kSuccess
                                  : lyrax::LyraxErrorCode::kFailure;
      event->msg = msg;
    } else if (event_name == "OnDeviceVolume") {
      // device volume changed event
      float volume = json_data["volume"];
      last_system_volume_ = volume;
      bool mute = json_data["mute"];
      LOG(INFO) << "SignalEvent OnDeviceVolume volume: " << volume
                << " mute: " << mute;
      event = lyrax::LyraxAudioDeviceDelegateEvent();
      event->key =
          lyrax::LyraxAudioDeviceDelegateEvent::EventKey::kMicVolumeChanged;
      event
          ->infos[lyrax::LyraxAudioDeviceDelegateEvent::AttributeKey::kVolume] =
          lyrax::LyraxValue(static_cast<double>(volume));
      event->infos
          [lyrax::LyraxAudioDeviceDelegateEvent::AttributeKey::kSystemMute] =
          lyrax::LyraxValue(mute);
    } else if (event_name == "WASAPIAudioSetMicBoostResult") {
      // mic boost set result event
      bool is_supported = json_data["is_supported"];
      float min_level_db = json_data["min_level_db"];
      float max_level_db = json_data["max_level_db"];
      float stepping = json_data["stepping"];
      float original_level_db = json_data["original_level_db"];
      float current_level_db = json_data["current_level_db"];
      int gain_proportion = json_data["gain_proportion"];
      LOG(INFO) << "SignalEvent WASAPIAudioSetMicBoostResult is_supported: "
                << is_supported << " min_level_db: " << min_level_db
                << " max_level_db: " << max_level_db
                << " stepping: " << stepping
                << " original_level_db: " << original_level_db
                << " current_level_db: " << current_level_db
                << " gain_proportion: " << gain_proportion;

      event = lyrax::LyraxAudioDeviceDelegateEvent();
      event->key =
          lyrax::LyraxAudioDeviceDelegateEvent::EventKey::kSetMicBoostGain;
      event->error_code = is_supported ? lyrax::LyraxErrorCode::kSuccess
                                       : lyrax::LyraxErrorCode::kNotSupport;
      event->infos[lyrax::LyraxAudioDeviceDelegateEvent::AttributeKey::
                       kMicBoostIsSupported] = lyrax::LyraxValue(is_supported);
      event->infos[lyrax::LyraxAudioDeviceDelegateEvent::AttributeKey::
                       kMicBoostMinLevelDb] =
          lyrax::LyraxValue(static_cast<int>(min_level_db));
      event->infos[lyrax::LyraxAudioDeviceDelegateEvent::AttributeKey::
                       kMicBoostMaxLevelDb] =
          lyrax::LyraxValue(static_cast<int>(max_level_db));
      event->infos
          [lyrax::LyraxAudioDeviceDelegateEvent::AttributeKey::kMicBoostStep] =
          lyrax::LyraxValue(static_cast<int>(stepping));
      event->infos[lyrax::LyraxAudioDeviceDelegateEvent::AttributeKey::
                       kMicBoostOriginalLevelDb] =
          lyrax::LyraxValue(static_cast<int>(original_level_db));
      event->infos[lyrax::LyraxAudioDeviceDelegateEvent::AttributeKey::
                       kMicBoostCurrentLevelDb] =
          lyrax::LyraxValue(static_cast<int>(current_level_db));
      event->infos
          [lyrax::LyraxAudioDeviceDelegateEvent::AttributeKey::kMicBoostGain] =
          lyrax::LyraxValue(gain_proportion);
    } else {
      return;
    }

    // safe to call handler_
    // handler_ set is before audio source attach, no need to cache json_str
    if (event) {
      LOG(INFO) << "Handle SignalEvent event_name: " << event_name;
      handler->onEvent(event.value());
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "Failed to parse json: " << e.what();
    return;
  }
}

lyrax::LyraxErrorCode LyraxAudioCaptureDelegate::onSetParameters(
    const std::unordered_map<lyrax::LyraxAudioDeviceDelegateOptionKey,
                             lyrax::LyraxValue>& params) {
  nlohmann::json json_params;
  for (const auto& param : params) {
    switch (param.first) {
      case lyrax::LyraxAudioDeviceDelegateOptionKey::kEnableRawData: {
        if (param.second.getType() == lyrax::LyraxValueType::kBool) {
          raw_data_mode_enabled_ = param.second.boolValue();
          json_params["enable_raw_data"] = raw_data_mode_enabled_ ? 1 : 0;
        }
        break;
      }
      case lyrax::LyraxAudioDeviceDelegateOptionKey::kSetInitMicVolume: {
        if (param.second.getType() == lyrax::LyraxValueType::kDouble) {
          json_params["init_mic_volume"] =
              static_cast<float>(param.second.doubleValue());
        }
        break;
      }
      case lyrax::LyraxAudioDeviceDelegateOptionKey::kSetMicVolume: {
        if (param.second.getType() == lyrax::LyraxValueType::kDouble) {
          double volume = param.second.doubleValue();
          if (fabs(volume - last_system_volume_) < 1e-5) {
            // same volume, no need to set
            break;
          }
          json_params["mic_volume"] = volume;
        }
        break;
      }
      case lyrax::LyraxAudioDeviceDelegateOptionKey::kSetMicBoostGain: {
        if (param.second.getType() == lyrax::LyraxValueType::kInt) {
          json_params["mic_boost_gain"] = param.second.intValue();
        }
        break;
      }
      case lyrax::LyraxAudioDeviceDelegateOptionKey::kSetMicBoostName: {
        if (param.second.getType() == lyrax::LyraxValueType::kString) {
          json_params["mic_boost_name"] = param.second.stringValue();
        }
        break;
      }
      default:
        break;
    }
  }

  auto audio_input = input_.lock();
  if (audio_input && !json_params.empty()) {
    json_params["event_name"] = "SetWASAPIAudioRuntimeConfig";
    LOG(INFO) << "send action to audio source, json_params: "
              << json_params.dump();
    audio_input->SendActionToAudioSource(json_params.dump());
  }

  return lyrax::LyraxErrorCode::kSuccess;
}

}  // namespace mediasdk
