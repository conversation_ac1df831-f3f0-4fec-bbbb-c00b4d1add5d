#pragma once

#include <memory>
#include <mutex>
#include <string>

#include "public/mediasdk_defines.h"

namespace mediasdk {

struct RTCVideoSourceConfig {
  int max_fps = 30;
  bool enable_simulcast = false;
  int max_bitrate = -1;
  int min_bitrate = 0;
  MSRectF region = {};
  MSScaleF scale = {1, 1};
  MSScaleF intermediate_output_scale = {1.0f, 1.0f};
};

class RTCVideoSourceConfigManager {
 public:
  RTCVideoSourceConfigManager() = default;

  ~RTCVideoSourceConfigManager() = default;

  bool Config(const std::string& config_json);

  RTCVideoSourceConfig GetConfig();

  void SetExcludedVisualIds(const std::vector<std::string>& visual_ids);

  std::vector<std::string> GetExcludedVisualIds();

 protected:
  virtual void OnConfigChanged();

 private:
  bool ApplyConfig(const std::string& config_json);

 private:
  RTCVideoSourceConfig config_;
  std::vector<std::string> excluded_visual_ids_;
  std::mutex mutex_;
};

}  // namespace mediasdk
