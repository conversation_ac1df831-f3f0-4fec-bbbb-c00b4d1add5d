#include "lyrax_audio_processor.h"
#include "lyrax_audio_listener.h"

namespace mediasdk {

class LyraxAudioFrameFilter : public lyrax::ILyraxAudioFilter {
 public:
  explicit LyraxAudioFrameFilter(
      std::function<int(lyrax::LyraxAudioFrame&)> callback)
      : callback_(std::move(callback)) {}

  int onProcessAudioFrame(lyrax::LyraxAudioFrame& lyrax_audio_frame) override {
    if (!callback_) {
      return 0;
    }
    return callback_(lyrax_audio_frame);
  }

 private:
  std::function<int(lyrax::LyraxAudioFrame&)> callback_;
};

void ConvertFromLyraxAudioFormat(lyrax::LyraxAudioFormat& lyrax_audio_format,
                                 AudioFormat& mediasdk_audio_format);

void ConvertToLyraxAudioFormat(const AudioFormat& mediasdk_audio_format,
                               lyrax::LyraxAudioFormat& lyrax_audio_format);

LyraxAudioProcessor::LyraxAudioProcessor(
    LyraxAudioProcessorDelegate* delegate,
    std::shared_ptr<lyrax::ILyraxAudio> lyrax)
    : delegate_(delegate), lyrax_audio_(lyrax) {
  LOG(INFO) << "[LyraxAudioProcessor][LyraxAudioProcessor] construct start.";
  SetANSOption(0);
  SetAECOption(false);
  pre_frame_filter_ = std::make_shared<LyraxAudioFrameFilter>(
      [this](lyrax::LyraxAudioFrame& lyrax_audio_frame) {
        return this->OnLyraxPreAudioFrame(lyrax_audio_frame);
      });
  post_frame_filter_ = std::make_shared<LyraxAudioFrameFilter>(
      [this](lyrax::LyraxAudioFrame& lyrax_audio_frame) {
        return this->OnLyraxPostAudioFrame(lyrax_audio_frame);
      });
  lyrax_audio_listener_ = std::make_shared<LyraxAudioListener>();
  LOG(INFO) << "[LyraxAudioProcessor][LyraxAudioProcessor] construct end.";
}

LyraxAudioProcessor::~LyraxAudioProcessor() {
  LOG(INFO) << "[LyraxAudioProcessor][~LyraxAudioProcessor] destruct start.";
  auto lyrax = GetLyraxAudio();
  if (!lyrax) {
    return;
  }
  lyrax->unregisterAudioFilter(pre_frame_filter_.get());
  lyrax->unregisterAudioFilter(post_frame_filter_.get());
  lyrax->setListener(nullptr);
  last_mic_ms_ts_ = 0;
  if (lyrax_audio_input_stream_) {
    lyrax_audio_input_stream_->dispose();
    lyrax_audio_input_stream_ = nullptr;
  }
  LOG(INFO) << "[LyraxAudioProcessor][~LyraxAudioProcessor] destruct end.";
}

bool ToLyraxAudioFormat(int sample_rate,
                        int channel_count,
                        lyrax::LyraxAudioFormat& format) {
  switch (sample_rate) {
    case 8000:
      format.sample_rate = lyrax::LyraxAudioSampleRate::k8000;
      break;
    case 11025:
      format.sample_rate = lyrax::LyraxAudioSampleRate::k11025;
      break;
    case 16000:
      format.sample_rate = lyrax::LyraxAudioSampleRate::k16000;
      break;
    case 22050:
      format.sample_rate = lyrax::LyraxAudioSampleRate::k22050;
      break;
    case 32000:
      format.sample_rate = lyrax::LyraxAudioSampleRate::k32000;
      break;
    case 44100:
      format.sample_rate = lyrax::LyraxAudioSampleRate::k44100;
      break;
    case 48000:
      format.sample_rate = lyrax::LyraxAudioSampleRate::k48000;
      break;
    default:
      return false;
  }
  switch (channel_count) {
    case 1:
      format.channel_num = lyrax::LyraxAudioChannel::kMono;
      break;
    case 2:
      format.channel_num = lyrax::LyraxAudioChannel::kStereo;
      break;
    default:
      return false;
  }
  return true;
}

// audio frame function
lyrax::LyraxAudioFrame ToLyraxAudioFrame(const AudioFrame& target_audio_frame) {
  lyrax::LyraxAudioFrame lyrax_audio_frame = {};
  lyrax_audio_frame.data =
      reinterpret_cast<int8_t*>(target_audio_frame.GetData(0));
  lyrax_audio_frame.data_size =
      target_audio_frame.GetBlockSize() * target_audio_frame.GetCount();
  if (!ToLyraxAudioFormat(target_audio_frame.GetSampleRate(),
                          target_audio_frame.GetChannelCount(),
                          lyrax_audio_frame.format)) {
    DCHECK(false) << "failed to convert to lyrax audio format: sample_rate="
                  << target_audio_frame.GetSampleRate()
                  << ", channel_count=" << target_audio_frame.GetChannelCount();
    return {};
  }

  return lyrax_audio_frame;
}

void LyraxAudioProcessor::PushMicFrame(const AudioFormat& format,
                                       const AudioFrame& frame) {
  CheckAndChangeLyraxProcessor();
  // DCHECK_CURRENTLY_ON(mic capture thread);

  const auto enable = IsNeedLyraxProcessor();
  if (!enable) {
    // delegate_->OnLyraxPreAudioFrame(format, frame); no need to calculate
    // audio volume
    delegate_->OnLyraxPostAudioFrame(format, frame);
    return;
  }
#ifdef ENABLE_AUDIO_DUMP
  if (!dump_mic_in_) {
    dump_mic_in_ = std::make_unique<WAVWriter>();
  }
  if (mic_name_.empty()) {
    mic_name_ += "mic_in_";
    mic_name_ += std::to_string(milli_now());
    mic_name_ += ".wav";
  }
  dump_mic_in_->Write(mic_name_, format, frame);
#endif
  auto lyrax = GetLyraxAudio();
  if (!lyrax) {
    DCHECK(false);
    return;
  }
  if (!lyrax_audio_input_stream_) {
    LOG(INFO) << "[LyraxAudioProcessor][CheckAndChangeLyraxProcessor] "
                 "lyrax_audio_input_stream_ create start.";
    if (format.GetChannel() == 1) {
      lyrax_mic_target_format_.SetChannel(1);
      lyrax_mic_target_format_.SetLayout(CHANNEL_LAYOUT::CHANNEL_MONO);
    } else {
      lyrax_mic_target_format_.SetChannel(2);
      lyrax_mic_target_format_.SetLayout(CHANNEL_LAYOUT::CHANNEL_STEREO);
    }

    lyrax_audio_input_stream_ = lyrax->createAudioInputStream("media_sdk");
    if (!lyrax_audio_input_stream_) {
      LOG(ERROR) << "Failed to create LyraxAudioInputStream";
      return;
    }
    lyrax_audio_input_stream_->setContentType(lyrax::kLyraxAudioContentTypeMic);
    ConvertToLyraxAudioFormat(lyrax_mic_target_format_,
                              lyrax_audio_mic_input_format_);
    lyrax_audio_input_stream_->setStreamFormat(lyrax_audio_mic_input_format_);
    lyrax_audio_input_stream_->start();
    if (post_frame_filter_) {
      post_frame_filter_->expected_channels_num =
          lyrax_mic_target_format_.GetChannel();
      post_frame_filter_->expected_sample_rate =
          lyrax_mic_target_format_.GetSampleRate();
    }
    if (pre_frame_filter_) {
      pre_frame_filter_->expected_channels_num =
          lyrax_mic_target_format_.GetChannel();
      pre_frame_filter_->expected_sample_rate =
          lyrax_mic_target_format_.GetSampleRate();
    }
    LOG(INFO) << "[LyraxAudioProcessor][CheckAndChangeLyraxProcessor] "
                 "lyrax_audio_input_stream_ create end.";
  }
  if (format != lyrax_mic_target_format_) {
    // resample to lyrax format
    if (!resample_to_lyrax_format_mic_ ||
        resample_to_lyrax_format_mic_->GetInputFormat() != format) {
      resample_to_lyrax_format_mic_ = CreateResample();
      if (!resample_to_lyrax_format_mic_->Open(format,
                                               lyrax_mic_target_format_)) {
        resample_to_lyrax_format_mic_ = nullptr;
        LOG(ERROR) << "Failed to resample to lyrax mic format";
        return;
      }
      LOG(INFO) << "lyrax mic format [" << format.ToString()
                << " convert to lyrax format["
                << lyrax_mic_target_format_.ToString() << "]";
    }
  }
#ifdef ENABLE_AUDIO_DUMP
  if (!dump_mic_after_resample_in_) {
    dump_mic_after_resample_in_ = std::make_unique<WAVWriter>();
  }
#endif
  lyrax::LyraxAudioFrame lyrax_audio_frame = {};
  if (resample_to_lyrax_format_mic_) {
    AudioFrame output_frame;
    if (!resample_to_lyrax_format_mic_->Resample(frame, output_frame)) {
      return;
    }
    lyrax_audio_frame = ToLyraxAudioFrame(output_frame);
#ifdef ENABLE_AUDIO_DUMP

    dump_mic_after_resample_in_->Write("lyrax_mic_resample_in.wav", lyrax_mic_target_format_,
                                       output_frame);
#endif
  } else {
    lyrax_audio_frame = ToLyraxAudioFrame(frame);
#ifdef ENABLE_AUDIO_DUMP

    dump_mic_after_resample_in_->Write("lyrax_mic_resample_in.wav", format,
                                       frame);
#endif
  }
  last_mic_ms_ts_ = mediasdk::milli_now();

  auto ret = lyrax->pushExternalAudioFrame(lyrax_audio_frame);
  if (ret != lyrax::LyraxErrorCode::kSuccess) {
    LOG(ERROR) << "Failed to pushExternalAudioFrame[" << ret << "] cost ["
               << mediasdk::milli_now() - last_mic_ms_ts_;
  }
}

void LyraxAudioProcessor::PushRefFrame(const AudioFormat& format,
                                       const AudioFrame& frame) {
  // DCHECK_CURRENTLY_ON(loopback capture);
  if (!aec_enable_ && !IsEchoDetectionEnable())
    return;
#ifdef ENABLE_AUDIO_DUMP
  if (!dump_ref_in_) {
    dump_ref_in_ = std::make_unique<WAVWriter>();
  }
  if (ref_name_.empty()) {
    ref_name_ += "ref_in_";
    ref_name_ += std::to_string(milli_now());
    ref_name_ += ".wav";
  }
  dump_ref_in_->Write(ref_name_, format, frame);
#endif
  auto lyrax = GetLyraxAudio();
  if (!lyrax) {
    DCHECK(false);
    return;
  }
  if (format != lyrax_ref_target_format_) {
    // resample to lyrax format
    if (!resample_to_lyrax_format_ref_ ||
        resample_to_lyrax_format_ref_->GetInputFormat() != format) {
      resample_to_lyrax_format_ref_ = CreateResample();
      if (!resample_to_lyrax_format_ref_->Open(format,
                                               lyrax_ref_target_format_)) {
        resample_to_lyrax_format_ref_ = nullptr;
        LOG(ERROR) << "Failed to resample to lyrax ref format";
        return;
      }
      LOG(INFO) << "lyrax ref format [" << format.ToString()
                << " convert to lyrax format["
                << lyrax_ref_target_format_.ToString() << "]";
    }
  }
  lyrax::LyraxAudioFrame lyrax_audio_frame;
  if (resample_to_lyrax_format_ref_) {
    AudioFrame output_frame;
    resample_to_lyrax_format_ref_->Resample(frame, output_frame);
    lyrax_audio_frame = ToLyraxAudioFrame(output_frame);
  } else {
    lyrax_audio_frame = ToLyraxAudioFrame(frame);
  }
  auto ret = lyrax->pushExternalReferenceAudioFrame(lyrax_audio_frame);
  if (ret != lyrax::LyraxErrorCode::kSuccess) {
    LOG(ERROR) << "Failed to pushExternalReferenceAudioFrame[" << ret << "]";
  }
}

void LyraxAudioProcessor::SetMute(bool mute) {
  LOG(INFO) << "LyraxAudioProcessor SetMute [" << mute << "]";
  is_mute_ = mute;
}

void LyraxAudioProcessor::SetAECOption(const bool v) {
  LOG(INFO) << "[LyraxAudioProcessor][SetAECOption] value: " << v;
#ifdef ENABLE_AUDIO_DUMP
  if (!v) {
    dump_ref_in_ = nullptr;
    ref_name_ = "";
  }
#endif
  aec_enable_ = v;
  auto lyrax = GetLyraxAudio();
  if (!lyrax) {
    DCHECK(false);
    return;
  }

  lyrax->setAudioProperty(lyrax::LyraxAudioOptionKey::kUIAecOption,
                          lyrax::LyraxValue(aec_enable_ ? 1 : 0));
}

void LyraxAudioProcessor::SetRawDataOption(const int32_t mode) {
  LOG(INFO) << "[LyraxAudioProcessor][SetRawDataOption] value: " << mode;
  auto lyrax = GetLyraxAudio();
  if (!lyrax) {
    DCHECK(false);
    return;
  }
  lyrax->setAudioProperty(lyrax::LyraxAudioOptionKey::kSetRawDataOption,
                          lyrax::LyraxValue(mode));
}

// The difference between this and SetRawDataOption is that SetRawDataMode is
// the value after decision, while SetRawDataOption is the API value This is
// mainly used to determine whether to go through the lyrax data path. If
// RawData is enabled, it will be accompanied by strategies such as AGC, virtual
// boost, etc.
void LyraxAudioProcessor::SetRawDataMode(const bool enable) {
  LOG(INFO) << "[LyraxAudioProcessor][SetRawDataMode] value: " << enable;
  raw_data_enabled_ = enable;
}

void LyraxAudioProcessor::SetANSOption(const int32_t level) {
  LOG(INFO) << "[LyraxAudioProcessor][SetANSOption] level: " << level;
  ans_level_ = level;
  int param = ans_level_;
  if (param == 4) {
    // 4 means Automatic, RTC not support yet, mapping to low
    param = 1;
  }
  auto lyrax = GetLyraxAudio();
  if (!lyrax) {
    LOG(ERROR) << "Failed to get lyrax";
    return;
  }

  lyrax->setAudioProperty(lyrax::LyraxAudioOptionKey::kUINsOption,
                          lyrax::LyraxValue(param));
}

bool LyraxAudioProcessor::EnableEchoDetection(const int32_t interval) {
  LOG(INFO) << "[LyraxAudioProcessor][EnableEchoDetection] interval: " << interval;
  auto lyrax = GetLyraxAudio();
  if (!lyrax) {
    DCHECK(false);
    return false;
  }
  echo_detection_interval_ = interval;
  return lyrax->enableEchoDetection(interval) == lyrax::LyraxErrorCode::kSuccess;
}

void LyraxAudioProcessor::SetAudioDeviceDelegate(
    std::shared_ptr<lyrax::ILyraxAudioDeviceDelegate> delegate) {
  auto lyrax = GetLyraxAudio();
  if (!lyrax) {
    LOG(ERROR) << "Failed to get lyrax";
    return;
  }
  auto audio_device_controller = lyrax->getAudioDeviceController();
  if (!audio_device_controller) {
    LOG(ERROR) << "Failed to get audio device controller";
    return;
  }
  audio_device_controller->setAudioDeviceDelegate(
      lyrax::LyraxAudioDeviceType::kCaptureDevice, std::move(delegate));
}

bool LyraxAudioProcessor::IsAECEnable() {
  return aec_enable_;
}

bool LyraxAudioProcessor::IsEchoDetectionEnable() {
  return echo_detection_interval_ > 0;
}

int64_t LyraxAudioProcessor::GetLastMicMSTS() {
  return last_mic_ms_ts_;
}

int LyraxAudioProcessor::OnLyraxPreAudioFrame(
    lyrax::LyraxAudioFrame& lyrax_audio_frame) {
  if (!delegate_) {
    return 0;
  }
  mediasdk::AudioFormat audio_format;
  AudioFrame ret;
  ConvertFromLyraxAudioFrame(lyrax_audio_frame, audio_format, ret);
  delegate_->OnLyraxPreAudioFrame(audio_format, ret);
  return 0;
}

int LyraxAudioProcessor::OnLyraxPostAudioFrame(
    lyrax::LyraxAudioFrame& lyrax_audio_frame) {
  if (!delegate_) {
    return 0;
  }
  mediasdk::AudioFormat audio_format;
  AudioFrame ret;
  ConvertFromLyraxAudioFrame(lyrax_audio_frame, audio_format, ret);
#ifdef ENABLE_AUDIO_DUMP
  if (!dump_output_) {
    dump_output_ = std::make_unique<WAVWriter>();
  }
  dump_output_->Write("lyrax_out.wav", audio_format, ret);
#endif
  delegate_->OnLyraxPostAudioFrame(audio_format, ret);
  return 0;
}

std::shared_ptr<lyrax::ILyraxAudio> LyraxAudioProcessor::GetLyraxAudio() {
  return lyrax_audio_.lock();
}

void LyraxAudioProcessor::CheckAndChangeLyraxProcessor() {
  // DCHECK_CURRENTLY_ON(mic capture thread);
  const auto new_enable = IsNeedLyraxProcessor();
  const auto pre = pre_enable_;
  if (new_enable != pre) {
    pre_enable_ = new_enable;
    LOG(INFO) << "lyrax processor change from [" << pre << "] to ["
              << new_enable << "]";
  } else {
    return;
  }
  auto lyrax = GetLyraxAudio();
  if (new_enable) {
    LOG(INFO) << "[LyraxAudioProcessor][CheckAndChangeLyraxProcessor] register "
                 "start.";
    lyrax->registerAudioFilter(pre_frame_filter_.get(),
                               lyrax::LyraxAudioFilterPosition::kFrontInput);
    lyrax->registerAudioFilter(post_frame_filter_.get(),
                               lyrax::LyraxAudioFilterPosition::kInput);
    lyrax->setListener(lyrax_audio_listener_);
    LOG(INFO)
        << "[LyraxAudioProcessor][CheckAndChangeLyraxProcessor] register end.";
  } else {
#ifdef ENABLE_AUDIO_DUMP
    dump_ref_in_ = nullptr;
    ref_name_ = "";
    dump_mic_in_ = nullptr;
    mic_name_ = nullptr;
#endif
    LOG(INFO) << "[LyraxAudioProcessor][CheckAndChangeLyraxProcessor] "
                 "unregister start.";
    lyrax->unregisterAudioFilter(pre_frame_filter_.get());
    lyrax->unregisterAudioFilter(post_frame_filter_.get());
    lyrax->setListener(nullptr);
    LOG(INFO) << "[LyraxAudioProcessor][CheckAndChangeLyraxProcessor] "
                 "unregister end.";
    if (lyrax_audio_input_stream_) {
      LOG(INFO) << "[LyraxAudioProcessor][CheckAndChangeLyraxProcessor] "
                   "lyrax_audio_input_stream_ dispose start.";
      lyrax_audio_input_stream_->dispose();
      lyrax_audio_input_stream_ =
          nullptr;  // for performance reason, we need to stop stream thread
                    // when 3a is all closed
      LOG(INFO) << "[LyraxAudioProcessor][CheckAndChangeLyraxProcessor] "
                   "lyrax_audio_input_stream_ dispose end.";
    }
  }
}

bool LyraxAudioProcessor::IsNeedLyraxProcessor() const {
  // bypass lyrax process when lyrax audio input is muted
  return (!is_mute_) && (aec_enable_ || raw_data_enabled_ ||
         (ans_level_ !=
          lyrax::LyraxAudioNsApiOption::kLyraxAudioNsApiOptionDisable) || (echo_detection_interval_ > 0));
}

void LyraxAudioProcessor::ConvertFromLyraxAudioFrame(
    lyrax::LyraxAudioFrame& input_frame,
    AudioFormat& out_format,
    AudioFrame& out_frame) {
  ConvertFromLyraxAudioFormat(input_frame.format, out_format);
  AudioFrameData data = {};
  data.buffer[0] = (uint8_t*)input_frame.data;
  data.block_size = out_format.GetBlockSize();
  data.channel_count = out_format.GetChannel();
  data.count = input_frame.data_size / out_format.GetBlockSize();
  out_frame.SetData(data);
  out_frame.SetSampleRate(out_format.GetSampleRate());
  out_frame.SetCaptureTimeStampNS(mediasdk::nano_now());
}

void ConvertFromLyraxAudioFormat(lyrax::LyraxAudioFormat& lyrax_audio_format,
                                 AudioFormat& mediasdk_audio_format) {
  // convert channel
  switch (lyrax_audio_format.channel_num) {
    case lyrax::LyraxAudioChannel::kMono:
      mediasdk_audio_format.SetChannel(1);
      mediasdk_audio_format.SetLayout(CHANNEL_MONO);
      break;
    case lyrax::LyraxAudioChannel::kStereo:
      mediasdk_audio_format.SetChannel(2);
      mediasdk_audio_format.SetLayout(CHANNEL_STEREO);
      break;
    default:
      DCHECK(false);
      mediasdk_audio_format.SetChannel(2);
      mediasdk_audio_format.SetLayout(CHANNEL_STEREO);
      break;
  }
  // convert samplerate
  switch (lyrax_audio_format.sample_rate) {
    case lyrax::LyraxAudioSampleRate::k8000:
      mediasdk_audio_format.SetSampleRate(8000);
      break;
    case lyrax::LyraxAudioSampleRate::k11025:
      mediasdk_audio_format.SetSampleRate(11025);
      break;
    case lyrax::LyraxAudioSampleRate::k16000:
      mediasdk_audio_format.SetSampleRate(16000);
      break;
    case lyrax::LyraxAudioSampleRate::k22050:
      mediasdk_audio_format.SetSampleRate(22050);
      break;
    case lyrax::LyraxAudioSampleRate::k32000:
      mediasdk_audio_format.SetSampleRate(32000);
      break;
    case lyrax::LyraxAudioSampleRate::k44100:
      mediasdk_audio_format.SetSampleRate(44100);
      break;
    case lyrax::LyraxAudioSampleRate::k48000:
      mediasdk_audio_format.SetSampleRate(48000);
      break;
    default:
      DCHECK(false);
      mediasdk_audio_format.SetSampleRate(48000);
      break;
  }
  mediasdk_audio_format.SetFormat(
      AUDIO_FORMAT_S16);  // rtc only support AUDIO_FORMAT_S16
}

void ConvertToLyraxAudioFormat(const AudioFormat& mediasdk_audio_format,
                               lyrax::LyraxAudioFormat& lyrax_audio_format) {
  // convert channel
  switch (mediasdk_audio_format.GetChannel()) {
    case 1:
      lyrax_audio_format.channel_num = lyrax::LyraxAudioChannel::kMono;
      break;
    case 2:
      lyrax_audio_format.channel_num = lyrax::LyraxAudioChannel::kStereo;
      break;
    default:
      // lyrax audio only support mono or stereo, otherwise need resample
      lyrax_audio_format.channel_num = lyrax::LyraxAudioChannel::kStereo;
      break;
  }
  // convert samples
  bool finded = false;
  lyrax_audio_format.sample_rate = lyrax::LyraxAudioSampleRate::k48000;
  std::vector<lyrax::LyraxAudioSampleRate> support_samples = {
      lyrax::LyraxAudioSampleRate::k8000,  lyrax::LyraxAudioSampleRate::k11025,
      lyrax::LyraxAudioSampleRate::k16000, lyrax::LyraxAudioSampleRate::k22050,
      lyrax::LyraxAudioSampleRate::k32000, lyrax::LyraxAudioSampleRate::k44100,
      lyrax::LyraxAudioSampleRate::k48000};
  for (auto sample : support_samples) {
    if (static_cast<int>(sample) == mediasdk_audio_format.GetSampleRate()) {
      lyrax_audio_format.sample_rate = sample;
      finded = true;
      break;
    }
  }
  DCHECK(finded);
}

}  // namespace mediasdk