#pragma once
#include <Pdh.h>

namespace cpu_collector {

class PdhCollector {
 public:
  PdhCollector() {}

  ~PdhCollector() { Destroy(); }

  bool Initialize();

  bool SystemUsage(double& system_usage, double& cpu_time);

 private:
  void Destroy();

 private:
  PDH_HQUERY query_ = NULL;
  PDH_HCOUNTER utility_counter_ = NULL;
  PDH_HCOUNTER time_counter_ = NULL;
  bool initialized_ = false;
};
}  // namespace cpu_collector