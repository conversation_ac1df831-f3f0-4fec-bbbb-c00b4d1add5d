#pragma once
#include <atomic>

int64_t ms_now_internal();

class AVClock {
 public:
  // result -1 for not running
  constexpr static int64_t kNotRunning = -1ll;
  int64_t NowMS() const noexcept;
  int64_t LastUpdateTS() const noexcept;
  int64_t LastPTS() const noexcept;
  void UpdateNano(int64_t);
  void Pause();
  bool IsPause() const;
  void Resume();
  void SetStep(bool step);
  bool IsStep() const;

  void SetSpeed(float);
  float GetSpeed() const;

  void Reset();

 private:
  float speed_ = 1.f;

  std::atomic_int64_t last_pts_ = 0;
  std::atomic_int64_t last_update_ns_ts_ = 0;
  bool pause_ = false;
  bool step_ = false;
};