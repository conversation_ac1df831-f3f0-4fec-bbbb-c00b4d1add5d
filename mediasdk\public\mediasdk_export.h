#pragma once

#if defined(WIN32)

#if defined(MEDIASDK_IMPLEMENTATION)
#define MEDIASDK_EXPORT __declspec(dllexport)
#else
#define MEDIASDK_EXPORT __declspec(dllimport)
#endif  // defined(MEDIASDK_IMPLEMENTATION)

// #define MS_SELECTANY __declspec(selectany)

#else  // defined(WIN32)

#if defined(MEDIASDK_IMPLEMENTATION)
#define MEDIASDK_EXPORT __attribute__((visibility("default")))
#else
#define MEDIASDK_EXPORT
#endif  // defined(MEDIASDK_IMPLEMENTATION)
#endif

#define MS_API __stdcall


