#include "ep_engine.h"

#include "PlatformFetchCallback.h"
#include "PlatformSettings.h"
#include "base/logging.h"
#include "base/strings/string_util.h"

#include "mediasdk/effect_platform/effect_sdk_loader.h"
#include "mediasdk/effect_platform/ep_helper.h"
#include "mediasdk/effect_platform/ms_curl.h"
#include "mediasdk/public/effect_platform/effect_bef_api.h"

namespace {

constexpr auto kLogLevel = davinci::effectplatform::LogLevel::LEVEL_WARNING;

}  // namespace

namespace mediasdk::ep {

struct RequirementsPeeker final
    : public davinci::effectplatform::loki::LokiRequirementsPeeker {
  std::vector<std::string> peekRequirements(
      const std::vector<std::string>& requirements) override {
    std::vector<std::string> result;
    if (requirements.empty()) {
      return result;
    }
    if (!bef_api::ms_bef_effect_peek_resources_needed_by_requirements) {
      return result;
    }
    std::vector<const char*> cRequirementsArray;
    cRequirementsArray.reserve(requirements.size());
    for (const auto& requirement : requirements) {
      cRequirementsArray.push_back(requirement.c_str());
    }
    const char** outResourceNames = nullptr;
    int size = 0;
    bef_api::ms_bef_effect_peek_resources_needed_by_requirements(
        cRequirementsArray.data(), (int)requirements.size(), &outResourceNames,
        &size);
    for (int i = 0; i < size; i++) {
      std::string out_name(outResourceNames[i] ? outResourceNames[i] : "");
      result.emplace_back(out_name);
    }
    return result;
  }
};

struct EPLogger : public davinci::effectplatform::LoggerFunc {
 public:
  virtual void onLog(davinci::effectplatform::LogLevel level,
                     const char* fmt,
                     va_list ap) override {
    using davinci::effectplatform::LogLevel;
    char szBuffer[1024] = {0};

    if (level < kLogLevel) {
      return;
    }
    int size = base::vsnprintf(szBuffer, 1024, fmt, ap);

    switch (level) {
      case LogLevel::LEVEL_INFO:
        LOG(INFO) << "[EffectPlatform] " << std::string(szBuffer, size);
        break;
      case LogLevel::LEVEL_WARNING:
        LOG(WARNING) << "[EffectPlatform] " << std::string(szBuffer, size);
        break;
      case LogLevel::LEVEL_ERROR:
        LOG(ERROR) << "[EffectPlatform] " << std::string(szBuffer, size);
        break;
      default:
        break;
    }
  }
};

Engine::Engine(EngineDelegate* delegate) : delegate_(delegate) {}

Engine::~Engine() {
  LOG(INFO) << "[EffectPlatform] ~Engine";
  UnInitialize();
}

bool Engine::Initialize(const std::string& json_params) {
  davinci::effectplatform::PlatformSettings::setLogLevel(
      davinci::effectplatform::LogLevel::LEVEL_WARNING);
  davinci::effectplatform::PlatformSettings::setLogger(
      std::make_shared<EPLogger>());

  config_ =
      CreateConfig(json_params, EffectSdkLoader::GetInstance()->GetBEFVersion(),
                   std::make_shared<RequirementsPeeker>());
  if (!config_) {
    LOG(ERROR) << "[EffectPlatform] Engine Initialize BuildConfig failed";
    return false;
  }

  std::optional<std::string> user_id = GetUserIdFromJson(json_params);
  std::optional<std::string> ttls_hardware_level =
      GetTTLSHardwareLevel(json_params);
  UpdateUrlParameter("user_id", user_id.value_or(""));
  UpdateUrlParameter("ttls_hardware_level", ttls_hardware_level.value_or(""));

  auto net_client = std::make_shared<NetClient>(this);
  davinci::effectplatform::PlatformSettings::setHttpClient(net_client);

  loki_platform_ =
      std::make_unique<davinci::effectplatform::loki::LokiPlatform>(config_);

  finder_.finder = (resource_finder)loki_platform_->getResourceFinder();
  finder_.platform_resource_releaser = [](void* effect_handle) {
    delete[] (char*)effect_handle;
  };
  finder_.finder_releaser = nullptr;
  LOG(INFO) << "[EffectPlatform] finder:" << std::hex
            << reinterpret_cast<void*>(finder_.finder);
  return true;
}

void Engine::UnInitialize() {
  if (loki_platform_) {
    loki_platform_.reset(nullptr);
  }
}

void Engine::UpdateUrlParameter(const std::string& key,
                                const std::string& value) {
  param_map_.insert_or_assign(key, value);
}

const std::map<std::string, std::string>& Engine::GetUrlParameter() {
  return param_map_;
}

void Engine::LoadModels(const std::vector<std::string>& requirements,
                        const std::string& model_name,
                        const std::string& request_id) {
  if (requirements.empty() && model_name.empty()) {
    LOG(ERROR) << "[EffectPlatform] requirements and model_name empty";
    return;
  } else {
    LOG(INFO) << "[EffectPlatform] requirst_id:" << request_id
              << ", model_name:" << model_name;
  }
  if (!loki_platform_) {
    return;
  }
  auto handle =
      std::make_shared<ModelDownloadHandle>(request_id, weak_from_this());
  loki_platform_->downloadAlgorithmResource(requirements, model_name, handle);
}

Engine::effect_resource_finder Engine::GetResourceFinder() {
  return finder_;
}

// Engine::ModelDownloadHandle
void Engine::ModelDownloadHandle::onSuccess(std::shared_ptr<long> result) {
  auto callback = base::BindOnce(
      [](std::weak_ptr<ep::Engine> engine, const std::string& request_id) {
        auto engine_lock = engine.lock();
        if (engine_lock && engine_lock->delegate_) {
          engine_lock->delegate_->OnDownloadModelSuccess(request_id);
        } else {
          LOG(ERROR) << "[EffectPlatform] download success, no delegate";
        }
      },
      engine_, request_id_);
  PostToNotify(FROM_HERE, std::move(callback));
}

void Engine::ModelDownloadHandle::onError(std::string error) {
  auto callback = base::BindOnce(
      [](std::weak_ptr<ep::Engine> engine, const std::string& request_id,
         const std::string& error) {
        auto engine_lock = engine.lock();
        if (engine_lock && engine_lock->delegate_) {
          engine_lock->delegate_->OnDownloadModelError(request_id, error);
        } else {
          LOG(ERROR) << "[EffectPlatform] download error, no delegate";
        }
      },
      engine_, request_id_, error);
  PostToNotify(FROM_HERE, std::move(callback));
}

void Engine::ModelDownloadHandle::onProgress(long progress) {
  auto callback = base::BindOnce(
      [](std::weak_ptr<ep::Engine> engine, const std::string& request_id,
         long progress) {
        auto engine_lock = engine.lock();
        if (engine_lock && engine_lock->delegate_) {
          engine_lock->delegate_->OnDownloadModelProgress(request_id, progress);
        } else {
          LOG(ERROR) << "[EffectPlatform] download progress, no delegate";
        }
      },
      engine_, request_id_, progress);
  PostToNotify(FROM_HERE, std::move(callback));
}

}  // namespace mediasdk::ep
