#pragma once

#include <ff_av_player/avsyn/av_clock.h>
#include <list>
#include <shared_mutex>

extern "C" {
#include <libavcodec/packet.h>
}

class AVPacketItemList {
 public:
  AVPacketItemList() = default;
  ~AVPacketItemList();

  void Clear();
  int32_t GetPendingSendPacket() const;
  int32_t GetPendingReceivePacket() const;
  void PushPacket(const AVPacket& packet);
  void PushFlushPacket();

  AVPacket* FrontPacket();
  void PopPendingReceive();

 private:
  mutable std::shared_mutex lock_packet_list_;
  std::list<AVPacket*> packet_list_;
  std::list<AVPacket*> pending_packet_list_;
};