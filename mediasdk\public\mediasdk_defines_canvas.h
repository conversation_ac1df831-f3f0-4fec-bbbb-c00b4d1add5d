#pragma once

#include "mediasdk_callback_defines.h"
#include "mediasdk_defines.h"

#pragma pack(push, 1)

namespace mediasdk {

struct CreateCanvasItemParams {
  MSTransform transform;
  bool is_visible;
};

enum TransitionType {
  kTransitionNone = 0,
  kTransitionSlide,
  kTransitionSwipe,
  kTransitionCartoon,
  kTransitionFade,
  kTransitionFade2Color,
  kTransitionLuminanceWide,
  kTransitionMove,
};

enum TransitionDirection {
  kTransitionDirectionLeft = 0,
  kTransitionDirectionRight,
  kTransitionDirectionUp,
  kTransitionDirectionDown,
};

enum TransitionProgressFunctionType {
  kTransitionProgressFunctionTypeLinear = 0,
  kTransitionProgressFunctionTypeEaseInSine,
  kTransitionProgressFunctionTypeEaseOutSine,
  kTransitionProgressFunctionTypeEaseInOutSine,
  kTransitionProgressFunctionTypeEaseInQuad,
  kTransitionProgressFunctionTypeEaseOutQuad,
  kTransitionProgressFunctionTypeEaseInOutQuad,
  kTransitionProgressFunctionTypeEaseInCubic,
  kTransitionProgressFunctionTypeEaseOutCubic,
  kTransitionProgressFunctionTypeEaseInOutCubic,
  kTransitionProgressFunctionTypeEaseInQuart,
  kTransitionProgressFunctionTypeEaseOutQuart,
  kTransitionProgressFunctionTypeEaseInOutQuart,
  kTransitionProgressFunctionTypeEaseInQuint,
  kTransitionProgressFunctionTypeEaseOutQuint,
  kTransitionProgressFunctionTypeEaseInOutQuint,
  kTransitionProgressFunctionTypeEaseInExpo,
  kTransitionProgressFunctionTypeEaseOutExpo,
  kTransitionProgressFunctionTypeEaseInOutExpo,
  kTransitionProgressFunctionTypeEaseInCirc,
  kTransitionProgressFunctionTypeEaseOutCirc,
  kTransitionProgressFunctionTypeEaseInOutCirc,
  kTransitionProgressFunctionTypeEaseInBack,
  kTransitionProgressFunctionTypeEaseOutBack,
  kTransitionProgressFunctionTypeEaseInOutBack,
  kTransitionProgressFunctionTypeEaseInElastic,
  kTransitionProgressFunctionTypeEaseOutElastic,
  kTransitionProgressFunctionTypeEaseInOutElastic,
  kTransitionProgressFunctionTypeEaseInBounce,
  kTransitionProgressFunctionTypeEaseOutBounce,
  kTransitionProgressFunctionTypeEaseInOutBounce,
};

enum TransitionSwipeType {
  TransitionSwipeTypeSwipeIn = 0,
  TransitionSwipeTypeSwipeOut = 1,
};

enum TransitionMoveType {
  kTransitionMoveTypeSlide = 0,
  kTransitionMoveTypeScale,
  kTransitionMoveTypeFade,
};

// Video source transition params
struct TransitionMoveParams {
  // Video source transition type, default is kTransitionMoveTypeSlide
  TransitionMoveType move_type;

  // Move into easing function, default is EaseInCubic
  TransitionProgressFunctionType move_in_function;

  // Move out easing function, default is EaseOutCubic
  TransitionProgressFunctionType move_out_function;

  // Same-origin switching easing function, default is EaseInOutCubic
  TransitionProgressFunctionType move_to_function;

  // Applied to kTransitionMoveTypeSlide, moves in from the left by default
  TransitionDirection move_in_from_direction;

  // Applied to kTransitionMoveTypeSlide, moves out to the right by default
  TransitionDirection move_out_to_direction;

  // Applied to kTransitionMoveTypeScale, scaling appears from the upper left
  // corner of the canvas by default (ratio of the canvas, 0.0~1.0)
  MSTranslateF move_in_from_position;

  // Applied to kTransitionMoveTypeScale, scaling disappears from the lower
  // right corner of the canvas by default (ratio of the canvas, 0.0~1.0)
  MSTranslateF move_out_to_position;
};

struct CreateTransitionParams {
  TransitionType transition_type;
  MediaSDKString transition_property;
};

}  // namespace mediasdk

#pragma pack(pop)