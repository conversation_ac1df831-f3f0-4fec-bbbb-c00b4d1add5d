#pragma once
#include <array>
#include <mutex>
#include "audio/audio_format.h"
#include "mediasdk/audio/audio_mixer_input.h"
#include "mediasdk/debug_helper.h"
#include "mediasdk/utils/mem_buffer.h"

namespace mediasdk {

class AudioMixerInputImpl : public AudioMixerInput {
 public:
  AudioMixerInputImpl();

  ~AudioMixerInputImpl() override;

  void OnData(const std::string_view& debug_str,
              const mediasdk::AudioFrame& data,
              int64_t syn_offset) override;

  bool CheckCanConsumeInputBuffer(
      const std::string_view& debug_str,
      const mediasdk::TimeDuration& target_duration) const override;

  TimeDurationFrame* GetAudioFrameDuration(
      const std::string_view& debug_str,
      const mediasdk::TimeDuration& ts) override;

  int64_t GetLastUpdateNS() override;

  void GetErrorCnt(int32_t& too_late, int32_t& too_early) override;

 private:
  mediasdk::TimeDuration GetInputDuration() const;

  mediasdk::TimeDuration GetOutputDuration();

  bool ConsumeInputBuffer(const std::string_view& debug_str,
                          const mediasdk::TimeDuration& target_duration);

  void BufferAudioFrame(const std::string_view& str,
                        const mediasdk::AudioFrame& frame);

  void ResetInputBuffer();

  void UpdateLastUpdateTS(int64_t ts);

  void PushBack(const std::string_view& debug_str,
                const mediasdk::TimeDuration& time,
                const mediasdk::AudioFrame& apk);

  void PopMiddle(const mediasdk::TimeDuration& input_buffer_ts,
                 const mediasdk::TimeDuration& target_ts);
  void PopPart(const mediasdk::TimeDuration& input_buffer_ts,
               const mediasdk::TimeDuration& ts);

  void PopFill(const mediasdk::TimeDuration& ts);

  void Trace(const std::string_view& str);

  void CheckAndReportErrorCnt(const std::string_view& str);

 private:
  mediasdk::AudioFormat target_format_;

  int64_t input_ts_ = 0;

  mediasdk::TimeDuration output_ts_;

  std::shared_ptr<AudioFrame> mix_output_buffer_;

  std::atomic<int64_t> last_update_ts_ = 0;

  mutable std::mutex lock_mix_buffer_;
  int64_t pre_offset_ = 0;
  TimeDurationFrame output_audio_frame_duraion_;

  std::array<mediasdk::RingMemBuffer<float>, mediasdk::kMaxAudioPlanes>
      mix_buffer_;
  int64_t last_log_ms_ = 0;

  std::atomic_int32_t error_cnt_too_late_ = 0;
  std::atomic_int32_t error_cnt_too_early_ = 0;

  int64_t last_check_mixer_error_cnt_ms_ = 0;
};
}  // namespace mediasdk
