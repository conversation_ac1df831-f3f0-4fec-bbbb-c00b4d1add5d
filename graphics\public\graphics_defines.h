#pragma once

#include "mediasdk/public/mediasdk_defines.h"

namespace graphics {

constexpr uint32_t kMaxVideoPlanes = mediasdk::kMaxVideoPlanes;

inline bool IsRGBFormat(mediasdk::PixelFormat format) {
  if (format == mediasdk::PixelFormat::kPixelFormatARGB ||
      format == mediasdk::PixelFormat::kPixelFormatXRGB ||
      format == mediasdk::PixelFormat::kPixelFormatRGB24 ||
      format == mediasdk::kPixelFormatRGBA ||
      format == mediasdk::kPixelFormatBGR24 ||
      format == mediasdk::PixelFormat::kPixelFormatBGRA) {
    return true;
  }
  return false;
}

inline const char* GetPixelFormatShorName(mediasdk::PixelFormat format) {
  switch (format) {
    case mediasdk::kPixelFormatUnspecified:
      return "unkown";
    case mediasdk::kPixelFormatI420:
      return "i420";
    case mediasdk::kPixelFormatYV12:
      return "yv12";
    case mediasdk::kPixelFormatNV12:
      return "nv12";
    case mediasdk::kPixelFormatNV21:
      return "nv21";
    case mediasdk::kPixelFormatUYVY:
      return "uyvy";
    case mediasdk::kPixelFormatYUY2:
      return "yuy2";
    case mediasdk::kPixelFormatARGB:
      return "argb";
    case mediasdk::kPixelFormatXRGB:
      return "xrgb";
    case mediasdk::kPixelFormatRGB24:
      return "rgb24";
    case mediasdk::kPixelFormatRGBA:
      return "rgba";
    case mediasdk::kPixelFormatBGR24:
      return "bgr24";
    case mediasdk::kPixelFormatBGRA:
      return "bgra";
    case mediasdk::kPixelFormatMJPEG:
      return "mjpeg";
    case mediasdk::kPixelFormatI444:
      return "i444";
    case mediasdk::kPixelFormatI444A:
      return "i444a";
    case mediasdk::kPixelFormatI420A:
      return "i420a";
    case mediasdk::kPixelFormatI422:
      return "i422";
    case mediasdk::kPixelFormatI422A:
      return "i422a";
    case mediasdk::kPixelFormatYVYU:
      return "yvyu";
    case mediasdk::kPixelFormatNV12TEXTURE:
      return "nv12_ms";
    case mediasdk::kPixelFormatY8:
      return "y8";
    case mediasdk::kPixelFormatRGBA16:
      return "rgba16";
    case mediasdk::kPixelFormatHDYC:
      return "hdyc";
    default:
      break;
  }
  return "";
}

}  // namespace graphics