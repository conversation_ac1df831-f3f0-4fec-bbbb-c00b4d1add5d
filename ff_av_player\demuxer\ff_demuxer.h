#pragma once

#include <atomic>
#include <string>
#include <vector>

#include <base/threading/thread.h>
extern "C" {

#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
}

class FFDemuxer {
 public:
  class DemuxerObserver {
   public:
    virtual ~DemuxerObserver() = default;
    virtual void OnOpen(const FFDemuxer*) = 0;
    virtual void OnAudioStream(const FFDemuxer* demuxer,
                               AVFormatContext* context,
                               int audio_stream_index) = 0;
    virtual void OnVideoStream(const FFDemuxer* demuxer,
                               AVFormatContext* context,
                               int video_stream_index) = 0;
    virtual void OnPacket(const FFDemuxer*,
                          AVPacket& packet,
                          AVMediaType type) = 0;
    virtual void OnStop(const FFDemuxer*) = 0;
    virtual void OnEOF(const FFDemuxer*) = 0;
    virtual void OnFirstAudioPacket(const FFDemuxer*) = 0;
    virtual void OnFirstVideoPacket(const FFDemuxer*) = 0;
    virtual void OnSeekResult(bool) = 0;
    virtual void OnNoAudioStream(const FFDemuxer*) = 0;
    virtual void OnNoVideoStream(const FFDemuxer*) = 0;
    virtual void OnOtherStreamPacket(const FFDemuxer*) = 0;
  };

 public:
  static bool GetFrameInfo(const std::string& file_path,
                           double* duration,
                           float* angle,
                           int* frame_width,
                           int* frame_height);

  void AddObserver(DemuxerObserver* obs);
  void RemoveObserver(DemuxerObserver* obs);

  bool OpenURL(const std::string& url, int32_t time_out = 0);

  bool Seek(const base::Thread& thread, int64_t ms_ts);

  int32_t GetRotate() const { return rotate_; }

  void EnableNoBuffer(bool no_buffer);
  bool CanSeek() const;
  bool IsEOF() const;
  void Close(base::Thread&);
  int64_t GetAudioCnt();
  int64_t GetVideoCnt();
  int64_t GetAudioByte();
  int64_t GetVideoByte();
  void AsyReadWork(const base::Thread& thread);
  AVFormatContext* GetContext() const;
  void SimpleSeek(const int64_t ms_ts);
 private:
  void SeekWork(int64_t ms_ts);
  void SignalPacket(AVPacket& packet);
  void SingleReadWork();
  void ResetStatics();

  std::atomic_bool stop_ = false;
  std::atomic_bool pause_ = false;
  AVFormatContext* format_context_ = nullptr;
  std::vector<DemuxerObserver*> obs_list_;
  int video_stream_index_ = -1;
  int audio_stream_index_ = -1;
  std::atomic<int64_t> audio_packet_cnt_ = 0;
  std::atomic<int64_t> video_packet_cnt_ = 0;
  std::atomic<int64_t> audio_packet_byte_ = 0;
  std::atomic<int64_t> video_packet_byte_ = 0;
  std::atomic_bool enable_loop_ = false;
  std::atomic_bool eof_ = false;
  bool no_buffer_ = false;
  std::atomic_int64_t seek_target_ms_ = {0};
  int32_t rotate_ = 0;
};
