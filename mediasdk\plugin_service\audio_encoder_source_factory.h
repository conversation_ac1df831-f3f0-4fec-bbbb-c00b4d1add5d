
#pragma once

#include <set>

#include <base/native_library.h>
#include <memory>
#include "base/memory/weak_ptr.h"
#include "mediasdk/public/plugin/plugin_defines.h"
#include "source_factory.h"

namespace mediasdk {

class AudioEncoderSource;

class AudioEncoderProxy;

class AudioEncoderSourceFactory
    : public SourceFactory,
      public base::SupportsWeakPtr<AudioEncoderSourceFactory> {
 public:
  static std::shared_ptr<AudioEncoderSourceFactory> Create(
      base::NativeLibrary library,
      std::shared_ptr<PluginInfo> info);

  AudioEncoderSourceFactory(base::NativeLibrary library,
                            std::shared_ptr<PluginInfo> info);

  ~AudioEncoderSourceFactory();

  // SourceFactory:
  PluginType GetType() const override { return PluginType::kAudioEncoder; }

  std::shared_ptr<AudioEncoderSource> CreateSource(
      AudioEncoderProxy* proxy,
      const std::string& json_params);

 private:
  bool Load();

  void Destroy(AudioEncoderSource* source);

 private:
  typedef AudioEncoderSource* (
      *CreateAudioEncoderSourceFunc)(AudioEncoderProxy*, const char*);
  typedef void* (*DestroyAudioEncoderSourceFunc)(AudioEncoderSource*);

 private:
  CreateAudioEncoderSourceFunc create_func_ = nullptr;
  DestroyAudioEncoderSourceFunc destroy_func_ = nullptr;
};

}  // namespace mediasdk
