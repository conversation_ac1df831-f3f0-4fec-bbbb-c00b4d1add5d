#pragma once

#include "bef_effect_api.h"
#include "bef_effect_composer.h"
#include "bef_effect_dynamic_load_library.h"
#include "bef_framework_public_constant_define.h"
#include "bef_msg_delegate_manager.h"

// declare here and define in effect_sdk_loader.cc
namespace mediasdk::bef_api {

#define DECLARE_EFFECT_FUNCTION(func) extern decltype(::func)* ms_##func;
#define DEFINE_EFFECT_FUNCTION(func) decltype(::func)* ms_##func;
DECLARE_EFFECT_FUNCTION(bef_effect_load_egl_library_with_func)
DECLARE_EFFECT_FUNCTION(bef_effect_load_glesv2_library_with_func)
DECLARE_EFFECT_FUNCTION(bef_effect_get_sdk_version)
DECLARE_EFFECT_FUNCTION(bef_effect_get_sdk_commit)
DECLARE_EFFECT_FUNCTION(bef_effect_create_handle)
DECLARE_EFFECT_FUNCTION(bef_effect_use_pipeline_processor)
DECLARE_EFFECT_FUNCTION(bef_effect_set_render_api)
DECLARE_EFFECT_FUNCTION(bef_effect_set_log_to_local_func)
DECLARE_EFFECT_FUNCTION(bef_effect_destroy)
DECLARE_EFFECT_FUNCTION(bef_effect_set_color_filter_v2)
DECLARE_EFFECT_FUNCTION(bef_effect_set_effect)
DECLARE_EFFECT_FUNCTION(bef_effect_composer_set_nodes)
DECLARE_EFFECT_FUNCTION(bef_effect_composer_replace_nodes)
DECLARE_EFFECT_FUNCTION(bef_effect_composer_update_node)
DECLARE_EFFECT_FUNCTION(bef_effect_composer_append_nodes)
DECLARE_EFFECT_FUNCTION(bef_effect_get_et_data)
DECLARE_EFFECT_FUNCTION(bef_effect_composer_remove_nodes)
DECLARE_EFFECT_FUNCTION(bef_effect_composer_check_node_exclusion)
DECLARE_EFFECT_FUNCTION(bef_effect_init_with_resource_finder_v2)
DECLARE_EFFECT_FUNCTION(bef_effect_init)
DECLARE_EFFECT_FUNCTION(bef_effect_composer_set_mode)
DECLARE_EFFECT_FUNCTION(bef_effect_set_camera_device_position)
DECLARE_EFFECT_FUNCTION(bef_effect_set_width_height)
DECLARE_EFFECT_FUNCTION(bef_effect_algorithm_texture)
DECLARE_EFFECT_FUNCTION(bef_effect_process_texture)
DECLARE_EFFECT_FUNCTION(bef_effect_get_requirment)
DECLARE_EFFECT_FUNCTION(bef_render_msg_delegate_manager_init)
DECLARE_EFFECT_FUNCTION(bef_render_msg_delegate_manager_add)
DECLARE_EFFECT_FUNCTION(bef_render_msg_delegate_manager_remove)
DECLARE_EFFECT_FUNCTION(bef_render_msg_delegate_manager_destroy)
DECLARE_EFFECT_FUNCTION(bef_effect_send_msg)
DECLARE_EFFECT_FUNCTION(bef_effect_set_orientation)
DECLARE_EFFECT_FUNCTION(bef_effect_set_render_cache_texture)
DECLARE_EFFECT_FUNCTION(bef_effect_peek_resources_needed_by_requirements)
DECLARE_EFFECT_FUNCTION(bef_effect_config_ab_value)
DECLARE_EFFECT_FUNCTION(bef_effect_set_render_cache_string_value)
DECLARE_EFFECT_FUNCTION(bef_effect_use_pipeline_3_buffer)
DECLARE_EFFECT_FUNCTION(bef_effect_enable_algorithm_syncer)
DECLARE_EFFECT_FUNCTION(bef_effect_algorithm_multi_texture)
DECLARE_EFFECT_FUNCTION(bef_effect_algorithm_multi_texture_with_params)
DECLARE_EFFECT_FUNCTION(bef_effect_free_raw_buffer)

}  // namespace mediasdk::bef_api
