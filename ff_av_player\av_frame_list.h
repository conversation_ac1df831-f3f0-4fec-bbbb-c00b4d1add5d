#pragma once

#include <list>
#include <mutex>

extern "C" {
#include <libavutil/frame.h>
}

struct AVFrameWithSerial {
  AVFrame* frame = nullptr;
  int32_t serial = 0;
};

class AVFrameList {
 public:
  ~AVFrameList();
  void Clear();
  int32_t GetLeftFrameCnt();
  void PushFrame(AVFrame& frame, int32_t serial);

  AVFrameWithSerial PopFrame();
  // -1 for invalid call
  int64_t GetFirstFrameTS(const AVRational&);

 private:
  std::mutex lock_frame_list_;
  std::list<AVFrameWithSerial> frame_list_;
};