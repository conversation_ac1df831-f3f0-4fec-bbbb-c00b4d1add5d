#pragma once

#include <string.h>
#include <mutex>
#include "shader.h"

using namespace DirectX;

namespace graphics {

class BorderShader : public Shader {
 public:
  struct VERTEXTYPE {
    XMFLOAT3 position;
    XMFLOAT2 texture;
    XMFLOAT4 color;
  };

  static inline const char* SHADER_ID_STRING = "border_shader";

  static std::shared_ptr<Shader> CreateBorderShader(const char* type) {
    if (strcmp(type, SHADER_ID_STRING) == 0) {
      return std::make_shared<BorderShader>();
    }
    return nullptr;
  }

  static void EnumPluginsFunction(void* param, EnumPluginCallBack fpn) {
    fpn(param,
        ShaderItem{SHADER_ID_STRING, "shader for draw texture to graphics",
                   BorderShader::CreateBorderShader});
  }

 public:
  bool Init(const std::shared_ptr<Device>&) override;

  void RenderBorder(ID3D11ShaderResourceView* view,
                    Microsoft::WRL::ComPtr<ID3D11Buffer>& vertex_buffer,
                    Microsoft::WRL::ComPtr<ID3D11Buffer>& index_buffer,
                    Microsoft::WRL::ComPtr<ID3D11Buffer>& crop_buffer,
                    Microsoft::WRL::ComPtr<ID3D11Buffer>& matrix_buffer,
                    Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_buffer);
  void Destroy() override;
  ~BorderShader() override;

 private:
  bool DoInit(const std::shared_ptr<Device>& ins);

 protected:
  ID3D11Device* GetDevice_();
  ID3D11DeviceContext* GetContext_();

 private:
  bool try_init_ = false;
  bool init_suc_ = false;
  std::shared_ptr<Device> device_;
  Microsoft::WRL::ComPtr<ID3D11VertexShader> vs_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11InputLayout> layout_;
  Microsoft::WRL::ComPtr<ID3D11SamplerState> sampler_;
};

}  // namespace graphics
