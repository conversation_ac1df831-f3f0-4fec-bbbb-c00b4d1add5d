#pragma once

#include <cstdint>
#include "mediasdk/public/mediasdk_export.h"

namespace mediasdk {

#pragma pack(push, 1)

struct AudioEncoderSourcePacket {
  uint8_t* data;
  uint32_t size;
  int64_t pts;
  int64_t dts;
};

#pragma pack(pop)

class AudioEncoderProxy {
 public:
  virtual ~AudioEncoderProxy() = default;

  virtual void OnEncodedData(const AudioEncoderSourcePacket& packet) = 0;

  virtual void OnAppendExtraData(const uint8_t* data, size_t size) = 0;
};

}  // namespace mediasdk
