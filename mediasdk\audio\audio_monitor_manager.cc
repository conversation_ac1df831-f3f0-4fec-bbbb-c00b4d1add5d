#include "audio/audio_monitor_manager.h"

#include "audio/audio_controller.h"
#include "audio/audio_frame.h"
#include "base/strings/sys_string_conversions.h"
#include "mediasdk/component_center.h"
#include "mediasdk/notify_center.h"

namespace mediasdk {

AudioMonitorManager::AudioMonitorManager(const AudioFormat& output_format)
    : output_format_(output_format) {
  // Register to listen to global audio device event
  com::GetNotifyCenter()->RegisterGlobalEventObserver(this);
}

AudioMonitorManager::~AudioMonitorManager() {
  // Unregister to listen to global audio device event
  com::GetNotifyCenter()->UnregisterGlobalEventObserver(this);
  CloseMonitor();
}

void AudioMonitorManager::SetMonitorType(uint32_t monitor_type) {
  if (monitor_type != monitor_type_) {
    LOG(INFO) << "SetMonitorType [" << monitor_type << "]";
  }
  monitor_type_ = monitor_type;
}

void AudioMonitorManager::SetNeedSync(bool need_sync) {
  if (need_sync != need_sync_) {
    LOG(INFO) << "SetNeedSync [" << need_sync << "]";
  }
  need_sync_ = need_sync;
}

void AudioMonitorManager::ChangeRenderTargetTo(const std::string& id) {
  std::lock_guard<std::mutex> lock(lock_monitor_);
  render_target_audio_dev_id_ = id;
  if (monitor_) {
    auto cur_dev = monitor_->GetCurrentDevice();
    if (render_target_audio_dev_id_.size() &&
        cur_dev != base::SysUTF8ToWide(render_target_audio_dev_id_)) {
      LOG(INFO) << "render device changed(" << render_target_audio_dev_id_
                << ") destroy[" << cur_dev << "] ";
      monitor_->Close();
      monitor_ = nullptr;
    } else if (render_target_audio_dev_id_.empty()) {
      auto default_dev = WASAPIDevice::GetDefaultOutput();
      if (default_dev.id != base::SysUTF8ToWide(render_target_audio_dev_id_)) {
        LOG(INFO) << "render device(default) changed, destroy["
                  << base::SysWideToUTF8(cur_dev) << "]";
        monitor_->Close();
        monitor_ = nullptr;
      }
    }
  }
}

void AudioMonitorManager::PlayFrame(const AudioFrame& frame) {
  std::lock_guard<std::mutex> lock(lock_monitor_);
  if (monitor_type_ & kAudioMonitorRender || need_sync_) {
    if (monitor_changed_) {
      // close old monitor
      if (monitor_) {
        monitor_->Close();
        monitor_ = nullptr;
      }
      monitor_changed_ = false;
    }

    if (!monitor_) {
      CreateMonitor();
    }
    if (monitor_) {
      if (!monitor_->Play(frame, !(monitor_type_ & kAudioMonitorRender))) {
        LOG(ERROR) << "Play Failed, close and reopen";
        monitor_->Close();
        monitor_ = nullptr;
      }
    } else {
      SleepFromNow(base::Nanoseconds(frame.GetTimeDuration().GetNSDuration()));
    }
  } else {
    if (monitor_) {
      monitor_->Close();
      monitor_ = nullptr;
    }
  }
}

void AudioMonitorManager::CreateMonitor() {
  auto audio = com::GetAudioController();
  if (!audio)
    return;
  WASAPIDevice::DeviceName target_device = {};
  if (render_target_audio_dev_id_.size()) {
    target_device.id = base::SysUTF8ToWide(render_target_audio_dev_id_);
  } else if (audio->GetRenderTargetDeviceID().size()) {
    target_device.id = base::SysUTF8ToWide(audio->GetRenderTargetDeviceID());
  } else {
    target_device = WASAPIDevice::GetDefaultOutput();
  }

  if (!target_device.id.size()) {
    return;
  }

  monitor_ = CreateAudioMonitor();

  if (!monitor_->Open(output_format_, target_device)) {
    LOG(ERROR) << "Failed to open[" << base::SysWideToUTF8(target_device.id)
               << "]";
    monitor_->Close();
    monitor_ = nullptr;
  }
}

void AudioMonitorManager::CloseMonitor() {
  std::lock_guard<std::mutex> lock(lock_monitor_);
  if (monitor_) {
    monitor_->Close();
    monitor_ = nullptr;
  }
}

void AudioMonitorManager::OnPluginGlobalEvent(PluginInfo info,
                                              MediaSDKString event) {
  if (info.type != kAudio || info.name.ToString() != "WASAPIAudioSource") {
    return;
  }

  nlohmann::json json_root;
  try {
    json_root = nlohmann::json::parse(event.ToString());
    std::string event_name =
        json_root.contains("event_name") ? json_root["event_name"] : "";
    if (event_name != "AUDIO_DEVICE_STATE_CHANGED" ||
        !json_root.contains("event_type")) {
      return;
    }
    int event_type = json_root["event_type"];
    std::string device_id = json_root["device_id"];
    int new_state = json_root["new_state"];
    int flow = json_root["flow"];
    int role = json_root["role"];

    // only deal with [on default device changed] of render
    if (event_type != 3 || flow != 0) {
      return;
    }

    if (monitor_type_ & kAudioMonitorRender || need_sync_) {
      LOG(INFO) << "[default monitor device changed, try to update monitor "
                   "device in audio capture thread]";
      monitor_changed_ = true;
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "OnPluginGlobalEvent catch exception: " << e.what()
               << " json string:" << event.ToString();
  }
}

}  // namespace mediasdk