#pragma once

#include "graphics/texture.h"
#include "mediasdk/public/mediasdk_defines.h"
#include "mediasdk/public/mediasdk_string.hpp"
#include "mediasdk/public/plugin/plugin_export.h"
#include "visual_filter_proxy.h"

namespace mediasdk {

struct VisualFilterTransform {
  bool vflip;
  bool hflip;
  float angle;
#ifdef PRE_PROCESS_CLIP
  int32_t xclip;
  int32_t yclip;
  int32_t zclip;
  int32_t wclip;
#endif  // PRE_PROCESS_CLIP
  int32_t target_cx;
  int32_t target_cy;
  int32_t origin_cx;
  int32_t origin_cy;
};

// Indicate at which stage this filter is being executed, currently available
// options are prepare and draw
enum VisualFilterUsePhase {
  kVisualFilterUsePhasePrepare = 0,
  kVisualFilterUsePhaseDraw = 1,
};

// Indicate at which scenario this filter is displayed, currently distinguishing
// between preview and output
enum VisualFilterEffectiveScenario {
  kVisualFilterEffectiveScenarioPreview = 0x1,
  kVisualFilterEffectiveScenarioOutput = 0x2,
  kVisualFilterEffectiveScenarioAll = 0xFFFF,
};

class VisualFilter {
 public:
  virtual ~VisualFilter() = default;

  virtual bool Create(const char* json_params) = 0;

  virtual void Destroy() = 0;

  virtual void InitGraphicsResource(VisualFilterProxy* proxy) = 0;

  virtual void ReleaseGraphicsResource() = 0;

  virtual void ClearVideoCache() = 0;

  virtual graphics::Texture* Process(graphics::Texture* texture,
                                     const VisualFilterTransform& texture_data,
                                     int64_t timestamp) = 0;

  virtual MediaSDKString GetProperty(const char* key) { return {}; }

  virtual bool SetProperty(const char* key, const char* value) { return false; }

  virtual MediaSDKString Action(const char* action, const char* param) {
    return {};
  }

  virtual VisualFilterUsePhase GetUsePhase() = 0;

  virtual VisualFilterEffectiveScenario GetEffectiveScenario() {
    return kVisualFilterEffectiveScenarioAll;
  }

  virtual int64_t GetProcessDelayNS() { return 0; }

  virtual bool IsTopmost() { return false; }

  virtual bool IsBottomMost() { return false; }

};

extern "C" PLUGIN_EXPORT VisualFilter* CreateVisualFilter(
    const char* json_params);

extern "C" PLUGIN_EXPORT void DestroyVisualFilter(VisualFilter* source);

}  // namespace mediasdk
