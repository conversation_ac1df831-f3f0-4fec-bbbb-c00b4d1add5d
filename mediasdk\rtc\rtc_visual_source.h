#ifndef MEDIASDK_RTC_VISUAL_SOURCE_H_
#define MEDIASDK_RTC_VISUAL_SOURCE_H_

#include <graphics/graphics.h>
#include <graphics/texture.h>

#include "mediasdk/public/mediasdk_defines.h"
#include "mediasdk/public/mediasdk_defines_rtc.h"
#include "mediasdk/public/plugin/visual_source.h"
#include "mediasdk/rtc/rtc_manager.h"
#include "mediasdk/utils/frame_rate_calculator.h"

namespace mediasdk {

class VideoInputI420Convertor;

class RTCVisualSource : public VisualSource {
 public:
  static std::shared_ptr<RTCVisualSource> Create(
      VisualProxy* proxy,
      const std::string& json_params);

  std::shared_ptr<RTCVideoRender> GetRender() { return render_; }

  // VisualSource:
  ~RTCVisualSource() override;

  bool Action(const char* json_params) override;

  bool Prepare(int64_t timestamp_ns) override;

  void Convert() override;

  graphics::Texture* GetTexture() override;

  int64_t GetTextureTimestampNS() override;

  bool Pause() override;

  bool Continue() override;

  bool IsPaused() override;

  MediaSDKString GetProperty(const char* key) override;

  bool SetProperty(const char* key, const char* json) override;

  void OnMouseEvent(const char* json_params) override;

  void OnKeyboardEvent(const char* json_params) override;

  bool HasAudio() override;

  void InitGraphicsResource() override;

  void ReleaseGraphicsResource() override;

  const char* GetName() const override;

  bool Reopen(const char* json_params) override;

  bool EnableAlpha() override;

  float GetFps() override;

 private:
  RTCVisualSource(VisualProxy* proxy, const std::string& json_params);

  bool OnFrame(const VisualSourceFrame& video_frame);

  int GetRenderElapseMS();

  class RTCVideoRenderImpl : public RTCVideoRender {
   public:
    RTCVideoRenderImpl(RTCVisualSource* source) : source_(source) {}

    void Detach() {
      std::lock_guard<std::mutex> lg(mtx_);
      source_ = nullptr;
    }

   protected:
    // RTCVideoRender:
    bool OnFrame(const VisualSourceFrame& video_frame) override {
      std::lock_guard<std::mutex> lg(mtx_);
      if (source_) {
        return source_->OnFrame(video_frame);
      }
      return false;
    }

    int GetRenderElapse() override {
      if (source_) {
        return source_->GetRenderElapseMS();
      }
      return 0;
    }

    bool IsValid() override { return source_ != nullptr; }

   private:
    std::mutex mtx_;
    RTCVisualSource* source_ = nullptr;
  };

 private:
  VisualProxy* proxy_ = nullptr;
  std::mutex mutex_;
  int64_t video_frame_timestmap_ns_ = 0;
  std::string json_params_;
  ThreadSafeFrameRateCalculator rate_calc_;
  std::shared_ptr<RTCVideoRenderImpl> render_;
  std::atomic_int last_elapse_ = 0;
};

}  // namespace mediasdk

#endif  // MEDIASDK_RTC_VISUAL_SOURCE_H_