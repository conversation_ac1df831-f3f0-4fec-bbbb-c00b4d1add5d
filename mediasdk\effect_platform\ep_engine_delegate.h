#pragma once

#include <string>

namespace mediasdk::ep {

class EngineDelegate {
 public:
  virtual void OnDownloadModelSuccess(const std::string& request_id) = 0;

  virtual void OnDownloadModelError(const std::string& request_id,
                                    const std::string& error) = 0;

  virtual void OnDownloadModelProgress(const std::string& request_id,
                                       int progress) = 0;
};

}  // namespace mediasdk::ep