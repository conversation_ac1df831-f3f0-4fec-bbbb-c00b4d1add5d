#pragma once
#include <DirectXMath.h>
#include <wrl/client.h>

#include "border.h"
#include "chroma_key_params.h"
#include "color_params.h"
#include "corner.h"
#include "color_lut_params.h"
#include "mediasdk/public/mediasdk_defines_visual.h"
#include "point_nine.h"
#include "sharpness_params.h"
#include "texture.h"
#include "transform.h"
#include "luminance_map_params.h"
#include "partial_scale_params.h"

namespace graphics {

struct BufferedTextureBuffer {
 public:
  bool TryUpdate(const Texture& texture,
                 const DirectX::XMFLOAT2& vp_size,
                 ID3D11DeviceContext* context,
                 const DirectX::XMMATRIX& view,
                 const DirectX::XMMATRIX& project,
                 const float tonemap_param,
                 const Transform& trans,
                 const PointNine& point_nine,
                 const CornerBuffer& corner_buffer,
                 const BorderBuffer& border_buffer,
                 const DirectX::XMFLOAT4& clip_mask,
                 bool force_tex_alpha = false,
                 const ColorParams& color_params = {},
                 const ChromaKeyParams& chroma_key_params = {},
                 const SharpnessParams& sharpness_params = {});

  bool TryUpdate(const DirectX::XMFLOAT2& texture_size,
                 const DirectX::XMFLOAT2& vp_size,
                 ID3D11DeviceContext* context,
                 const DirectX::XMMATRIX& view,
                 const DirectX::XMMATRIX& project,
                 const float tonemap_param,
                 const Transform& trans,
                 const PointNine& point_nine,
                 const CornerBuffer& corner_buffer,
                 const BorderBuffer& border_buffer,
                 const DirectX::XMFLOAT4& clip_mask,
                 bool force_tex_alpha = false,
                 const ColorParams& color_params = {},
                 const ChromaKeyParams& chroma_key_params = {},
                 const SharpnessParams& sharpness_params = {},
                 const LuminanceMapParams& luminance_map_params = {});

  bool TryUpdate(const DirectX::XMFLOAT2& texture_size,
      const DirectX::XMFLOAT2& vp_size,
      ID3D11DeviceContext* context,
      const DirectX::XMMATRIX& view,
      const DirectX::XMMATRIX& project,
      const Transform& trans,
      const ColorLutParams& params);

bool TryUpdate(const DirectX::XMFLOAT2& texture_size,
    const DirectX::XMFLOAT2& vp_size,
    ID3D11DeviceContext* context,
    const DirectX::XMMATRIX& view,
    const DirectX::XMMATRIX& project,
    const Transform& trans,
    const PartialScaleParams& params);

  void Use() { skip_cnt_ = 0; }

  void Skip() { ++skip_cnt_; }

  int32_t GetSkipCnt() const { return skip_cnt_; }

  void UpdateBuffers(ID3D11DeviceContext* context,
                     const DirectX::XMMATRIX& view_matrix,
                     const DirectX::XMMATRIX& projection_matrix,
                     const float tonemap_param,
                     const PointNine& point_nine,
                     const CornerBuffer& corner_buffer,
                     const BorderBuffer& border_buffer,
                     const DirectX::XMFLOAT4& clip_mask,
                     bool  is_force_alpha,
                     const ColorParams& color_params,
                     const ChromaKeyParams& chroma_key_params,
                     const SharpnessParams& sharpness_params,
                     const LuminanceMapParams& luminance_map_params);

  void UpdateLutBuffers(ID3D11DeviceContext* context,
      const DirectX::XMMATRIX& view_matrix,
      const DirectX::XMMATRIX& projection_matrix,
      const ColorLutParams& params);

      void UpdateScaleBuffers(ID3D11DeviceContext* context,
        const DirectX::XMMATRIX& view_matrix,
        const DirectX::XMMATRIX& projection_matrix,
        const PartialScaleParams& params);

  bool Equal(const Transform& trans,
             const DirectX::XMFLOAT2 texture_size,
             const DirectX::XMFLOAT2 vp_size,
             float tonemap_param,
             const PointNine& point_nine,
             const CornerBuffer& corner_buffer,
             const BorderBuffer& border_buffer,
             const DirectX::XMFLOAT4& clip_mask,
             bool  is_force_alpha,
             const ColorParams& color_params,
             const ChromaKeyParams& chroma_key_params,
             const SharpnessParams& sharpness_params,
             const LuminanceMapParams& luminance_map_params) const;

  Transform pre_transform_;
  DirectX::XMFLOAT2 pre_texture_size_;
  DirectX::XMFLOAT2 pre_vp_size_;
  float pre_tonemap_param_ = 0.0f;
  PointNine pre_point_nine_;
  CornerBuffer pre_corner_buffer_;
  BorderBuffer pre_border_buffer_;
  bool is_force_alpha_ = false;
  ChromaKeyParams chroma_key_params_{};
  ColorParams color_params_{};
  SharpnessParams sharpness_params_{};
  LuminanceMapParams luminance_map_params_{};
  DirectX::XMFLOAT4 pre_clip_mask_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> vertex_buffer_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> index_buffer_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> matrix_buffer_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> crop_buffer_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> ps_buffer_;

  void Destroy();

 private:
  int32_t skip_cnt_ = 0;
};
}  // namespace graphics
