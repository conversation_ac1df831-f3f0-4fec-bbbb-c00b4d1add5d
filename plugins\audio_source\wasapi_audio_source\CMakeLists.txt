cmake_minimum_required(VERSION 3.20)

set(PROJECT_NAME wasapi_audio_source)

set(PLUGIN_SOURCES
        ${CMAKE_PROJECT_DIR}/mediasdk/public/plugin/plugin_defines.h
        ${CMAKE_PROJECT_DIR}/mediasdk/public/plugin/audio_input_source.h
        dllmain.cc
        wasapi_audio_device_monitor.h
        wasapi_audio_device_monitor.cc
        wasapi_audio_input_source.cc
        wasapi_audio_input_source.h
        audio_input_source.cc
        wasapi_capture_stream.h
        wasapi_capture_stream.cc
        wasapi_policy_config.cc
        wasapi_policy_config.h
        wasapi_audio_source_helper.h
        wasapi_audio_input_microphone_source.h
        wasapi_audio_input_microphone_source.cc
        wasapi_audio_input_loopback_source.h
        wasapi_audio_input_loopback_source.cc
        win_dev_info.h
        audio_device_factory.h
        audio_device_factory.cc
        dshow_audio_only_capture.h
        dshow_audio_only_capture.cc
)

add_library(${PROJECT_NAME} SHARED ${PLUGIN_SOURCES})


target_link_libraries(${PROJECT_NAME} PUBLIC base wasapi task_server_delegate mediasdk Avrt comsupp.lib dshow_helper)

target_include_directories(${PROJECT_NAME} PRIVATE
        "${CMAKE_PROJECT_DIR}/mediasdk/public"
)

target_compile_definitions(${PROJECT_NAME} PRIVATE PLUGIN_IMPLEMENTATION)

mediasdk_export_target(${PROJECT_NAME})
