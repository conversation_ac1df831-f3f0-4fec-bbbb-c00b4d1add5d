#pragma once

#include <mediasdk/public/mediasdk_defines.h>
#include <cstdint>

namespace mediasdk {

class AudioFormat;

struct AudioSourceEvent {
  const char* source_name;
  int event_type;
  int extra_code;
  MediaSDKString extra_msg;
};

class AudioFrameHandler {
 public:
  virtual ~AudioFrameHandler() = default;

  virtual void OnAudio(const AudioFormat&, const AudioSourceFrame&) = 0;
};

class AudioInputProxy : public AudioFrameHandler {
 public:
  ~AudioInputProxy() override = default;

  virtual void SignalSourceEvent(const char*) = 0;

  virtual void ReportSourceEvent(const AudioSourceEvent& event) = 0;

  virtual MediaSDKString GetInitParams() = 0;
};

}  // namespace mediasdk
