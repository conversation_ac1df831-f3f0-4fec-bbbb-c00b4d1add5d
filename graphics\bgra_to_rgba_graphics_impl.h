#pragma once

#include "graphics.h"

#include "bgra_to_rgba_graphics.h"

namespace graphics {

class BGRAToRGBAGraphicsImpl : public BGRAToRGBAGraphics {
 public:
  BGRAToRGBAGraphicsImpl(Device& ins);

 public:
  std::shared_ptr<Texture> Draw(Texture&) override;
  void Destroy() override;

  ~BGRAToRGBAGraphicsImpl() override;

 private:
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice_();
  Microsoft::WRL::ComPtr<ID3D11DeviceContext> GetContext_();

 private:
  ShaderManager* GetShaderManager_();

 private:
  std::shared_ptr<Graphics> graphics_;
  Device& device_;
};

}  // namespace graphics
