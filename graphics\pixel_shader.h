
#pragma once

namespace graphics {

static const char* TEXTURE_PIXEL_SHADER() {
  return R"(
Texture2D shader_texture : register(t0);
Texture2D luminance_map : register(t1);
SamplerState sample_type : register(s0);

cbuffer PS_BUFFER : register(b0)
{
    float force_alpha = -1.0;
    float pad0;
    float pad1;
    float pad2;

    float outside_gamma;
    float opacity;
    float tone_map_param;
    float color_adjust_flag;

    float3 chroma_key;
    float chroma_key_flags;

    float pixel_size_x;  //  1 / width
    float pixel_size_y;  //  1 / height
    float similarity;    //  The smaller it is, the higher the similarity required.
    float smoothness;    //  The smaller it is, the less smooth it becomes.

    float spill;         //  The smaller it is, the more severe the color bleeding becomes.
    float extrude;       //  The surrounding pixel expands the influence factor
    float chroma_key_opacity;
    float chroma_key_gamma;

    matrix chroma_key_color_matrix;
    matrix color_matrix;
    
    float4 sharpness_params; // width, height, sharpness, flags

    float4 luminance_map_param; // is_luminance_wipe, luminance_map_progress, luminance_map_softness, luminance_invert
    float is_use_luminance_map_param;
    float3 luminance_map_pending;
    
    // PointNineBuffer
    float4 point_nine_scale; // just use xy
    float4 border;

    // ClipMaskBuffer
    float4 clip_mask;

    // CornerBuffer
    float4 corner_radius;
    float4 corner_clip;
    float4 corner_scale;

    // BorderBuffer
    float4 border_color;
    float4 border_width;
};

static const float4x4 yuv_mat = 
    float4x4(
      float4(0.2126,  0.7152,  0.0722,  0.0),  
      float4(-0.1146, -0.3554,  0.5000,  0.5), 
      float4(0.5000, -0.4042, -0.0458,  0.5),  
      float4(0.0,     0.0,     0.0,     1.0)
    );

struct PS_INPUT
{
    float4 position : SV_POSITION;
    float2 tex : TEXCOORD0;
};

float4 laplace_filter_opt(float2 tex)
{
    float2 offsets[8] = {
        float2(-1,-1),
        float2(0, -1),
        float2(1,-1),
        float2(-1, 0),
        float2(1, 0),
        float2(-1,1),
        float2(0, 1),
        float2(1,1),
    };
    
    float4 color =shader_texture.Sample( sample_type, tex);
    float4 laplace = 8 * color;
    float2 p_size = (1.0 / sharpness_params.x, 1.0 / sharpness_params.y);

    for (int i = 0; i < 8; i++) {
        laplace -= shader_texture.Sample(sample_type, tex + offsets[i] * p_size);
    }
    
    float4 out_color = saturate(color + sharpness_params.z * laplace);
    out_color.a = color.a;

    return out_color;
}

float3 apply_gamma(float3 original_color, float input_gamma)
{
    original_color.rgb = pow(original_color.rgb, input_gamma);
    return original_color.rgb;
}

float3 RGBtoYUV(float3 rgb)
{
    return mul(float4(rgb, 1.0), yuv_mat);
}

float3 RGBtoHSV(float3 rgb) {
    float r = rgb.r, g = rgb.g, b = rgb.b;
    float maxVal = max(r, max(g, b));
    float minVal = min(r, min(g, b));
    float delta = maxVal - minVal;
    float h = 0.0, s = 0.0, v = maxVal;
    if (delta > 1e-5) {
        s = delta / maxVal;
        if (maxVal == r) {
            h = (g - b) / delta;
        } else if (maxVal == g) {
            h = 2.0 + (b - r) / delta;
        } else {
            h = 4.0 + (r - g) / delta;
        }
    }
    return float3(h, s, v);
}

float ChromaMatchDistance(float3 rgb)
{
    float3 hsv = RGBtoHSV(rgb);
    float3 chroma_key_hsv = RGBtoHSV(chroma_key);
    float angel_diff = radians(60.0 * (hsv.x - chroma_key_hsv.x));
    float grey_sat = (43.0 / 255.0);
    float black_vue = (46.0 / 255.0);
	float src_sat = hsv.y > grey_sat ? hsv.y : 0.0;
    float dst_sat = chroma_key_hsv.y;
    float src_vue = hsv.z > black_vue ? hsv.z : 0.0;;
    float dst_vue = chroma_key_hsv.z;
    float vue_dis = saturate(distance(src_vue, dst_vue));
    float dist = sqrt(pow(src_sat, 2.0) + pow(dst_sat, 2.0) - 2.0 * src_sat * dst_sat * cos(angel_diff));
    float dis = 0.4472136 * sqrt(pow(dist, 2.0) + pow(vue_dis, 2.0));
    return dis; 
}

float4 SampleTexture(float2 uv)
{
    return shader_texture.Sample(sample_type, saturate(uv * float2(1.0 - pixel_size_x * extrude, 1.0 - pixel_size_y * extrude)));
}

float CalcNonlinearChannel(float u)
{
    return (u < 0.018) ? 4.5 * u : (1.099 * pow(u, 0.45)) - 0.099;
}

float3 CalcNonLinearGamma(float3 rgb)
{
    return float3(CalcNonlinearChannel(rgb.r), CalcNonlinearChannel(rgb.g), CalcNonlinearChannel(rgb.b));
}

float ChromaSimilarityWithFilter(float3 rgb, float2 tex)
{
    float2 pixel_size = float2(pixel_size_x, pixel_size_y);
    float2 h_pixel_size = pixel_size / 2.0;

    float dist_value = 0.0;
    float2 point0 = float2(-h_pixel_size.x, h_pixel_size.y);
    float2 point1 = float2(0, h_pixel_size.y);
    float2 point2 = float2(h_pixel_size.x, h_pixel_size.y);   
    float2 point3 = float2(h_pixel_size.x, 0);
    dist_value += ChromaMatchDistance(CalcNonLinearGamma(SampleTexture(tex - point0).rgb));
    dist_value += ChromaMatchDistance(CalcNonLinearGamma(SampleTexture(tex + point0).rgb));
    dist_value += ChromaMatchDistance(CalcNonLinearGamma(SampleTexture(tex - point1).rgb));
    dist_value += ChromaMatchDistance(CalcNonLinearGamma(SampleTexture(tex + point1).rgb));
    dist_value += ChromaMatchDistance(CalcNonLinearGamma(SampleTexture(tex - point2).rgb));
    dist_value += ChromaMatchDistance(CalcNonLinearGamma(SampleTexture(tex + point2).rgb));
    dist_value += ChromaMatchDistance(CalcNonLinearGamma(SampleTexture(tex - point3).rgb));
    dist_value += ChromaMatchDistance(CalcNonLinearGamma(SampleTexture(tex + point3).rgb));
    dist_value += ChromaMatchDistance(CalcNonLinearGamma(rgb)); 
    return dist_value / 9.0;
}

float4 ApplyChromaKey(float4 rgba, float2 tex)
{
    float chroma_dist = ChromaSimilarityWithFilter(rgba.rgb, tex);
    
    float base_mask = chroma_dist - similarity;
    float safe_smoothness = max(smoothness, 1e-5);
    float safe_spill = max(spill, 1e-5);
    
    float full_mask = pow(saturate(base_mask / safe_smoothness), 1.5);
    float spill_value = pow(saturate(base_mask / safe_spill), 1.5);
   
    rgba.a *= full_mask;

    float desat = dot(rgba.rgb, float3(0.2126, 0.7152, 0.0722));
    rgba.rgb = lerp(float3(desat, desat, desat), rgba.rgb, spill_value);

    return rgba;
}

float4 apply_chroma_key(float4 color, float2 tex)
{
    float4 origin_color = SampleTexture(tex);
    float4 chroma_color = ApplyChromaKey(origin_color, tex);
    
    // do color correction
    chroma_color.rgb = apply_gamma(chroma_color.rgb, chroma_key_gamma);
    chroma_color = mul(chroma_key_color_matrix, chroma_color);
    chroma_color.a *= chroma_key_opacity;

    return chroma_color;
}

float4 SampleLuminance(float4 color, float2 tex)
{
    float is_wipe = luminance_map_param.x;
    float softness = luminance_map_param.z;
    float progess = luminance_map_param.y;
    float luminance_invert = luminance_map_param.w;

    float luminance = luminance_map.Sample(sample_type, tex).r;
    if (luminance_invert > 0.0f) {
        luminance = 1.0 - luminance;
    }

    progess = lerp(0, 1.0 + softness, progess);
    
    if (luminance <= progess - softness) {
        if (is_wipe > 0.0f) {
            color.a = 0.0f;
        } else {
            color.a = 1.0f; 
        }
    } else if (luminance >= progess) {
        if (is_wipe > 0.0f) {
            color.a = 1.0f;
        } else {
            color.a = 0.0f; 
        }
    } else {
        float t = (progess - luminance) / softness;
        if (is_wipe > 0.0f) {
            color.a = 1.0f - lerp(0.0f, 1.0f, t);
        } else {
            color.a = lerp(0.0f, 1.0f, t);
        }
    }

    return color;
}

float4 apply_param(float4 color, float2 tex)
{
    if (force_alpha > 0.0f)
    {
        color.a = 1.0f;
    }

    if (color_adjust_flag > 0.0f) 
    {
      // outside_gamma
      color.rgb = apply_gamma(color.rgb, outside_gamma);
      color = mul(color_matrix, color);
      color.a *= opacity;
    }

    if (chroma_key_flags > 0.0f) {
      color = apply_chroma_key(color, tex);
    }
    
    if (sharpness_params.w > 0.0f) {
      color = laplace_filter_opt(tex);
    }

    if (is_use_luminance_map_param > 0.0f) {
      color = SampleLuminance(color, tex);
    }

    return color;
}

float4 Gamma(float4 color) 
{
    return float4(pow(color.xyz, 0.45455), color.a);
}
)"

         R"(

float4 PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 color = shader_texture.Sample(sample_type, input.tex);
    return apply_param(color, input.tex);
}

float4 SRGB_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 color = shader_texture.Sample(sample_type, input.tex);
    return apply_param(color, input.tex);
}

float4 SRGB_GAMA_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 color = shader_texture.Sample(sample_type, input.tex);
    color = apply_param(color, input.tex);
    color = Gamma(color);
    return color;
}

float4 Y8_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float color = shader_texture.Sample(sample_type, input.tex);
    return color;
}

float4 sRGB_TO_Rec2020(float4 color)
{
    static const float3x3 convert_matrix =
    {
        0.627404, 0.329283, 0.043313,
        0.069097, 0.919540, 0.011362,
        0.016391, 0.088013, 0.895595
    };
    float3 color_709 = color.xyz;
    mul(convert_matrix, color_709);
    return float4(color_709.xyz, color.a);
} 

float3 UnchartedTransfer(float3 color)
{
    float a = 0.22;
    float b = 0.30;
    float c = 0.10;
    float d = 0.20;
    float e = 0.01;
    float f = 0.30;
    return ((color * (a * color + c * b) + d * e) / (color * (a * color + b) + d * f)) - e / f;
}

float3 UnchartedToneMapping(float3 color, float adapted_lum)
{
    float3 WHITE = {11.2, 11.2, 11.2};
    return UnchartedTransfer(1.6 * adapted_lum * color) / UnchartedTransfer(WHITE);
}

float3 ReinhardToneMapping(float3 color, float k)
{
    return color / (k + color);
}

float3 ACESToneMapping(float3 color, float adapted_lum)
{
    const float A = 2.51f;    
    const float B = 0.03f;
    const float C = 2.43f;
    const float D = 0.59f;
    const float E = 0.14f;
    color *= adapted_lum;
    return (color * (A * color + B)) / (color * (C * color + D) + E);
}

float4 Rec2020_TO_sRGB(float4 color)
{
    static const float3x3 convert_matrix =
    {
        1.660491, -0.587641, -0.072850,
        -0.124550, 1.132900, -0.008349,
        -0.018151, -0.100579, 1.118730
    };
    float3 color_2020 = color.xyz;
    mul(convert_matrix, color_2020);
    return float4(color_2020.xyz, color.a);
}

float4 HDR_Reinhard_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 color = shader_texture.Sample(sample_type, input.tex);
	  color = apply_param(color, input.tex);
    color = sRGB_TO_Rec2020(color);
    color = float4(ReinhardToneMapping(color.xyz, tone_map_param), color.a);
    color = Rec2020_TO_sRGB(color);
    return Gamma(color);
}

float4 HDR_Uncharted_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 color = shader_texture.Sample(sample_type, input.tex);
	  color = apply_param(color, input.tex);
    color = sRGB_TO_Rec2020(color);
    color = float4(UnchartedToneMapping(color.xyz, tone_map_param), color.a);
    color = Rec2020_TO_sRGB(color);
    return Gamma(color);
}

float4 HDR_ACES_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 color = shader_texture.Sample(sample_type, input.tex);
	  color = apply_param(color, input.tex);
    color = sRGB_TO_Rec2020(color);
    color = float4(ACESToneMapping(color.xyz, tone_map_param), color.a);
    color = Rec2020_TO_sRGB(color);
    return Gamma(color);
}

float4 CORNER_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 texture_color = shader_texture.Sample(sample_type, input.tex);
    float cx, cy;
    shader_texture.GetDimensions(cx, cy);
    float ratio = cy / cx / corner_scale.x;
    
    float2 size_after_clip;
    size_after_clip.x = cx - corner_clip.x - corner_clip.z;
    size_after_clip.y = cy - corner_clip.y - corner_clip.w;

    float2 pos_left_top;
    pos_left_top.x = corner_clip.x / cx;
    pos_left_top.y = corner_clip.y / cy;
    float2 pos_right_top;
    pos_right_top.x = (corner_clip.x + size_after_clip.x) / cx;
    pos_right_top.y = pos_left_top.y;
    float2 pos_left_bottom;
    pos_left_bottom.x = pos_left_top.x;
    pos_left_bottom.y = (corner_clip.y + size_after_clip.y) / cy;
    float2 pos_right_bottom;
    pos_right_bottom.x = pos_right_top.x;
    pos_right_bottom.y = pos_left_bottom.y;

    float2 pos_middle = (pos_left_top + pos_right_bottom) / 2.0;

    float2 center_left_top = float2(pos_left_top.x + corner_radius.x, pos_left_top.y + corner_radius.x / ratio);
    float2 center_right_top = float2(pos_right_top.x - corner_radius.y, pos_right_top.y + corner_radius.y / ratio);
    float2 center_left_bottom = float2(pos_left_bottom.x + corner_radius.z, pos_left_bottom.y - corner_radius.z / ratio);
    float2 center_right_bottom = float2(pos_right_bottom.x - corner_radius.w, pos_right_bottom.y - corner_radius.w / ratio);

    [unroll]
    for (int i = 0; i < 4; ++i)
    {
        float2 pos_corner;
        float2 pos_center;
        float radius;
        switch (i)
        {
            case 0: // top left
                pos_corner = pos_left_top;
                pos_center = center_left_top;
                radius = corner_radius.x;
                break;
            case 1: // top right
                pos_corner = pos_right_top;
                pos_center = center_right_top;
                radius = corner_radius.y;
                break;
            case 2: // bottom left
                pos_corner = pos_left_bottom;
                pos_center = center_left_bottom;
                radius = corner_radius.z;
                break;
            case 3: // bottom right
                pos_corner = pos_right_bottom;
                pos_center = center_right_bottom;
                radius = corner_radius.w;
                break;
        }
        float2 tex = input.tex;
        float2 dist = float2(tex.x - pos_center.x, tex.y * ratio - pos_center.y * ratio);
        float len = length(dist);
        if (step(pos_middle.x, tex.x) == step(pos_middle.x, pos_corner.x) 
            && step(pos_middle.y, tex.y) == step(pos_middle.y, pos_corner.y) 
            && step(pos_center.x, tex.x) == step(pos_center.x, pos_corner.x) 
            && step(pos_center.y, tex.y) == step(pos_center.y, pos_corner.y)
            && step(radius, len))
        {
            texture_color.a = 0.0;
            return texture_color;
        }
    }
    return texture_color;
}

float4 BORDER_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 texture_color = shader_texture.Sample(sample_type, input.tex);
    float cx, cy;
    shader_texture.GetDimensions(cx, cy);
    float x_pos = border_width.x / cx / border_width.z;
    float y_pos = border_width.y / cy / border_width.w;
    if (step(x_pos, input.tex.x) && step(y_pos, input.tex.y) 
        && step(input.tex.x, 1-x_pos) && step(input.tex.y, 1-y_pos))
    {
        return texture_color;
    }
    return texture_color * (1.0 - border_color.w) + border_color * border_color.w;
}

float4 REMOVE_BORDER_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 texture_color = shader_texture.Sample(sample_type, input.tex);
    float cx, cy;
    shader_texture.GetDimensions(cx, cy);
    if (input.tex.x <= 2.0f / cx 
        || input.tex.x >= (cx - 2.0f) / cx 
        || input.tex.y <= 2.0f / cy 
        || input.tex.y >= (cy - 2.0f) / cy)
    {
        texture_color.a = 0;
        return texture_color;
    }
    return apply_param(texture_color, input.tex);
}

float4 SCALE_X_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float cx, cy;
    shader_texture.GetDimensions(cx, cy);
    float k = point_nine_scale.x / point_nine_scale.y;
    float top_left = border.x / cx;
    float top_right = border.y / cx;
    float4 texture_color;
    if (input.tex.x * k < top_left)
    {
        float2 tex;
        tex.x = k * input.tex.x;
        tex.y = input.tex.y;
        texture_color = shader_texture.Sample(sample_type, tex);
    }
    else if (input.tex.x * k < k - (1.0 - top_right))
    {
        float k2 = (k * top_right - k * top_left) / (k - 1 + top_right - top_left);
        float2 tex;
        tex.x = (input.tex.x - top_left / k) * k2 + top_left;
        tex.y = input.tex.y;
        texture_color = shader_texture.Sample(sample_type, tex);
    }
    else
    {
        float2 tex;
        tex.x = 1.0 - k * (1.0 -input.tex.x);
        tex.y = input.tex.y;
        texture_color = shader_texture.Sample(sample_type, tex);
    }
    return apply_param(texture_color, input.tex);
}

float4 SCALE_Y_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float cx, cy;
    shader_texture.GetDimensions(cx, cy);
    float4 texture_color;
    float k = point_nine_scale.y / point_nine_scale.x;
    float left_top = border.z / cy;
    float left_bottom = border.w / cy;
    if (input.tex.y * k < left_top)
    {
        float2 tex;
        tex.x = input.tex.x;
        tex.y = k * input.tex.y;
        texture_color = shader_texture.Sample(sample_type, tex);
    }
    else if (input.tex.y * k < k - (1.0 - left_bottom))
    {
        float k2 = (k * left_bottom - k * left_top) / (k - 1 + left_bottom - left_top);
        float2 tex;
        tex.x = input.tex.x;
        tex.y = (input.tex.y - left_top / k) * k2 + left_top;
        texture_color = shader_texture.Sample(sample_type, tex);
    }
    else
    {
        float2 tex;
        tex.x = input.tex.x;
        tex.y = 1.0 - k * (1.0 -input.tex.y);
        texture_color = shader_texture.Sample(sample_type, tex);
    }
    return apply_param(texture_color, input.tex);
}

float4 REMOVE_BORDER_SCALE_X_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 texture_color = shader_texture.Sample(sample_type, input.tex);
    float cx, cy;
    shader_texture.GetDimensions(cx, cy);
    if (input.tex.x <= 2.0f / cx 
        || input.tex.x >= (cx - 2.0f) / cx 
        || input.tex.y <= 2.0f / cy 
        || input.tex.y >= (cy - 2.0f) / cy)
    {
        texture_color.a = 0;
        return texture_color;
    }
    float k = point_nine_scale.x / point_nine_scale.y;
    float top_left = border.x / cx;
    float top_right = border.y / cx;
    if (input.tex.x * k < top_left)
    {
        float2 tex;
        tex.x = k * input.tex.x;
        tex.y = input.tex.y;
        texture_color = shader_texture.Sample(sample_type, tex);
    }
    else if (input.tex.x * k < k - (1.0 - top_right))
    {
        float k2 = (k * top_right - k * top_left) / (k - 1 + top_right - top_left);
        float2 tex;
        tex.x = (input.tex.x - top_left / k) * k2 + top_left;
        tex.y = input.tex.y;
        texture_color = shader_texture.Sample(sample_type, tex);
    }
    else
    {
        float2 tex;
        tex.x = 1.0 - k * (1.0 -input.tex.x);
        tex.y = input.tex.y;
        texture_color = shader_texture.Sample(sample_type, tex);
    }
    return apply_param(texture_color, input.tex);
}

float4 REMOVE_BORDER_SCALE_Y_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 texture_color = shader_texture.Sample(sample_type, input.tex);
    float cx, cy;
    shader_texture.GetDimensions(cx, cy);
    if (input.tex.x <= 2.0f / cx 
        || input.tex.x >= (cx - 2.0f) / cx 
        || input.tex.y <= 2.0f / cy 
        || input.tex.y >= (cy - 2.0f) / cy)
    {
        texture_color.a = 0;
        return texture_color;
    }
    float k = point_nine_scale.y / point_nine_scale.x;
    float left_top = border.z / cy;
    float left_bottom = border.w / cy;
    if (input.tex.y * k < left_top)
    {
        float2 tex;
        tex.x = input.tex.x;
        tex.y = k * input.tex.y;
        texture_color = shader_texture.Sample(sample_type, tex);
    }
    else if (input.tex.y * k < k - (1.0 - left_bottom))
    {
        float k2 = (k * left_bottom - k * left_top) / (k - 1 + left_bottom - left_top);
        float2 tex;
        tex.x = input.tex.x;
        tex.y = (input.tex.y - left_top / k) * k2 + left_top;
        texture_color = shader_texture.Sample(sample_type, tex);
    }
    else
    {
        float2 tex;
        tex.x = input.tex.x;
        tex.y = 1.0 - k * (1.0 -input.tex.y);
        texture_color = shader_texture.Sample(sample_type, tex);
    }
    return apply_param(texture_color, input.tex);
}

float4 CLIP_MASK_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 color = float4(0.0f, 0.0f, 0.0f,0.5f);
	if(input.tex.x > clip_mask.x 
        && input.tex.x < 1 - clip_mask.z 
        && input.tex.y > clip_mask.y 
        && input.tex.y < 1 - clip_mask.w)
	{
           color.a = 0.0f;
	}
    return color;
}
)";
}

static const char* COLOR_LUT_PIXEL_SHADER() {
        return R"(
Texture2D shader_texture : register(t0);
Texture2D lut_1d_texture : register(t1);
Texture3D lut_3d_texture : register(t2);
SamplerState sample_type : register(s0);

cbuffer PS_BUFFER : register(b0)
{
    // LutBuffer
    float4 domain_min;
    float4 domain_max;
    float amount;
    int pass_through_alpha;
};

struct PS_INPUT
{
    float4 position : SV_POSITION;
    float2 tex : TEXCOORD0;
};


float4 COLOR_LUT_1D_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 color = shader_texture.Sample(sample_type , input.tex);
    float4 color_lut_out = color;
    float4 color_out = color;
	if (color.r >= domain_min.r && color.r <= domain_max.r &&
		color.g >= domain_min.g && color.g <= domain_max.g &&
		color.b >= domain_min.b && color.b <= domain_max.b)
	{
        float3 color_alignment = color * (1.0 - 1.0 / 64) + 0.5 * 1.0 / 64;
        color_lut_out.r = lut_1d_texture.Sample(sample_type, float2(color_alignment.r, 0.1)).r;
        color_lut_out.g = lut_1d_texture.Sample(sample_type, float2(color_alignment.g, 0.1)).g;
        color_lut_out.b = lut_1d_texture.Sample(sample_type, float2(color_alignment.b, 0.1)).b;
        color_out.rgb = lerp(color.rgb, color_lut_out.rgb, amount);
    }
    color_out.rgb = saturate(color_out.rgb); 
    return color_out;
}

float4 COLOR_LUT_3D_PS_MAIN(PS_INPUT input) : SV_TARGET
{
    float4 color = shader_texture.Sample(sample_type , input.tex);
    float4 color_out = color;
	if (color.r >= domain_min.r && color.r <= domain_max.r &&
		color.g >= domain_min.g && color.g <= domain_max.g &&
		color.b >= domain_min.b && color.b <= domain_max.b)
	{
        float3 color_alignment = color * (1.0 - 1.0 / 64) + 0.5 * 1.0 / 64;
        float3 color_lut_out = lut_3d_texture.Sample(sample_type, color_alignment);
        color_out.rgb = lerp(color.rgb, color_lut_out.rgb, amount);
    }
    color_out.rgb = saturate(color_out.rgb); 
    return color_out;
}

)";
}

static const char* SCALE_PIXEL_SHADER() {
return R"(
    Texture2D g_inputTexture    : register(t0);
SamplerState g_samplerState : register(s0);

cbuffer StretchParameters : register(b0)
{
    float g_stretchStartPoint; // Normalized coordinate [0, 1] where stretching starts.
                               // For vertical stretch (1 or 3), this is Y.
                               // For horizontal stretch (2 or 4), this is X.
    float g_stretchRatio;      // Scale factor for the stretched region. 
                               // Example: 2.0 means this part appears twice as large.
                               // Must be > 0.
    int g_stretchDirection;    // 1: Down, 2: Right, 3: Up, 4: Left
};

struct PixelShaderInput
{
    float4 position   : SV_POSITION;
    float2 texcoord   : TEXCOORD0;
};

float4 SCALE_PS_MAIN(PixelShaderInput input) : SV_Target
{
    float2 sampleCoord = input.texcoord;
    float safe_stretch_ratio = max(g_stretchRatio, 0.00001f);

    if (g_stretchDirection == 1) // Stretch Down
    {
        float cond_pixel_in_stretch_zone = step(g_stretchStartPoint, input.texcoord.y);
        float cond_valid_stretch_region = step(g_stretchStartPoint, 1.0f);
        float apply_stretch_factor = cond_pixel_in_stretch_zone * cond_valid_stretch_region;

        float stretch_region_size_output = max(1.0f - g_stretchStartPoint, 0.00001f);
        float norm_coord_output = (input.texcoord.y - g_stretchStartPoint) / stretch_region_size_output;
        float norm_coord_input = norm_coord_output / safe_stretch_ratio;
        float stretched_coord = g_stretchStartPoint + norm_coord_input * stretch_region_size_output;

        sampleCoord.y = lerp(input.texcoord.y, stretched_coord, apply_stretch_factor);
    }
    else if (g_stretchDirection == 2) // Stretch Right
    {
        float cond_pixel_in_stretch_zone = step(g_stretchStartPoint, input.texcoord.x);
        float cond_valid_stretch_region = step(g_stretchStartPoint, 1.0f);
        float apply_stretch_factor = cond_pixel_in_stretch_zone * cond_valid_stretch_region;

        float stretch_region_size_output = max(1.0f - g_stretchStartPoint, 0.00001f);
        float norm_coord_output = (input.texcoord.x - g_stretchStartPoint) / stretch_region_size_output;
        float norm_coord_input = norm_coord_output / safe_stretch_ratio;
        float stretched_coord = g_stretchStartPoint + norm_coord_input * stretch_region_size_output;

        sampleCoord.x = lerp(input.texcoord.x, stretched_coord, apply_stretch_factor);
    }
    else if (g_stretchDirection == 3) // Stretch Up (Y grows downwards, 0.0 is top, 1.0 is bottom)
    {
        // g_stretchStartPoint: proportion of the texture (from bottom, 0 to 1) that is NOT stretched.
        // Non-stretched region (visual bottom): [1.0 - g_stretchStartPoint, 1.0]
        // Stretched region (visual top, source is [0, 1.0 - g_stretchStartPoint) ): [0, 1.0 - g_stretchStartPoint)
        float non_stretch_start_y = 1.0f - g_stretchStartPoint;

        float cond_pixel_in_stretch_zone = step(input.texcoord.y, non_stretch_start_y); // 1.0 if input.texcoord.y < non_stretch_start_y (in stretch zone)
        float apply_stretch_factor = cond_pixel_in_stretch_zone;

        // Size of the source region that will be stretched (the top part of the texture)
        float source_stretch_zone_size = max(non_stretch_start_y, 0.00001f);

        // Calculate the source Y offset for the visual top of the stretch zone (input.texcoord.y = 0)
        // based on the user's formula: (safe_stretch_ratio - 1) / safe_stretch_ratio * (1.0 - g_stretchStartPoint)
        // This offset can be negative if safe_stretch_ratio < 1 (compression), which is fine as it will be clamped by sampler.
        float y_offset_at_top = (safe_stretch_ratio - 1.0f) / max(safe_stretch_ratio, 0.00001f) * non_stretch_start_y;

        // As input.texcoord.y increases (moving down visually within the stretch zone),
        // the source Y coordinate starts from y_offset_at_top and increases by input.texcoord.y / safe_stretch_ratio.
        float stretched_coord_y_in_source = y_offset_at_top + (input.texcoord.y / safe_stretch_ratio);
        
        sampleCoord.y = lerp(input.texcoord.y, stretched_coord_y_in_source, apply_stretch_factor);
    }
    else if (g_stretchDirection == 4) // Stretch Left (X grows rightwards, 0.0 is left, 1.0 is right)
    {
        // g_stretchStartPoint: proportion of the texture (from right, 0 to 1) that is NOT stretched.
        // Non-stretched region (visual right): [1.0 - g_stretchStartPoint, 1.0]
        // Stretched region (visual left): [0, 1.0 - g_stretchStartPoint)
        float non_stretch_start_x = 1.0f - g_stretchStartPoint;

        float cond_pixel_in_stretch_zone = step(input.texcoord.x, non_stretch_start_x); // 1.0 if input.texcoord.x < non_stretch_start_x (in stretch zone)
        float apply_stretch_factor = cond_pixel_in_stretch_zone;

        // Apply similar logic for left stretch.
        // Calculate the source X offset for the visual left of the stretch zone (input.texcoord.x = 0)
        float x_offset_at_left = (safe_stretch_ratio - 1.0f) / max(safe_stretch_ratio, 0.00001f) * non_stretch_start_x;

        // As input.texcoord.x increases (moving right visually within the stretch zone),
        // the source X coordinate starts from x_offset_at_left and increases by input.texcoord.x / safe_stretch_ratio.
        float stretched_coord_x_in_source = x_offset_at_left + (input.texcoord.x / safe_stretch_ratio);

        sampleCoord.x = lerp(input.texcoord.x, stretched_coord_x_in_source, apply_stretch_factor);
    }

    return g_inputTexture.Sample(g_samplerState, sampleCoord);
}
)";
}
}  // namespace graphics
