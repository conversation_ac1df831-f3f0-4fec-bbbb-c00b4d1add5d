
#pragma once

#include "plugin_manager.h"
#include "plugin_service.h"

namespace mediasdk {

class PluginServiceImpl final : public PluginService {
 public:
  PluginServiceImpl();

  ~PluginServiceImpl() override;

  void ReportLoadEvent() override;

  // Component:
  bool Initialize() override;

  void Uninitialize() override;

  // PluginService:
  std::shared_ptr<PluginInfoArray> EnumSource(PluginType type) override;

  // Visual
  std::shared_ptr<MediaSDKString> EnumVisualInput(
      const std::string& plugin_name,
      const std::string& json_config) override;

  std::shared_ptr<MediaSDKString> EnumVisualFormat(
      const std::string& plugin_name,
      const std::string& device_id) override;

  std::shared_ptr<MediaSDKString> EnumAudioFormat(
      const std::string& plugin_name,
      const std::string& device_id) override;

  std::shared_ptr<VisualSource> CreateVisualSource(
      std::shared_ptr<VisualProxy> proxy,
      const std::string& plugin_name,
      const std::string& json_params) override;

  bool ReopenVisual(std::shared_ptr<VisualSource> source,
                    const std::string& json_params) override;

  bool PauseVisualCapture(std::shared_ptr<VisualSource> source) override;

  ResultBoolBool IsVisualCapturePause(
      std::shared_ptr<VisualSource> source) override;

  bool ContinueVisualCapture(std::shared_ptr<VisualSource> source) override;

  void DestroyVisualSource(std::shared_ptr<VisualSource> source) override;

  std::shared_ptr<MediaSDKString> GetVisualProperty(
      std::shared_ptr<VisualSource> source,
      const std::string& key) override;

  bool SetVisualProperty(std::shared_ptr<VisualSource> source,
                         const std::string& key,
                         const std::string& json) override;

  bool DoVisualAction(std::shared_ptr<VisualSource> source,
                      const std::string& json) override;

  void DestroyAllVisualSource() override;

  bool RegisterExternalVisualSourceFactory(
      const PluginInfo& info,
      std::shared_ptr<VisualSourceFactory> factory) override;

  bool RegisterExternalAudioInputSourceFactory(
      const PluginInfo& info,
      std::shared_ptr<AudioInputSourceFactory> factory) override;

  std::shared_ptr<MediaSDKString> GetVisualSourceProperty(
      const std::string& plugin_name,
      const std::string& json) override;

  bool SetVisualSourceProperty(const std::string& plugin_name,
                               const std::string& key,
                               const std::string& json) override;

  std::shared_ptr<VisualFilter> CreateVisualFilter(
      const std::string& plugin_name,
      const std::string& json_params) override;

  std::shared_ptr<MediaSDKString> GetVisualFilterProperty(
      std::shared_ptr<VisualFilter> filter,
      const std::string& key) override;

  bool SetVisualFilterProperty(std::shared_ptr<VisualFilter> filter,
                               const std::string& key,
                               const std::string& json) override;

  std::shared_ptr<MediaSDKString> VisualFilterAction(
      std::shared_ptr<VisualFilter> filter,
      const std::string& action,
      const std::string& param) override;

  // Audio input
  std::shared_ptr<MediaSDKString> EnumAudioInput(
      const std::string& plugin_name) override;

  std::shared_ptr<MediaSDKString> GetDefaultCaptureAudio(
      const std::string& plugin_name) override;

  std::shared_ptr<MediaSDKString> GetDefaultRenderAudio(
      const std::string& plugin_name) override;

  std::shared_ptr<MediaSDKString> EnumCaptureAudio(
      const std::string& plugin_name) override;

  std::shared_ptr<MediaSDKString> EnumRenderAudio(
      const std::string& plugin_name) override;

  std::shared_ptr<AudioInputSource> CreateAudioInputSource(
      std::shared_ptr<AudioInputProxy> proxy,
      const std::string& plugin_name,
      const std::string& json_params) override;

  std::shared_ptr<AudioFilter> CreateAudioFilter(
      const std::string& plugin_name,
      const std::string& json_params) override;

  bool InitAudioFilter(std::shared_ptr<AudioFilter> filter,
                       const AudioFormat& audio_format,
                       AudioFilterProxy* proxy) override;

  void UninitAudioFilter(std::shared_ptr<AudioFilter> filter) override;

  bool SetAudioFilterEnable(std::shared_ptr<AudioFilter> filter,
                            bool enable) override;
  ResultBoolBool IsAudioFilterEnable(
      std::shared_ptr<AudioFilter> filter) override;

  ResultBoolString GetAudioFilterProperty(std::shared_ptr<AudioFilter> filter,
                                          const std::string& key) override;

  bool SetAudioFilterProperty(std::shared_ptr<AudioFilter> filter,
                              const std::string& key,
                              const std::string& json) override;

  ResultBoolString AudioFilterAction(std::shared_ptr<AudioFilter> filter,
                                     const std::string& action,
                                     const std::string& param) override;

  void DestroyAudioInputSource(
      std::shared_ptr<AudioInputSource> source) override;

  void DestroyAudioFilter(
      std::vector<std::shared_ptr<AudioFilter>> filters) override;

  void DestroyAllAudioInputSource() override;

  void DestroyAllAudioFilter() override;

  // Stream service
  std::shared_ptr<StreamServiceSource> CreateStreamServiceSource(
      std::shared_ptr<StreamServiceProxy> proxy,
      const std::string& plugin_name,
      const std::string& json_params) override;

  void DestroyStreamServiceSource(
      std::shared_ptr<StreamServiceSource> source) override;

  void DestroyAllStreamServiceSource() override;

  // Video Encoder
  std::shared_ptr<VideoEncoderSource> CreateVideoEncoderSource(
      std::shared_ptr<VideoEncoderProxy> proxy,
      const std::string& plugin_name,
      const std::string& json_params) override;

  bool TestEncoderSessionCountSupported(const std::string& plugin_name,
                                        uint32_t count) override;

  // Audio Encoder
  std::shared_ptr<AudioEncoderSource> CreateAudioEncoderSource(
      std::shared_ptr<AudioEncoderProxy> proxy,
      const std::string& plugin_name,
      const std::string& json_params) override;

 private:
  std::unique_ptr<PluginManager> plugin_manager_;
};

}  // namespace mediasdk
