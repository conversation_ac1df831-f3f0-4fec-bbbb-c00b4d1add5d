#pragma once

#include <mutex>
#include "mediasdk/data_center/biz_center/end_to_end_delay.h"

namespace mediasdk {

class EndtoEndDelayImpl : public EndtoEndDelay {
 public:
  explicit EndtoEndDelayImpl(const std::string& name);

  ~EndtoEndDelayImpl() override;

  void CollectCostUS(int64_t cost_us);

  bool FillCostData(std::map<std::string, int64_t>& cost_map) override;

 private:
  const std::string name_;
  EndtoEndDelayCostData cost_data_;
};

class EndtoEndDelayImplThreadSafe : public EndtoEndDelay {
 public:
  EndtoEndDelayImplThreadSafe(const std::string& name);

  void CollectCostUS(int64_t cost_us);

  bool FillCostData(std::map<std::string, int64_t>& cost_map) override;

 private:
  const std::string name_;
  std::mutex mutex_;
  EndtoEndDelayCostData cost_data_;
};

}  // namespace mediasdk