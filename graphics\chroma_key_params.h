#pragma once

#include "graphics_export.h"
#include "mediasdk/public/mediasdk_defines_visual.h"

namespace graphics {

class GRAPHICS_EXPORT ChromaKeyParams {
 public:
  struct PixelSize {
    float x;
    float y;
  };

  ChromaKeyParams() = default;

  ChromaKeyParams(const ChromaKeyParams& other);

  ChromaKeyParams& operator=(const ChromaKeyParams& other);

  bool operator==(const ChromaKeyParams& other) const;

  bool operator!=(const ChromaKeyParams& other) const;

  bool IsEmpty() const;

  void Reset();

  float ChromaKey() const;

  void SetChromaKey(uint32_t chroma_key);

  float PixelSizeX() const;

  float PixelSizeY() const;

  void SetPixelSize(PixelSize pixel_size);

  float Similarity() const;

  void SetSimilarity(float similarity);

  float Smoothness() const;

  void SetSmoothness(float smoothness);

  float Spill() const;

  void SetSpill(float spill);

  float Extrude() const;

  void SetExtrude(float extrude);

  float Opacity() const;

  void SetOpacity(float opacity);

  float Gamma() const;

  void SetGamma(float gamma);

  float Brightness() const;

  void SetBrightness(float brightness);

  float Contrast() const;

  void SetContrast(float contrast);

 private:
  void Assign(const ChromaKeyParams& other);

  uint32_t chroma_key_ = 0;
  PixelSize pixel_size_ = {};
  float similarity_ = 0.0f;
  float smoothness_ = 0.0f;
  float spill_ = 0.0f;
  float extrude_ = 0.0f;
  float opacity_ = 1.0f;
  float gamma_ = 0.0f;
  float brightness_ = 0.0f;
  float contrast_ = 0.0f;
};

}  // namespace graphics