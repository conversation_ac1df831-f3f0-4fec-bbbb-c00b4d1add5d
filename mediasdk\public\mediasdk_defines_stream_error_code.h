#pragma once

#include <stdint.h>

#pragma pack(push, 1)

namespace mediasdk {

enum StreamErrorCode {
  kStreamSuccess = 0,
  kStreamFailed = 1,
  kStreamCreateVideoEncoderFailed = 2,
  kStreamCreateAudioEncoderFailed = 3,
  kStreamCreateServiceFailed = 4,
  kStreamConnectFailed = 5,
  kStreamServiceStartFailed = 6,
  kStreamServiceWriteError = 7,
  kStreamInvalidParams = 8,
  kStreamSourceInitializeFailed = 9,
  kStreamAlreadyConnect = 10,
  kStreamSocketConnectFailed = 11,
  kStreamShakeHandFailed = 12,
  kStreamPreprocessingAfterConnectedFailed = 13,
  kStreamInvalidUrl = 14,
  kStreamInvalidFilePath = 15,
  kStreamAlreadyStarted = 16,
  kStreamCreateFileFailed = 17,
  kStreamWriteAudioFailed = 18,
  kStreamWriteVideoFailed = 19,
  kStreamWriteFirstPacket = 20,
  kStreamSocketIPFailed = 21,
  kStreamSocketHostFailed = 22,
  kStreamSendFailedAfterEncoderReconfig = 23,
};

}  // namespace mediasdk

#pragma pack(pop)
