#include "yuv_graphics_impl.h"

#include <base/check.h>
#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include "framework.h"
#include "shader_manager.h"
#include "texture_color_convert_matrix_builder.h"
#include "texture_impl.h"
#include "yuv_graphics_shader.h"

namespace graphics {

auto FORMAT_MAP(DXGI_FORMAT format) {
  if (format == DXGI_FORMAT::DXGI_FORMAT_R8_UNORM) {
    return mediasdk::PixelFormat::kPixelFormatY8TEXTURE;
  } else if (format == DXGI_FORMAT_NV12) {
    return mediasdk::PixelFormat::kPixelFormatNV12TEXTURE;
  }
  return mediasdk::PixelFormat::kPixelFormatUnspecified;
};

YUVToBGRAGraphicsImpl::YUVToBGRAGraphicsImpl(Device& ins) : device_(ins) {}

bool YUVToBGRAGraphicsImpl::ConvertTextureToBGRA(
    std::shared_ptr<Texture> texture,
    mediasdk::ColorSpace cs,
    mediasdk::VideoRange cr) {
  if (texture->GetDesc().Format != DXGI_FORMAT::DXGI_FORMAT_NV12 &&
      texture->GetDesc().Format != DXGI_FORMAT::DXGI_FORMAT_R8_UNORM) {
    DCHECK(false);
    return false;
  }
  HRESULT hRes = S_OK;
  VIDEO_CONVERT_FORMAT info =
      GetStaticInfo(FORMAT_MAP(texture->GetDesc().Format));
  return DrawNV12ToTexture(*texture, cs, cr, info);
}

bool YUVToBGRAGraphicsImpl::ConvertMemoryToBGRAPrepare(
    const TextureFrame& frame) {
  if (frame.color_space == mediasdk::ColorSpace::kColorSpaceUnspecified ||
      frame.video_range == mediasdk::VideoRange::kVideoRangeUnspecified ||
      frame.width <= 0 || frame.height <= 0 ||
      frame.format == mediasdk::PixelFormat::kPixelFormatUnspecified) {
    DCHECK(false);
    return false;
  }
  VIDEO_CONVERT_FORMAT info = GetStaticInfo(frame.format);
  if (info.format != frame.format) {
    DCHECK(false);
    return false;
  }

  if (pre_frame_.width != frame.width || pre_frame_.height != frame.height ||
      pre_frame_.format != frame.format ||
      pre_frame_.color_space != frame.color_space ||
      pre_frame_.video_range != frame.video_range) {
    if (!TryCreateResource(frame, info)) {
      return false;
    }
  }
  new_ = true;
  return DrawFrameToTexture(frame, info);
}

bool YUVToBGRAGraphicsImpl::ConvertMemoryToBGRADraw() {
  if (!graphics_) {
    return false;
  }
  if (!new_)
    return true;
  auto shader = graphics_->GetDevice()
                    .GetShaderManager()
                    ->GetOrCreateShader<YUVGraphicsShader>(
                        graphics_->GetDevice().shared_from_this());
  if (!shader)
    return false;
  graphics_->GetDevice().AllowBlend(false);

  bool ret = graphics_->BeginDraw(false);
  if (!ret) {
    return false;
  }
  graphics::ScopedEndDraw end_draw(*graphics_);

  ID3D11ShaderResourceView* views[kMaxVideoPlanes] = {};
  for (UINT32 i = 0; i < kMaxVideoPlanes; i++)
    views[i] = tex_for_map_[i] ? tex_for_map_[i]->GetSRV() : nullptr;

  shader->Render(pre_frame_.format, vs_buffer_, ps_buffer_, views);
  new_ = false;
  return true;
}

inline void CopyToGPUMap(const TextureFrame& frame,
                         int plane,
                         int cy,
                         D3D11_MAPPED_SUBRESOURCE& ms) {
  uint8_t* ps = static_cast<uint8_t*>(ms.pData);
  VideoPlanerCopy(ps, ms.RowPitch, cy, frame.data[plane],
                  frame.line_size[plane], cy);
  return;
}

bool YUVToBGRAGraphicsImpl::MapYUVToTexture(const TextureFrame& frame,
                                            const VIDEO_CONVERT_FORMAT& info) {
  // other format
  // frame planner count equal to texForMap count
  // loop and copy

  HRESULT hRes = S_OK;
  for (int i = 0; i < tex_for_map_.size(); i++) {
    auto& texture = tex_for_map_[i];
    if (!texture)
      break;

    D3D11_MAPPED_SUBRESOURCE ms = {};
    if (!texture->Map(ms, false)) {
      return false;
    }

    CopyToGPUMap(frame, i, frame.height / info.planes_desc[i].sub_y, ms);

    texture->UnMap();
  }
  return true;
}

bool YUVToBGRAGraphicsImpl::DrawYUVTextureToBGRA(
    const TextureFrame& frame,
    const VIDEO_CONVERT_FORMAT& info) {
  bool build = false;
  if (pre_frame_.video_range != frame.video_range ||
      pre_frame_.color_space != frame.color_space ||
      pre_frame_.width != frame.width || pre_frame_.height != frame.height ||
      pre_frame_.format != frame.format) {
    const_buffer_v_ = {
        static_cast<float>(frame.width),
        static_cast<float>(frame.height),
    };
    const_buffer_ps_ = {};

    BuildPlanesYUVToBGRA(frame.color_space, frame.video_range,
                         const_buffer_ps_.Y, const_buffer_ps_.U,
                         const_buffer_ps_.V, const_buffer_ps_.min_range,
                         const_buffer_ps_.max_range, info.format);

    pre_frame_.video_range = frame.video_range;
    pre_frame_.color_space = frame.color_space;
    pre_frame_.height = frame.height;
    pre_frame_.width = frame.width;
    pre_frame_.format = frame.format;

    D3D11_MAPPED_SUBRESOURCE map = {};
    HRESULT hRes = S_FALSE;
    if (SUCCEEDED(hRes = GetContext()->Map(vs_buffer_.Get(), 0,
                                           D3D11_MAP_WRITE_DISCARD, 0, &map))) {
      auto dataPtr = (YUVGraphicsShader::VS_BUFFER*)map.pData;
      memcpy_s(dataPtr, sizeof(YUVGraphicsShader::VS_BUFFER), &const_buffer_v_,
               sizeof(YUVGraphicsShader::VS_BUFFER));
      GetContext()->Unmap(vs_buffer_.Get(), 0);
    } else {
      DCHECK(false);
      LOG(ERROR) << "Failed to map[" << GetErrorString(hRes) << "]";
      return false;
    }

    if (SUCCEEDED(hRes = GetContext()->Map(ps_buffer_.Get(), 0,
                                           D3D11_MAP_WRITE_DISCARD, 0, &map))) {
      auto dataPtr = (YUVGraphicsShader::PS_BUFFER*)map.pData;
      memcpy_s(dataPtr, sizeof(YUVGraphicsShader::PS_BUFFER), &const_buffer_ps_,
               sizeof(YUVGraphicsShader::PS_BUFFER));
      GetContext()->Unmap(ps_buffer_.Get(), 0);

    } else {
      DCHECK(false);
      LOG(ERROR) << "Failed to map[" << GetErrorString(hRes) << "]";
      return false;
    }

    build = true;
  }

  return true;
}

bool YUVToBGRAGraphicsImpl::DrawNV12TextureToBGRA(
    const Texture& texture,
    mediasdk::ColorSpace cs,
    mediasdk::VideoRange vr,
    const VIDEO_CONVERT_FORMAT& info) {
  bool build = false;
  if (pre_frame_.video_range != vr || pre_frame_.color_space != cs ||
      pre_frame_.width != texture.GetDesc().Width ||
      pre_frame_.height != texture.GetDesc().Height) {
    const_buffer_v_ = {
        static_cast<float>(pre_frame_.width),
        static_cast<float>(pre_frame_.height),
    };
    const_buffer_ps_ = {};
    BuildPlanesYUVToBGRA(cs, vr, const_buffer_ps_.Y, const_buffer_ps_.U,
                         const_buffer_ps_.V, const_buffer_ps_.min_range,
                         const_buffer_ps_.max_range, info.format);

    pre_frame_.video_range = vr;
    pre_frame_.color_space = cs;
    pre_frame_.width = texture.GetDesc().Width;
    pre_frame_.height = texture.GetDesc().Height;
    D3D11_MAPPED_SUBRESOURCE map = {};
    HRESULT res = S_OK;
    if (SUCCEEDED(res = GetContext()->Map(ps_buffer_.Get(), 0,
                                          D3D11_MAP_WRITE_DISCARD, 0, &map))) {
      auto dataPtr = (YUVGraphicsShader::PS_BUFFER*)map.pData;
      memcpy_s(dataPtr, sizeof(YUVGraphicsShader::PS_BUFFER), &const_buffer_ps_,
               sizeof(YUVGraphicsShader::PS_BUFFER));
      GetContext()->Unmap(ps_buffer_.Get(), 0);

    } else {
      DCHECK(false);
      LOG(ERROR) << "Failed to map[" << GetErrorString(res) << "]";
      return false;
    }
    build = true;
  }
  auto shader =
      device_.GetShaderManager()->GetOrCreateShader<YUVGraphicsShader>(
          graphics_->GetDevice().shared_from_this());
  if (!shader)
    return false;
  graphics_->GetDevice().AllowBlend(false);
  if (!graphics_->BeginDraw(false)) {
    return false;
  }
  graphics::ScopedEndDraw end_draw(*graphics_);
  ID3D11ShaderResourceView* views[kMaxVideoPlanes] = {};
  for (UINT32 i = 0; i < kMaxVideoPlanes; i++)
    views[i] = texture.GetSRV(i) ? texture.GetSRV(i) : nullptr;

  shader->Render(FORMAT_MAP(texture.GetDesc().Format), vs_buffer_, ps_buffer_,
                 views);

  return true;
}

// load CPU memory to GPU texture && convert to BGRA
bool YUVToBGRAGraphicsImpl::DrawFrameToTexture(
    const TextureFrame& frame,
    const VIDEO_CONVERT_FORMAT& info) {
  if (!MapYUVToTexture(frame, info)) {
    return false;
  }
  return DrawYUVTextureToBGRA(frame, info);
}

bool YUVToBGRAGraphicsImpl::DrawNV12ToTexture(
    const Texture& texture,
    mediasdk::ColorSpace cs,
    mediasdk::VideoRange cr,
    const VIDEO_CONVERT_FORMAT& info) {
  if (!graphics_) {
    graphics_ = CreateGraphics2D(device_);
  }
  if (!graphics_)
    return false;
  if (graphics_->IsEmpty()) {
    if (!graphics_->CreateNewBGRAGraphics(texture.GetSize().x,
                                          texture.GetSize().y)) {
      return false;
    }
    if (!TryCreatePSBuffer()) {
      return false;
    }
  }

  if (graphics_->IsEmpty()) {
    return false;
  }
  return DrawNV12TextureToBGRA(texture, cs, cr, info);
}

// prepare GPU resource
bool YUVToBGRAGraphicsImpl::TryCreateResource(
    const TextureFrame& frame,
    const VIDEO_CONVERT_FORMAT& info) {
  HRESULT hRes = S_OK;
  Destroy();
  if (!graphics_) {
    graphics_ = CreateGraphics2D(device_);
  }
  if (graphics_->IsEmpty()) {
    if (!graphics_->CreateNewBGRAGraphics(frame.width, frame.height)) {
      return false;
    }
  }

  if (graphics_->IsEmpty()) {
    return false;
  }

  for (int i = 0; i < kMaxVideoPlanes && i < info.planes; i++) {
    if (info.planes_desc[i].format == DXGI_FORMAT::DXGI_FORMAT_UNKNOWN) {
      DCHECK(false);
      return false;
    }
    if (info.planes_desc[i].mul) {
      tex_for_map_[i] = CreateTexture2D(
          graphics_->GetDevice(), frame.width * info.planes_desc[i].mul,
          frame.height, info.planes_desc[i].format,
          Texture::TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_WRITE |
              Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE,
          "yuv_graphics_map");
    } else {
      tex_for_map_[i] = CreateTexture2D(
          graphics_->GetDevice(), frame.width / info.planes_desc[i].sub_x,
          frame.height / info.planes_desc[i].sub_y, info.planes_desc[i].format,
          Texture::TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_WRITE |
              Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE,
          "yuv_graphics_map");
    }
    if (!tex_for_map_[i])
      return false;
  }
  D3D11_BUFFER_DESC bufferDesc = {};
  bufferDesc.Usage = D3D11_USAGE_DYNAMIC;
  bufferDesc.ByteWidth = sizeof(YUVGraphicsShader::VS_BUFFER);
  bufferDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
  bufferDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  hRes = GetDevice()->CreateBuffer(&bufferDesc, NULL, &vs_buffer_);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  if (!TryCreatePSBuffer())
    return false;
  return true;
}

bool YUVToBGRAGraphicsImpl::TryCreatePSBuffer() {
  D3D11_BUFFER_DESC desc = {};
  desc.Usage = D3D11_USAGE_DYNAMIC;
  desc.ByteWidth = sizeof(YUVGraphicsShader::PS_BUFFER);
  desc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
  desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  auto hRes = GetDevice()->CreateBuffer(&desc, NULL, &ps_buffer_);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  return true;
}

std::shared_ptr<Texture> YUVToBGRAGraphicsImpl::GetOutputTexture() {
  if (graphics_) {
    return graphics_->GetOutputTexture();
  }
  return nullptr;
}

void YUVToBGRAGraphicsImpl::Destroy() {
  if (graphics_) {
    graphics_->Destroy();
    graphics_ = nullptr;
  }

  for (auto& tex : tex_for_map_) {
    if (tex) {
      tex->Destroy();
      tex = nullptr;
    }
  }
  if (vs_buffer_) {
    vs_buffer_.Reset();
  }
  if (ps_buffer_) {
    ps_buffer_.Reset();
  }
}

YUVToBGRAGraphicsImpl::~YUVToBGRAGraphicsImpl() {
  YUVToBGRAGraphicsImpl::Destroy();
}

Microsoft::WRL::ComPtr<ID3D11Device> YUVToBGRAGraphicsImpl::GetDevice() {
  return graphics_->GetDevice().GetDevice();
}

Microsoft::WRL::ComPtr<ID3D11DeviceContext>
YUVToBGRAGraphicsImpl::GetContext() {
  return graphics_->GetDevice().GetContext();
}
}  // namespace graphics