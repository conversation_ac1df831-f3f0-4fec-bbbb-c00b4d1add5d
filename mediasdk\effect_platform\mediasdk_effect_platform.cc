#include "mediasdk/public/effect_platform/mediasdk_effect_platform.h"
#include "mediasdk/component_center.h"
#include "mediasdk/effect_platform/effect_platform_controller.h"

namespace mediasdk::ep {

bool UseFinder(Finder* finder) {
  if (!finder) {
    return false;
  }
  auto ctrl = com::GetComponent<EffectPlatformController>();
  if (!ctrl) {
    return false;
  }
  return ctrl->UseFinder(*finder);
}

bool IsEffectLibLoaded() {
  auto ctrl = com::GetComponent<EffectPlatformController>();
  if (!ctrl) {
    return false;
  }
  return ctrl->IsLoaded();
}

}  // namespace mediasdk::ep
