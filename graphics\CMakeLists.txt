cmake_minimum_required(VERSION 3.20)

set(PROJECT_NAME graphics)
file(GLOB SOURCES
        "*.h"
        "*.cpp"
        "*.cc"
        "third_party/WICTextureLoader/*.h"
        "third_party/WICTextureLoader/*.cpp"
        "third_party/WICTextureSaveAS/*.h"
        "third_party/WICTextureSaveAS/*.cpp"
)

add_library(${PROJECT_NAME} SHARED ${SOURCES})

target_link_libraries(${PROJECT_NAME}
        PUBLIC
        base
        nlohmann
        gdiplus
        DXGI.lib
        D3D11.lib
        D3DCompiler.lib
        dxguid.lib
        SetupAPI.lib
        directx_helper
)
target_link_libraries(${PROJECT_NAME} PRIVATE dcomp)

target_compile_definitions(${PROJECT_NAME} PRIVATE GRAPHICS_IMPLEMENTATION)

target_include_directories(${PROJECT_NAME} PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})
mediasdk_export_target(${PROJECT_NAME})
