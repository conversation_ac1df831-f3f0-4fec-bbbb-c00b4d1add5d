#include "audio_track.h"

#include <mediasdk/audio/audio_input.h>
#include <mediasdk/component_center.h>
#include <mediasdk/debug_helper.h>
#include <mediasdk/notify_center.h>
#include <mediasdk/public/mediasdk_trace_event.h>
#include "mediasdk/utils/time_helper.h"

#include <mediasdk/audio/audio_mixer_input.h>
#include <fstream>
#include "audio/audio_common.h"
#include "frame_convert.h"
#include "hook_api/hook_call.h"
#include "mediasdk/audio/aligned_audio_frame.h"
#include "mediasdk/audio/audio_frame_utils.h"

namespace {
constexpr int64_t kLogAudioPumpCnt = 120000;

}  // namespace

namespace mediasdk {

AudioTrack::AudioTrack(const uint32_t track_id) : track_id_(track_id) {}

AudioTrack::~AudioTrack() = default;

std::shared_ptr<AudioInput> AudioTrack::GetAudioInput(const std::string& id) {
  std::lock_guard<std::mutex> lock(lock_inputs_);
  auto it = inputs_.find(id);
  if (it != inputs_.end()) {
    if (!it->second)
      return nullptr;
    return it->second->audio_input;
  }
  return nullptr;
}

bool AudioTrack::AddAudioInput(std::shared_ptr<AudioInput> input) {
  std::lock_guard<std::mutex> lock(lock_inputs_);
  if (!input) {
    return false;
  }

  if (inputs_.count(input->GetId()) != 0) {
    LOG(WARNING) << "add a audio input already exist";
    return true;
  }

  TRACE_COST(
      trace::TypeToString(trace::TASK_COST_TYPE::CreateAudioInput_AddToTrack));

  inputs_[input->GetId()] = std::make_shared<AudioInputWithAudioMixerInput>(
      input, CreateAudioMixerInput());
  input->AddObserver(this->shared_from_this());
  return true;
}

bool AudioTrack::RemoveAudioInput(std::shared_ptr<AudioInput> input) {
  if (!input) {
    return false;
  }

  return RemoveAudioInput(input->GetId());
}

bool AudioTrack::RemoveAudioInput(const std::string& id) {
  std::lock_guard<std::mutex> lock(lock_inputs_);
  auto it = inputs_.find(id);
  if (it != inputs_.end()) {
    TRACE_COST(trace::TypeToString(
        trace::TASK_COST_TYPE::DestroyAudioInput_RemoveFromTrack));
    if (it->second && it->second->audio_input) {
      it->second->audio_input->RemoveObserver(this->shared_from_this());
    }
    inputs_.erase(it);
    return true;
  }
  return false;
}

void AudioTrack::SetTrackDealy(int64_t delay_ms) {
  delay_ms_ = delay_ms;
}

int64_t AudioTrack::GetTrackDelay() const {
  return delay_ms_;
}

AudioTrack::AudioInputsType& AudioTrack::GetAudioInputs() {
  return inputs_;
}

void AudioTrack::OnAudioPump(const mediasdk::TimeDuration& target,
                             const bool have_observer,
                             AudioTrackMixedFrameObserver* observer) {
#ifdef DEBUG_AUDIO_MIX
  std::ifstream fread(std::to_string(track_id_) + "_delay_conf.ini");
  std::string delay;
  if (fread >> delay) {
    auto new_delay = std::atoi(delay.c_str());
    if (new_delay != delay_ms_) {
      LOG(INFO) << "audio track [" << track_id_ << "] change to [" << new_delay;
      delay_ms_ = new_delay;
    }
  }
#endif

  AUDIO_MIX_TRACE_DURATION(
      std::to_string(track_id_) +
          trace::TypeToString(trace::AUDIO_DURATION_TYPE::MIX_START),
      target.GetBeginTimeStampNS(), target.GetNSDuration());
  if (have_observer_ != have_observer) {
    LOG(INFO) << "track [" << track_id_ << "] have observer from ["
              << have_observer_ << "] to [" << have_observer << "]";
    have_observer_ = have_observer;
  }
  if (!have_observer) {
    pump_cnt_ = 0;
  }
  if (!pump_frame_) {
    pump_frame_ =
        CreateAlignedAudioFrame(kAudioOutputFormat, target.GetSampleCnt());
  }

  pending_mixer_task_.push_back(target);
  while (1) {
    // loop until there is no mix task or frame is not ready
    if (pending_mixer_task_.empty())
      break;
    if (have_observer) {
      auto begin = pending_mixer_task_.begin();
      auto now = mediasdk::nano_now();

      if (begin->GetBeginTimeStampNS() + delay_ms_ * 1000000ll > now) {
        break;
      }
      if (!IsAllFramesReady(*begin)) {
        break;
      }
      if (!ConsumeAllMixerTask(*begin, observer)) {
        break;
      }
    }
    pending_mixer_task_.pop_front();
  }
#ifdef DEBUG_AUDIO_MIX
  if (last_pending_mixer_task_size_ != pending_mixer_task_.size()) {
    last_pending_mixer_task_size_ = pending_mixer_task_.size();
    LOG(INFO) << "audio track [" << track_id_ << "] left task ["
              << pending_mixer_task_.size() << "] ms ["
              << pending_mixer_task_.size() * target.GetNSDuration() / 1000000ll
              << "]";
  }

#endif
}

bool AudioTrack::ConsumeAllMixerTask(const TimeDuration& target,
                                     AudioTrackMixedFrameObserver* observer) {
  auto& input_list = GetAudioInputs();

  bool suc = false;
  bool first = true;
  for (int i = 0; i < kAudioOutputFormat.GetPlane(); i++) {
    AudioZero((float*)pump_frame_->GetData(i), pump_frame_->GetCount());
  }
  for (auto& input : input_list) {
    if (!input.second->audio_mixer_input || !input.second->audio_input) {
      DCHECK(false);
      continue;
    }
    auto frame = input.second->audio_mixer_input->GetAudioFrameDuration(
        input.second->audio_input->GetId(), target);
    if (!frame) {
      continue;
    }

    if (!frame->duration.GetSampleCnt())
      continue;

    int index = 0;
    DCHECK(frame->duration.GetBeginTimeStampNS() >=
           target.GetBeginTimeStampNS());
    if (frame->duration.GetBeginTimeStampNS() < target.GetBeginTimeStampNS()) {
      index = NSDurationToAudioSamples(
          kAudioSampleRate,
          target.GetBeginTimeStampNS() - frame->duration.GetBeginTimeStampNS());
    }
    if (first) {
      first = false;
      for (int i = 0; i < kAudioOutputFormat.GetPlane(); i++) {
        DCHECK(frame->frame.GetData(i));

        std::memcpy((float*)pump_frame_->GetData(i) + index,
                    reinterpret_cast<float*>(frame->frame.GetData(i)) + index,
                    sizeof(float) * pump_frame_->GetCount() - index);
      }
      suc = true;
    } else {
      for (int i = 0; i < kAudioOutputFormat.GetPlane(); i++) {
        DCHECK(frame->frame.GetData(i));

        AudioAdd((float*)pump_frame_->GetData(i) + index,
                 reinterpret_cast<float*>(frame->frame.GetData(i)) + index,
                 pump_frame_->GetCount() - index);
      }
      suc = true;
    }
  }

  if (suc) {
    AUDIO_MIX_TRACE_DURATION(
        std::to_string(track_id_) +
            trace::TypeToString(trace::AUDIO_DURATION_TYPE::MIX_SUCCESS),
        target.GetBeginTimeStampNS(), target.GetNSDuration());
  } else {
    AUDIO_MIX_TRACE_DURATION(
        std::to_string(track_id_) +
            trace::TypeToString(trace::AUDIO_DURATION_TYPE::MIX_EMPTY),
        target.GetBeginTimeStampNS(), target.GetNSDuration());
  }

  for (int i = 0; i < kAudioOutputFormat.GetPlane(); i++) {
    LimitAudioMinMax((float*)pump_frame_->GetData(i), pump_frame_->GetCount());
  }

  AudioFrameData frame_data;
  frame_data.block_size = kAudioOutputFormat.GetBlockSize();
  frame_data.count = pump_frame_->GetCount();
  frame_data.channel_count = kAudioOutputFormat.GetChannel();

  for (int i = 0; i < kAudioOutputFormat.GetPlane(); i++) {
    frame_data.buffer[i] = pump_frame_->GetData(i);
  }
  AudioFrame frame;
  frame.SetCaptureTimeStampNS(target.GetBeginTimeStampNS());
  frame.SetSampleRate(kAudioOutputFormat.GetSampleRate());
  frame.SetData(frame_data);

  Notify(frame, observer);
  return true;
}

namespace {
constexpr base::TimeDelta kSourceNotUpdatedRecent1Seconds = base::Seconds(1);
}  // namespace

bool AudioTrack::IsAllFramesReady(const TimeDuration& target) {
  const auto now = mediasdk::nano_now();
  auto& input_list = GetAudioInputs();
  if (input_list.empty())
    return true;
  for (auto& input_it : input_list) {
    auto& input = input_it.second;
    if (!input) {
      DCHECK(false);
      continue;
    }
    AUDIO_MIX_TRACE_DURATION(
        std::to_string(track_id_) + "_" + input->audio_input->GetId() + "_" +
            std::to_string(0) +
            trace::TypeToString(trace::AUDIO_DURATION_TYPE::TRY_PULL_FROM),
        target.GetBeginTimeStampNS(), target.GetNSDuration());
    if (!input->audio_mixer_input->CheckCanConsumeInputBuffer(
            input->audio_input->GetId(), target)) {
      const auto last_update_ts = input->audio_mixer_input->GetLastUpdateNS();
      if (last_update_ts) {
        if (now > last_update_ts) {
          if (now > last_update_ts +
                        kSourceNotUpdatedRecent1Seconds.InNanoseconds()) {
#ifdef DEBUG_AUDIO_MIX
            LOG(WARNING) << "SourceNotUpdatedRecent1Seconds ["
                         << input->audio_input->GetId() << "] now [" << now
                         << "] [" << last_update_ts << "] diff ["
                         << now - last_update_ts << "]";
#endif
            // mixer not update more than 1 second, just skip
            continue;
          } else {
            // mixer not update in 1 second, wait for a while
            return false;
          }
        }
      }
    }
  }
  return true;
}

std::string AudioTrack::ToString() {
  std::string ret = "[";
  for (auto& it : inputs_) {
    if (!it.second) {
      continue;
    }
    if (!it.second->audio_input) {
      continue;
    }
    ret += "\t" + it.second->audio_input->ToString() + "\n";
  }
  ret += "]";
  return ret;
}

void AudioTrack::OnProcessedAudioFrame(const std::string& input_id,
                                       const AudioFrame& frame,
                                       const AudioFormat& format) {
  if (!have_observer_) {
    return;
  }
  std::lock_guard<std::mutex> lock(lock_inputs_);
  auto it = inputs_.find(input_id);
  if (it == inputs_.end()) {
    LOG(WARNING) << "audio input not found";
    DCHECK(false);
    return;
  }
  if (!it->second->audio_input) {
    LOG(WARNING) << "audio input not found";
    DCHECK(false);
    return;
  }
  if (!it->second->audio_mixer_input) {
    LOG(WARNING) << "audio mixer input not found";
    DCHECK(false);
    return;
  }
  it->second->audio_mixer_input->OnData(
      input_id, frame, it->second->audio_input->GetSyncOffset() * 1000000ll);
}

void NotifyAudioRMS(uint32_t track_id, AudioVolume& volume_calculate) {
  auto& peak_data = volume_calculate.GetRMS();
  auto* nc = com::GetNotifyCenter();
  auto now = low_precision_milli_now();
  if (!nc)
    return;
  nc->AudioEvent()->Notify(FROM_HERE,
                           &MediaSDKAudioStatusObserver::OnAudioTrackPeak,
                           track_id, peak_data.at(0), peak_data.at(1));
}

void AudioTrack::Notify(const AudioFrame& frame,
                        AudioTrackMixedFrameObserver* observer) {
  if (volume_notify_.RMS(frame)) {
    NotifyAudioRMS(track_id_, volume_notify_);
  }

  HOOK_CALL_AUDIO(OnMixedAudioFrame, track_id_,
                  AudioFrameToAudioSourceFrame(frame));

  if (observer) {
    observer->OnMixedAudioFrame(track_id_, frame);
  }
  ++pump_cnt_;
  const auto now = mediasdk::low_precision_milli_now();

  if (!last_log_ts_) {
    last_log_ts_ = now;
  }
  if (now - last_log_ts_ > kLogAudioPumpCnt) {
    last_log_ts_ = now;
    LOG(INFO) << "audio track [" << track_id_ << "] pump cnt [" << pump_cnt_
              << "]";
  }
}

}  // namespace mediasdk
