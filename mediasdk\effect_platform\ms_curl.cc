#include "ms_curl.h"

#include "base/logging.h"

namespace mediasdk {

MSURL::MSURL() : m_curl(NULL) {
  static auto ret = curl_global_init(CURL_GLOBAL_ALL);
}

MSURL::~MSURL() {
  Close();
}

bool MSURL::Open() {
  m_curl = curl_easy_init();
  if (!m_curl) {
    LOG(ERROR) << "Failed to curl_easy_init";
    goto _ERROR;
  }
  return true;
_ERROR:
  Close();
  return false;
}

void MSURL::Close() {
  if (m_curl)
    curl_easy_cleanup(m_curl);
  m_curl = NULL;
}

bool MSURL::Get(const std::string& url, const std::string& field) {
  return Request(url, field, "GET");
}

bool MSURL::Post(const std::string& url, const std::string& field) {
  return Request(url, field, "POST");
}

void MSURL::SetHeaders(
    const std::unordered_map<std::string, std::string>& headers) {
  m_headerMap = headers;
}

void MSURL::SetContentType(const std::string& headers) {
  m_contentType = headers;
}

void MSURL::SetRedirect(bool redirect) {
  m_redirect = redirect;
}

void MSURL::SetHosts(const std::vector<std::string>& hosts) {
  m_hosts = hosts;
}

CURLcode MSURL::GetLastError() {
  return m_errCode;
}

std::vector<uint8_t> MSURL::GetResponse() {
  return m_response;
}

bool MSURL::Request(const std::string& url,
                    const std::string& field,
                    const std::string& method) {
  m_errCode = CURLE_FAILED_INIT;

  if (!m_curl)
    return false;
  m_response.resize(0);
  curl_easy_setopt(m_curl, CURLOPT_CUSTOMREQUEST, method.c_str());
  curl_easy_setopt(m_curl, CURLOPT_SSL_VERIFYPEER, false);
  curl_easy_setopt(m_curl, CURLOPT_URL, url.c_str());
  curl_easy_setopt(m_curl, CURLOPT_POSTFIELDS, field.c_str());
  curl_easy_setopt(m_curl, CURLOPT_WRITEFUNCTION, Callback);
  curl_easy_setopt(m_curl, CURLOPT_WRITEDATA, this);
  curl_easy_setopt(m_curl, CURLOPT_CONNECTTIMEOUT, 5);  // default 5s timeout
  curl_easy_setopt(m_curl, CURLOPT_TIMEOUT, 10);        // default 10s timeout

  struct curl_slist* headers = NULL;
  std::string serverDomain = GetDomain(url);
  if (!serverDomain.empty()) {
    headers = curl_slist_append(headers, ("host: " + serverDomain).c_str());
  }

  if (!m_contentType.empty()) {
    headers =
        curl_slist_append(headers, ("Content-Type: " + m_contentType).c_str());
  }

  for (auto it = m_headerMap.cbegin(); it != m_headerMap.cend(); it++) {
    headers =
        curl_slist_append(headers, (it->first + ":" + it->second).c_str());
  }

  if (m_redirect) {
    curl_easy_setopt(m_curl, CURLOPT_FOLLOWLOCATION, 1);
  }

  if (headers) {
    curl_easy_setopt(m_curl, CURLOPT_HTTPHEADER, headers);
  }

  struct curl_slist* hosts = NULL;
  if (!m_hosts.empty()) {
    for (auto& host : m_hosts) {
      hosts = curl_slist_append(hosts, host.c_str());
    }
    curl_easy_setopt(m_curl, CURLOPT_RESOLVE, hosts);
  }

  m_errCode = curl_easy_perform(m_curl);
  if (m_errCode != CURLE_OK) {
    LOG(ERROR) << "Failed to curl_easy_perform:" << m_errCode;
    return false;
  }
  return true;
}

int MSURL::GetErrorCode() {
  return m_errCode;
}

int MSURL::GetRecvPacketSize() {
  return m_response.size();
}

std::string MSURL::GetDomain(const std::string& uri) {
  std::string result;
  INT32 pos = uri.find("_domain_");
  if (pos != std::string::npos) {
    INT32 startPos = uri.find("=", pos);
    INT32 endPos = uri.find("&", pos);
    result = uri.substr(startPos + 1, endPos);
  }
  return result;
}

size_t MSURL::Callback(void* ptr,
                       size_t size,
                       size_t byteSize,
                       void* userdata) {
  MSURL* _this = static_cast<MSURL*>(userdata);
  size_t len = size * byteSize;
  uint8_t* data = reinterpret_cast<uint8_t*>(ptr);
  _this->m_response.insert(_this->m_response.end(), data, data + len);
  return len;
}
}  // namespace mediasdk
