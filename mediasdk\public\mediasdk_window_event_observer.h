#pragma once

#include "mediasdk_defines.h"

namespace mediasdk {

class MediaSDKWindowEventObserver {
 public:
  virtual void OnRButtonDown(int sink_id) = 0;

  virtual void OnRButtonUp(int sink_id) = 0;

  virtual void OnLButtonDblClk(int sink_id) = 0;

  virtual void OnLButtonDown(int sink_id) = 0;

  virtual void OnLButtonUp(int sink_id) = 0;

  virtual void OnMouseLeave(int sink_id) = 0;

  virtual void OnMouseHover(int sink_id) = 0;
};

}  // namespace mediasdk

