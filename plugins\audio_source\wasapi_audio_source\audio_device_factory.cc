#include "audio_device_factory.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <base/strings/utf_string_conversions.h>
#include "wasapi_audio_input_microphone_source.h"
#include "plugins/visual_source/dshow_visual_source/dshow_helper/dshow_helper.h"
#include "dshow_audio_only_capture.h"

namespace mediasdk {

// 根据设备信息创建音频输入源
std::shared_ptr<AudioInputSource> AudioDeviceFactory::CreateAudioInputSource(
    AudioInputProxy* proxy,
    const std::string& device_id,
    const nlohmann::json& json_params) {
  
  AudioDeviceType device_type = GetDeviceType(device_id);
  
  LOG(INFO) << base::StringPrintf(
      "Creating audio input source for device: %s, type: %s",
      device_id.c_str(),
      (device_type == AudioDeviceType::WASAPI_DEVICE) ? "WASAPI" : "DirectShow");

  if (device_type == AudioDeviceType::WASAPI_DEVICE) {
    return CreateWASAPIAudioInputSource(proxy, json_params);
  } else {
    return CreateDirectShowAudioInputSource(proxy, device_id, json_params);
  }
}

// 检查设备是否为 DirectShow 设备
bool AudioDeviceFactory::IsDirectShowDevice(const std::string& device_id) {
  return GetDeviceType(device_id) == AudioDeviceType::DSHOW_DEVICE;
}

// 从设备 ID 获取设备类型
AudioDeviceType AudioDeviceFactory::GetDeviceType(const std::string& device_id) {
  // 获取合并的设备列表，查找设备类型
  std::list<WASAPIDevice::DeviceName> merged_devices = WASAPIDevice::GetMergedInputDevices();
  
  std::wstring wide_device_id = base::UTF8ToWide(device_id);
  
  for (const auto& device : merged_devices) {
    if (device.id == wide_device_id) {
      return device.device_type;
    }
  }
  
  // 如果在输入设备中没找到，检查输出设备
  std::list<WASAPIDevice::DeviceName> output_devices = WASAPIDevice::GetMergedOutputDevices();
  for (const auto& device : output_devices) {
    if (device.id == wide_device_id) {
      return device.device_type;
    }
  }
  
  // 默认返回 WASAPI 设备类型
  LOG(WARNING) << base::StringPrintf(
      "Device type not found for device_id: %s, defaulting to WASAPI",
      device_id.c_str());
  return AudioDeviceType::WASAPI_DEVICE;
}

// 创建 WASAPI 音频输入源
std::shared_ptr<AudioInputSource> AudioDeviceFactory::CreateWASAPIAudioInputSource(
    AudioInputProxy* proxy,
    const nlohmann::json& json_params) {
  
  auto wasapi_source = std::make_shared<WASApiAudioInputMicrophoneSource>(proxy);
  if (wasapi_source && wasapi_source->Create(json_params)) {
    LOG(INFO) << "WASAPI audio input source created successfully";
    return wasapi_source;
  } else {
    LOG(ERROR) << "Failed to create WASAPI audio input source";
    return nullptr;
  }
}

// 创建 DirectShow 音频输入源
std::shared_ptr<AudioInputSource> AudioDeviceFactory::CreateDirectShowAudioInputSource(
    AudioInputProxy* proxy,
    const std::string& device_id,
    const nlohmann::json& json_params) {
  
  auto dshow_source = std::make_shared<DirectShowAudioInputSource>(proxy);
  if (dshow_source && dshow_source->Create(device_id, json_params)) {
    LOG(INFO) << "DirectShow audio input source created successfully";
    return dshow_source;
  } else {
    LOG(ERROR) << "Failed to create DirectShow audio input source";
    return nullptr;
  }
}

// DirectShowAudioInputSource 实现
DirectShowAudioInputSource::DirectShowAudioInputSource(AudioInputProxy* proxy)
    : proxy_(proxy), is_created_(false), is_muted_(false), volume_(1.0f) {
}

DirectShowAudioInputSource::~DirectShowAudioInputSource() {
  Destroy();
}

bool DirectShowAudioInputSource::Create(const std::string& device_id, const nlohmann::json& json_params) {
  if (is_created_) {
    LOG(WARNING) << "DirectShow audio input source already created";
    return true;
  }

  device_id_ = device_id;

  // 创建纯音频捕获实例
  dshow_audio_capture_ = CreateDShowAudioOnlyCapture(proxy_);
  if (!dshow_audio_capture_) {
    LOG(ERROR) << "Failed to create DShow audio only capture";
    return false;
  }

  // 创建音频捕获
  if (!dshow_audio_capture_->Create(device_id, json_params)) {
    LOG(ERROR) << "Failed to create DirectShow audio capture for device: " << device_id;
    dshow_audio_capture_.reset();
    return false;
  }

  // 启动音频捕获
  if (!dshow_audio_capture_->Start()) {
    LOG(ERROR) << "Failed to start DirectShow audio capture";
    dshow_audio_capture_.reset();
    return false;
  }

  is_created_ = true;
  LOG(INFO) << "DirectShow audio input source created and started for device: " << device_id;

  return true;
}

void DirectShowAudioInputSource::Destroy() {
  if (!is_created_) {
    return;
  }

  // 停止并销毁 DirectShow 音频捕获
  if (dshow_audio_capture_) {
    dshow_audio_capture_->Stop();
    dshow_audio_capture_->Destroy();
    dshow_audio_capture_.reset();
  }

  is_created_ = false;
  LOG(INFO) << "DirectShow audio input source destroyed";
}

MediaSDKString DirectShowAudioInputSource::GetProperty(const char* key) {
  // TODO: 实现属性获取
  return MediaSDKString();
}

const char* DirectShowAudioInputSource::GetAudioSourceName() {
  return "DirectShowAudioInputSource";
}

bool DirectShowAudioInputSource::Action(const char* json_params) {
  // TODO: 实现动作处理
  return true;
}

void DirectShowAudioInputSource::SetDeviceVolume(float volume) {
  volume_ = volume;
  // TODO: 实现音量设置
}

float DirectShowAudioInputSource::GetDeviceVolume() {
  return volume_;
}

void DirectShowAudioInputSource::SetDeviceMute(bool mute) {
  is_muted_ = mute;
  // TODO: 实现静音设置
}

bool DirectShowAudioInputSource::GetDeviceMute() {
  return is_muted_;
}

void DirectShowAudioInputSource::SetDeviceUsedQPC(bool used) {
  // TODO: 实现 QPC 设置
}

bool DirectShowAudioInputSource::GetDeviceUsedQPC() {
  // TODO: 实现 QPC 获取
  return false;
}

bool DirectShowAudioInputSource::NeedSyn() {
  return false;
}

}  // namespace mediasdk
