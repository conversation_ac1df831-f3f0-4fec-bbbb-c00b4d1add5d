#include "audio_pump.h"

#include <base/threading/thread.h>
#include <base/time/time.h>
#include <mediasdk/debug_helper.h>
#include <mediasdk/public/mediasdk_trace_event.h>
#include <mediasdk/random_hung_mock_center.h>
#include <mediasdk/utils/time_helper.h>
#include "audio/audio_common.h"

namespace mediasdk {

AudioPump::AudioPump(std::shared_ptr<AudioInputManager> audio_input_manager)
    : audio_input_manager_(audio_input_manager) {}

void AudioPump::Start(uint32_t sample_unit) {
  sample_unit_ = sample_unit;
  if (IsRunning()) {
    return;
  }

  UpdateFps(sample_unit);
  stop_ = false;

  if (!timing_thread_) {
    timing_thread_ = std::make_shared<base::Thread>("audio_timing_thread");
    timing_thread_->Start();
    timing_thread_->task_runner()->PostTask(
        FROM_HERE,
        base::BindOnce(&AudioPump::Timing<PERSON>oop, base::Unretained(this)));
  }
}

void AudioPump::UpdateFps(uint32_t sample_unit) {
  frame_interval_ns_ = base::Seconds(1).InNanoseconds() * sample_unit /
                       mediasdk::kAudioSampleRate;
}

void AudioPump::Stop() {
  stop_ = true;
  render_done_event_.Signal();
  if (timing_thread_) {
    timing_thread_->Stop();
    timing_thread_.reset();
  }
  audio_input_manager_.reset();
}

bool AudioPump::IsRunning() {
  return !stop_;
}

void AudioPump::AddObserver(Observer* observer) {
  observer_list_.AddObserver(observer);
}

void AudioPump::RemoveObserver(Observer* observer) {
  observer_list_.RemoveObserver(observer);
}

void AudioPump::Pump(const mediasdk::TimeDuration& target) {
  TRACE_VALUE("AudioPumpTs", target.GetBeginTimeStampNS() / 1000000);

  TRACE_ACTION_DURATION(
      trace::TypeToString(trace::TASK_COST_TYPE::StreamPath_A_Frame));
  if (!audio_input_manager_)
    return;

  for (auto& observer : observer_list_) {
    observer.OnPump(target, audio_input_manager_);
  }
  render_done_event_.Signal();
}

void AudioPump::TimingLoop() {
  auto current_time = base::TimeTicks::Now() - base::TimeTicks();
  const auto begin_ns = nano_now();
  mediasdk::TimeDuration duration = {};

  duration.SetSampleCnt(sample_unit_);
  duration.SetTimeBase(mediasdk::kAudioSampleRate);
  auto get_begin = [begin_ns](uint64_t sample_cnt, int32_t sample_rate) {
    return begin_ns + mediasdk::SamplesToNSDuration(sample_rate, sample_cnt);
  };
  uint64_t sample_cnt = 0;

  // loop every [1024*1000/48000]ms
  while (!stop_) {
#ifdef ENABLE_RANDOM_HUNG_TEST
    mock_audio_mix_hung();
#endif  // ENABLE_RANDOM_HUNG_TEST

    while (!stop_) {
      if (stop_)
        break;

      duration.SetBeginTimeStampNS(get_begin(sample_cnt, kAudioSampleRate));

      duration.SetEndTimeStampNS(duration.GetBeginTimeStampNS() +
                                 frame_interval_ns_);

      PostToAudio(FROM_HERE, base::BindOnce(&AudioPump::Pump,
                                            base::Unretained(this), duration));

      render_done_event_.Wait();

      sample_cnt += sample_unit_;
      if (duration.GetEndTimeStampNS() >= nano_now())
        break;
    }

    if (stop_)
      break;
    SleepUntil(base::Nanoseconds(duration.GetEndTimeStampNS()));
  }
}

}  // namespace mediasdk
