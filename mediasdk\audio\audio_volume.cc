#include "audio_volume.h"

#include <mediasdk/audio/audio_frame_utils.h>
#include <mediasdk/utils/time_helper.h>
#include "public/mediasdk_defines.h"

namespace mediasdk {

AudioVolume::AudioVolume() : delta_ms_(100), last_(0) {}

AudioVolume::~AudioVolume() {}

const AudioVolume::ResultType& AudioVolume::GetPeak() {
  return peak_;
}

const AudioVolume::ResultType& AudioVolume::GetRMS() {
  return RMS_;
}

void AudioVolume::SetCalcDelta(int32_t interval) {
  delta_ms_ = interval;
}

int32_t AudioVolume::GetCalcDelta() const {
  return delta_ms_;
}

// https://github.com/webrtc/samples/blob/gh-pages/src/content/getusermedia/volume/js/soundmeter.js
bool AudioVolume::Peak(const mediasdk::AudioFrame& frame) {
  if (!CheckAudioFrame(frame)) {
    return false;
  }

  PeakForce(frame);
  return true;
}

void AudioVolume::PeakForce(const AudioFrame& apk) {
  for (int32_t i = 0; i < mediasdk::kMaxAudioPlanes; i++) {
    if (!apk.GetData(i)) {
      peak_[i] = 0.f;
      continue;
    }
    peak_[i] =
        AudioPeak(reinterpret_cast<float*>(apk.GetData(i)), apk.GetCount());
  }
}

// https://github.com/webrtc/samples/blob/gh-pages/src/content/getusermedia/volume/js/soundmeter.js
bool AudioVolume::RMS(const mediasdk::AudioFrame& frame) {
  if (!CheckAudioFrame(frame)) {
    return false;
  }
  RMSForce(frame);
  return true;
}

bool AudioVolume::RMS(const AudioFormat& format,
                      const mediasdk::AudioFrame& frame) {
  if (!CheckAudioFrame(frame)) {
    return false;
  }
  RMSForce(format, frame);
  return true;
}

void AudioVolume::RMSForce(const mediasdk::AudioFrame& frame) {
  for (int32_t i = 0; i < mediasdk::kMaxAudioPlanes; i++) {
    if (!frame.GetData(i)) {
      RMS_[i] = 0.f;
      continue;
    }
    float instant = 0.0;
    auto* samples = (const float*)frame.GetData(i);
    for (int32_t j = 0; j < frame.GetCount(); j++) {
      float sample = samples[j];
      instant += sample * sample;
    }
    RMS_[i] = sqrtf(instant / frame.GetCount());
  }
}

void AudioVolume::RMSForce(const AudioFormat& format,
                           const mediasdk::AudioFrame& frame) {
  if (format.GetFormat() == AUDIO_FORMAT_S16) {
    // lyrax audio format is s16, so we need to convert to float
    for (int32_t i = 0; i < mediasdk::kMaxAudioPlanes; i++) {
      RMS_[i] = 0.0f;
    }

    auto* samples = (const int16_t*)frame.GetData(0);
    auto channel = format.GetChannel();
    auto sample_count = frame.GetCount();
    for (int32_t i = 0; i < channel; i++) {
      float instant = 0.0f;
      for (int32_t j = 0; j < sample_count; j++) {
        float sample = samples[j * channel + i] / 32768.0f;
        instant += sample * sample;
      }
      RMS_[i] = sqrtf(instant / sample_count);
    }
  } else {
    RMSForce(frame);
  }
}

bool AudioVolume::CheckAudioFrame(const mediasdk::AudioFrame& frame) {
  if (frame.GetCount() <= 0)
    return false;
  const int64_t current_ts = milli_now();
  if (last_ == 0)
    last_ = current_ts;
  const int64_t ms = static_cast<int32_t>((current_ts - last_));
  // not every packet
  if (ms <= delta_ms_)
    return false;
  last_ = current_ts;

  return true;
}
}  // namespace mediasdk
