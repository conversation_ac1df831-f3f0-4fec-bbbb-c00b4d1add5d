#pragma once

#include "mediasdk/public/plugin/plugin_export.h"
#include "mediasdk/public/mediasdk_export.h"

namespace mediasdk::ep {

struct FinderObserver {};

class Finder {
 public:
  virtual ~Finder() {}

  virtual bool SetFinder(void* finder, void (*finder_release)(void*)) const = 0;
};

extern "C" MEDIASDK_EXPORT bool UseFinder(Finder* finder);
extern "C" MEDIASDK_EXPORT bool IsEffectLibLoaded();

}  // namespace mediasdk::ep