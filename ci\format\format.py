# !/usr/bin/python
# python3
# coding: utf-8

import os
import threading
import time
import platform

kMaxCmdLen = 8000

exclude_folders = [".git",
                   ".bits",
                   ".vscode",
                   ".build",
                   ".build64",
                   "build",
                   "build64",
                   "bins",
                   ".vs",
                   "CI",
                   "CMake",
                   "Release",
                   "Debug",
                   "base",
                   "dependencies",
                   "deps"]

file_extensions = [".h", ".cpp", ".hpp", ".cc", ".c", ".m", ".mm"]


def getSrcPath():
    cur_dir = os.path.dirname(os.path.abspath(__file__))
    return os.path.dirname(os.path.dirname(cur_dir))


def getToolPath():
    cur_dir = os.path.dirname(os.path.abspath(__file__))
    platform_id = platform.system()
    if platform_id == "Windows":
        return os.path.join(cur_dir, "win", "x64", "clang-format.exe")
    if platform_id == "OS X":
        pass


progress = 0
lock = threading.Lock()


def countProgress(cnt, total_file_cnt):
    global progress
    global lock

    lock.acquire()
    progress += cnt
    print("formating: {0} / {1}".format(progress, total_file_cnt))
    lock.release()


def isSourceCode(filepath):
    for file_extension in file_extensions:
        if filepath.endswith(file_extension):
            return True
    return False


def threadAction(cmd, cnt, total_file_cnt):
    os.system(cmd)
    countProgress(cnt, total_file_cnt)


def GenCmd(file_list):
    tool_path = getToolPath()
    cmd_init = "{0} -style=file -i".format(tool_path)

    cmd_list = []
    cmd = cmd_init
    file_cnt = 0

    for i in range(0, len(file_list)):
        new_file = " " + "\"" + file_list[i] + "\""
        if (len(cmd) + len(new_file) > kMaxCmdLen):
            cmd_list.append((cmd, file_cnt))
            cmd = cmd_init + new_file
            file_cnt = 1
        else:
            file_cnt += 1
            cmd = cmd + new_file

    if (file_cnt > 0):
        cmd_list.append((cmd, file_cnt))

    return cmd_list


if __name__ == '__main__':
    file_list = []
    # list all src file
    time_start = time.time()
    root = getSrcPath()
    for root, dirs, files in os.walk(root):
        for exclude_folder in exclude_folders:
            if exclude_folder in dirs:
                dirs.remove(exclude_folder)
        for file in files:
            filepath = os.path.join(root, file)
            if isSourceCode(filepath):
                file_list.append(filepath)

    total_file_cnt = len(file_list)
    print("total file count: {0}".format(total_file_cnt))

    # multi-threading action
    cmd_list = GenCmd(file_list)
    thread_list = []
    for cmd, file_cnt in cmd_list:
        t = threading.Thread(target=threadAction, args=(
            cmd, file_cnt, total_file_cnt))
        thread_list.append(t)
        t.start()

    for t in thread_list:
        t.join()
    print("format complete, time cost: {0} s".format(time.time() - time_start))
