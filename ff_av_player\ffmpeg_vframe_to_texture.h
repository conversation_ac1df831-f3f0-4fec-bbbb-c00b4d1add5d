#pragma once

extern "C" {
#include <libavutil/frame.h>
}

#include "ffmpeg_common.h"
#include "graphics/public/graphics_texture_frame.h"
#include "mediasdk/public/plugin/visual_proxy.h"

inline mediasdk::VideoRange fromFFVideoFormatWithDefault(
    AVColorRange range,
    mediasdk::VideoRange default_value) {
  auto ret = fromFFColorRange(range);
  if (ret == mediasdk::VideoRange::kVideoRangeUnspecified) {
    ret = default_value;
  }
  return ret;
}

inline mediasdk::ColorSpace fromFFColorSpaceWithDefault(
    AVColorSpace cs,
    mediasdk::ColorSpace default_value) {
  auto ret = fromFFColorSpace(cs);
  if (ret == mediasdk::ColorSpace::kColorSpaceUnspecified) {
    ret = default_value;
  }
  return ret;
}

inline bool FFFrameToSourceFrame(AVFrame& v_frame,
                                 mediasdk::VisualSourceFrame& frame) {
  DCHECK(v_frame.width && v_frame.height);
  frame.format = fromFFVideoFormat((AVPixelFormat)v_frame.format);
  // default part
  frame.video_range = fromFFVideoFormatWithDefault(
      v_frame.color_range, mediasdk::VideoRange::kVideoRangePartial);
  // default 709
  frame.color_space = fromFFColorSpaceWithDefault(
      v_frame.colorspace, mediasdk::ColorSpace::kColorSpaceBT709);

  frame.width = v_frame.width;
  frame.height = v_frame.height;

  for (int i = 0; i < mediasdk::kVisualSourceMaxVideoPlane; i++) {
    frame.line_size[i] = v_frame.linesize[i];
    frame.data[i] = v_frame.data[i];
  }
  return true;
}

inline bool FFFrameToTextureFrame(AVFrame& v_frame,
                                  graphics::TextureFrame& frame) {
  DCHECK(v_frame.width && v_frame.height);
  frame.format = fromFFVideoFormat((AVPixelFormat)v_frame.format);
  // default part
  frame.video_range = fromFFVideoFormatWithDefault(
      v_frame.color_range, mediasdk::VideoRange::kVideoRangePartial);
  // default 709
  frame.color_space = fromFFColorSpaceWithDefault(
      v_frame.colorspace, mediasdk::ColorSpace::kColorSpaceBT709);

  frame.width = v_frame.width;
  frame.height = v_frame.height;

  for (int i = 0; i < graphics::kMaxVideoPlanes; i++) {
    frame.line_size[i] = v_frame.linesize[i];
    frame.data[i] = v_frame.data[i];
  }
  return true;
}
