#pragma once
#include "texture.h"

namespace graphics {
class GRAPHICS_EXPORT YUVToBGRAGraphics {
 public:
  virtual bool ConvertMemoryToBGRAPrepare(const TextureFrame& frame) = 0;
  virtual bool ConvertMemoryToBGRADraw() = 0;
  virtual bool ConvertTextureToBGRA(std::shared_ptr<Texture>,
                                    mediasdk::ColorSpace cs,
                                    mediasdk::VideoRange cr) = 0;
  virtual std::shared_ptr<Texture> GetOutputTexture() = 0;
  virtual void Destroy() = 0;

  virtual ~YUVToBGRAGraphics(){};
};

GRAPHICS_EXPORT std::shared_ptr<YUVToBGRAGraphics> CreateYUVToBGRAGraphics(
    Device& ins);
}  // namespace graphics
