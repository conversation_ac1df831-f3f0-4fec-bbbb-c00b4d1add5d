#pragma once

#include <mediasdk/callback_utils.h>
#include <mediasdk/public/plugin/audio_filter.h>
#include <mediasdk/public/plugin/audio_filter_proxy.h>
#include <memory>
#include <shared_mutex>

namespace mediasdk {

class AudioInput;

class AudioFilterChain final : public AudioFilterProxy,
                               public base::SupportsWeakPtr<AudioFilterChain> {
 public:
  explicit AudioFilterChain(AudioInput* audio_input);

  ~AudioFilterChain() override;

  void CreateFilter(const std::string& filter_id,
                    const std::string& plugin_name,
                    const std::string& json_params,
                    MSCallbackBool callback);

  void DestroyFilter(const std::string& filter_id, MSCallbackBool callback);

  void SetFilterEnable(const std::string& filter_id,
                       bool enable,
                       MSCallbackBool callback);
  void IsFilterEnable(const std::string& filter_id,
                      MSCallback<ResultBoolBool> callback);

  void SetFilterProperty(const std::string& filter_id,
                         const std::string& key,
                         const std::string& json_params,
                         MSCallbackBool callback);

  void GetFilterProperty(const std::string& filter_id,
                         const std::string& key,
                         MSCallback<ResultBoolString> callback);

  void FilterAction(const std::string& filter_id,
                    const std::string& action,
                    const std::string& param,
                    MSCallback<ResultBoolString> callback);

  void ApplyFilter(AudioFrame* frame);

  void DestroyAllFilter();

 private:
  void OnAudioFilterCreated(MSCallbackBool callback,
                            const std::string& filter_id,
                            std::shared_ptr<AudioFilter> filter);
  void OnAudioFilterInited(MSCallbackBool callback,
                           const std::string& filter_id,
                           std::shared_ptr<AudioFilter> filter,
                           bool success);

  std::shared_ptr<AudioFilter> FindFilterByIdInner(
      const std::string& filter_id);

  struct FilterItem {
    std::string id;
    std::shared_ptr<AudioFilter> filter;
  };

  AudioInput* const audio_input_ = nullptr;

  mutable std::recursive_mutex filters_mutex_;
  std::vector<FilterItem> filters_;

  // AudioFilterProxy interface
 public:
  MediaSDKString GetFilterId(const AudioFilter* filter) const override;
  void SendNotify(const AudioFilter* filter, const char* event) override;
};

}  // namespace mediasdk
