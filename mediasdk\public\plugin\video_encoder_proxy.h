#pragma once

#include <cstdint>
#include "mediasdk/public/mediasdk_defines.h"
#include "mediasdk/public/mediasdk_defines_video.h"
#include "mediasdk/public/mediasdk_video_frame_type.h"

namespace mediasdk {

#pragma pack(push, 1)

struct VideoEncoderSourceNals {
  int nal_type;
  uint8_t* payload;
  int payload_size;
};

struct VideoEncoderSourcePacket {
  StreamType stream_type;
  VideoFrameType frame_type;
  int priority;
  bool is_key_frame;
  int64_t pts;
  int64_t dts;
  VideoEncoderSourceNals* nals;
  uint32_t nals_count;
  bool is_refferred;
};

#pragma pack(pop)

class VideoEncoderProxy {
 public:
  virtual ~VideoEncoderProxy() = default;

  virtual void OnEncodedData(const VideoEncoderSourcePacket& packet) = 0;

  virtual void OnAppendExtraData(const uint8_t* data, size_t size) = 0;

  virtual void OnResetExtraData(const uint8_t* data, size_t size) = 0;
};

}  // namespace mediasdk
