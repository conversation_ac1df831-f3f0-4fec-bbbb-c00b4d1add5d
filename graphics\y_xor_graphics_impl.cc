#include "y_xor_graphics_impl.h"

#include "base./logging.h"
#include "base/check.h"
#include "base/strings/stringprintf.h"
#include "public/graphics_defines.h"
#include "shader_manager.h"
#include "y_xor_shader.h"

namespace graphics {

YXORGraphicsImpl::YXORGraphicsImpl(Device& ins) : device_(ins) {}

bool YXORGraphicsImpl::XOR(std::shared_ptr<Texture> left,
                           std::shared_ptr<Texture> right) {
  if (!left || !right) {
    DCHECK(false);
    return false;
  }
  if (!TryCreateResource(*left))
    return false;
  if (!TryUpdateParam(*left)) {
    return false;
  }
  return DoXOR(*left, *right);
}

bool YXORGraphicsImpl::TryUpdateParam(const Texture& texutre) {
  return true;
}

// prepare GPU resource
bool YXORGraphicsImpl::TryCreateResource(const Texture& texture) {
  if (graphics_ && !graphics_->IsEmpty())
    return true;
  HRESULT hRes = S_OK;
  Destroy();
  if (!graphics_) {
    graphics_ = CreateGraphics2D(device_);
  }
  if (graphics_->IsEmpty()) {
    if (!graphics_->CreateNewY8Graphics(texture.GetSize().x,
                                        texture.GetSize().y)) {
      return false;
    }
  }

  if (graphics_->IsEmpty()) {
    return false;
  }

  return true;
}

bool YXORGraphicsImpl::DoXOR(Texture& left, Texture& right) {
  ID3D11ShaderResourceView* views[kMaxVideoPlanes] = {};
  views[0] = left.GetSRV(0);
  views[1] = right.GetSRV(0);

  auto shader =
      device_.GetShaderManager()->GetOrCreateShader<YXORShader>(
          graphics_->GetDevice().shared_from_this());
  if (!shader)
    return false;
  graphics_->GetDevice().AllowBlend(false);
  if (!graphics_->BeginDraw(false)) {
    return false;
  }
  graphics::ScopedEndDraw end_draw(*graphics_);
  shader->Render(views);

  return true;
}

std::shared_ptr<Texture> YXORGraphicsImpl::GetOutputTexture() {
  if (graphics_) {
    return graphics_->GetOutputTexture();
  }
  return nullptr;
}

void YXORGraphicsImpl::Destroy() {
  if (graphics_) {
    graphics_->Destroy();
    graphics_ = nullptr;
  }
}

YXORGraphicsImpl::~YXORGraphicsImpl() {
  YXORGraphicsImpl::Destroy();
}

Microsoft::WRL::ComPtr<ID3D11Device> YXORGraphicsImpl::GetDevice() {
  return graphics_->GetDevice().GetDevice();
}

Microsoft::WRL::ComPtr<ID3D11DeviceContext> YXORGraphicsImpl::GetContext() {
  return graphics_->GetDevice().GetContext();
}

}  // namespace graphics