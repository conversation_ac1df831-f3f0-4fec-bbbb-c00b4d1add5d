#include "yuv_color_shader.h"

using namespace Microsoft::WRL;

namespace graphics {

static const char* CONST_PIXEL_SHADER() {
  return R"(

cbuffer PS_BUFFER
{
   float4   Y;
   float4	U;
   float4	V;
   float4	min_v;
   float4	max_v;

   float    raw;
   float    A;
   float un_use_0;
   float un_use_1;
};

inline float3 YUV_2_RGB(float3 yuv)
{
	yuv = clamp(yuv, min_v.xyz, max_v.xyz);// limit range

    // yuv to rgb
	float r = dot(Y.xyz, yuv) + Y.w;
	float g = dot(U.xyz, yuv) + U.w;
	float b = dot(V.xyz, yuv) + V.w;

	return float3(r, g, b);
}

struct PSInput
{
    float4 position : SV_POSITION;
    float4 color : COLOR;
};

float4 PS_MAIN(PSInput input) : SV_TARGET
{
    return float4(YUV_2_RGB(float3(input.color.xyz)), 1.f);
}
)";
}

static const char* CONST_VERTEX_SHADER() {
  return R"(
cbuffer Matrix : register(b0)
{
	matrix world;
	matrix view;
	matrix projection;
};

struct VSInput
{
    float4 position : POSITION;
    float4 color : COLOR;
};

struct PSInput
{
    float4 position : SV_POSITION;
    float4 color : COLOR;
};

PSInput VS_MAIN(VSInput input)
{
	PSInput output; 

    input.position.w = 1.0f;

    output.position = mul(input.position, world);

    output.position = mul(output.position, view);

    output.position = mul(output.position, projection);

	output.color = input.color;

    return output;
}
)";
}

bool YUVColorShader::Init(const std::shared_ptr<Device>& ins) {
  if (!try_init_) {
    init_suc_ = Init_(ins);
    try_init_ = true;
  }

  return init_suc_;
}

bool YUVColorShader::Init_(const std::shared_ptr<Device>& ins) {
  device_ = ins;
  Device::CompileShaderParam param = {};
  param.ps = CONST_PIXEL_SHADER();
  param.vs = CONST_VERTEX_SHADER();
  param.ps_name = "PS_MAIN";
  param.vs_name = "VS_MAIN";
  D3D11_INPUT_ELEMENT_DESC layout[2];
  layout[0].SemanticName = "POSITION";
  layout[0].SemanticIndex = 0;
  layout[0].Format = DXGI_FORMAT_R32G32B32_FLOAT;
  layout[0].InputSlot = 0;
  layout[0].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
  layout[0].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
  layout[0].InstanceDataStepRate = 0;
  layout[1].SemanticName = "COLOR";
  layout[1].SemanticIndex = 0;
  layout[1].Format = DXGI_FORMAT_R32G32B32A32_FLOAT;
  layout[1].InputSlot = 0;
  layout[1].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
  layout[1].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
  layout[1].InstanceDataStepRate = 0;
  param.layout_descs_ = layout;
  param.layout_cnt_ = 2;
  if (!device_->CompileShader(param)) {
    return false;
  }
  vs_shader_ = param.vs_shader_;
  ps_shader_ = param.ps_shader_;

  layout_ = param.layout_;

  D3D11SetDebugObjectName(vs_shader_.Get(), "yuv_color_vs_buffer");

  D3D11SetDebugObjectName(ps_shader_.Get(), "yuv_color_ps_buffer");

  D3D11SetDebugObjectName(layout_.Get(), "yuv_color_layout");

  return true;
}

void YUVColorShader::Render(int vertex, int index) {
  auto context = GetContext_();
  context->IASetInputLayout(layout_.Get());
  context->VSSetShader(vs_shader_.Get(), NULL, 0);
  context->PSSetShader(ps_shader_.Get(), NULL, 0);
  if (index > 0) {
    context->DrawIndexed(index, 0, 0);
  } else {
    if (vertex > 0) {
      context->Draw(vertex, 0);
    }
  }
  return;
}

ComPtr<ID3D11Device> YUVColorShader::GetDevice_() {
  return device_->GetDevice();
}

ID3D11DeviceContext* YUVColorShader::GetContext_() {
  return device_->GetContext().Get();
}

void YUVColorShader::Destroy() {
  if (vs_shader_) {
    vs_shader_.Reset();
  }
  if (ps_shader_) {
    ps_shader_.Reset();
  }
  if (layout_) {
    layout_.Reset();
  }
}

YUVColorShader::~YUVColorShader() {
  YUVColorShader::Destroy();
}
}  // namespace graphics