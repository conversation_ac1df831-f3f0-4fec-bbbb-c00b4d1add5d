#include "color_shader.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include "DXSimpleMath.h"

namespace graphics {
static const char* CONST_PIXEL_SHADER() {
  return R"(
struct PSInput
{
    float4 position : SV_POSITION;
    float4 color : COLOR;
};

float4 PS_MAIN(PSInput input) : SV_TARGET
{
    return input.color;
}
)";
}

static const char* CONST_VERTEX_SHADER() {
  return R"(
cbuffer Matrix
{
	matrix world;
	matrix view;
	matrix projection;
};

struct VSInput
{
    float4 position : POSITION;
    float4 color : COLOR;
};

struct PSInput
{
    float4 position : SV_POSITION;
    float4 color : COLOR;
};

PSInput VS_MAIN(VSInput input)
{
	PSInput output; 

    input.position.w = 1.0f;

    output.position = mul(input.position, world);

    output.position = mul(output.position, view);

    output.position = mul(output.position, projection);

	output.color = input.color;  

    return output;
}
)";
}

bool ColorShader::Init(const std::shared_ptr<Device>& ins) {
  if (!try_init_) {
    init_suc_ = Init_(ins);
    try_init_ = true;
  }

  return init_suc_;
}

bool ColorShader::Init_(const std::shared_ptr<Device>& ins) {
  device_ = ins;
  context_ = device_->GetContext().Get();

  Device::CompileShaderParam param = {};
  param.ps = CONST_PIXEL_SHADER();
  param.vs = CONST_VERTEX_SHADER();
  param.ps_name = "PS_MAIN";
  param.vs_name = "VS_MAIN";
  D3D11_INPUT_ELEMENT_DESC layout[2];
  layout[0].SemanticName = "POSITION";
  layout[0].SemanticIndex = 0;
  layout[0].Format = DXGI_FORMAT_R32G32B32_FLOAT;
  layout[0].InputSlot = 0;
  layout[0].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
  layout[0].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
  layout[0].InstanceDataStepRate = 0;
  layout[1].SemanticName = "COLOR";
  layout[1].SemanticIndex = 0;
  layout[1].Format = DXGI_FORMAT_R32G32B32A32_FLOAT;
  layout[1].InputSlot = 0;
  layout[1].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
  layout[1].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
  layout[1].InstanceDataStepRate = 0;
  param.layout_descs_ = layout;
  param.layout_cnt_ = 2;
  if (!device_->CompileShader(param)) {
    return false;
  }
  vs_shader_ = param.vs_shader_;
  ps_shader_ = param.ps_shader_;

  layout_ = param.layout_;

  D3D11SetDebugObjectName(vs_shader_.Get(), "color_vs_buffer");

  D3D11SetDebugObjectName(ps_shader_.Get(), "color_ps_buffer");

  D3D11SetDebugObjectName(layout_.Get(), "color_layout");

  return true;
}

void ColorShader::Render(int vertex, int index) {
  auto context = GetContext_();
  context->IASetInputLayout(layout_.Get());
  context->VSSetShader(vs_shader_.Get(), NULL, 0);
  context->PSSetShader(ps_shader_.Get(), NULL, 0);
  if (index > 0) {
    context->DrawIndexed(index, 0, 0);
  } else {
    if (vertex > 0) {
      context->Draw(vertex, 0);
    }
  }
  return;
}

Microsoft::WRL::ComPtr<ID3D11Device> ColorShader::GetDevice_() {
  return device_->GetDevice();
}

ID3D11DeviceContext* ColorShader::GetContext_() {
  return context_;
}

void ColorShader::Destroy() {
  if (vs_shader_) {
    vs_shader_.Reset();
  }
  if (ps_shader_) {
    ps_shader_.Reset();
  }
  if (layout_) {
    layout_.Reset();
  }
}

ColorShader::~ColorShader() {
  ColorShader::Destroy();
}
}  // namespace graphics