#pragma once

#include <shared_mutex>
#include "base/mediasdk/observer_list_thread_safe_weak.h"
#include "base/threading/thread.h"
#include "cae_processor.h"
#include "encoder_optimizer.h"
#include "encoder_rate_calc.h"
#include "gop_checker.h"
#include "mediasdk/public/plugin/video_encoder_proxy.h"
#include "mediasdk/public/video_encoder_config.hpp"
#include "mediasdk/stream/video_encoder_adaptive_director.h"
#include "mediasdk/video/video_model_output_observer.h"
#include "shared_video_encode_texture_factory.h"
#include "timestamp_smoothing.h"
#include "video_encode_fallback.h"
#include "video_encode_intput_adaptive.h"
#include "video_encode_processor_statistics.h"
#include "video_encode_pts_generator.h"
#include "video_packet.h"

namespace mediasdk {

class AudioEncodeProcessor;
class VideoEncoderSource;
class EndtoEndDelayEncoder;

class VideoEncodeProcessorObserver {
 public:
  virtual ~VideoEncodeProcessorObserver() = default;

  virtual void OnVideoFrame() = 0;

  virtual void OnVideoPacket(std::shared_ptr<VideoPacket> video_packet) = 0;

  virtual void OnEncodeError() = 0;

  virtual void OnEncodeEvent(const std::string& event_json) = 0;

  virtual bool IsRecord() = 0;

  virtual void OnVideoEncoderFallback() = 0;
};

class VideoEncodeProcessor final
    : public std::enable_shared_from_this<VideoEncodeProcessor>,
      public base::SupportsWeakPtr<VideoEncodeProcessor>,
      public VideoEncoderProxy,
      public VideoModelOutputObserver,
      public EncoderOptimizerDelegate,
      public VideoEncoderAdaptiveDirectorDelegate,
      public VideoEncoderFallbackDelegate,
      public VideoEncodeInputAdaptiveDelegate {
 public:
  explicit VideoEncodeProcessor(std::shared_ptr<VideoEncoderConfig> config);

  ~VideoEncodeProcessor() override;

  void SetEncoder(std::shared_ptr<VideoEncoderSource> encoder);

  void SetSinkId(uint32_t sink_id);

  uint32_t GetSinkId() { return sink_id_; }

  std::string GetId() const;

  std::shared_ptr<VideoEncoderConfig> GetConfig() const { return config_; }

  void SetConfig(const std::shared_ptr<VideoEncoderConfig>& config);

  void UpdateConfig();

  std::shared_ptr<std::vector<uint8_t>> GetExtraData() const;

  bool IsRunning() const { return is_running_; }

  void AddObserver(std::shared_ptr<VideoEncodeProcessorObserver> observer);

  void RemoveObserver(std::shared_ptr<VideoEncodeProcessorObserver> observer);

  void SetAudioEncoderProcessor(std::shared_ptr<AudioEncodeProcessor> ptr);

  int64_t GetVideoEncoderStartVideoFrameTS() const;

  StreamType GetStreamType() const;

  uint32_t GetCurrentTargetBitrate() const;

  bool OptimizeBitrate(uint32_t bitrate_kbps,
                       ReconfigReasonType reason,
                       base::OnceCallback<void(uint32_t, bool)> callback);

  void Adaptive(VideoEncodeAdaptiveConfig config,
                base::OnceCallback<void(VideoEncodeAdaptiveResult)> callback);

  bool HasRecordObserver();

  void ResetOptimizer();

  std::string GetEncoderConfigPrettyString(const int32_t indent) const;

  const char* GetVideoEncoderName() const;

  bool IsHardWareEncoder();

  int64_t GetVideoInPtsNs() const { return latest_video_in_pts_ns_.load(); }

  int64_t GetVideoOutPtsNs() const {
    return latest_encoded_video_pts_ns_.load();
  }

  int64_t GetVideoOutDtsNs() const {
    return latest_encoded_video_dts_ns_.load();
  }

  // Get current frame rates and reset counters
  // Returns: pair<frame_rate_input, frame_rate_sent>
  std::pair<int, int> PopCurrentFrameRate();

  bool FallBackEncoder(const std::string config);

  // EncoderOptimizerDelegate
  void OnTargetBitrate(
      const int bitrate_kbps,
      base::OnceCallback<void(uint32_t, bool)> callback) override;

  void OnEncoderOptimizerEvent(const std::string& event) override;

  // VideoEncoderFallbackDelegate
  void OnFallbackResult(
      std::shared_ptr<VideoEncoderSource> encoder,
      const std::shared_ptr<VideoEncoderConfig>& config) override;

 protected:
  // VideoModelOutputObserver:
  VideoModelOutputObserverType RequiredBufferType() const override;

  PixelFormat OnGetRequestedPixelFormat() const override;

  void OnAppendExtraData(const uint8_t* data, size_t size) override;

  void OnResetExtraData(const uint8_t* data, size_t size) override;

  void OnVideoFrame(std::shared_ptr<VideoFrame> frame) override;

  void OnModelTexture(const VideoTexture& texture) override;

  // VideoEncodeInputAdaptiveDelegate:
  void OnAdaptiveTexture(const VideoTexture& texture) override;

  // VideoEncoderProxy:
  void OnEncodedData(const VideoEncoderSourcePacket& packet) override;

  // VideoEncoderAdaptiveDirectorDelegate:
  void OnAdaptiveCompleted(
      bool successed,
      std::shared_ptr<std::vector<uint8_t>> extra_data = nullptr) override;

  bool OnBeforeReconfig(
      std::shared_ptr<std::vector<uint8_t>>& extra_data) override;

  bool SupportReconfigSizeFps() override;

  bool ReconfigEncoderWithParam(uint32_t width,
                                uint32_t height,
                                uint32_t fps) override;

  bool RebuildEncoderWithParam(uint32_t width,
                               uint32_t height,
                               uint32_t fps) override;

  VideoEncodeInputAdaptive* GetInputAdaptive() override;

 private:
  void HandleVideoFrame(std::shared_ptr<VideoFrame> frame);

  void HandleSharedTexture(
      std::shared_ptr<SharedVideoEncodeTexture> shared_tex);

  void HandleFrame(int64_t timestamp_ns,
                   std::function<bool(bool)> encode_process);

  void EncodeFrame(bool is_first_frame,
                   std::function<bool(bool)> encode_process);

  VideoEncodeAdaptiveResult AdaptiveInternal(VideoEncodeAdaptiveConfig config);

  bool ReconfigBitrateInternal(uint32_t bitrate_kbps);

  bool GenerateCurrentPts(int64_t timestamp_ns, bool& is_first_frame);

  bool NeedDiscardCurrentFrame();

  bool CheckFirstEncodedDataKeyFrameAppeared(
      const VideoEncoderSourcePacket& packet,
      bool& is_first_key_frame);

  void AdjustEncodedPacketTimestampIndex(
      std::shared_ptr<VideoPacket>& video_packet);

  bool Start();

  bool Stop();

  void FlushEncoder();

  void PostFlushTaskToEncodeThread();

  void DispatchVideoFrame();

  void DispatchVideoPacket(std::shared_ptr<VideoPacket> video_packet);

  void DispatchEncodeError();

  void DispatchEncodeEvent(const std::string& event);

  std::string BuildPtsOverFlow(uint32_t size);

  std::string BuildFirstKeyFrameCost(uint64_t diff_ms);

  void DispatchVideoEncoderFallback();

  void OnePendingFrameHasBeenHandled();

  void AddOnePendingFrameForHandle();

  bool CheckAudioEncoderStartUP(int64_t video_timestamp) const;

  void ResetStatus();

  void ResetPtsStatusAfterReconfig(const int64_t timebase);

  void CheckEncodeFreezing();

  std::string BuildEncodeFreezing() const;

  std::shared_ptr<VideoEncoderConfig> BuildFallbackConfig(
      const std::string config);

  bool CheckRebuild();

 private:
  std::weak_ptr<AudioEncodeProcessor> audio_encoder_;
  std::unique_ptr<EncoderOptimizer> encoder_optimizer_;
  std::atomic_int64_t begin_video_frame_timestamp_ns_ = 0;
  std::atomic_bool is_running_ = false;
  uint32_t sink_id_ = -1;
  std::shared_ptr<VideoEncoderSource> encoder_;
  std::shared_ptr<VideoEncoderConfig> config_;
  std::shared_ptr<base::Thread> thread_;
  std::atomic_int32_t pending_count_ = 0;
  int64_t pre_drop_ts_ = 0;
  std::shared_ptr<std::vector<uint8_t>> latest_extra_data_;
  base::ObserverListThreadSafeWeak<VideoEncodeProcessorObserver> observer_list_;
  std::unique_ptr<SharedVideoEncodeTextureFactory> shared_texture_factory_;
  std::unique_ptr<VideoEncodePtsGenerator> pts_generator_;
  int64_t pre_dts_ = MEDIASDK_INVALID_DTS;
  int64_t last_capture_dts_timestamp_ = 0;
  bool checked_first_is_i_packet_ = false;
  EncoderRateCalc encoder_rate_calc_;
  bool encode_error_handled_ = false;
  int64_t latest_video_in_encoder_pts_index_ = 0;
  std::atomic_int64_t latest_video_in_pts_ns_ = 0;
  std::atomic_int64_t latest_encoded_video_pts_ns_ = 0;
  std::atomic_int64_t latest_encoded_video_dts_ns_ = 0;
  std::shared_ptr<EndtoEndDelayEncoder> e2d_delay_;
  std::unique_ptr<VideoEncoderFallback> encoder_fallback_ = nullptr;
  std::shared_ptr<VideoEncoderAdaptiveDirector> adaptive_director_;
  std::unique_ptr<VideoEncodeInputAdaptive> input_adaptive_;
  bool pts_overflow_reported_ = false;
  uint64_t encoded_timestamp_ms_ = 0;
  bool encode_freeze_reported_ = false;
  mutable std::shared_mutex encoder_lock_;
  GopChecker gop_checker_{PIPELINE_POSITION::kEncode};
  // after fall back or adaptive process, encoded video packet dts may increase
  // more than 1 seconds or more for smooth video packet send&&play process ,
  // adjust video dts capture ts by adjust video packet startup ts
  TimeStampSmoothing smoothing_{};

  // adaptive code use this logic only
  bool use_new_version_pushing_timestamp_ = false;

  uint32_t rebuild_count_ = 0;
  int reconfig_num_ = 0;
  int rebuild_num_ = 0;

  // Frame rate statistics
  std::unique_ptr<VideoEncodeProcessorStatistics> frame_rate_statistics_;
};

}  // namespace mediasdk
