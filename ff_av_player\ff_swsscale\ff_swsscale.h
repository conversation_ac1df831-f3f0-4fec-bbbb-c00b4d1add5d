#pragma once
extern "C"
{
    #include <libavutil/frame.h>
    #include <libswscale/swscale.h>
}

class FFSWSScale {
 public:
  ~FFSWSScale();
  bool HWFrameToFrame(AVFrame*& frame,
                      AVPixelFormat format = AVPixelFormat::AV_PIX_FMT_NONE);
  bool HWFrameToFrameDefault(AVFrame*& frame);
  bool HWFrameToFrameToTarget(AVFrame*& frame, AVPixelFormat format);
  bool FrameScaleByFFmpeg(AVFrame*& frame, AVPixelFormat format);

 private:
  SwsContext* swscontext_ = nullptr;
};
