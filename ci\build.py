import shutil
import sys
import os
import requests
import lark
import utils
import sladar
from config import CONFIG, BuildConfiguration, Platform
from sdk_check_util import SDKFile<PERSON>heck


def checkout_to_publish_branch():
    os.chdir(CONFIG.code_dir)
    if not utils.exec_cmd("git fetch origin " + CONFIG.args.branch):
        return False
    if not utils.exec_cmd(
        "git checkout -q -f -B "
        + CONFIG.publish_branch_name
        + " origin/"
        + CONFIG.args.branch
    ):
        return False
    return True


def replace_version(version_str):
    ## client
    if not utils.replace_file_str_line(
        "UTF-8",
        CONFIG.code_dir + "\\mediasdk\\mediasdk_version.h",
        '#define MEDIASDK_VERSION "',
        '#define MEDIASDK_VERSION "' + version_str + '"\n',
    ):
        return False

    version_separated_by_commas = version_str.replace(".", ",")
    ## server
    rc_file = os.path.join(CONFIG.code_dir, "mediasdk", "mediasdk.rc")
    if not utils.replace_file_str_line(
        "UTF-8",
        rc_file,
        "FILEVERSION ",
        "FILEVERSION " + version_separated_by_commas + "\n",
    ):
        return False

    if not utils.replace_file_str_line(
        "UTF-8",
        rc_file,
        "PRODUCTVERSION ",
        "PRODUCTVERSION " + version_separated_by_commas + "\n",
    ):
        return False

    if not utils.replace_file_str_line(
        "UTF-8",
        rc_file,
        'VALUE "FileVersion", "',
        '            VALUE "FileVersion", "' + version_str + '"\n',
    ):
        return False

    if not utils.replace_file_str_line(
        "UTF-8",
        rc_file,
        'VALUE "ProductVersion", "',
        '            VALUE "ProductVersion", "' + version_str + '"\n',
    ):
        return False

    return True


def cleanup_build_output():
    utils.remove_path(CONFIG.build_dir)
    utils.remove_path(CONFIG.symbol_temp_dir)
    utils.remove_path(CONFIG.output_bins_dir)
    return True


def cmake_generate(code_dir, build_dir, platform, extra_params=[]):
    cmake_command = 'cmake -G "Visual Studio 16 2019" -A {Platform} -B {build_dir} "{code_dir}" -DENABLE_GPL=OFF -DRELEASEONLY=ON'.format(
        Platform=platform, build_dir=build_dir, code_dir=code_dir
    )

    extra_params_str = " ".join(extra_params)
    if extra_params_str != "":
        cmake_command += " " + extra_params_str

    if not utils.exec_cmd(cmake_command, 0):
        return False
    return True


def cmake_build(dir, build_configuration):
    build_cmd = '"cmake --build {dir} --config {configuration}"'.format(
        dir=dir, configuration=build_configuration
    )
    if not utils.exec_cmd(build_cmd, 0):
        return False
    return True


def generate_win32_game_detour():
    utils.remove_path(CONFIG.build_dir)

    if not cmake_generate(CONFIG.code_dir, CONFIG.build_dir, Platform.WIN32):
        return False

    if not cmake_build(CONFIG.build_dir, BuildConfiguration.RELEASE):
        return False

    bins64_path = CONFIG.exec_dir(Platform.X64)
    bins32_path = CONFIG.exec_dir(Platform.WIN32)
    files = [
        "plugins/game_detour_32.dll",
        "plugins/game_helper_32.exe",
    ]

    # copy to x64 output dir
    for file in files:
        path_64 = os.path.join(bins64_path, file)
        path_32 = os.path.join(bins32_path, file)

        if os.path.exists(path_32):
            shutil.copyfile(path_32, path_64)
        else:
            print("[error] generate_win32_game_detour {} not exists".format(file))
            return False

    return True


def publish_pdb(platform):
    print(f"publish_pdb begin, platform: {platform}")

    print("publish pdb to local symstore")
    # output bins
    symbol_dir = CONFIG.symbol_temp_dir
    if not utils.publish_pdb_to_local(symbol_dir, CONFIG.symstore_dir):
        return False

    print("upload pdb to sladar")
    # output bins
    if not sladar.publish_pdb(symbol_dir):
        return False

    print("publish_pdb end")
    return True


def check_file_and_upload_to_tos(platform):
    SDKFileCheck(CONFIG.args.branch, CONFIG.args.release)
    check_mes, tos_download_url, error = SDKFileCheck.run_file_check(
        platform,
        CONFIG.args.version,
        CONFIG.output_bins_dir,
        CONFIG.publish_branch_name,
    )
    return (check_mes, tos_download_url, error)


def sign_for_mediasdk_tt(check_mes, tos_download_url):
    SIGN_URL = "https://cloudapi.bytedance.net/faas/services/tt2k4m/invoke/sign_for_mediasdk_tt"

    data = {
        "project_id": "569342",
        "sdk_version": CONFIG.args.version,
        "branch": CONFIG.args.branch,
        "user_id": CONFIG.user_open_id,
        "other_mes": {
            "tos_download_url": tos_download_url,
            "check_mes": check_mes,
        },
    }

    res = requests.post(SIGN_URL, json=data)
    if res.ok == True and res.status_code == 200:
        print("sign_for_mediasdk_tt_2024 success")
        return True
    else:
        print("[error] sign_for_mediasdk_tt_2024 failed:%s" % res.text)
        return False


def push_public_branch(code_dir):
    if not utils.git_push_force(code_dir):
        return False
    return True


def run_op(operation, *args, **kwargs):
    print(">>> " + operation.__name__)
    result = operation(*args, **kwargs)
    if not result:
        error_message = operation.__name__ + " failed"
        print("[error] " + error_message)
        return error_message, False
    return "success", True


def run(platform, build_configuration):
    if not CONFIG.args.no_publish_branch:
        exit_str, result = run_op(checkout_to_publish_branch)
        if not result:
            return exit_str, -1

    exit_str, result = run_op(replace_version, CONFIG.args.version)
    if not result:
        return exit_str, -1

    exit_str, result = run_op(cleanup_build_output)
    if not result:
        return exit_str, -1

    exit_str, result = run_op(
        cmake_generate,
        CONFIG.code_dir,
        CONFIG.build_dir,
        platform,
        CONFIG.extra_cmake_params,
    )
    if not result:
        return exit_str, -1

    exit_str, result = run_op(cmake_build, CONFIG.build_dir, build_configuration)
    if not result:
        return exit_str, -1

    exit_str, result = run_op(generate_win32_game_detour)
    if not result:
        return exit_str, -1

    exit_str, result = run_op(publish_pdb, platform)
    if not result:
        return exit_str, -1

    if not CONFIG.args.no_publish_branch:
        exit_str, result = run_op(push_public_branch, CONFIG.code_dir)
        if not result:
            return exit_str, -1

    return "success", 0


def on_build_success(exit_str, exit_code, platform):
    print(">>> check_file_and_upload_to_tos")
    check_mes, tos_download_url, error = check_file_and_upload_to_tos(platform.value)
    if len(error) != 0:
        exit_str = "[error] check_file_and_upload_to_tos failed"
        exit_code = -1
        print(exit_str)
        lark.report_failure(CONFIG.user_open_id, exit_str, exit_code)
        return

    # If release build, When the signature is complete,
    # the signing server notifies the group chat
    if CONFIG.args.release:
        print(">>> sign_for_mediasdk_tt_2024")
        if not sign_for_mediasdk_tt(check_mes, tos_download_url):
            exit_str = "[error] sign_for_mediasdk_tt_2024 failed"
            exit_code = -1
            lark.report_failure(CONFIG.user_open_id, exit_str, exit_code)
    else:
        # Non-release builds are notified directly to the group chat
        lark.report_success(
            CONFIG.user_open_id, check_mes, tos_download_url, platform.value
        )


def on_build_failed(exit_str, exit_code):
    lark.report_failure(CONFIG.user_open_id, exit_str, exit_code)


if __name__ == "__main__":
    # eg. build.py -b dev_tt_build1016 -j 76 -v 6.5.1.76 -u liguodong -c ENABLE_STUDIO=ON;ENABLE_HTTPTEST=ON --release --sladar
    CONFIG.parse_args()

    current_build_paltform = Platform.X64
    current_build_configuration = BuildConfiguration.RELEASE

    lark.report_start(CONFIG.user_open_id)
    exit_str, exit_code = run(current_build_paltform, current_build_configuration)
    if exit_code == 0:
        on_build_success(exit_str, exit_code, current_build_paltform)
    else:
        on_build_failed(exit_str, exit_code)

    print("end exit_code:{} exit_str:{}".format(exit_code, exit_str))
    sys.exit(exit_code)
