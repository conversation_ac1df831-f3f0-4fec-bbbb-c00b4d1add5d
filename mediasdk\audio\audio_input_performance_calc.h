#pragma once

#include <stdint.h>

namespace mediasdk {

struct AudioPerformance {
  int32_t cost;
  int32_t offset;
  int32_t reset_times;
};

class AudioInputPerformanceCalc {
 public:
  AudioInputPerformanceCalc();

  ~AudioInputPerformanceCalc();

 public:
  void OnAudioPacket();

  void OnResampleFinished();

  void SetSyncOffset(int offset);

  void GetPerformance(AudioPerformance& performance);

 private:
  int64_t audio_ts_ = 0;
  int resample_cost_ = 0;
  int offset_ = 0;
  int reset_times_ = 0;
  int audio_times_ = 0;
  bool need_update_ = false;
};
}  // namespace mediasdk