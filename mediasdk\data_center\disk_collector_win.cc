#include "disk_collector_win.h"
#include <Windows.h>
#include <base/logging.h>
#include <winnt.h>

namespace {
constexpr int kInMb = 1048576;
}

namespace disk_collector {

std::shared_ptr<DiskCollector> DiskCollector::Create() {
  return std::make_shared<DiskCollectorImpl>();
}

bool DiskCollectorImpl::GetDiskSpace(int& free_in_mb, int& total_in_mb) {
  ULARGE_INTEGER remainingSpace;
  ULARGE_INTEGER totalNumberOfBytes;
  if (!::GetDiskFreeSpaceExW(L"C:\\", (PULARGE_INTEGER)&remainingSpace,
                             (PULARGE_INTEGER)&totalNumberOfBytes, NULL)) {
    return false;
  }

  free_in_mb = remainingSpace.QuadPart / kInMb;
  total_in_mb = totalNumberOfBytes.QuadPart / kInMb;
  return true;
}
}  // namespace disk_collector