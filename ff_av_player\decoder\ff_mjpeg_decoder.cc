#include "ff_mjpeg_decoder.h"
#include "base/files/file.h"

bool FF_MJPEG_Decoder::DecodeTo(const uint8_t* data,
                                int32_t data_size,
                                AVFrame* frame) {
  if (!TryInit()) {
    return false;
  }

  auto packet =
      FFDecoder::BuildPacketFromMemory(buffered_packet_, data, data_size, true);
  if (!packet)
    return false;

  bool retry = false;
  decoder_.SynDecode(*packet, retry);

  if (decoder_.Receive(*frame)) {
    return true;
  } else {
    return false;
  }

  return true;
}

FF_MJPEG_Decoder::~FF_MJPEG_Decoder() {
  buffered_packet_ = nullptr;
}

bool FF_MJPEG_Decoder::TryInit() {
  if (have_try_) {
    return init_suc_;
  }

  init_suc_ = decoder_.Open(AVCodecID::AV_CODEC_ID_MJPEG);
  have_try_ = true;
  return init_suc_;
}