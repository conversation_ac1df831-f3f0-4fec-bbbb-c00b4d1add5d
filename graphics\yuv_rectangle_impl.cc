﻿#include "yuv_rectangle_impl.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <comdef.h>
#include <dxgi.h>
#include <dxgitype.h>
#include <stdio.h>
#include <windows.h>
#include <wrl/client.h>
#include "DXSimpleMath.h"
#include "texture_color_convert_matrix_builder.h"
#include "transform_calc.h"
#include "yuv_color_shader.h"
#include "yuv_graphics_shader.h"

using namespace DirectX;
using namespace Microsoft::WRL;

namespace graphics {

int graphics::YUVRectangleImpl::GetIndexCnt() {
  SetupIAS();
  std::lock_guard<std::mutex> lock(new_config_lock_);
  return cur_index_cnt_;
}

bool IsEqual(const YUVRectangle::YUVRectangleConfig& left,
             const YUVRectangle::YUVRectangleConfig& right) {
  return std::memcmp(&left, &right, sizeof(left)) == 0;
}

void YUVRectangleImpl::UpdateRectangleConf(
    const YUVRectangle::YUVRectangleConfig* YUVRectangleConfig) {
  std::lock_guard<std::mutex> lock(new_config_lock_);

  if (!YUVRectangleConfig) {
    config_->show = false;
  } else {
    if (config_) {
    } else {
      if (IsEqual(pre_conf_, *YUVRectangleConfig)) {
        return;
      }
    }
    config_ = std::make_unique<YUVRectangleConfig_>();
    memcpy(config_.get(), YUVRectangleConfig,
           sizeof(YUVRectangle::YUVRectangleConfig));
    config_->show = true;
  }
}

bool YUVRectangleImpl::UpdateVertexBuffer_(
    const YUVRectangleConfig_& YUVRectangleConfig) {
  if (vertex_buffer_ && index_buffer_lines_ && index_buffer_fill_ && matrix_)
    return true;
  D3D11_BUFFER_DESC desc = {};
  desc.Usage = D3D11_USAGE_DYNAMIC;
  desc.ByteWidth = sizeof(YUVColorShader::VERTEXTYPE) * 8;
  desc.BindFlags = D3D11_BIND_VERTEX_BUFFER;
  desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  desc.MiscFlags = 0;
  desc.StructureByteStride = 0;
  D3D11_SUBRESOURCE_DATA data = {};
  data.pSysMem = nullptr;
  data.SysMemPitch = 0;
  data.SysMemSlicePitch = 0;
  HRESULT hRes = GetDevice_()->CreateBuffer(&desc, nullptr, &vertex_buffer_);
  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  D3D11SetDebugObjectName(vertex_buffer_.Get(), "YUVRectangle_vertex_buffer");
  ULONG indicesFill[24] = {
      1, 2, 6, 1, 6, 5, 6, 2, 3, 6, 3, 7, 4, 7, 3, 4, 3, 0, 1, 5, 4, 1, 4, 0,
  };
  desc = {};
  desc.Usage = D3D11_USAGE_DEFAULT;
  desc.ByteWidth = sizeof(ULONG) * 24;
  desc.BindFlags = D3D11_BIND_INDEX_BUFFER;
  desc.CPUAccessFlags = 0;
  desc.MiscFlags = 0;
  data = {};
  data.pSysMem = indicesFill;
  data.SysMemPitch = 0;
  data.SysMemSlicePitch = 0;
  hRes = GetDevice_()->CreateBuffer(&desc, &data, &index_buffer_lines_);
  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  D3D11SetDebugObjectName(index_buffer_lines_.Get(),
                          "YUVRectangle_index_buffer");
  ULONG indicesLines[6] = {
      0, 1, 2, 2, 3, 0,
  };
  desc = {};
  desc.Usage = D3D11_USAGE_DEFAULT;
  desc.ByteWidth = sizeof(ULONG) * 6;
  desc.BindFlags = D3D11_BIND_INDEX_BUFFER;
  desc.CPUAccessFlags = 0;
  desc.MiscFlags = 0;
  data = {};
  data.pSysMem = indicesLines;
  data.SysMemPitch = 0;
  data.SysMemSlicePitch = 0;
  hRes = GetDevice_()->CreateBuffer(&desc, &data, &index_buffer_fill_);
  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  D3D11SetDebugObjectName(index_buffer_fill_.Get(),
                          "YUVRectangle_index_fill_buffer");
  D3D11_BUFFER_DESC bufferDesc = {};
  bufferDesc.Usage = D3D11_USAGE_DYNAMIC;
  bufferDesc.ByteWidth = sizeof(MATRIXBUFFER);
  bufferDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
  bufferDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  bufferDesc.MiscFlags = 0;
  bufferDesc.StructureByteStride = 0;
  hRes = GetDevice_()->CreateBuffer(&bufferDesc, NULL, &matrix_);

  D3D11SetDebugObjectName(matrix_.Get(), "YUVRectangle_matrix_buffer");

  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  bufferDesc.Usage = D3D11_USAGE_DYNAMIC;
  bufferDesc.ByteWidth = sizeof(YUVGraphicsShader::PS_BUFFER);
  bufferDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
  bufferDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  bufferDesc.MiscFlags = 0;
  bufferDesc.StructureByteStride = 0;
  hRes = GetDevice_()->CreateBuffer(&bufferDesc, NULL, &ps_yuv_param_);

  D3D11SetDebugObjectName(ps_yuv_param_.Get(), "YUVRectangle_ps_param_buffer");

  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }

  return true;
}

bool YUVRectangleImpl::UpdateBufferWithVPSize_(
    const XMFLOAT2& vpSize,
    const YUVRectangleConfig_& YUVRectangleConfig) {
  auto picSize = XMFLOAT2{
      YUVRectangleConfig.bottom_right.x - YUVRectangleConfig.top_left.x,
      YUVRectangleConfig.bottom_right.y - YUVRectangleConfig.top_left.y};
  int vertexCnt = 0;
  vertex_mem_buffer_[0].position = XMFLOAT3(0.f, 0.f, 0.f);

  vertex_mem_buffer_[1].position = XMFLOAT3(0.f, 1.f, 0.f);

  vertex_mem_buffer_[2].position = XMFLOAT3(1.f, 1.f, 0.f);

  vertex_mem_buffer_[3].position = XMFLOAT3(1.f, 0.f, 0.f);

  if (YUVRectangleConfig.fill) {
    fill_ = true;
    vertexCnt = 4;
    cur_index_cnt_ = 6;
    for (int i = 0; i < 4; i++) {
      vertex_mem_buffer_[i].color = XMFLOAT4{
          YUVRectangleConfig.color.x,
          vertex_mem_buffer_[i].position.x * YUVRectangleConfig.color.y,
          vertex_mem_buffer_[i].position.y * YUVRectangleConfig.color.z, 1.f};
    }
  } else {
    vertexCnt = 8;
    fill_ = false;
    cur_index_cnt_ =
        24;  // every line is a YUVRectangle,every YUVRectangle is two TRIANGLE
    for (int i = 0; i < vertex_mem_buffer_.size(); i++) {
      vertex_mem_buffer_[i].color = YUVRectangleConfig.color;
    }

    vertex_mem_buffer_[4].position =
        XMFLOAT3(vertex_mem_buffer_[0].position.x +
                     (YUVRectangleConfig.thinkness) / picSize.x,
                 vertex_mem_buffer_[0].position.y +
                     (YUVRectangleConfig.thinkness) / picSize.y,
                 0.f);
    vertex_mem_buffer_[5].position =
        XMFLOAT3(vertex_mem_buffer_[1].position.x +
                     (YUVRectangleConfig.thinkness) / picSize.x,
                 vertex_mem_buffer_[1].position.y -
                     (YUVRectangleConfig.thinkness) / picSize.y,
                 0.f);
    vertex_mem_buffer_[6].position =
        XMFLOAT3(vertex_mem_buffer_[2].position.x -
                     (YUVRectangleConfig.thinkness) / picSize.x,
                 vertex_mem_buffer_[2].position.y -
                     (YUVRectangleConfig.thinkness) / picSize.y,
                 0.f);
    vertex_mem_buffer_[7].position =
        XMFLOAT3(vertex_mem_buffer_[3].position.x -
                     (YUVRectangleConfig.thinkness) / picSize.x,
                 vertex_mem_buffer_[3].position.y +
                     (YUVRectangleConfig.thinkness) / picSize.y,
                 0.f);
  }

  D3D11_MAPPED_SUBRESOURCE mappedResource;
  HRESULT hRes = GetContext_()->Map(
      vertex_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &mappedResource);
  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to Map(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }

  std::memcpy(mappedResource.pData, (void*)vertex_mem_buffer_.data(),
              sizeof(YUVColorShader::VERTEXTYPE) * vertexCnt);
  GetContext_()->Unmap(vertex_buffer_.Get(), 0);
  return true;
}

bool YUVRectangleImpl::DrawTo(const XMFLOAT2& vpSize,
                              const XMMATRIX& view,
                              const XMMATRIX& projection) {
  std::unique_ptr<YUVRectangleConfig_> config;

  {
    std::lock_guard<std::mutex> lock(new_config_lock_);
    if (config_) {
      pre_conf_ = *config_;
    }
    std::swap(config, config_);
  }
  if (config) {
    if (!config->show) {
      cur_index_cnt_ = 0;
      return false;
    }

    if (!UpdateVertexBuffer_(*config))
      return false;

    if (!UpdateBufferWithVPSize_(vpSize, *config))
      return false;
    auto picSize = XMFLOAT2{config->bottom_right.x - config->top_left.x,
                            config->bottom_right.y - config->top_left.y};
    XMMATRIX world = BuildMatrixToTextureRender(
        picSize, vpSize, config->top_left, XMFLOAT2_ONE, XMFLOAT4_EMPTY,
        config->rotate, config->shear, config->shear_angle);
    auto context = GetContext_();
    DoCopyMatrixBuffer(context, &world, &view, &projection, matrix_);

    YUVGraphicsShader::PS_BUFFER ps_param;
    BuildPlanesYUVToBGRA(config->cs, config->vr, ps_param.Y, ps_param.U,
                         ps_param.V, ps_param.min_range, ps_param.max_range,
                         mediasdk::kPixelFormatNV12);
    D3D11_MAPPED_SUBRESOURCE map = {};
    if (SUCCEEDED(context->Map(ps_yuv_param_.Get(), 0, D3D11_MAP_WRITE_DISCARD,
                               0, &map))) {
      YUVGraphicsShader::PS_BUFFER* dataPtr =
          (YUVGraphicsShader::PS_BUFFER*)map.pData;
      memcpy(dataPtr, &ps_param, sizeof(ps_param));
      context->Unmap(ps_yuv_param_.Get(), 0);
    }
  }
  return true;
}

bool YUVRectangleImpl::SetupIAS() {
  if (!cur_index_cnt_) {
    return false;
  }

  UINT stride = sizeof(YUVColorShader::VERTEXTYPE);
  UINT offset = 0;
  auto context = GetContext_();
  context->IASetVertexBuffers(0, 1, vertex_buffer_.GetAddressOf(), &stride,
                              &offset);
  context->IASetIndexBuffer(
      fill_ ? index_buffer_fill_.Get() : index_buffer_lines_.Get(),
      DXGI_FORMAT_R32_UINT, 0);
  context->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);
  context->VSSetConstantBuffers(0, 1, matrix_.GetAddressOf());
  context->PSSetConstantBuffers(0, 1, ps_yuv_param_.GetAddressOf());

  return true;
}

YUVRectangleImpl::YUVRectangleImpl(Device& ins) : instance_(ins) {}

ID3D11DeviceContext* YUVRectangleImpl::GetContext_() {
  return instance_.GetContext().Get();
}

ID3D11Device* YUVRectangleImpl::GetDevice_() {
  return instance_.GetDevice().Get();
}

std::shared_ptr<YUVRectangleImpl> YUVRectangleImpl::CreateYUVRectangle(
    Device& inst) {
  return std::make_shared<YUVRectangleImpl>(inst);
}

void YUVRectangleImpl::Destroy() {
  if (vertex_buffer_) {
    vertex_buffer_.Reset();
  }
  if (index_buffer_lines_) {
    index_buffer_lines_.Reset();
  }
  if (index_buffer_fill_) {
    index_buffer_fill_.Reset();
  }
  if (ps_yuv_param_) {
    ps_yuv_param_.Reset();
  }

  {
    std::lock_guard<std::mutex> lock(new_config_lock_);
    if (config_) {
      config_ = nullptr;
    }
  }
  if (matrix_) {
    matrix_.Reset();
  }
}

YUVRectangleImpl::~YUVRectangleImpl() {
  Destroy();
}
}  // namespace graphics