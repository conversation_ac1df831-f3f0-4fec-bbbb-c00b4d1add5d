#include "flv_meta_data_builder.h"

#include <base/check.h>
#include "amf0.h"
#include "audio/audio_format.h"
#include "crypto.h"
#include "data_center/os_version.h"
#include "mediasdk/mediasdk_version.h"

namespace {
constexpr int kMaxMetaDataSize = 4096;
constexpr double kAudioCodecIdAac = 10.0;
constexpr double kAudioSampleSizeValue = 16.0;

constexpr std::string_view kMetaData = "onMetaData";
constexpr std::string_view kDurationField = "duration";
constexpr std::string_view kSetDataFrame = "@setDataFrame";
constexpr std::string_view kVersion = "sdk_version";
constexpr std::string_view kSdkVersion = MEDIASDK_VERSION;
constexpr std::string_view kFileSize = "fileSize";
constexpr std::string_view kWidth = "width";
constexpr std::string_view kHeight = "height";
constexpr std::string_view kFrameRate = "framerate";
constexpr std::string_view kVideoDataRate = "videodatarate";
constexpr std::string_view kInterval = "interval";
constexpr std::string_view kVideoCodecId = "videocodecid";
constexpr float kVideoCodecIdH264 = 7.0;
constexpr float kVideoCodecIdH265 = 12.0;
constexpr std::string_view kPlatform = "platform";
constexpr std::string_view kWindows = "windows";
constexpr std::string_view kModel = "model";
constexpr std::string_view k64Bit = "64-bit";
constexpr std::string_view kAudioDataRate = "audiodatarate";
constexpr std::string_view kAudioSampleRateName = "audiosamplerate";
constexpr std::string_view kStereo = "stereo";
constexpr std::string_view kAudioSampleSize = "audiosamplesize";
constexpr std::string_view kAudioCodecId = "audiocodecid";
constexpr std::string_view kAudioChannels = "audiochannels";
constexpr std::string_view kEncoder = "Encoder";
constexpr std::string_view kEncoderInfo = "bytedmediasdk";
constexpr std::string_view kMinBitrate = "min_bitrate";
constexpr std::string_view kMaxBitrate = "max_bitrate";
constexpr std::string_view kDefaultBitrate = "default_bitrate";
constexpr std::string_view kIsHardwareEncode = "is_hardware_encode";
constexpr std::string_view kOsVersion = "os_version";

}  // namespace

namespace mediasdk {

// TODO: fix hard code
std::vector<uint8_t> FlvMetaDataBuilder::Build(
    std::shared_ptr<StreamConfig> config) {
  if (auto encoder_config = config->GetVideoEncoderConfig(); encoder_config) {
    auto adaptive_output_width = encoder_config->GetAdaptiveOutputWidth();
    auto adaptive_output_height = encoder_config->GetAdaptiveOutputHeight();
    auto adaptive_timebase = encoder_config->GetAdaptiveTimeBase();
    return Build(
        std::move(config),
        adaptive_output_width ? *adaptive_output_width
                              : encoder_config->GetOutputWidth(),
        adaptive_output_height ? *adaptive_output_height
                               : encoder_config->GetOutputHeight(),
        adaptive_timebase ? *adaptive_timebase : encoder_config->GetTimeBase());
  } else {
    LOG(ERROR) << "[FlvMetaDataBuilder] Build base info is null";
    return Build(std::move(config), 0, 0, 0);
  }
}

std::vector<uint8_t> FlvMetaDataBuilder::Build(
    std::shared_ptr<StreamConfig> config,
    uint32_t width,
    uint32_t height,
    uint32_t fps) {
  DCHECK(config);
  DCHECK(config->GetVideoEncoderConfig());

  std::vector<uint8_t> out_data(kMaxMetaDataSize);

  uint8_t* begin = out_data.data();
  uint8_t* end = begin + out_data.size();

  begin =
      AMFWriteString(begin, end, kSetDataFrame.data(), kSetDataFrame.size());
  begin = AMFWriteString(begin, end, kMetaData.data(), kMetaData.size());

  begin = AMFWriteECMAArarry(begin, end);
  begin = AMFWriteNamedDouble(begin, end, kDurationField.data(),
                              kDurationField.size(), 0.0);
  begin =
      AMFWriteNamedDouble(begin, end, kFileSize.data(), kFileSize.size(), 0.0);
  begin = AMFWriteNamedDouble(begin, end, kWidth.data(), kWidth.size(), width);
  begin =
      AMFWriteNamedDouble(begin, end, kHeight.data(), kHeight.size(), height);
  begin = AMFWriteNamedDouble(begin, end, kFrameRate.data(), kFrameRate.size(),
                              fps);
  if (auto encoder_config = config->GetVideoEncoderConfig(); encoder_config) {
    begin = AMFWriteNamedDouble(begin, end, kVideoDataRate.data(),
                                kVideoDataRate.size(),
                                encoder_config->GetBitRateKbps());
    begin = AMFWriteNamedDouble(begin, end, kInterval.data(), kInterval.size(),
                                encoder_config->GetGOP());

    bool is_h264 = encoder_config->GetStreamType() == kStreamTypeH264;
    begin = AMFWriteNamedDouble(
        begin, end, kVideoCodecId.data(), kVideoCodecId.size(),
        is_h264 ? kVideoCodecIdH264 : kVideoCodecIdH265);
    begin =
        AMFWriteNamedDouble(begin, end, kMinBitrate.data(), kMinBitrate.size(),
                            encoder_config->GetBitRateKbps());
    begin =
        AMFWriteNamedDouble(begin, end, kMaxBitrate.data(), kMaxBitrate.size(),
                            encoder_config->GetMaxBitRateKbps());
    begin = AMFWriteNamedDouble(begin, end, kDefaultBitrate.data(),
                                kDefaultBitrate.size(),
                                encoder_config->GetBitRateKbps());
    begin = AMFWriteNamedBoolean(begin, end, kIsHardwareEncode.data(),
                                 kIsHardwareEncode.size(),
                                 encoder_config->GetIsHardWare());
  }

  begin = AMFWriteNamedString(begin, end, kPlatform.data(), kPlatform.size(),
                              kWindows.data(), kWindows.size());
  begin = AMFWriteNamedDouble(
      begin, end, kAudioDataRate.data(), kAudioDataRate.size(),
      config->GetAudioEncoderConfig()->GetBitRateKbps());
  begin = AMFWriteNamedDouble(
      begin, end, kAudioSampleRateName.data(), kAudioSampleRateName.size(),
      (double)mediasdk::kAudioOutputFormat.GetSampleRate());
  begin = AMFWriteNamedBoolean(begin, end, kStereo.data(), kStereo.size(),
                               mediasdk::kAudioOutputFormat.GetChannel() == 2);
  begin = AMFWriteNamedDouble(begin, end, kAudioSampleSize.data(),
                              kAudioSampleSize.size(), kAudioSampleSizeValue);
  begin = AMFWriteNamedDouble(begin, end, kAudioCodecId.data(),
                              kAudioCodecId.size(), kAudioCodecIdAac);
  begin = AMFWriteNamedDouble(begin, end, kAudioChannels.data(),
                              kAudioChannels.size(),
                              mediasdk::kAudioOutputFormat.GetChannel());

  begin = AMFWriteNamedString(begin, end, kVersion.data(), kVersion.size(),
                              kSdkVersion.data(), kSdkVersion.size());
  begin = AMFWriteNamedString(begin, end, kModel.data(), kModel.size(),
                              k64Bit.data(), k64Bit.size());
  auto version = os_version::OsVersion::GetOsVersion();
  begin = AMFWriteNamedString(begin, end, kOsVersion.data(), kOsVersion.size(),
                              version.data(), version.size());

  std::ostringstream oss_encoder;
  auto cipher = config->GetCipher();
  auto plain_text = config->GetPlainText();
  std::string encrypted_cipher;
  if (Crypto::Encrypt(cipher, plain_text, encrypted_cipher) &&
      !encrypted_cipher.empty()) {
    oss_encoder << kEncoderInfo << ":" << encrypted_cipher;
  } else {
    oss_encoder << kEncoderInfo;
  }

  std::string encoder_info = oss_encoder.str();
  begin = AMFWriteNamedString(begin, end, kEncoder.data(), kEncoder.size(),
                              encoder_info.c_str(), encoder_info.size());

  auto custom_meta_data = config->GetCustomFlvMetadata();
  for (auto& [key, value] : custom_meta_data) {
    uint8_t* ret = AMFWriteNamedString(begin, end, key.data(), key.size(),
                                       value.data(), value.size());
    while (!ret) {
      // resize out_data
      size_t diff = begin - out_data.data();
      out_data.resize(out_data.size() + kMaxMetaDataSize);
      begin = out_data.data() + diff;
      end = out_data.data() + out_data.size();

      // re-write named string
      ret = AMFWriteNamedString(begin, end, key.data(), key.size(),
                                value.data(), value.size());
    }
    begin = ret;
  }

  if (int64_t left = end - begin; left < 3) {
    int64_t added = 3 - left;
    out_data.resize(out_data.size() + added);
    end = out_data.data() + out_data.size();
    begin = end - 3;
  }

  *begin++ = 0;
  *begin++ = 0;
  *begin++ = AMFDataType::AMF_OBJECT_END;

  out_data.resize(begin - out_data.data());
  return out_data;
}

std::vector<uint8_t> FlvMetaDataBuilder::Build(const MetaDataField& config) {
  std::vector<uint8_t> out_data(kMaxMetaDataSize);

  uint8_t* begin = out_data.data();
  uint8_t* end = begin + out_data.size();

  begin =
      AMFWriteString(begin, end, kSetDataFrame.data(), kSetDataFrame.size());
  begin = AMFWriteString(begin, end, kMetaData.data(), kMetaData.size());

  begin = AMFWriteECMAArarry(begin, end);
  begin = AMFWriteNamedDouble(begin, end, kDurationField.data(),
                              kDurationField.size(), 0.0);
  begin =
      AMFWriteNamedDouble(begin, end, kFileSize.data(), kFileSize.size(), 0.0);
  begin = AMFWriteNamedDouble(begin, end, kWidth.data(), kWidth.size(),
                              config.output_width);
  begin = AMFWriteNamedDouble(begin, end, kHeight.data(), kHeight.size(),
                              config.output_height);
  begin = AMFWriteNamedDouble(begin, end, kFrameRate.data(), kFrameRate.size(),
                              config.frame_rate);
  begin = AMFWriteNamedDouble(begin, end, kVideoDataRate.data(),
                              kVideoDataRate.size(), config.video_bitrate);
  begin = AMFWriteNamedDouble(begin, end, kInterval.data(), kInterval.size(),
                              config.gop);
  begin = AMFWriteNamedDouble(
      begin, end, kVideoCodecId.data(), kVideoCodecId.size(),
      config.is_h264 ? kVideoCodecIdH264 : kVideoCodecIdH265);
  begin = AMFWriteNamedString(begin, end, kPlatform.data(), kPlatform.size(),
                              kWindows.data(), kWindows.size());
  begin = AMFWriteNamedDouble(begin, end, kAudioDataRate.data(),
                              kAudioDataRate.size(), config.audio_bitrate);
  begin = AMFWriteNamedDouble(
      begin, end, kAudioSampleRateName.data(), kAudioSampleRateName.size(),
      (double)mediasdk::kAudioOutputFormat.GetSampleRate());
  begin = AMFWriteNamedBoolean(begin, end, kStereo.data(), kStereo.size(),
                               mediasdk::kAudioOutputFormat.GetChannel() == 2);
  begin = AMFWriteNamedDouble(begin, end, kMinBitrate.data(),
                              kMinBitrate.size(), config.video_bitrate);
  begin = AMFWriteNamedDouble(begin, end, kMaxBitrate.data(),
                              kMaxBitrate.size(), config.video_bitrate);
  begin = AMFWriteNamedDouble(begin, end, kDefaultBitrate.data(),
                              kDefaultBitrate.size(), config.video_bitrate);
  begin = AMFWriteNamedBoolean(begin, end, kIsHardwareEncode.data(),
                               kIsHardwareEncode.size(), config.is_hardware);
  begin = AMFWriteNamedDouble(begin, end, kAudioSampleSize.data(),
                              kAudioSampleSize.size(), kAudioSampleSizeValue);
  begin = AMFWriteNamedDouble(begin, end, kAudioCodecId.data(),
                              kAudioCodecId.size(), kAudioCodecIdAac);
  begin = AMFWriteNamedDouble(begin, end, kAudioChannels.data(),
                              kAudioChannels.size(),
                              mediasdk::kAudioOutputFormat.GetChannel());

  begin = AMFWriteNamedString(begin, end, kVersion.data(), kVersion.size(),
                              kSdkVersion.data(), kSdkVersion.size());
  begin = AMFWriteNamedString(begin, end, kModel.data(), kModel.size(),
                              k64Bit.data(), k64Bit.size());

  std::ostringstream oss_encoder;
  std::string encrypted_cipher;
  if (Crypto::Encrypt(config.cipher, config.plain_text, encrypted_cipher) &&
      !encrypted_cipher.empty()) {
    oss_encoder << kEncoderInfo << ":" << encrypted_cipher;
  } else {
    oss_encoder << kEncoderInfo;
  }

  std::string encoder_info = oss_encoder.str();
  begin = AMFWriteNamedString(begin, end, kEncoder.data(), kEncoder.size(),
                              encoder_info.c_str(), encoder_info.size());

  *begin++ = 0;
  *begin++ = 0;
  *begin++ = AMFDataType::AMF_OBJECT_END;

  out_data.resize(begin - out_data.data());
  return out_data;
}

}  // namespace mediasdk
