#pragma once

#include <mutex>
#include "shader.h"

namespace graphics {

class BGRAToRGBAShader : public Shader {
 public:

  static inline const char* SHADER_ID_STRING = "bgra_to_rgba_shader";

  static std::shared_ptr<Shader> CreateTextureShader(const char* type) {
    if (strcmp(type, SHADER_ID_STRING) == 0) {
      return std::make_shared<BGRAToRGBAShader>();
    }
    return nullptr;
  }

  static void EnumPluginsFunction(void* param, EnumPluginCallBack fpn) {
    fpn(param,
        ShaderItem{SHADER_ID_STRING, "shader for draw texture to graphics",
                   BGRAToRGBAShader::CreateTextureShader});
  }

 public:
  bool Init(const std::shared_ptr<Device>&) override;

  void RenderTexture(ID3D11ShaderResourceView* pSRView);
  void Destroy() override;
  ~BGRAToRGBAShader() override;

 private:
  bool _Init(const std::shared_ptr<Device>& ins);
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice_();
  Microsoft::WRL::ComPtr<ID3D11DeviceContext> GetContext_();

 private:
  bool try_init_ = false;
  bool init_suc_ = false;
  std::shared_ptr<Device> device_;

  Microsoft::WRL::ComPtr<ID3D11VertexShader> vs_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11SamplerState> sampler_;
};

}  // namespace graphics
