#pragma once

#include <string>
#include "DXSimpleMath.h"

namespace graphics {

struct pos_and_yuv_value {
  XMFLOAT2 pos;

  uint8_t r;
  uint8_t g;
  uint8_t b;
  uint8_t a;
};

inline bool less_than_1(uint8_t a, uint8_t b) {
  return a == b || a + 1 == b || b + 1 == a;
}

struct r_g_b_a {
  uint8_t r;
  uint8_t g;
  uint8_t b;
  uint8_t a;

  bool operator==(const r_g_b_a& right) const {
    // return memcmp(this, &right, sizeof(right)) == 0;
    return less_than_1(right.r, this->r) && less_than_1(right.g, this->g) &&
           less_than_1(right.b, this->b) && less_than_1(right.a, this->a);
  }

  bool operator!=(const r_g_b_a& right) const { return !operator==(right); }

  std::string ToString() const {
    return std::to_string(r) + "," + std::to_string(g) + "," +
           std::to_string(b) + "," + std::to_string(a);
  }
};

struct float_r_g_b_a {
  float r;
  float g;
  float b;
  float a;

  std::string ToString() const {
    return std::to_string(r) + "," + std::to_string(g) + "," +
           std::to_string(b) + "," + std::to_string(a);
  }
};

struct pos_and_r_g_b_a_value {
  XMFLOAT2 pos;
  r_g_b_a color;
};

}  // namespace graphics
