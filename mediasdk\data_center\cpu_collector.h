#pragma once
#include <memory>
#include <string>

namespace cpu_collector {

struct CpuHardwareInfo {
  std::string name;
  uint32_t num = 0;
  uint32_t clock_speed = 0;
};

class CpuCollector {
 public:
  static std::shared_ptr<CpuCollector> CreateCollector();

  CpuCollector() = default;

  virtual ~CpuCollector() = default;

  virtual double ProcessUsage() = 0;

  virtual bool SystemUsage(double& system_usage, double& cpu_time) = 0;

  virtual bool CpuInfo(CpuHardwareInfo& info) = 0;
};
}  // namespace cpu_collector