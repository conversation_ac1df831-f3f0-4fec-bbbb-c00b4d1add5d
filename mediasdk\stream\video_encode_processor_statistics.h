#pragma once

#include <atomic>
#include <mutex>
#include "mediasdk/utils/time_helper.h"

namespace mediasdk {

// Statistics for video encode processor frame rates
class VideoEncodeProcessorStatistics {
 public:
  VideoEncodeProcessorStatistics();
  ~VideoEncodeProcessorStatistics() = default;

  // Record input frame (called when frame is received for encoding)
  void OnInputFrame();

  // Record output frame (called when encoded packet is generated)
  void OnOutputFrame();

  // Get current frame rates and reset counters
  // Returns: pair<frame_rate_input, frame_rate_sent>
  std::pair<int, int> PopCurrentFrameRate();

  // Reset all statistics
  void Reset();

 private:
  int CalculateFrameRate(uint32_t frame_count, int64_t total_time_us);

  // Frame counters
  std::atomic<uint32_t> input_frame_count_{0};
  std::atomic<uint32_t> output_frame_count_{0};

  // Time tracking
  std::atomic<int64_t> first_input_time_us_{0};
  std::atomic<int64_t> first_output_time_us_{0};

  // Mutex for thread safety during calculation
  mutable std::mutex calculation_mutex_;
};

}  // namespace mediasdk