#pragma once
#include <dcomp.h>
#include <dxgi1_2.h>
#include "display.h"
#include "mediasdk/utils/time_helper.h"

namespace graphics {

class DisplayImplFlipModeAlpha : public Display {
 public:
  static std::shared_ptr<DisplayImplFlipModeAlpha> CreateDisplay2D(Device& ins,
                                                                   HWND hWnd,
                                                                   int cx,
                                                                   int cy);

  DisplayImplFlipModeAlpha(Device& ins);

  bool IsReady(uint32_t wait_time = 0) override;

  bool Present() override;

  Graphics& GetGraphics() override;
  void Destroy() override;
  bool Resize(uint32_t cx, uint32_t cy) override;
  virtual ~DisplayImplFlipModeAlpha() override;

 private:
  bool OpenWithHWND(HWND hWnd);
  bool CreateTexture();

 protected:
  std::shared_ptr<Graphics> graphics_;
  HWND hwnd_ = NULL;
  DXGI_SWAP_CHAIN_DESC1 swap_chain_desc1_ = {};
  HANDLE waitable_event_ = NULL;
  base::TimeDelta last_present_ts_ = mediasdk::GetCurrentTimeDelta();

  Microsoft::WRL::ComPtr<IDXGISwapChain1> swap_chain1_;
  Device& device_;
  int32_t not_ready_cnt_ = 0;
  Microsoft::WRL::ComPtr<IDCompositionDevice> dcompDevice_;
  Microsoft::WRL::ComPtr<IDCompositionTarget> target_;
  Microsoft::WRL::ComPtr<IDCompositionVisual> visual_;
};

}  // namespace graphics
