#include "bgra_calc_shader.h"
#include <base/logging.h>

namespace graphics {

// https://www.reddit.com/r/gamedev/comments/2j17wk/a_slightly_faster_bufferless_vertex_shader_trick/
static const char* CONST_VERTEX_SHADER() {
  return R"(

struct PS_Pos
{
	float4 pos : SV_POSITION;
};

// common get pos from vid logic

float4 VS_GET_POS_TEX_XY(uint vid)
{
	bool right = vid == 2;
	bool top = vid == 1;
	float x = - 1.0f + right * 4.0f;
	float y = - 1.0f + top * 4.0f;
	float u = right * 2.0f;
	float v =  1.0 - top * 2.f;
	return float4(x,y,u,v);
}

PS_Pos RAWCXCY_VS_Main(uint vid : SV_VERTEXID)
{
	float4 pos_tex_xy = VS_GET_POS_TEX_XY(vid);

	PS_Pos output;

	output.pos = float4(pos_tex_xy.x, pos_tex_xy.y, 0.0, 1.0);

	return output;
}

)";
}

static const char* CONST_PIXEL_SHADER() {
  return R"(
Texture2D texture0 : register(t0);
Texture2D texture1 : register(t1);
SamplerState tex_sampler : register(s0);

cbuffer TEXTURE_SIZE : register(b0)
{
   float4 texture_move;
   float4 mask_move;
};

struct PS_Pos
{
	float4 pos : SV_POSITION;
};

float4 AlphaMask_PS_MAIN(PS_Pos input) : SV_TARGET
{
	float4 raw = texture0.Load(int3(input.pos.xy + texture_move, 0));
    float4 mask = texture1.Load(int3(input.pos.xy + mask_move, 0));
	
	raw.a = mask.r;

    return raw;
}

)";
}

bool BGRACalcShader::Init(const std::shared_ptr<Device>& ins) {
  instance_ = ins;
  if (!try_init_) {
    init_suc_ = DoInit();
    try_init_ = true;
  }

  return init_suc_;
}

bool BGRACalcShader::DoInit() {
  Device::CompileShaderParam param = {};
  param.ps = CONST_PIXEL_SHADER();
  param.vs = CONST_VERTEX_SHADER();
  param.ps_name = "AlphaMask_PS_MAIN";
  param.vs_name = "RAWCXCY_VS_Main";

  if (!instance_->CompileShader(param)) {
    LOG(ERROR) << "Failed to Compile Shader";
    return false;
  }

  ps_ = param.ps_shader_;
  vs_ = param.vs_shader_;

  return true;
}

BGRACalcShader::~BGRACalcShader() {
  BGRACalcShader::Destroy();
}

void BGRACalcShader::Destroy() {
  if (vs_) {
    vs_.Reset();
  }
  if (ps_) {
    ps_.Reset();
  }
}

void BGRACalcShader::Render(Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_buffer,
                            ID3D11ShaderResourceView* texture,
                            ID3D11ShaderResourceView* mask) {
  GetContext()->IASetVertexBuffers(0, 0, nullptr, nullptr, nullptr);
  GetContext()->IASetIndexBuffer(nullptr, DXGI_FORMAT::DXGI_FORMAT_UNKNOWN, 0);

  GetContext()->PSSetConstantBuffers(0, 1, ps_buffer.GetAddressOf());

  GetContext()->PSSetShaderResources(0, 1, &texture);
  GetContext()->PSSetShaderResources(1, 1, &mask);

  GetContext()->IASetInputLayout(nullptr);

  GetContext()->PSSetShader(ps_.Get(), NULL, 0);

  GetContext()->VSSetShader(vs_.Get(), NULL, 0);

  GetContext()->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);

  GetContext()->Draw(3, 0);  // input only VID
}

Microsoft::WRL::ComPtr<ID3D11Device> BGRACalcShader::GetDevice() {
  return instance_->GetDevice();
}

Microsoft::WRL::ComPtr<ID3D11DeviceContext> BGRACalcShader::GetContext() {
  return instance_->GetContext();
}
}  // namespace graphics