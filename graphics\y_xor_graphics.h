#pragma once
#include "texture.h"

namespace graphics {
class GRAPHICS_EXPORT YXORGraphics {
 public:
  // texture is Y8/R8 format
  virtual bool XOR(std::shared_ptr<Texture> left,
                   std::shared_ptr<Texture> right) = 0;
  virtual std::shared_ptr<Texture> GetOutputTexture() = 0;
  virtual ~YXORGraphics(){};
};

GRAPHICS_EXPORT std::shared_ptr<YXORGraphics> CreateYXORGraphics(
    Device& ins);
}  // namespace graphics
