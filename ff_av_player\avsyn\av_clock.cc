#include "av_clock.h"

#include <base/check.h>
#if BUILDFLAG(IS_WIN)
#include <windows.h>
#endif
extern "C" {
#include "libavutil/time.h"
}

int64_t ms_now_internal() {
#if BUILDFLAG(IS_WIN)
  return ::GetTickCount64();
#else
  return av_gettime_relative() / 1000;
#endif
}

int64_t AVClock::NowMS() const noexcept {
  if (last_update_ns_ts_) {
    if (pause_ || step_) {
      return last_pts_;
    } else {
      return last_pts_ + (ms_now_internal() - last_update_ns_ts_) * speed_;
    }
  }
  return kNotRunning;
}

int64_t AVClock::LastUpdateTS() const noexcept {
  return last_update_ns_ts_;
}

int64_t AVClock::LastPTS() const noexcept {
  return last_pts_;
}

void AVClock::UpdateNano(const int64_t val) {
  last_pts_ = val;
  DCHECK(last_pts_ <= val);
  last_update_ns_ts_ = ms_now_internal();
}

void AVClock::Pause() {
  pause_ = true;
}

bool AVClock::IsPause() const {
  return pause_;
}

void AVClock::Resume() {
  UpdateNano(last_pts_);  // for update last update time
  pause_ = false;
}

void AVClock::SetStep(const bool step) {
  step_ = step;
}

bool AVClock::IsStep() const {
  return step_;
}

void AVClock::SetSpeed(const float speed) {
  speed_ = speed;
}

float AVClock::GetSpeed() const {
  return speed_;
}

void AVClock::Reset() {
  last_pts_ = 0;
  last_update_ns_ts_ = 0;
  pause_ = false;
  step_ = false;
}