#pragma once
#include <graphics/Device.h>
#include <array>
#include <string>

#include <graphics/Texture.h>
#include <base/files/file.h>
#include <base/strings/string_split.h>
#include <base/strings/sys_string_conversions.h>
#include <media/video/video_frame.h>
#include <graphics/yuv_graphics_impl.h>

namespace graphics {

std::shared_ptr<Texture> inline GetTextureFromFile(
    Device& dev,
    const std::wstring& file_path) {
  auto yuv_frame_with_buffer =
      mediasdk::VideoFrame::GetVideoFrameFromFile(file_path);
  if (yuv_frame_with_buffer->GetFormat() ==
      mediasdk::PIXEL_FORMAT::PIXEL_FORMAT_RGBA16) {
    auto new_texture = CreateTexture2D(
        dev, yuv_frame_with_buffer->GetWidth(),
        yuv_frame_with_buffer->GetHeight(), yuv_frame_with_buffer->GetFormat(),
        Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE |
            Texture::TEXTURE_USAGE::TEXTURE_USAGE_COPY |
            Texture::TEXTURE_USAGE::TEXTURE_USAGE_RENDER_TARGET);

    auto cpu_texture = CreateTexture2D(
        dev, yuv_frame_with_buffer->GetWidth(),
        yuv_frame_with_buffer->GetHeight(), yuv_frame_with_buffer->GetFormat(),
        Texture::TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_WRITE |
            Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
    cpu_texture->CopyFrom(yuv_frame_with_buffer->GetPlaneData(0),
                          yuv_frame_with_buffer->GetPlaneSize(0),
                          yuv_frame_with_buffer->GetHeight());
    new_texture->CopyTextureFrom(cpu_texture->GetTexture());
    return new_texture;
  } else {
    auto yuv_graphics = graphics::CreateYUVToBGRAGraphics(dev);
    yuv_graphics->ConvertMemoryToBGRAPrePare(*yuv_frame_with_buffer);
    yuv_graphics->ConvertMemoryToBGRADraw();

    auto new_texture = CreateTexture2D(
        dev, yuv_frame_with_buffer->GetWidth(),
        yuv_frame_with_buffer->GetHeight(),
        mediasdk::PIXEL_FORMAT::PIXEL_FORMAT_BGRA,
        Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE |
            Texture::TEXTURE_USAGE::TEXTURE_USAGE_COPY |
            Texture::TEXTURE_USAGE::TEXTURE_USAGE_RENDER_TARGET);
    new_texture->CopyTextureFrom(
        yuv_graphics->GetOutputTexture()->GetTexture());
    return new_texture;
  }
}

}  // namespace graphics
