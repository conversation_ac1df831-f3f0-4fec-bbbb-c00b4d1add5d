#pragma once
#include <vector>
#include "lines.h"
#include "color_shader.h"
#include "device.h"
using namespace DirectX;

namespace graphics {
class GRAPHICS_EXPORT LinesImpl : public Lines {
 public:
  static std::shared_ptr<LinesImpl> CreateLines(Device& inst);
  LinesImpl(Device& ins);

 public:
  bool DrawTo(const DirectX::XMFLOAT2& vp_size,
              const DirectX::XMMATRIX& world,
              const DirectX::XMMATRIX& view,
              const DirectX::XMMATRIX& projection) override;
  void UpdateLineConf(const LinesConfig& config) override;
  int32_t GetIndexCnt() override;
  void Destroy() override;

  ~LinesImpl() override;

 private:
  struct LinesConfigInternal {
    std::vector<XMFLOAT2> points;
    std::vector<XMFLOAT4> colors;
    int32_t count = 0;
    int32_t cnt = 0;
  };

  ID3D11DeviceContext* GetContext();
  ID3D11Device* GetDevice();
  bool UpdateBufferWithVPSize(const XMFLOAT2& vp_size,
                              const LinesConfigInternal& config);
  bool UpdateVertexBuffer(const LinesConfigInternal& config);

 private:
  Microsoft::WRL::ComPtr<ID3D11Buffer> vs_vertex_buffer_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> index_buffer_;
  std::vector<ColorShader::VERTEXTYPE> vertex_buffer_;

  Microsoft::WRL::ComPtr<ID3D11Buffer> matrix_;
  int32_t cur_index_cnt_ = 0;
  Device& instance_;

  // for dynamic update line config
  std::unique_ptr<LinesConfigInternal> config_;
};
}  // namespace graphics
