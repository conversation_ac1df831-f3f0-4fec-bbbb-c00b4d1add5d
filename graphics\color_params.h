#pragma once

#include <DirectXMath.h>

#include "graphics_export.h"

namespace graphics {

class GRAPHICS_EXPORT ColorParams {
 public:
  ColorParams() = default;

  ColorParams(const ColorParams& other);

  ColorParams& operator=(const ColorParams& other);

  bool operator==(const ColorParams& other) const;

  bool operator!=(const ColorParams& other) const;

  bool IsEmpty() const;

  void Reset();

  float Brightness() const;

  void SetBrightness(float brightness);

  float Saturation() const;

  void SetSaturation(float saturation);

  float Contrast() const;

  void SetContrast(float contrast);

  float HueShift() const;

  void SetHueShift(float hue_shift);

  float Opacity() const;

  void SetOpacity(float opacity);

  float Gamma() const;

  void SetGamma(float gamma);

  void SetAddColor(uint32_t add_color);

  uint32_t GetAddColor() const;

  void SetMulColor(uint32_t mul_color);

  uint32_t GetMulColor() const;

 private:
  void Assign(const ColorParams& other);

  uint32_t add_color_ = 0;
  uint32_t mul_color_ = 0xFFFFFF;
  float brightness_ = 0.0f;
  float saturation_ = 0.0f;
  float contrast_ = 0.0f;
  float hue_shift_ = 0.0f;
  float opacity_ = 1.0f;
  float gamma_ = 0.0f;
};

DirectX::XMMATRIX BuildColorCorrectionMatrix(const ColorParams& color_params);

DirectX::XMMATRIX BuildBrightnessAndContrastMatrix(
    const ColorParams& color_params);

DirectX::XMMATRIX BuildHueShiftMatrix(const ColorParams& color_params);

DirectX::XMMATRIX BuildSaturationMatrix(const ColorParams& color_params);

DirectX::XMMATRIX BuildCorrectionColorMatrix(const ColorParams& color_params);

float GammaCorrection(float input_gamma);
}  // namespace graphics