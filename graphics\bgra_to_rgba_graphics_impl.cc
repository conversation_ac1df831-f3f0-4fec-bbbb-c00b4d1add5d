#include "bgra_to_rgba_graphics_impl.h"

#include <base/logging.h>
#include "bgra_to_rgba_shader.h"
#include "shader_manager.h"

namespace graphics {

BGRAToRGBAGraphicsImpl::BGRAToRGBAGraphicsImpl(Device& ins) : device_(ins) {}

void BGRAToRGBAGraphicsImpl::Destroy() {
  if (graphics_) {
    graphics_->Destroy();
    graphics_ = nullptr;
  }
}

std::shared_ptr<Texture> BGRAToRGBAGraphicsImpl::Draw(Texture& texture) {
  if (!graphics_ || graphics_->GetSize() != texture.GetSize()) {
    graphics_ = nullptr;
    graphics_ = CreateGraphics2D(device_);
    if (!graphics_->CreateNewRGBAGraphics(texture.GetSize().x,
                                          texture.GetSize().y)) {
      LOG(ERROR) << "Failed to Create graphics[" << texture.GetSize().x << ","
                 << texture.GetSize().y << "]";
      return nullptr;
    }
  }

  auto shader = GetShaderManager_()->GetOrCreateShader<BGRAToRGBAShader>(
      device_.shared_from_this());

  if (!shader)
    return nullptr;

  device_.AllowBlend(true);
  if (!graphics_->BeginDraw(false))
    return nullptr;
  {
    graphics::ScopedEndDraw end_draw(*graphics_);

    shader->RenderTexture(texture.GetSRV());
  }
  return graphics_->GetOutputTexture();
}

BGRAToRGBAGraphicsImpl::~BGRAToRGBAGraphicsImpl() {
  BGRAToRGBAGraphicsImpl::Destroy();
}

Microsoft::WRL::ComPtr<ID3D11Device> BGRAToRGBAGraphicsImpl::GetDevice_() {
  return device_.GetDevice();
}

Microsoft::WRL::ComPtr<ID3D11DeviceContext>
BGRAToRGBAGraphicsImpl::GetContext_() {
  return device_.GetContext();
}

ShaderManager* BGRAToRGBAGraphicsImpl::GetShaderManager_() {
  return device_.GetShaderManager();
}

}  // namespace graphics