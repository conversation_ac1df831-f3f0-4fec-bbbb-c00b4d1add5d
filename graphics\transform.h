#pragma once
#include <DirectXMath.h>
#include <string>
#include "graphics_export.h"
#include "mediasdk/public/mediasdk_defines.h"

namespace graphics {

class GRAPHICS_EXPORT Transform {
 public:
  static const Transform& Empty();

  static Transform FromTranslate(const DirectX::XMFLOAT2& trans);

  static Transform FromScale(const DirectX::XMFLOAT2& scale);

  static Transform FromMsTransform(const mediasdk::MSTransform& ms_trans);

  Transform();

  Transform(const Transform& other);

  Transform& operator=(const Transform& other);

  bool operator==(const Transform& other) const;

  bool operator!=(const Transform& other) const;

  bool IsEmpty() const;

  void Reset();

  bool IsFlipH() const;

  bool SetFlipH(bool value);

  bool IsFlipV() const;

  bool SetFlipV(bool value);

  bool SetTransParent(bool value);

  bool GetTransParent() const;

  float GetRotate() const;

  bool SetRotate(float value);

  DirectX::XMFLOAT2 GetScale() const;

  bool SetScale(const DirectX::XMFLOAT2& value);

  DirectX::XMFLOAT2 GetTranslate() const;

  bool SetTranslate(const DirectX::XMFLOAT2& value);

  bool AddTranslate(const DirectX::XMFLOAT2& delta);

  DirectX::XMFLOAT4 GetClip() const;

  bool SetClip(const DirectX::XMFLOAT4& value);

  bool AddClip(const DirectX::XMFLOAT4& value);

  float GetShearAngle() const;

  bool SetShearAngle(float value);

  DirectX::XMFLOAT2 GetShear() const;

  bool SetShear(DirectX::XMFLOAT2& value);

  std::string ToString() const;

  mediasdk::MSTransform ToMSTransform() const;

 private:
  void Assign(const Transform& other);

 private:
  bool transparent_ = true;
  bool flipH_ = false;
  bool flipV_ = false;
  float angle_ = 0;
  DirectX::XMFLOAT2 scale_ = {1.0, 1.0};
  DirectX::XMFLOAT2 translate_ = {};
  DirectX::XMFLOAT4 clip_ = {};
  DirectX::XMFLOAT2 shear_ = {1.f, 1.f};
  float shear_angle_ = 0.f;
};

}  // namespace graphics
