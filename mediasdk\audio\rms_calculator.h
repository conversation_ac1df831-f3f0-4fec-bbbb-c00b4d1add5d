#pragma once

#include <array>
#include <string>

#include "audio/audio_frame.h"
#include "audio_volume.h"
#include "time_helper.h"

namespace mediasdk {

// DoubleAudioVolume is used to calculate and store audio volume metrics
// for both raw and processed audio data
struct DoubleAudioVolume {
  void RMSRaw(const AudioFormat& format, const AudioFrame& frame) {
    raw_handled_ = dev.RMS(format, frame);
  }

  bool RMSHandled(const AudioFormat& format, const AudioFrame& frame) {
    if (raw_handled_) {
      handled.RMSForce(format, frame);
    }
    return raw_handled_;
  }

  bool IsRMSHandled() { return raw_handled_; }

  void ResetRMSHandled() { raw_handled_ = false; }

  bool raw_handled_ = false;
  AudioVolume dev;
  AudioVolume handled;
};

class RMSCalculator {
 public:
  RMSCalculator() = default;
  ~RMSCalculator() = default;

  // Calculate RMS values for raw audio data
  void CalculateRMSRaw(const AudioFormat& format, const AudioFrame& frame);

  // Calculate RMS values for processed audio data
  bool CalculateRMSHandled(const AudioFormat& format, const AudioFrame& frame);

  // Get RMS values for device audio
  const AudioVolume::ResultType& GetDeviceRMS() {
    return volume_calculate_.dev.GetRMS();
  }

  // Get RMS values for handled audio
  const AudioVolume::ResultType& GetHandledRMS() {
    return volume_calculate_.handled.GetRMS();
  }

  // Set calculation interval
  void SetInterval(int32_t interval) {
    volume_calculate_.dev.SetCalcDelta(interval);
  }

  int32_t GetInterval() const { return volume_calculate_.dev.GetCalcDelta(); }

  void NotifyRMS(const std::string& id, bool is_mute, float volume);

  bool IsRMSHandled() { return volume_calculate_.IsRMSHandled(); }

  void ResetRMSHandled() { volume_calculate_.ResetRMSHandled(); }

 private:
  DoubleAudioVolume volume_calculate_;
  int64_t last_log_ts_ms_ = low_precision_milli_now();
  int64_t last_log_buffer_ts_ms_ = low_precision_milli_now();
  std::array<int32_t, 4> volume_buffer_ = {0, 0, 0, 0};
  int32_t log_volume_cnt_ = 0;
  std::string log_volume_str_buffer_;
};

}  // namespace mediasdk