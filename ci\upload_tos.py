import math
import os
import pathlib
import time
import zipfile
import bytedtos


# pip install bytedtos --index-url=https://bytedpypi.byted.org/simple/
def upload_slice_file(file_path):
    Bucket = 'mediasdkclientproduct'
    Access_Key = 'YUS4YO633M26XK9JCHWQ'
    Endpoint = 'tos-cn-north.byted.org'
    Download_url_prefix = 'http://tosv.byted.org/obj/mediasdkclientproduct/'

    tos_client = bytedtos.Client(Bucket, Access_Key, endpoint=Endpoint, timeout=3000, connect_timeout=100)
    total_size = os.path.getsize(file_path)
    chunk_size = 1024 * 1024 * 40
    key = file_path.split("\\")[-1:][0]

    if total_size <= chunk_size:
        #   直接上传
        try:
            tos_client.put_object(key, open(file_path,"rb"))
            return Download_url_prefix + key,''
        except Exception as e:
            return '', "文件上传tos失败：" + str(e)

    payload_id = tos_client.init_upload(key).upload_id
    current_chunk = 1
    total_chunk = math.ceil(total_size / chunk_size)
    msg = "文件大小: " + str(total_size / 1024 / 1024) + 'MB;' + '分片数：' + str(total_chunk)

    tmp_part_list = []
    try:
        while current_chunk <= total_chunk:
            start = (current_chunk - 1) * chunk_size
            end = min(total_size, start + chunk_size)
            file_size = (total_size - end) / 1024 / 1024
            with open(file_path, 'rb') as f:
                f.seek(start)
                if file_size < 10.0:
                    file_chunk_data = f.read(total_size - start)
                    msg += "最后上传的文件分片大小为:" + str((total_size - start) / 1024 / 1024) + "MB"
                    current_chunk = current_chunk + 1
                else:
                    file_chunk_data = f.read(end - start)
            upload_resp = tos_client.upload_part(key, payload_id, str(current_chunk), file_chunk_data)
            tmp_part_list.append(upload_resp.part_number)
            current_chunk = current_chunk + 1
        tos_client.complete_upload(key, payload_id, tmp_part_list)
        return Download_url_prefix + key,''
    except Exception as e:
        return '',msg + str(e)


def upload_tos(filepath):
    global download_url,e
    start = time.time()
    count = 0
    for i in range(2):
        download_url,e = upload_slice_file(file_path=filepath)

        if download_url != '':
            print('==================[ upload tos success,cost ' + str(
                time.time() - start) + ']  seconds]==================', '\ntos download_url:', download_url)
            break
        count += 1
    if count == 2:
        print('==================[ upload tos fail,cost ' + str(time.time() - start) + ']  seconds]==================',
              '\nplease check!')
    return download_url,e

