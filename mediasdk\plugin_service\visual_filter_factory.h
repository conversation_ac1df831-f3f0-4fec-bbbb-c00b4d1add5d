
#pragma once

#include <base/memory/weak_ptr.h>
#include <base/native_library.h>
#include <memory>
#include <string>
#include "mediasdk/public/mediasdk_defines.h"
#include "mediasdk/public/mediasdk_string.hpp"
#include "source_factory.h"

namespace mediasdk {

class VisualFilter;

class VisualFilterFactory : public SourceFactory,
                            public base::SupportsWeakPtr<VisualFilterFactory> {
 public:
  static std::shared_ptr<VisualFilterFactory> Create(
      base::NativeLibrary library,
      std::shared_ptr<PluginInfo> info);

  VisualFilterFactory(base::NativeLibrary library,
                      std::shared_ptr<PluginInfo> info);

  ~VisualFilterFactory();

  // SourceFactory:
  PluginType GetType() const override { return PluginType::kVisualFilter; }

  std::shared_ptr<VisualFilter> CreateFilter(const std::string& json_params);

 private:
  bool Load();

  void Destroy(VisualFilter* filter);

  typedef VisualFilter* (*CreateVisualFilterFunc)(const char*);
  typedef void* (*DestroyVisualFilterFunc)(VisualFilter*);

  CreateVisualFilterFunc create_func_ = nullptr;
  DestroyVisualFilterFunc destroy_func_ = nullptr;
};

}  // namespace mediasdk
