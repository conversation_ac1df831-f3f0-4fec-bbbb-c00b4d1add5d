#pragma once
#include <array>
#include "device.h"
#include "yuv_color_shader.h"
#include "yuv_rectangle.h"

namespace graphics {
// current just for draw to graphics
class YUVRectangleImpl : public YUVRectangle {
 public:
  static std::shared_ptr<YUVRectangleImpl> CreateYUVRectangle(Device& inst);
  YUVRectangleImpl(Device& ins);

 public:
  bool DrawTo(const XMFLOAT2& vpSize,
              const XMMATRIX& view,
              const XMMATRIX& projection) override;

  void UpdateRectangleConf(
      const YUVRectangle::YUVRectangleConfig* rectangleConfig) override;

  int32_t GetIndexCnt() override;
  void Destroy() override;
  ~YUVRectangleImpl() override;

 private:
  struct YUVRectangleConfig_ : public YUVRectangle::YUVRectangleConfig {
    bool show = false;
  };

  bool SetupIAS();

  ID3D11DeviceContext* GetContext_();
  ID3D11Device* GetDevice_();
  bool UpdateBufferWithVPSize_(const XMFLOAT2& vpSize,
                               const YUVRectangleConfig_& rectangleConfig);
  bool UpdateVertexBuffer_(const YUVRectangleConfig_& rectangleConfig);

 private:
  Microsoft::WRL::ComPtr<ID3D11Buffer> vertex_buffer_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> index_buffer_fill_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> index_buffer_lines_;

  Microsoft::WRL::ComPtr<ID3D11Buffer> ps_yuv_param_;

  std::array<YUVColorShader::VERTEXTYPE, 8> vertex_mem_buffer_;

  int32_t cur_index_cnt_ = 0;
  bool fill_ = false;
  Device& instance_;

  // for dynamic update line config
  std::mutex new_config_lock_;
  std::unique_ptr<YUVRectangleConfig_> config_;
  XMMATRIX view_;

  Microsoft::WRL::ComPtr<ID3D11Buffer> matrix_;
  YUVRectangleConfig pre_conf_ = {};
};

}  // namespace graphics
