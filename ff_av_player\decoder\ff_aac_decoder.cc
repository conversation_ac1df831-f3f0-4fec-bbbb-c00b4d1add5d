#include "ff_aac_decoder.h"

std::shared_ptr<AVFrameRAII> FF_AAC_Decoder::Decode(const uint8_t* data,
                                                    int32_t data_size) {
  if (!init_suc_) {
    return nullptr;
  }
  if (!buffered_frame_) {
    AVFrame* frame = av_frame_alloc();
    buffered_frame_ = std::make_shared<AVFrameRAII>(frame);
  }

  auto packet =
      FFDecoder::BuildPacketFromMemory(buffered_packet_, data, data_size);
  if (!packet)
    return nullptr;

  bool retry = false;
  decoder_.SynDecode(*packet, retry);

  if (decoder_.Receive(*buffered_frame_->frame())) {
    return buffered_frame_;
  } else {
    return nullptr;
  }

  return nullptr;
}

bool FF_AAC_Decoder::OpenWithExtraData(const uint8_t* data, int32_t data_size) {
  if (have_try_) {
    return init_suc_;
  }

  init_suc_ = decoder_.Open(AVCodecID::AV_CODEC_ID_AAC,
                            std::vector<uint8_t>(data, data + data_size));
  have_try_ = true;
  return init_suc_;
}

FF_AAC_Decoder::~FF_AAC_Decoder() {
  buffered_frame_ = nullptr;
}
