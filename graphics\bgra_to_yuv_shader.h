#pragma once

#include <mutex>
#include "shader.h"

namespace graphics {
class BGRAToYUVShader : public Shader {
 public:
  static inline const char* SHADER_ID_STRING = "bgra_to_yuv_graphics_shader";

  static std::shared_ptr<Shader> CreateBGRAToNV12Shader(const char* type) {
    if (strcmp(type, SHADER_ID_STRING) == 0) {
      return std::make_shared<BGRAToYUVShader>();
    }
    return nullptr;
  }

  static void EnumPluginsFunction(void* param, EnumPluginCallBack fpn) {
    fpn(param,
        ShaderItem{SHADER_ID_STRING, "shader for draw texture to graphics",
                   BGRAToYUVShader::CreateBGRAToNV12Shader});
  }

 public:
  __declspec(align(16)) struct VS_CONSTANT_BUFFER {
    float cx;
    float cy;
  };

  __declspec(align(16)) struct PS_CONSTANT_BUFFER {
    DirectX::XMFLOAT4 Y;
    DirectX::XMFLOAT4 U;
    DirectX::XMFLOAT4 V;
  };

  bool Init(const std::shared_ptr<Device>&) override;
  void RenderY(Microsoft::WRL::ComPtr<ID3D11Buffer>& vs,
               Microsoft::WRL::ComPtr<ID3D11Buffer>& ps,
               ID3D11ShaderResourceView* views);
  void RenderUV(Microsoft::WRL::ComPtr<ID3D11Buffer>& vs,
                Microsoft::WRL::ComPtr<ID3D11Buffer>& ps,
                ID3D11ShaderResourceView* views);

  void RenderU(Microsoft::WRL::ComPtr<ID3D11Buffer>& vs,
               Microsoft::WRL::ComPtr<ID3D11Buffer>& ps,
               ID3D11ShaderResourceView* views);

  void RenderV(Microsoft::WRL::ComPtr<ID3D11Buffer>& vs,
               Microsoft::WRL::ComPtr<ID3D11Buffer>& ps,
               ID3D11ShaderResourceView* views);

  void Render(Microsoft::WRL::ComPtr<ID3D11Buffer>& vs_buffer,
              Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_buffer,
              ID3D11ShaderResourceView* views,
              Microsoft::WRL::ComPtr<ID3D11PixelShader>& ps_shader,
              Microsoft::WRL::ComPtr<ID3D11VertexShader>& vs_shader);

  void Destroy() override;
  ~BGRAToYUVShader() override;

 private:
  bool DoInit();
  bool CreateIAS_();

 protected:
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice_();
  ID3D11DeviceContext* GetContext_();

  bool try_init_ = false;
  bool init_suc_ = false;
  std::shared_ptr<Device> instance_;
  ID3D11DeviceContext* context_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_shader_y_;
  Microsoft::WRL::ComPtr<ID3D11VertexShader> vs_shader_y_;

  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_shader_uv_;
  Microsoft::WRL::ComPtr<ID3D11VertexShader> vs_shader_uv_;

  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_shader_v_;

  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_shader_u_;

  Microsoft::WRL::ComPtr<ID3D11SamplerState> sampler_;
};
}  // namespace graphics
