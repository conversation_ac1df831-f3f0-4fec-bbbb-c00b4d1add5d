#pragma once

#include <memory>

#include "base/memory/scoped_refptr.h"
#include "base/observer_list_threadsafe.h"
#include "ep_engine_delegate.h"
#include "mediasdk/effect_platform/effect_platform_controller.h"

namespace mediasdk {

namespace ep {
class Engine;
}

class EffectPlatformControllerImpl : public EffectPlatformController,
                                     public ep::EngineDelegate {
 public:
  EffectPlatformControllerImpl();

  ~EffectPlatformControllerImpl() override;

  // Component:
  bool Initialize() override;

  void Uninitialize() override;

  bool EPInitialize(const std::string& json_param) override;

  bool EPUninitialize() override;

  bool EPRegistEventObserver(MediaSDKEPEventObserver* observer) override;

  bool EPUnregistEventObserver(MediaSDKEPEventObserver* observer) override;

  bool EPUpdateConfig(const std::string& user_id,
                      const std::string& hardware_level) override;

  bool EPLoadModels(const std::vector<std::string>& requirments,
                    const std::string& request_id,
                    const std::string& model_name) override;

  bool UseFinder(const ep::Finder& finder) override;

  bool IsLoaded() override;

  // ep::EngineDelegate:
  void OnDownloadModelSuccess(const std::string& request_id) override;

  void OnDownloadModelError(const std::string& request_id,
                            const std::string& error) override;
                            
  void OnDownloadModelProgress(const std::string& request_id,
                               int progress) override;

 private:
  std::shared_ptr<ep::Engine> ep_engine_;
  scoped_refptr<base::ObserverListThreadSafe<MediaSDKEPEventObserver>>
      event_observer_;
};

}  // namespace mediasdk