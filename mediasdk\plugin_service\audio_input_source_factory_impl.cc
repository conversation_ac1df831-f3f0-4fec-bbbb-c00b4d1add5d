#include "audio_input_source_factory_impl.h"

#include <base/logging.h>
#include "mediasdk/mediasdk_thread.h"
#include "mediasdk/public/plugin/audio_input_proxy.h"
#include "mediasdk/public/plugin/audio_input_source.h"

namespace mediasdk {

// static
std::shared_ptr<AudioInputSourceFactory> AudioInputSourceFactoryImpl::Create(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info) {
  auto factory = std::make_shared<AudioInputSourceFactoryImpl>(library, info);
  if (factory && factory->Load()) {
    return factory;
  }

  return nullptr;
}

AudioInputSourceFactoryImpl::AudioInputSourceFactoryImpl(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info)
    : AudioInputSourceFactory(library, info) {}

MediaSDKStringData AudioInputSourceFactoryImpl::EnumAudioInput() {
  if (enum_input_device_func_) {
    return enum_input_device_func_();
  }

  return MediaSDKString().Detach();
}

MediaSDKStringData AudioInputSourceFactoryImpl::GetDefaultAudioInput() {
  if (get_default_audio_input_device_func_) {
    return get_default_audio_input_device_func_();
  }
  return MediaSDKString().Detach();
}

MediaSDKStringData AudioInputSourceFactoryImpl::GetDefaultAudioOutput() {
  if (get_default_audio_output_device_func_) {
    return get_default_audio_output_device_func_();
  }
  return MediaSDKString().Detach();
}

MediaSDKStringData AudioInputSourceFactoryImpl::EnumCaptureAudio() {
  if (enum_capture_audio_device_func_) {
    return enum_capture_audio_device_func_();
  }
  return MediaSDKString().Detach();
}

MediaSDKStringData AudioInputSourceFactoryImpl::EnumRenderAudio() {
  if (enum_render_audio_device_func_) {
    return enum_render_audio_device_func_();
  }
  return MediaSDKString().Detach();
}

AudioInputSourceFactoryImpl::~AudioInputSourceFactoryImpl() {
  DCHECK(sources_.empty());
}

std::function<void(AudioInputSource*)>
AudioInputSourceFactoryImpl::DestroyWithCheck(DestroyAudioSourceFunc func) {
  return [func](AudioInputSource* input) {
    DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);
    return func(input);
  };
}

std::shared_ptr<AudioInputSource> AudioInputSourceFactoryImpl::CreateSource(
    std::shared_ptr<AudioInputProxy> proxy,
    const std::string& json_params) {
  if (create_func_) {
    auto ret = create_func_(proxy.get(), json_params.c_str());
    if (ret) {
      std::shared_ptr<AudioInputSource> source(ret,
                                               DestroyWithCheck(destroy_func_));
      sources_.push_back(source);
      return source;
    }
  }
  return nullptr;
}

void AudioInputSourceFactoryImpl::Destroy(
    std::shared_ptr<AudioInputSource> source) {
  sources_.erase(std::remove(sources_.begin(), sources_.end(), source),
                 sources_.end());
}

bool AudioInputSourceFactoryImpl::Load() {
  create_func_ = reinterpret_cast<CreateAudioSourceFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "CreateAudioInputSource"));

  if (!create_func_) {
    LOG(ERROR) << info_->name.data()
               << ": Failed to get function pointer for CreateAudioInputSource";
    return false;
  }

  destroy_func_ = reinterpret_cast<DestroyAudioSourceFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "DestroyAudioInputSource"));

  if (!destroy_func_) {
    LOG(ERROR)
        << info_->name.data()
        << ": Failed to get function pointer for DestroyAudioInputSource";
    return false;
  }

  enum_input_device_func_ = reinterpret_cast<EnumAudioInputDeviceFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "EnumAudioInputDevice"));

  if (!enum_input_device_func_) {
    LOG(INFO) << info_->name.data()
              << ": Failed to get function pointer for EnumAudioInputDevice";
    // optional interface (APP audio doesn't have)
  }

  get_default_audio_input_device_func_ =
      reinterpret_cast<GetDefaultAudioInputDeviceFunc>(
          base::GetFunctionPointerFromNativeLibrary(
              library_, "GetDefaultAudioInputDevice"));
  if (!get_default_audio_input_device_func_) {
    LOG(INFO) << info_->name.data()
              << ": Has no interface for GetDefaultAudioInputDevice";
    // optional interface
  }

  get_default_audio_output_device_func_ =
      reinterpret_cast<GetDefaultAudioOutDeviceFunc>(
          base::GetFunctionPointerFromNativeLibrary(
              library_, "GetDefaultAudioOutDevice"));
  if (!get_default_audio_output_device_func_) {
    LOG(INFO) << info_->name.data()
              << ": Has no interface for GetDefaultAudioOutDevice";
    // optional interface
  }

  enum_capture_audio_device_func_ =
      reinterpret_cast<EnumCaptureAudioDeviceFunc>(
          base::GetFunctionPointerFromNativeLibrary(library_,
                                                    "EnumCaptureAudioDevice"));
  if (!enum_capture_audio_device_func_) {
    LOG(INFO) << info_->name.data()
              << ": Has no interface for EnumCaptureAudioDevice";
    // optional interface
  }

  enum_render_audio_device_func_ = reinterpret_cast<EnumRenderAudioDeviceFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "EnumRenderAudioDevice"));
  if (!enum_render_audio_device_func_) {
    LOG(INFO) << info_->name.data()
              << ": Has no interface for EnumRenderAudioDevice";
    // optional interface
  }

  return true;
}

}  // namespace mediasdk
