#pragma once
#include <dxgidebug.h>
#include <memory>
#include <mutex>
#include <vector>
#include "device.h"
#ifdef _DEBUG
#include "d3d11_device_context_debug_layer.h"
#endif  // _DEBUG

namespace graphics {

class DeviceImpl final : public Device {
 public:
  DeviceImpl();

  static std::shared_ptr<DeviceImpl> CreateDevice(
      const CreateDeviceOption& opt);
  bool CompileShader(Device::CompileShaderParam& param) override;
  bool D3D11CreateVertexShader(
      const char* shader,
      const char* name,
      Microsoft::WRL::ComPtr<ID3D11VertexShader>& vs,
      Microsoft::WRL::ComPtr<ID3D10Blob>& vsblob) override;
  bool D3D11CompileShader(const char* shader,
                          const char* name,
                          const char* target,
                          Microsoft::WRL::ComPtr<ID3D10Blob>& vs) override;
  void GetViewport(DirectX::XMFLOAT2& pos, DirectX::XMFLOAT2& size) override;
  void SetViewport(const DirectX::XMFLOAT2& pos,
                   const DirectX::XMFLOAT2& size) override;

  void SignalDeviceLostEventObserver(
      const graphics::DeviceLostObserver::DevLostEvent& event) override;

  void AddDeviceLostEventObserver(DeviceLostObserver*) override;

  void RemoveDeviceLostEventObserver(DeviceLostObserver*) override;

  Microsoft::WRL::ComPtr<IDXGIFactory1> GetDXGIFactory() override;

  Microsoft::WRL::ComPtr<IDXGIAdapter> GetAdapter() override;

  LUID GetAdapterLUID() override;

  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice() override;

  Microsoft::WRL::ComPtr<ID3D11DeviceContext> GetContext() override;

  ShaderManager* GetShaderManager() override;

  void AllowBlend(bool allow) override;

  bool D3D11CreatePixelShader(
      const char* shader,
      const char* name,
      Microsoft::WRL::ComPtr<ID3D11PixelShader>& ps) override;

  void Destroy() override;

  void ReportLiveObjects() override;

  bool SetGPUThreadPriority(int32_t priority) override;

  std::string GetDriverDate() const override;

  std::string GetDriverVersion () const override;
  
  void CheckDevLost(HRESULT res) override;

  ~DeviceImpl() override;

 private:
  bool InitWithOPT(const CreateDeviceOption& opt);

 private:
  std::string driver_date_;
  std::string driver_ver_;
  Microsoft::WRL::ComPtr<IDXGIFactory1> factory_;
  Microsoft::WRL::ComPtr<IDXGIAdapter> adapter_;
  Microsoft::WRL::ComPtr<ID3D11Device> device_;
  D3D_FEATURE_LEVEL feature_level_ = D3D_FEATURE_LEVEL_10_0;
  Microsoft::WRL::ComPtr<ID3D11DeviceContext> device_context_;
  Microsoft::WRL::ComPtr<ID3D11BlendState> enable_bs_;
  Microsoft::WRL::ComPtr<ID3D11BlendState> disable_bs_;
  // we only process 2D
  Microsoft::WRL::ComPtr<ID3D11DepthStencilState> disable_ds_;
#ifdef _DEBUG
  D3D11ContextDebugLayer* debug_context_ = nullptr;
  Microsoft::WRL::ComPtr<IDXGIDebug> debug_;
#endif
  std::unique_ptr<ShaderManager> shaders_;
  std::mutex lock_dev_lost_observer_;
  std::vector<DeviceLostObserver*> dev_lost_observer_;
  uint64_t last_report_ts_ = 0;
};
}  // namespace graphics
