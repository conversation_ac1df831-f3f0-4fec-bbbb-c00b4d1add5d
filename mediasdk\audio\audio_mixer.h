#pragma once

#include <map>
#include "audio_frame_observer.h"
#include "audio_pump.h"
#include "audio_volume.h"
#include "base/mediasdk/observer_list_thread_safe_weak.h"
#include "mediasdk/audio/audio_input_manager.h"
#include "mediasdk/audio/mixed_audio_observer.h"
#include "audio_track.h"

namespace mediasdk {

class AudioMixer : public AudioPump::Observer,
                   public AudioTrackMixedFrameObserver {
 public:
  AudioMixer();

  ~AudioMixer();

  void AddOutputObserver(uint32_t track_id,
                         std::shared_ptr<MixedAudioObserver> observer);

  void RemoveOutputObserver(uint32_t track_id,
                            std::shared_ptr<MixedAudioObserver> observer);

  void OnMixedAudioFrame(uint32_t track_id, const AudioFrame& frame) override;

 protected:
  // AudioPump::Observer:
  void OnPump(const mediasdk::TimeDuration& duraiton,
              std::shared_ptr<AudioInputManager> manager) override;

 private:
  using ObserverListType = base::ObserverListThreadSafeWeak<MixedAudioObserver>;
  std::shared_ptr<AudioInputManager> audio_input_manager_;
  std::map<uint32_t, ObserverListType> observer_list_;
};

}  // namespace mediasdk
