﻿#include "graphics_impl.h"

#include <base/check.h>
#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include "DXSimpleMath.h"
#include "border_shader.h"
#include "clip_mask_shader.h"
#include "color_lut_shader.h"
#include "color_shader.h"
#include "corner_shader.h"
#include "graphics_utils.h"
#include "partial_scale_shader.h"
#include "point_nine_shader.h"
#include "shader_manager.h"
#include "texture_shader.h"
#include "transform_calc.h"
#include "yuv_color_shader.h"
#include "gradual_rectangle_impl.h" // Added for GradualRectangle

using namespace Microsoft::WRL;

namespace {
constexpr bool kUseAutoLock = false;
}

namespace graphics {

std::shared_ptr<GraphicsImpl> GraphicsImpl::CreateGraphics2D(Device& ins) {
  return std::make_shared<GraphicsImpl>(ins);
}

GraphicsImpl::GraphicsImpl(Device& ins)
    : device_(ins), context_(device_.GetContext().Get()) {}

bool GraphicsImpl::BeginDraw(bool clear,
                             XMFLOAT2* target_size,
                             const DirectX::XMFLOAT4& bk_color) {
  if (IsEmpty()) {
    DCHECK(false && "not valid");
    return false;
  }
  if (kUseAutoLock) {
    if (GetOutputTexture()->IsKeyedMutex()) {
      if (!GetOutputTexture()->AcquireKeyedAccess(0, INFINITE)) {
        DCHECK(false);
        return false;
      }
    }
  }
  auto context = GetContext_();
  context->OMGetRenderTargets(1, &previous_rtv_, &previous_dsv_);
  device_.GetViewport(previous_view_port_[0], previous_view_port_[1]);

  ID3D11RenderTargetView* const rtv[1] = {render_view_.Get()};
  context->OMSetRenderTargets(1, rtv, depth_view_.Get());
  if (target_size) {
    device_.SetViewport(XMFLOAT2_EMPTY, *target_size);
  } else {
    device_.SetViewport(XMFLOAT2_EMPTY, GetSize());
  }
  if (clear) {
    float color[4] = {bk_color.x, bk_color.y, bk_color.z, bk_color.w};
    context->ClearRenderTargetView(render_view_.Get(), color);
    context->ClearDepthStencilView(
        depth_view_.Get(), D3D11_CLEAR_DEPTH | D3D11_CLEAR_STENCIL, 1.0F, 0);
  }

  return true;
}

void GraphicsImpl::EndDraw() {
  if (kUseAutoLock) {
    if (GetOutputTexture()->IsKeyedMutex()) {
      GetOutputTexture()->ReleaseKeyedAccess();
    }
  }

  auto context = GetContext_();
  ID3D11Buffer* const
      buffer[D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT] = {NULL};
  context->PSSetConstantBuffers(
      0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1, buffer);
  context->VSSetConstantBuffers(
      0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1, buffer);
  context->CSSetConstantBuffers(
      0, D3D11_COMMONSHADER_CONSTANT_BUFFER_API_SLOT_COUNT - 1, buffer);
  ID3D11ShaderResourceView* const
      srv[D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT] = {NULL};
  context->VSSetShaderResources(
      0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1, srv);
  context->PSSetShaderResources(
      0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1, srv);
  context->CSSetShaderResources(
      0, D3D11_COMMONSHADER_INPUT_RESOURCE_SLOT_COUNT - 1, srv);
  ID3D11SamplerState* const ss[D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT] = {NULL};
  context->PSSetSamplers(0, D3D11_COMMONSHADER_SAMPLER_SLOT_COUNT - 1, ss);
  ID3D11RenderTargetView* const rtv[D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT] = {
      previous_rtv_.Get()};
  context->OMSetRenderTargets(D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT, rtv,
                              previous_dsv_.Get());

  previous_rtv_.Reset();
  previous_dsv_.Reset();
  device_.SetViewport(previous_view_port_[0], previous_view_port_[1]);
  ClearBufferedBuffer();
}

void GraphicsImpl::ClearBufferedBuffer() {
  auto it = buffered_texture_buffers_.begin();
  while (it != buffered_texture_buffers_.end()) {
    if (!it->second) {
      break;
    }
    it->second->Skip();
    if (it->second->GetSkipCnt() >= 60) {
      it->second->Destroy();
      it->second = nullptr;
      it = buffered_texture_buffers_.erase(it);
    } else {
      ++it;
    }
  }
}

void GraphicsImpl::DestroyBuffer() {
  for (auto& [it, buffer] : buffered_texture_buffers_) {
    if (!buffer)
      continue;
    buffer->Destroy();
  }
  buffered_texture_buffers_.clear();
}

void GraphicsImpl::DrawRectangles(graphics::Rectangle** rectangle, int cnt) {
  if (!DrawRectanglesPrepare(rectangle, cnt)) {
    return;
  }
  DrawRectanglesDraw(rectangle, cnt);
}

void GraphicsImpl::DrawYUVColorRectangle(YUVRectangle& rectangle) {
  auto shader = GetShaderManager_()->GetOrCreateShader<YUVColorShader>(
      device_.shared_from_this());

  if (!shader)
    return;
  rectangle.DrawTo(GetSize(), view_matrix_, projection_matrix_);
  shader->Render(0, rectangle.GetIndexCnt());
}

bool GraphicsImpl::DrawGradualRectanglePrepare(GradualRectangle& rectangle) {
  if (!rectangle.DrawTo(GetSize(), view_matrix_, projection_matrix_))
  return false;
return true;
}

void GraphicsImpl::DrawGradualRectangleDraw(GradualRectangle& rectangle) {
  auto shader = GetShaderManager_()->GetOrCreateShader<ColorShader>(
    device_.shared_from_this());
if (!shader)
  return;
shader->Render(0, rectangle.GetIndexCnt());
}

// Added for GradualRectangle
void GraphicsImpl::DrawGradualRectangle(GradualRectangle& conf) {
  if (!DrawGradualRectanglePrepare(conf)) {
    return;
  }
  DrawGradualRectangleDraw(conf);
}

bool GraphicsImpl::DrawRectanglesPrepare(graphics::Rectangle** rectangle,
                                         int cnt) {
  for (int i = 0; i < cnt; i++) {
    if (!rectangle[i]->DrawTo(GetSize(), view_matrix_, projection_matrix_))
      return false;
  }
  return true;
}

void GraphicsImpl::DrawRectanglesDraw(graphics::Rectangle** rectangle,
                                      int cnt) {
  auto shader = GetShaderManager_()->GetOrCreateShader<ColorShader>(
      device_.shared_from_this());
  if (!shader)
    return;

  for (int i = 0; i < cnt; i++) {
    shader->Render(0, (rectangle[i])->GetIndexCnt());
  }
}

void GraphicsImpl::DrawRectangle(graphics::Rectangle& rectangle) {
  if (!DrawRectanglePrepare(rectangle)) {
    return;
  }
  DrawRectangleDraw(rectangle);
}

bool GraphicsImpl::DrawRectanglePrepare(graphics::Rectangle& rectangle) {
  if (!rectangle.DrawTo(GetSize(), view_matrix_, projection_matrix_))
    return false;
  return true;
}

void GraphicsImpl::DrawRectangleDraw(graphics::Rectangle& rectangle) {
  auto shader = GetShaderManager_()->GetOrCreateShader<ColorShader>(
      device_.shared_from_this());
  if (!shader)
    return;
  shader->Render(0, rectangle.GetIndexCnt());
}

bool GraphicsImpl::DrawLinesPrepare(Lines& lines) {
  if (!lines.DrawTo(GetSize(), graphics_world_matrix_, view_matrix_,
                    projection_matrix_))
    return false;
  return true;
}

void GraphicsImpl::DrawLinesDraw(Lines& lines) {
  auto shader = GetShaderManager_()->GetOrCreateShader<ColorShader>(
      device_.shared_from_this());
  if (!shader)
    return;

  shader->Render(0, lines.GetIndexCnt());
}

void GraphicsImpl::DrawVLine(const XMFLOAT4& line,
                             const XMFLOAT4& color,
                             const float thinkness) {
  auto rectangle = CreateRectangle(device_);
  {
    Rectangle::RectangleConfig config;
    config.rotate = 0;
    config.color = color;
    config.fill = true;

    config.top_left = XMFLOAT2{line.x - thinkness / 2, line.y};
    config.bottom_right = XMFLOAT2{line.z + thinkness / 2, line.w};
    config.vp_size = GetSize();
    rectangle->UpdateRectangleConf(&config);
  }
  DrawRectangle(*rectangle.get());
}

void GraphicsImpl::DrawVDottedLine(const XMFLOAT4& line,
                                   const XMFLOAT4& color,
                                   const float thinkness) {
  auto lines = GetDottedLines(line);
  for (auto line : lines) {
    DrawVLine(line, color, thinkness);
  }
}

void GraphicsImpl::DrawHLine(const XMFLOAT4& line,
                             const XMFLOAT4& color,
                             const float thinkness) {
  auto rectangle = CreateRectangle(device_);
  {
    Rectangle::RectangleConfig config;
    config.rotate = 0;
    config.color = color;
    config.fill = true;

    config.top_left = XMFLOAT2{line.x, line.y - thinkness / 2};
    config.bottom_right = XMFLOAT2{line.z, line.w + thinkness / 2};
    config.vp_size = GetSize();
    rectangle->UpdateRectangleConf(&config);
  }
  DrawRectangle(*rectangle.get());
}

void GraphicsImpl::DrawHDottedLine(const XMFLOAT4& line,
                                   const XMFLOAT4& color,
                                   const float thinkness) {
  auto lines = GetDottedLines(line);
  for (auto line : lines) {
    DrawHLine(line, color, thinkness);
  }
}

void GraphicsImpl::DrawLines(Lines& lines) {
  if (!DrawLinesPrepare(lines))
    return;
  DrawLinesDraw(lines);
}

void GraphicsImpl::DrawBGRATexture(Texture& texture,
                                   const Transform& transform,
                                   const bool tone_mapping) {
  DCHECK(texture.GetUsage() &
         Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
  switch (const auto format = texture.GetDesc().Format) {
    case DXGI_FORMAT_B5G6R5_UNORM:
    case DXGI_FORMAT_B5G5R5A1_UNORM:
    case DXGI_FORMAT_B8G8R8X8_UNORM:
    case DXGI_FORMAT_B8G8R8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM_SRGB:
    case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
    case DXGI_FORMAT_R10G10B10A2_UNORM:
    case DXGI_FORMAT_R16G16B16A16_FLOAT:
    case DXGI_FORMAT_R16G16B16A16_UNORM:
      DrawBGRATextureInternal(texture, format, transform, false, tone_mapping);
      break;
    default:
      LOG(ERROR) << "Not Impl [" << format << "]";
      DCHECK(false);
  }
}

void GraphicsImpl::DrawBGRATextureChromaKeyParams(
    Texture& texture,
    const Transform& transform,
    const ChromaKeyParams& chroma_key_params) {
  DCHECK(texture.GetUsage() &
         Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
  switch (const auto format = texture.GetDesc().Format) {
    case DXGI_FORMAT_B5G6R5_UNORM:
    case DXGI_FORMAT_B5G5R5A1_UNORM:
    case DXGI_FORMAT_B8G8R8X8_UNORM:
    case DXGI_FORMAT_B8G8R8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM_SRGB:
    case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
    case DXGI_FORMAT_R10G10B10A2_UNORM:
    case DXGI_FORMAT_R16G16B16A16_FLOAT:
    case DXGI_FORMAT_R16G16B16A16_UNORM:
      DrawBGRAChromaKeyParamsTextureInternal(texture, format, transform,
                                             chroma_key_params);
      break;
    default:
      LOG(ERROR) << "Not Impl [" << format << "]";
      DCHECK(false);
  }
}

void GraphicsImpl::DrawPartialScaleTexture(Texture& texture,
                                           const Transform& transform,
                                           const PartialScaleParams& params) {
  DCHECK(texture.GetUsage() &
         Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
  switch (const auto format = texture.GetDesc().Format) {
    case DXGI_FORMAT_B5G6R5_UNORM:
    case DXGI_FORMAT_B5G5R5A1_UNORM:
    case DXGI_FORMAT_B8G8R8X8_UNORM:
    case DXGI_FORMAT_B8G8R8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM_SRGB:
    case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
    case DXGI_FORMAT_R10G10B10A2_UNORM:
    case DXGI_FORMAT_R16G16B16A16_FLOAT:
    case DXGI_FORMAT_R16G16B16A16_UNORM:
      DrawPartialScaleTextureInternal(texture, transform, params);
      break;
    default:
      LOG(ERROR) << "Not Impl [" << format << "]";
      DCHECK(false);
  }
}

void GraphicsImpl::DrawBGRATextureForceAlpha(Texture& texture,
                                             const Transform& transform,
                                             bool force_tex_alpha) {
  DCHECK(texture.GetUsage() &
         Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
  switch (const auto format = texture.GetDesc().Format) {
    case DXGI_FORMAT_B5G6R5_UNORM:
    case DXGI_FORMAT_B5G5R5A1_UNORM:
    case DXGI_FORMAT_B8G8R8X8_UNORM:
    case DXGI_FORMAT_B8G8R8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM_SRGB:
    case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
    case DXGI_FORMAT_R10G10B10A2_UNORM:
    case DXGI_FORMAT_R16G16B16A16_FLOAT:
    case DXGI_FORMAT_R16G16B16A16_UNORM:
      DrawBGRATextureInternal(texture, format, transform, force_tex_alpha);
      break;
    default:
      LOG(ERROR) << "Not Impl [" << format << "]";
      DCHECK(false);
  }
}

bool GraphicsImpl::DrawBGRATextureSampleFlags(Texture& texture,
                                              const Transform& transform,
                                              bool sample_anisotropic) {
  DCHECK(texture.GetUsage() &
         Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
  bool format_support = false;
  switch (const auto format = texture.GetDesc().Format) {
    case DXGI_FORMAT_B5G6R5_UNORM:
    case DXGI_FORMAT_B5G5R5A1_UNORM:
    case DXGI_FORMAT_B8G8R8X8_UNORM:
    case DXGI_FORMAT_B8G8R8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM_SRGB:
    case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
    case DXGI_FORMAT_R10G10B10A2_UNORM:
    case DXGI_FORMAT_R16G16B16A16_FLOAT:
    case DXGI_FORMAT_R16G16B16A16_UNORM:
      DrawBGRATextureInternal(texture, format, transform, false, true,
                              sample_anisotropic);
      format_support = true;
      break;
    default:
      LOG(ERROR) << "Not Impl [" << format << "]";
      DCHECK(false);
  }
  return format_support;
}

void GraphicsImpl::DrawBGRATextureColorParams(Texture& texture,
                                              const Transform& transform,
                                              const ColorParams& color_params) {
  DCHECK(texture.GetUsage() &
         Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
  switch (const auto format = texture.GetDesc().Format) {
    case DXGI_FORMAT_B5G6R5_UNORM:
    case DXGI_FORMAT_B5G5R5A1_UNORM:
    case DXGI_FORMAT_B8G8R8X8_UNORM:
    case DXGI_FORMAT_B8G8R8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM_SRGB:
    case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
    case DXGI_FORMAT_R10G10B10A2_UNORM:
    case DXGI_FORMAT_R16G16B16A16_FLOAT:
    case DXGI_FORMAT_R16G16B16A16_UNORM:
      DrawBGRAColorParamsTextureInternal(texture, format, transform,
                                         color_params);
      break;
    default:
      LOG(ERROR) << "Not Impl [" << format << "]";
      DCHECK(false);
  }
}

void GraphicsImpl::DrawRGBATextureLuminanceMapParams(
    Texture& texture,
    const Transform& transform,
    const LuminanceMapParams& luminance_map_params) {
  DCHECK(texture.GetUsage() &
         Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
  switch (const auto format = texture.GetDesc().Format) {
    case DXGI_FORMAT_B5G6R5_UNORM:
    case DXGI_FORMAT_B5G5R5A1_UNORM:
    case DXGI_FORMAT_B8G8R8X8_UNORM:
    case DXGI_FORMAT_B8G8R8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM_SRGB:
    case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
    case DXGI_FORMAT_R10G10B10A2_UNORM:
    case DXGI_FORMAT_R16G16B16A16_FLOAT:
    case DXGI_FORMAT_R16G16B16A16_UNORM:
      DrawBGRATextureLuminanceMapParamsInternal(texture, format, transform,
                                                luminance_map_params);
      break;
    default:
      LOG(ERROR) << "Not Impl [" << format << "]";
      DCHECK(false);
  }
}

void GraphicsImpl::DrawBGRATextureSharpnessParams(
    Texture& texture,
    const Transform& transform,
    const SharpnessParams& sharpness_params) {
  DCHECK(texture.GetUsage() &
         Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
  const DXGI_FORMAT format = texture.GetDesc().Format;
  switch (format) {
    case DXGI_FORMAT_B8G8R8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM:
    case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
      DrawBGRASharpnessParamsTextureInternal(texture, format, transform,
                                             sharpness_params);
      break;
    default:
      LOG(ERROR) << "Not Impl [" << format << "]";
      DCHECK(false);
  }
}

void GraphicsImpl::DrawBGRAPointNineTexture(Texture& texture,
                                            const Transform& transform,
                                            const PointNine& point_nine) {
  DCHECK(texture.GetUsage() &
         Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
  DXGI_FORMAT format = texture.GetDesc().Format;
  switch (format) {
    case DXGI_FORMAT_B8G8R8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM:
    case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
      DrawBGRAPointNineTextureInternal(texture, format, transform, point_nine);
      break;
    default:
      LOG(ERROR) << "Not Impl [" << format << "]";
      DCHECK(false);
  }
}

void GraphicsImpl::DrawCornerTexture(Texture& texture,
                                     const XMFLOAT4& corner_radius,
                                     const float& ratio,
                                     const Transform& transform,
                                     bool refer_width,
                                     bool fixed_radius) {
  DCHECK(texture.GetUsage() &
         Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
  DCHECK(IsGreaterThan(ratio, 0.0f));
  XMFLOAT4 corner_radius_ = corner_radius / ratio;
  DXGI_FORMAT format = texture.GetDesc().Format;
  switch (format) {
    case DXGI_FORMAT_B8G8R8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM:
    case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
    case DXGI_FORMAT_R16G16B16A16_FLOAT:
      if (corner_radius == XMFLOAT4_EMPTY) {
        auto trans = graphics::Transform{};
        trans.SetScale(DirectX::XMFLOAT2{ratio, ratio});
        DrawBGRATextureInternal(texture, format, trans);
      } else {
        DrawCornerTextureInternal(texture, corner_radius, ratio, transform,
                                  refer_width, fixed_radius);
      }
      break;
    default:
      LOG(ERROR) << "Not Impl [" << format << "]";
      DCHECK(false);
  }
}

void GraphicsImpl::DrawBorderTexture(Texture& texture,
                                     const XMFLOAT4& color,
                                     const XMFLOAT2& width,
                                     const float& ratio,
                                     const Transform& transform) {
  DCHECK(texture.GetUsage() &
         Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
  DCHECK(IsGreaterThan(ratio, 0.0f));
  DXGI_FORMAT format = texture.GetDesc().Format;
  XMFLOAT2 width_ = width / ratio;
  switch (format) {
    case DXGI_FORMAT_B8G8R8A8_UNORM:
    case DXGI_FORMAT_R8G8B8A8_UNORM:
    case DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
    case DXGI_FORMAT_R16G16B16A16_FLOAT:
      if (width_.x < 1.0f || width_.y < 1.0f) {
        auto trans = graphics::Transform{};
        trans.SetScale(DirectX::XMFLOAT2{ratio, ratio});
        DrawBGRATextureInternal(texture, format, trans);
      } else {
        DrawBorderTextureInternal(texture, color, width_, ratio, transform);
      }
      break;
    default:
      LOG(ERROR) << "Not Impl [" << format << "]";
      DCHECK(false);
  }
}

void GraphicsImpl::DrawLutTexture(Texture& texture,
                                  Texture* texture_1dlut,
                                  Texture* texture_3dlut,
                                  const XMFLOAT4& domain_min,
                                  const XMFLOAT4& domain_max,
                                  const Transform& transform,
                                  float amount,
                                  bool pass_through_alpha) {
  DrawLutTextureInternal(texture, texture_1dlut, texture_3dlut, domain_min,
                         domain_max, transform, amount, pass_through_alpha);
}

void GraphicsImpl::DrawBGRAClipMask(const DirectX::XMFLOAT2& texture_size,
                                    const Transform& transform,
                                    const DirectX::XMFLOAT4& clip_mask) {
  DrawBGRAClipMaskInternal(&clip_mask, texture_size, transform, clip_mask);
}

void GraphicsImpl::DrawY8Texture(Texture& texture, const Transform& transform) {
  DCHECK(texture.GetUsage() &
         Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);
  DXGI_FORMAT format = texture.GetDesc().Format;
  switch (format) {
    case DXGI_FORMAT_R8_UNORM:
      DrawBGRATextureInternal(texture, format, transform);
      break;
    default:
      LOG(ERROR) << "Not Impl [" << format << "]";
      DCHECK(false);
  }
}

BufferedTextureBuffer* GraphicsImpl::GetBufferedBuffer(
    Texture* texture,
    const Transform* trans,
    const PointNine& point_nine,
    const CornerBuffer& corner_buffer,
    const BorderBuffer& border_buffer,
    const DirectX::XMFLOAT4& clip_mask_param,
    bool force_tex_alpha,
    const ColorParams& color_params,
    const ChromaKeyParams& chroma_key_params,
    const SharpnessParams& sharpness_params,
    const ColorLutParams& color_lut_params,
    const LuminanceMapParams& luminance_map_params,
    const PartialScaleParams& partial_scale_params) {
  auto it = buffered_texture_buffers_.find(texture);
  BufferedTextureBuffer* ret = nullptr;
  if (it == buffered_texture_buffers_.end()) {
    auto buffer = CreateBufferedBuffer();
    if (!buffer) {
      ret = nullptr;
    } else {
      buffered_texture_buffers_[texture] = std::move(buffer);
      ret = buffered_texture_buffers_[texture].get();
    }
  } else {
    ret = it->second.get();
    if (!ret) {
      it->second = CreateBufferedBuffer();
      ret = it->second.get();
    }
  }

  if (ret) {
    ret->TryUpdate(texture->GetSize(), GetSize(), GetContext_(),
                   GetViewMatrix(), GetProjectionMatrix(),
                   texture->GetToneMapParam(), *trans, point_nine,
                   corner_buffer, border_buffer, clip_mask_param,
                   force_tex_alpha, color_params, chroma_key_params,
                   sharpness_params, luminance_map_params);
  }

  return ret;
}

BufferedTextureBuffer* GraphicsImpl::GetNoTextureBufferedBuffer(
    const void* handle,
    const DirectX::XMFLOAT2& texture_size,
    const Transform* trans,
    const PointNine& point_nine,
    const CornerBuffer& corner_buffer,
    const BorderBuffer& border_buffer,
    const DirectX::XMFLOAT4& clip_mask_param) {
  auto it = buffered_texture_buffers_.find(handle);
  BufferedTextureBuffer* ret = nullptr;
  if (it == buffered_texture_buffers_.end()) {
    buffered_texture_buffers_[handle] = CreateBufferedBuffer();
    ret = buffered_texture_buffers_[handle].get();

  } else {
    ret = it->second.get();
  }
  ret->TryUpdate(texture_size, GetSize(), GetContext_(), GetViewMatrix(),
                 GetProjectionMatrix(), 0, *trans, point_nine, corner_buffer,
                 border_buffer, clip_mask_param);
  return ret;
}

BufferedTextureBuffer* GraphicsImpl::GetLutBufferedBuffer(
    Texture* texture,
    const Transform* trans,
    const ColorLutParams& color_lut_params) {
  auto it = buffered_texture_buffers_.find(texture);
  BufferedTextureBuffer* ret = nullptr;
  if (it == buffered_texture_buffers_.end()) {
    buffered_texture_buffers_[texture] = CreateBufferedBuffer();
    ret = buffered_texture_buffers_[texture].get();
  } else {
    ret = it->second.get();
    if (!ret) {
      it->second = CreateBufferedBuffer();
      ret = it->second.get();
    }
  }
  if (ret) {
    ret->TryUpdate(texture->GetSize(), GetSize(), GetContext_(),
                   GetViewMatrix(), GetProjectionMatrix(), *trans,
                   color_lut_params);
  }
  return ret;
}

BufferedTextureBuffer* GraphicsImpl::GetScaleBufferedBuffer(
    Texture* texture,
    const Transform* trans,
    const PartialScaleParams& scale_params) {
  auto it = buffered_texture_buffers_.find(texture);
  BufferedTextureBuffer* ret = nullptr;
  if (it == buffered_texture_buffers_.end()) {
    buffered_texture_buffers_[texture] = CreateBufferedBuffer();
    ret = buffered_texture_buffers_[texture].get();
  } else {
    ret = it->second.get();
    if (!ret) {
      it->second = CreateBufferedBuffer();
      ret = it->second.get();
    }
  }
  if (ret) {
    ret->TryUpdate(texture->GetSize(), GetSize(), GetContext_(),
                   GetViewMatrix(), GetProjectionMatrix(), *trans,
                   scale_params);
  }
  return ret;
}

void GraphicsImpl::ReleaseBufferedBuffer(Texture* texture) {}

std::unique_ptr<BufferedTextureBuffer> GraphicsImpl::CreateBufferedBuffer() {
  std::unique_ptr<BufferedTextureBuffer> ret =
      std::make_unique<BufferedTextureBuffer>();

  TextureShader::CreateInputBuffer(GetDevice().GetDevice(), ret->crop_buffer_,
                                   ret->matrix_buffer_, ret->ps_buffer_);

  TextureShader::VERTEXTYPE vertex_types[4] = {};
  D3D11_BUFFER_DESC desc = {};
  desc.Usage = D3D11_USAGE_DYNAMIC;
  desc.ByteWidth = sizeof(TextureShader::VERTEXTYPE) * 4;
  desc.BindFlags = D3D11_BIND_VERTEX_BUFFER;
  desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  desc.MiscFlags = 0;
  desc.StructureByteStride = 0;
  D3D11_SUBRESOURCE_DATA data = {};
  data.pSysMem = vertex_types;
  data.SysMemPitch = 0;
  data.SysMemSlicePitch = 0;
  HRESULT res =
      device_.GetDevice()->CreateBuffer(&desc, &data, &ret->vertex_buffer_);
  if (res != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(res).c_str());
    return nullptr;
  }
  ULONG indices[6] = {
      0, 1, 2, 0, 2, 3,
  };
  desc = {};
  desc.Usage = D3D11_USAGE_DEFAULT;
  desc.ByteWidth = sizeof(ULONG) * 6;
  desc.BindFlags = D3D11_BIND_INDEX_BUFFER;
  desc.CPUAccessFlags = 0;
  desc.MiscFlags = 0;
  ZeroMemory(&data, sizeof(data));
  data.pSysMem = indices;
  data.SysMemPitch = 0;
  data.SysMemSlicePitch = 0;
  res = device_.GetDevice()->CreateBuffer(&desc, &data, &ret->index_buffer_);
  if (res != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                     GetErrorString(res).c_str());
    DCHECK(false);
    return nullptr;
  }
  return std::move(ret);
}

void GraphicsImpl::DrawBGRATextureInternal(Texture& texture,
                                           DXGI_FORMAT format,
                                           const Transform& transform,
                                           bool force_tex_alpha,
                                           bool tone_mapping,
                                           bool sample_anisotropic) {
  auto shader = GetShaderManager_()->GetOrCreateShader<TextureShader>(
      device_.shared_from_this());
  if (!shader) {
    return;
  }

  device_.AllowBlend(transform.GetTransParent());

  if (!texture.SetupVertex()) {
    return;
  }

  auto buffer = GetBufferedBuffer(
      &texture, &transform,
      PointNine::CreatePointNine(XMFLOAT2_EMPTY, XMFLOAT4_EMPTY, false),
      CornerBuffer::CreateCorner(XMFLOAT4_EMPTY, XMFLOAT4_EMPTY, 1.0f),
      BorderBuffer::CreateBorder(XMFLOAT4_EMPTY, XMFLOAT2_EMPTY,
                                 graphics::Transform()),
      XMFLOAT4_EMPTY, force_tex_alpha);
  if (!buffer) {
    return;
  }

  if (sample_anisotropic) {
    shader->RenderBGRA(texture.GetSRV(), buffer->vertex_buffer_,
                       buffer->index_buffer_, buffer->crop_buffer_,
                       buffer->matrix_buffer_, buffer->ps_buffer_, format,
                       texture.GetToneMapType(), tone_mapping, ANISOTROPIC);
  } else {
    shader->RenderBGRA(texture.GetSRV(), buffer->vertex_buffer_,
                       buffer->index_buffer_, buffer->crop_buffer_,
                       buffer->matrix_buffer_, buffer->ps_buffer_, format,
                       texture.GetToneMapType(), tone_mapping, LINER);
  }
}

void GraphicsImpl::DrawBGRAChromaKeyParamsTextureInternal(
    Texture& texture,
    DXGI_FORMAT format,
    const Transform& transform,
    const ChromaKeyParams& chroma_key_params) {
  auto shader = GetShaderManager_()->GetOrCreateShader<TextureShader>(
      device_.shared_from_this());
  if (!shader) {
    return;
  }

  device_.AllowBlend(false);

  if (!texture.SetupVertex()) {
    return;
  }

  auto buffer = GetBufferedBuffer(
      &texture, &transform,
      PointNine::CreatePointNine(XMFLOAT2_EMPTY, XMFLOAT4_EMPTY, false),
      CornerBuffer::CreateCorner(XMFLOAT4_EMPTY, XMFLOAT4_EMPTY, 1.0f),
      BorderBuffer::CreateBorder(XMFLOAT4_EMPTY, XMFLOAT2_EMPTY,
                                 graphics::Transform()),
      XMFLOAT4_EMPTY, false, ColorParams(), chroma_key_params);
  if (!buffer) {
    return;
  }
  shader->RenderBGRA(texture.GetSRV(), buffer->vertex_buffer_,
                     buffer->index_buffer_, buffer->crop_buffer_,
                     buffer->matrix_buffer_, buffer->ps_buffer_, format,
                     texture.GetToneMapType());
}

void GraphicsImpl::DrawPartialScaleTextureInternal(
    Texture& texture,
    const Transform& transform,
    const PartialScaleParams& params) {
  auto shader = GetShaderManager_()->GetOrCreateShader<PartialScaleShader>(
      device_.shared_from_this());
  if (!shader) {
    LOG(ERROR) << "Failed to GetOrCreateShader<PartialScaleShader>";
    return;
  }
  device_.AllowBlend(transform.GetTransParent());

  if (!texture.SetupVertex()) {
    LOG(ERROR) << "Failed to SetupVertex";
    return;
  }

  auto buffer = GetScaleBufferedBuffer(&texture, &transform, params);
  if (!buffer) {
    return;
  }
  shader->Render(texture.GetSRV(), buffer->vertex_buffer_,
                 buffer->index_buffer_, buffer->crop_buffer_,
                 buffer->matrix_buffer_, buffer->ps_buffer_);
}

void GraphicsImpl::DrawBGRAColorParamsTextureInternal(
    Texture& texture,
    DXGI_FORMAT format,
    const Transform& transform,
    const ColorParams& color_params) {
  auto shader = GetShaderManager_()->GetOrCreateShader<TextureShader>(
      device_.shared_from_this());
  if (!shader) {
    return;
  }

  device_.AllowBlend(transform.GetTransParent());

  if (!texture.SetupVertex()) {
    return;
  }

  auto buffer = GetBufferedBuffer(
      &texture, &transform,
      PointNine::CreatePointNine(XMFLOAT2_EMPTY, XMFLOAT4_EMPTY, false),
      CornerBuffer::CreateCorner(XMFLOAT4_EMPTY, XMFLOAT4_EMPTY, 1.0f),
      BorderBuffer::CreateBorder(XMFLOAT4_EMPTY, XMFLOAT2_EMPTY,
                                 graphics::Transform()),
      XMFLOAT4_EMPTY, false, color_params);
  if (!buffer) {
    return;
  }

  shader->RenderBGRA(texture.GetSRV(), buffer->vertex_buffer_,
                     buffer->index_buffer_, buffer->crop_buffer_,
                     buffer->matrix_buffer_, buffer->ps_buffer_, format,
                     texture.GetToneMapType());
}

void GraphicsImpl::DrawBGRASharpnessParamsTextureInternal(
    Texture& texture,
    DXGI_FORMAT format,
    const Transform& transform,
    const SharpnessParams& sharpness_params) {
  const auto shader = GetShaderManager_()->GetOrCreateShader<TextureShader>(
      device_.shared_from_this());
  if (!shader) {
    return;
  }

  device_.AllowBlend(false);

  if (!texture.SetupVertex()) {
    return;
  }

  const auto buffer = GetBufferedBuffer(
      &texture, &transform,
      PointNine::CreatePointNine(XMFLOAT2_EMPTY, XMFLOAT4_EMPTY, false),
      CornerBuffer::CreateCorner(XMFLOAT4_EMPTY, XMFLOAT4_EMPTY, 1.0f),
      BorderBuffer::CreateBorder(XMFLOAT4_EMPTY, XMFLOAT2_EMPTY,
                                 graphics::Transform()),
      XMFLOAT4_EMPTY, false, ColorParams{}, ChromaKeyParams{},
      sharpness_params);

  if (!buffer) {
    return;
  }

  shader->RenderBGRA(texture.GetSRV(), buffer->vertex_buffer_,
                     buffer->index_buffer_, buffer->crop_buffer_,
                     buffer->matrix_buffer_, buffer->ps_buffer_, format,
                     texture.GetToneMapType());
}

void GraphicsImpl::DrawBGRATextureLuminanceMapParamsInternal(
    Texture& texture,
    DXGI_FORMAT format,
    const Transform& transform,
    const LuminanceMapParams& luminance_map_params) {
  auto shader = GetShaderManager_()->GetOrCreateShader<TextureShader>(
      device_.shared_from_this());
  if (!shader) {
    return;
  }

  device_.AllowBlend(transform.GetTransParent());

  if (!texture.SetupVertex()) {
    return;
  }

  auto buffer = GetBufferedBuffer(
      &texture, &transform,
      PointNine::CreatePointNine(XMFLOAT2_EMPTY, XMFLOAT4_EMPTY, false),
      CornerBuffer::CreateCorner(XMFLOAT4_EMPTY, XMFLOAT4_EMPTY, 1.0f),
      BorderBuffer::CreateBorder(XMFLOAT4_EMPTY, XMFLOAT2_EMPTY,
                                 graphics::Transform()),
      XMFLOAT4_EMPTY, false, ColorParams{}, ChromaKeyParams{},
      SharpnessParams{}, ColorLutParams{}, luminance_map_params);
  if (!buffer) {
    return;
  }

  shader->RenderBGRAWithLuminanceMap(
      texture.GetSRV(), luminance_map_params.luminance_map->GetSRV(),
      buffer->vertex_buffer_, buffer->index_buffer_, buffer->crop_buffer_,
      buffer->matrix_buffer_, buffer->ps_buffer_, format,
      texture.GetToneMapType());
}

void GraphicsImpl::DrawBGRAPointNineTextureInternal(
    Texture& texture,
    DXGI_FORMAT format,
    const Transform& transform,
    const PointNine& point_nine) {
  auto shader = GetShaderManager_()->GetOrCreateShader<PointNineShader>(
      device_.shared_from_this());
  if (!shader) {
    return;
  }
  device_.AllowBlend(transform.GetTransParent());

  if (!texture.SetupVertex())
    return;
  auto buffer = GetBufferedBuffer(
      &texture, &transform, point_nine,
      CornerBuffer::CreateCorner(XMFLOAT4_EMPTY, XMFLOAT4_EMPTY, 1.0f),
      BorderBuffer::CreateBorder(XMFLOAT4_EMPTY, XMFLOAT2_EMPTY,
                                 graphics::Transform()),
      XMFLOAT4_EMPTY);
  shader->RenderPointNine(texture.GetSRV(), buffer->vertex_buffer_,
                          buffer->index_buffer_, buffer->crop_buffer_,
                          buffer->matrix_buffer_, buffer->ps_buffer_,
                          point_nine);
}

void GraphicsImpl::DrawCornerTextureInternal(Texture& texture,
                                             const XMFLOAT4& corner_radius,
                                             const float& ratio,
                                             const Transform& transform,
                                             bool refer_width,
                                             bool fixed_radius) {
  auto shader = GetShaderManager_()->GetOrCreateShader<CornerShader>(
      device_.shared_from_this());
  if (!shader) {
    return;
  }
  ScopedBlendHelper restore(device_);

  device_.AllowBlend(transform.GetTransParent());

  if (!texture.SetupVertex())
    return;

  auto size = texture.GetSize();
  auto scale = transform.GetScale();
  if (fixed_radius) {
    size *= scale;
  }
  XMFLOAT4 radius = {size.x > 0 ? corner_radius.x / size.x : 0.0f,
                     size.x > 0 ? corner_radius.y / size.x : 0.0f,
                     size.x > 0 ? corner_radius.z / size.x : 0.0f,
                     size.x > 0 ? corner_radius.w / size.x : 0.0f};

  if (!refer_width) {
    if (transform.GetClip() != XMFLOAT4_EMPTY) {
      auto clip_scale = transform.GetScale();
      auto texture_size = texture.GetSize();
      XMFLOAT2 size_after_clip = {
          texture_size.x - transform.GetClip().x - transform.GetClip().z,
          texture_size.y - transform.GetClip().y - transform.GetClip().w};
      clip_scale = size_after_clip / texture_size;
      radius *= clip_scale.x > clip_scale.y ? clip_scale.y : clip_scale.x;
    }
    if (transform.GetScale().x > 0.f) {
      float ratio = transform.GetScale().y / transform.GetScale().x;
      if (ratio < 1.f) {
        radius *= ratio;
      }
    }
  }

  auto trans = graphics::Transform();
  trans.SetScale(DirectX::XMFLOAT2{ratio, ratio});
  auto buffer = GetBufferedBuffer(
      &texture, &trans,
      PointNine::CreatePointNine(XMFLOAT2_EMPTY, XMFLOAT4_EMPTY, false),
      CornerBuffer::CreateCorner(
          radius, transform.GetClip(),
          scale.x > 0 && scale.y > 0 ? scale.x / scale.y : 1.0),
      BorderBuffer::CreateBorder(XMFLOAT4_EMPTY, XMFLOAT2_EMPTY,
                                 graphics::Transform()),
      XMFLOAT4_EMPTY);
  shader->RenderCorner(texture.GetSRV(), buffer->vertex_buffer_,
                       buffer->index_buffer_, buffer->crop_buffer_,
                       buffer->matrix_buffer_, buffer->ps_buffer_);
}

void GraphicsImpl::DrawBorderTextureInternal(Texture& texture,
                                             const XMFLOAT4& color,
                                             const XMFLOAT2& width,
                                             const float& ratio,
                                             const Transform& transform) {
  auto shader = GetShaderManager_()->GetOrCreateShader<BorderShader>(
      device_.shared_from_this());
  if (!shader) {
    return;
  }
  device_.AllowBlend(transform.GetTransParent());

  if (!texture.SetupVertex())
    return;

  auto trans = graphics::Transform();
  trans.SetScale(DirectX::XMFLOAT2{ratio, ratio});
  auto buffer = GetBufferedBuffer(
      &texture, &trans,
      PointNine::CreatePointNine(XMFLOAT2_EMPTY, XMFLOAT4_EMPTY, false),
      CornerBuffer::CreateCorner(XMFLOAT4_EMPTY, XMFLOAT4_EMPTY, 1.0f),
      BorderBuffer::CreateBorder(color, width, transform), XMFLOAT4_EMPTY);
  shader->RenderBorder(texture.GetSRV(), buffer->vertex_buffer_,
                       buffer->index_buffer_, buffer->crop_buffer_,
                       buffer->matrix_buffer_, buffer->ps_buffer_);
}

void GraphicsImpl::DrawBGRAClipMaskInternal(
    const void* handle,
    const DirectX::XMFLOAT2& texture_size,
    const Transform& transform,
    const DirectX::XMFLOAT4& clip_mask) {
  auto shader = GetShaderManager_()->GetOrCreateShader<ClipMaskShader>(
      device_.shared_from_this());
  if (!shader) {
    return;
  }
  device_.AllowBlend(transform.GetTransParent());

  auto buffer = GetNoTextureBufferedBuffer(
      handle, texture_size, &transform,
      PointNine::CreatePointNine(XMFLOAT2_EMPTY, XMFLOAT4_EMPTY, false),
      CornerBuffer::CreateCorner(XMFLOAT4_EMPTY, XMFLOAT4_EMPTY, 1.0f),
      BorderBuffer::CreateBorder(XMFLOAT4_EMPTY, XMFLOAT2_EMPTY,
                                 graphics::Transform()),
      clip_mask);
  shader->RenderClipMask(buffer->vertex_buffer_, buffer->index_buffer_,
                         buffer->crop_buffer_, buffer->matrix_buffer_,
                         buffer->ps_buffer_);
}

void GraphicsImpl::DrawLutTextureInternal(Texture& texture,
                                          Texture* texture_1dlut,
                                          Texture* texture_3dlut,
                                          const XMFLOAT4& domain_min,
                                          const XMFLOAT4& domain_max,
                                          const Transform& transform,
                                          float amount,
                                          bool pass_through_alpha) {
  auto shader = GetShaderManager_()->GetOrCreateShader<ColorLutShader>(
      device_.shared_from_this());
  if (!shader) {
    return;
  }
  device_.AllowBlend(transform.GetTransParent());

  if (!texture.SetupVertex())
    return;

  ColorLutParams params;
  params.SetAmount(amount);
  params.SetPassThroughAlpha(pass_through_alpha);
  params.SetDomainMax({domain_max.x, domain_max.y, domain_max.z});
  params.SetDomainMin({domain_min.x, domain_min.y, domain_min.z});
  auto buffer = GetLutBufferedBuffer(&texture, &transform, params);
  if (!buffer)
    return;

  if (texture_1dlut) {
    shader->Color1DLutRender(texture.GetSRV(), texture_1dlut->GetSRV(),
                             buffer->vertex_buffer_, buffer->index_buffer_,
                             buffer->crop_buffer_, buffer->matrix_buffer_,
                             buffer->ps_buffer_);
  } else if (texture_3dlut) {
    shader->Color3DLutRender(texture.GetSRV(), texture_3dlut->GetSRV(),
                             buffer->vertex_buffer_, buffer->index_buffer_,
                             buffer->crop_buffer_, buffer->matrix_buffer_,
                             buffer->ps_buffer_);
  }
}

bool GraphicsImpl::CreateGraphics(
    const D3D11_TEXTURE2D_DESC& descTexture,
    const D3D11_RENDER_TARGET_VIEW_DESC* render_target_view_desc) {
  HRESULT res = S_OK;
  if (!render_2d_) {
    res = device_.GetDevice()->CreateTexture2D(&descTexture, NULL, &render_2d_);
    if (res != S_OK) {
      LOG(ERROR) << base::StringPrintf("Failed to CreateTexture2D(%s)",
                                       GetErrorString(res).c_str());
      return false;
    }
  }
  if (descTexture.MiscFlags & D3D11_RESOURCE_MISC_SHARED ||
      descTexture.MiscFlags & D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX) {
    auto evictionPriority = render_2d_->GetEvictionPriority();
    if (evictionPriority != DXGI_RESOURCE_PRIORITY_MAXIMUM)
      render_2d_->SetEvictionPriority(DXGI_RESOURCE_PRIORITY_MAXIMUM);
  }
  res = device_.GetDevice()->CreateRenderTargetView(
      render_2d_.Get(), render_target_view_desc, &render_view_);
  if (res != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateRenderTargetView(%s)",
                                     GetErrorString(res).c_str());
    return false;
  }

  D3D11_TEXTURE2D_DESC depth_desc;
  ZeroMemory(&depth_desc, sizeof(depth_desc));
  depth_desc.Width = descTexture.Width;
  depth_desc.Height = descTexture.Height;
  depth_desc.MipLevels = 1;
  depth_desc.ArraySize = 1;
  depth_desc.Format = DXGI_FORMAT_D24_UNORM_S8_UINT;
  depth_desc.SampleDesc.Count = 1;
  depth_desc.SampleDesc.Quality = 0;
  depth_desc.Usage = D3D11_USAGE_DEFAULT;
  depth_desc.BindFlags = D3D11_BIND_DEPTH_STENCIL;
  depth_desc.CPUAccessFlags = 0;
  depth_desc.MiscFlags = 0;
  res = device_.GetDevice()->CreateTexture2D(&depth_desc, NULL, &depth_2d_);
  if (res != S_OK) {
    LOG(INFO) << base::StringPrintf("Failed to CreateTexture2D(%s)",
                                    GetErrorString(res).c_str());
    return false;
  }
  D3D11SetDebugObjectName(depth_2d_.Get(), "graphics depth texture");

  D3D11_DEPTH_STENCIL_VIEW_DESC depth_view_desc;
  ZeroMemory(&depth_view_desc, sizeof(depth_view_desc));
  depth_view_desc.Format = DXGI_FORMAT_D24_UNORM_S8_UINT;
  depth_view_desc.ViewDimension = D3D11_DSV_DIMENSION_TEXTURE2D;
  depth_view_desc.Texture2D.MipSlice = 0;
  res = device_.GetDevice()->CreateDepthStencilView(
      depth_2d_.Get(), &depth_view_desc, &depth_view_);
  if (res != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateDepthStencilView(%s)",
                                     GetErrorString(res).c_str());
    return false;
  }
  uint64_t usage = 0;
  usage |= (descTexture.MiscFlags & D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX)
               ? Texture::TEXTURE_USAGE::TEXTURE_USAGE_KEYED_MUTEXED
               : 0;

  usage |= Texture::TEXTURE_USAGE::TEXTURE_USAGE_COPY;
  usage |= Texture::TEXTURE_USAGE::TEXTURE_USAGE_RENDER_TARGET;
  if (render_target_view_desc &&
      render_target_view_desc->Format == DXGI_FORMAT_R8G8_UNORM) {
  } else {
    usage |= (((descTexture.BindFlags & D3D11_BIND_SHADER_RESOURCE) != 0)
                  ? Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE
                  : 0);
  }

  output_texture_ = CreateTexture2D(device_, render_2d_.Get(), usage);
  if (!output_texture_) {
    return false;
  }
  BuildMatrixs(0.0F, 0.0F, -10.0F, XMFLOAT3(0.f, 0.f, 1.f), GetSize(),
               up_view_matrix_, view_matrix_, projection_matrix_);
  graphics_world_matrix_ = GetLineView();
  return true;
}

bool GraphicsImpl::CreateNewY8Graphics(int cx, int cy) {
  return CreateNewGraphics(DXGI_FORMAT_R8_UNORM, cx, cy);
}

bool GraphicsImpl::CreateNewBGRAGraphics(int cx, int cy) {
  return CreateNewGraphics(DXGI_FORMAT_B8G8R8A8_UNORM, cx, cy);
}

bool GraphicsImpl::CreateNewSharedBGRAGraphics(int cx,
                                               int cy,
                                               D3D11_RESOURCE_MISC_FLAG flag) {
  return CreateSharedGraphics(DXGI_FORMAT_B8G8R8A8_UNORM, cx, cy, flag);
}

bool GraphicsImpl::CreateNewRGBAGraphics(int cx, int cy) {
  return CreateNewGraphics(DXGI_FORMAT_R8G8B8A8_UNORM, cx, cy);
}

bool GraphicsImpl::CreateNewGraphics(DXGI_FORMAT dxgiFormat, int cx, int cy) {
  if (!IsEmpty()) {
    DCHECK(false && "not empty");
    return false;
  }
  D3D11_TEXTURE2D_DESC desc_texture_desc;
  ZeroMemory(&desc_texture_desc, sizeof(desc_texture_desc));
  desc_texture_desc.Width = cx;
  desc_texture_desc.Height = cy;
  desc_texture_desc.MipLevels = 1;
  desc_texture_desc.ArraySize = 1;
  desc_texture_desc.Format = dxgiFormat;
  desc_texture_desc.SampleDesc.Count = 1;
  desc_texture_desc.SampleDesc.Quality = 0;
  desc_texture_desc.BindFlags =
      D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_RENDER_TARGET;
  desc_texture_desc.MiscFlags = 0;
  desc_texture_desc.Usage = D3D11_USAGE_DEFAULT;

  return CreateGraphics(desc_texture_desc, NULL);
}

bool GraphicsImpl::CreateSharedGraphics(DXGI_FORMAT format,
                                        int cx,
                                        int cy,
                                        D3D11_RESOURCE_MISC_FLAG flag) {
  if (!IsEmpty()) {
    DCHECK(false && "not empty");
    return false;
  }
  D3D11_TEXTURE2D_DESC desc_texture_desc;
  ZeroMemory(&desc_texture_desc, sizeof(desc_texture_desc));
  desc_texture_desc.Width = cx;
  desc_texture_desc.Height = cy;
  desc_texture_desc.MipLevels = 1;
  desc_texture_desc.ArraySize = 1;
  desc_texture_desc.Format = format;
  desc_texture_desc.SampleDesc.Count = 1;
  desc_texture_desc.SampleDesc.Quality = 0;
  desc_texture_desc.BindFlags =
      D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_RENDER_TARGET;
  desc_texture_desc.MiscFlags = flag;
  desc_texture_desc.Usage = D3D11_USAGE_DEFAULT;

  return CreateGraphics(desc_texture_desc, NULL);
}

XMMATRIX GraphicsImpl::GetLineView() {
  return BuildMatrixToTextureRender(GetSize(), GetSize(), XMFLOAT2_EMPTY,
                                    XMFLOAT2_ONE, XMFLOAT4_EMPTY, 0.f);
}

const XMMATRIX& GraphicsImpl::GetViewMatrix() {
  return view_matrix_;
}

const XMMATRIX& GraphicsImpl::GetProjectionMatrix() {
  return projection_matrix_;
}

bool GraphicsImpl::IsEmpty() {
  return !output_texture_ || output_texture_->IsEmpty();
}

XMFLOAT2 GraphicsImpl::GetSize() {
  if (!output_texture_) {
    return XMFLOAT2_EMPTY;
  }
  return output_texture_->GetSize();
}

std::shared_ptr<Texture> GraphicsImpl::GetOutputTexture() {
  return output_texture_;
}

std::shared_ptr<Texture> GraphicsImpl::MoveOutputTexture() {
  DestroyExcludeOutputTexture();
  return std::move(output_texture_);
}

Device& GraphicsImpl::GetDevice() {
  return device_;
}

bool GraphicsImpl::CreateGraphics(Texture& texture) {
  D3D11_RENDER_TARGET_VIEW_DESC render_target_view_desc = {};

  render_target_view_desc.Format = texture.GetDesc().Format;
  render_target_view_desc.ViewDimension = D3D11_RTV_DIMENSION_TEXTURE2D;
  render_target_view_desc.Texture2D.MipSlice = 0;

  return CreateGraphics(texture.GetTexture(), &render_target_view_desc);
}

bool GraphicsImpl::CreateGraphics(
    ID3D11Texture2D* texture,
    const D3D11_RENDER_TARGET_VIEW_DESC* render_target_view_desc) {
  if (!texture) {
    DCHECK(false);
    return false;
  }

  render_2d_ = texture;
  D3D11_TEXTURE2D_DESC desc = {};
  render_2d_->GetDesc(&desc);
  return CreateGraphics(desc, render_target_view_desc);
}

bool GraphicsImpl::CreateGraphicsFromTexture(
    Microsoft::WRL::ComPtr<ID3D11Texture2D> texture) {
  if (!texture) {
    return false;
  }
  render_2d_ = texture;
  D3D11_TEXTURE2D_DESC desc = {};
  render_2d_->GetDesc(&desc);
  D3D11_RENDER_TARGET_VIEW_DESC render_target_view_desc = {};
  render_target_view_desc.Format = desc.Format;
  render_target_view_desc.ViewDimension = D3D11_RTV_DIMENSION_TEXTURE2D;
  render_target_view_desc.Texture2D.MipSlice = 0;
  return CreateGraphics(desc, &render_target_view_desc);
}

bool GraphicsImpl::CreateWithSwapChain(IDXGISwapChain* swap_chain) {
  // gain access to texture sub resource in swap chain (back buffer)
  HRESULT res =
      swap_chain->GetBuffer(0, __uuidof(ID3D11Texture2D), &render_2d_);

  if (FAILED(res)) {
    LOG(ERROR) << base::StringPrintf("Failed to GetBuffer %s",
                                     GetErrorString(res).c_str());
    return false;
  }
  D3D11SetDebugObjectName(render_2d_.Get(), "swap chain texture");
  D3D11_TEXTURE2D_DESC desc = {};
  render_2d_->GetDesc(&desc);
  return CreateGraphics(desc, nullptr);
}

ID3D11Device* GraphicsImpl::GetDevice_() {
  return device_.GetDevice().Get();
}

ID3D11DeviceContext* GraphicsImpl::GetContext_() {
  return context_;
}

ShaderManager* GraphicsImpl::GetShaderManager_() {
  return device_.GetShaderManager();
}

GraphicsImpl::~GraphicsImpl() {
  Destroy();
}

void GraphicsImpl::Destroy() {
  if (output_texture_) {
    output_texture_->Destroy();
    output_texture_ = nullptr;
  }
  DestroyExcludeOutputTexture();
}

void GraphicsImpl::DestroyExcludeOutputTexture() {
  render_view_.Reset();
  depth_view_.Reset();
  depth_2d_.Reset();
  render_2d_.Reset();
  previous_dsv_.Reset();
  previous_rtv_.Reset();

  up_view_matrix_ = XMMatrixIdentity();
  view_matrix_ = XMMatrixIdentity();
  projection_matrix_ = XMMatrixIdentity();
  graphics_world_matrix_ = XMMatrixIdentity();

  DestroyBuffer();
}

std::vector<XMFLOAT4> GraphicsImpl::GetDottedLines(const XMFLOAT4& line) {
  XMFLOAT2 points[2] = {{line.x, line.y}, {line.z, line.w}};
  float xDis = std::abs(line.x - line.z);
  float yDis = std::abs(line.y - line.w);
  float dis = std::sqrt(xDis * xDis + yDis * yDis);
  if (dis < 1.0f)
    return {};
  float sinL = yDis / dis;
  float cosL = xDis / dis;

  const float dotted_line_length = 10;
  XMFLOAT2 start = points[0];

  std::vector<XMFLOAT4> lines;
  while (!DottedLineFinished(points[0], points[1], start)) {
    XMFLOAT2 target;
    target.x = points[1].x > points[0].x ? start.x + dotted_line_length * cosL
                                         : start.x - dotted_line_length * cosL;
    if ((points[1].x > points[0].x && target.x > points[1].x) ||
        (points[1].x < points[0].x && target.x < points[1].x)) {
      target.x = points[1].x;
    }
    target.y = points[1].y > points[0].y ? start.y + dotted_line_length * sinL
                                         : start.y - dotted_line_length * sinL;
    if ((points[1].y > points[0].y && target.y > points[1].y) ||
        (points[1].y < points[0].y && target.y < points[1].y)) {
      target.y = points[1].y;
    }
    lines.emplace_back(XMFLOAT4{start.x, start.y, target.x, target.y});
    start.x = points[1].x > points[0].x
                  ? start.x + dotted_line_length * cosL * 2
                  : start.x - dotted_line_length * cosL * 2;
    start.y = points[1].y > points[0].y
                  ? start.y + dotted_line_length * sinL * 2
                  : start.y - dotted_line_length * sinL * 2;
  }
  return lines;
}

bool GraphicsImpl::DottedLineFinished(const XMFLOAT2& start,
                                      const XMFLOAT2& end,
                                      const XMFLOAT2& cur) {
  if (start.x < end.x) {
    return cur.x >= end.x;
  }
  if (start.x > end.x) {
    return cur.x <= end.x;
  }
  if (start.y < end.y) {
    return cur.y >= end.y;
  }
  if (start.y > end.y) {
    return cur.y <= end.y;
  }
  return false;
}

}  // namespace graphics