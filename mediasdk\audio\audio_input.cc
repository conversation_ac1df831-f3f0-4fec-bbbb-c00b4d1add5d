#include "audio_input.h"

#include <array>
#include <cmath>

#include "base/mediasdk/thread_safe_deleter.h"
#include "base/strings/sys_string_conversions.h"

#include "audio/audio_controller.h"
#include "audio/audio_dump_manager.h"
#include "audio/audio_frame_utils.h"
#include "audio/audio_resample.h"
#include "data_center/vqos_data.h"
#include "frame_convert.h"
#include "hook_api/hook_call.h"
#include "mediasdk/component_center.h"
#include "mediasdk/notify_center.h"

namespace mediasdk {

namespace {

void NotifyFormat(const std::string& id, const AudioFormat& format) {
  auto* nc = com::GetNotifyCenter();
  if (!nc)
    return;
  nlohmann::json j_format = nlohmann::json();
  j_format["format"] = format.GetFormat();
  j_format["channel"] = format.GetChannel();
  j_format["sample_rate"] = format.GetSampleRate();
  j_format["layout"] = format.GetLayout();
  j_format["block_size"] = format.GetBlockSize();
  nlohmann::json json_root;
  j_format["event_name"] = "AudioSourceFormat";
  j_format["error_msg"] = j_format;

  nc->AudioEvent()->Notify(FROM_HERE,
                           &MediaSDKAudioStatusObserver::OnAudioEvent, id,
                           std::move(j_format.dump()));
}

}  // namespace

AudioInput::AudioInput(const std::string& id, const AudioFormat& output_format)
    : id_(id),
      output_format_(output_format),
      filter_chain_(this),
      monitor_manager_(output_format),
      AudioPerformanceStoreCalc(id) {
#ifdef ENABLE_AUDIO_DUMP
  audio_dump_manager_ = std::make_unique<AudioDumpManager>(id);
#endif  // ENABLE_AUDIO_DUMP
}

AudioInput::~AudioInput() {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  LOG(INFO) << "~AudioInput [" << GetId() << "]";

#if ENABLE_HOOK_API_AUDIO_DUMP
  if (raw_audio_dump_helper_) {
    raw_audio_dump_helper_.reset();
  }
  if (processed_audio_dump_helper_) {
    processed_audio_dump_helper_.reset();
  }
#endif  // ENABLE_HOOK_API_AUDIO_DUMP
}

void AudioInput::NotifyRawFrame(const AudioFormat& format,
                                const AudioFrame& frames) {
  HOOK_CALL_AUDIO(OnAudioInputRawFrame, GetId().c_str(), IsMute(),
                  AudioFrameToAudioSourceFrame(frames));

  observer_list_.Notify(&AudioInputFrameObserver::OnInputAudioFrame, id_,
                        frames, format);

#if ENABLE_HOOK_API_AUDIO_DUMP
  if (!raw_audio_dump_helper_) {
    raw_audio_dump_helper_ =
        std::make_unique<HookApiAudioDumpHelper>(GetId() + "_raw");
  }

  if (raw_audio_dump_helper_) {
    raw_audio_dump_helper_->DumpAudioFrame(format, frames);
  }
#endif  // ENABLE_HOOK_API_AUDIO_DUMP
}

void AudioInput::NotifyProcessedFrame(const AudioFormat& format,
                                      const AudioFrame& frames) {
  HOOK_CALL_AUDIO(OnAudioInputFilteredFrame, GetId().c_str(), IsMute(),
                  AudioFrameToAudioSourceFrame(frames));

  observer_list_.Notify(&AudioInputFrameObserver::OnProcessedAudioFrame, id_,
                        frames, format);
}

void AudioInput::ProcessAudioFrame(const AudioFormat& format,
                                   const AudioFrame& frame) {
  auto start = low_precision_milli_now();
  ProcessAudioFrameInternal(format, frame);
  auto end = low_precision_milli_now();
  if (end - start > 2000) {
    LOG(ERROR) << "Error, Unexpected cost [" << end - start << "] [" << GetId()
               << "]";
    VqosData data;
    data.ReportFatalError(event_tracking_data::FatalError{
        static_cast<int>(FATAL_ERROR_TYPE::kAudioTimeOut), GetName(),
        static_cast<int>(end - start), 0});
  }
}

void AudioInput::ProcessAudioFrameInternal(const AudioFormat& format,
                                           const AudioFrame& input_frame) {
  if (!notify_format_) {
    notify_format_ = true;
    NotifyFormat(GetId(), format);
  }

#ifdef ENABLE_AUDIO_DUMP
  audio_dump_manager_->DumpInputAudio(format, input_frame);
#endif  // ENABLE_AUDIO_DUMP

  performance_calc_.OnAudioPacket();

#if ENABLE_HOOK_API_AUDIO_DUMP
  if (!processed_audio_dump_helper_) {
    processed_audio_dump_helper_ =
        std::make_unique<HookApiAudioDumpHelper>(GetId() + "_before_resample");
  }

  if (processed_audio_dump_helper_) {
    processed_audio_dump_helper_->DumpAudioFrame(format, input_frame);
  }
#endif  // ENABLE_HOOK_API_AUDIO_DUMP

  AudioFrame frame;
  if (!ResampleFrameIfNeeded(format, input_frame, frame)) {
    return;
  }

  // Notify audio frame observer (now for reference from speaker, so before
  // filters)
  NotifyRawFrame(output_format_, frame);

  // peak raw
  if (!rms_calculator_.IsRMSHandled()) {
    rms_calculator_.CalculateRMSRaw(output_format_, frame);
  }

  AudioPreProcess(frame);

  // peak handled
  if (rms_calculator_.CalculateRMSHandled(output_format_, frame)) {
    rms_calculator_.NotifyRMS(GetId(), IsMute(), GetVolume());
  }
  rms_calculator_.ResetRMSHandled();

  performance_calc_.OnResampleFinished();

  AudioConsume(frame);
}

bool AudioInput::ResampleFrameIfNeeded(const AudioFormat& input_format,
                                       const AudioFrame& input_frame,
                                       AudioFrame& output_frame) {
  if (input_format == output_format_) {
    output_frame = input_frame;
    return true;
  }

  if (!resample_) {
    resample_ = CreateResample();
    if (!resample_->Open(input_format, output_format_)) {
      resample_ = nullptr;
      return false;
    }
  } else {
    if (resample_->GetInputFormat() != input_format) {
      resample_ = nullptr;
      resample_ = CreateResample();
      if (!resample_->Open(input_format, output_format_)) {
        resample_ = nullptr;
        return false;
      }
    }
  }
  if (resample_) {
    return resample_->Resample(input_frame, output_frame);
  }
  return false;
}

void AudioInput::AudioConsume(AudioFrame& frame) {
  if (monitor_manager_.GetMonitorType() & kAudioMonitorOutput) {
    const auto syn_offset = GetSyncOffset();
    performance_calc_.SetSyncOffset(syn_offset);
    NotifyProcessedFrame(output_format_, frame);
  }

  monitor_manager_.PlayFrame(frame);
}

void AudioInput::AudioPreProcess(AudioFrame& frame) {
  if (IsMute()) {
    OnApplyMute(frame);

#ifdef ENABLE_AUDIO_DUMP
    audio_dump_manager_->DumpAfterMute(output_format_, frame);
#endif  // ENABLE_AUDIO_DUMP
  } else {
    // Muted frame do not need to be proceed

    // Must run before mono process
    OnApplyBalance(frame);

#ifdef ENABLE_AUDIO_DUMP
    audio_dump_manager_->DumpAfterBalance(output_format_, frame);
#endif  // ENABLE_AUDIO_DUMP

    if (IsMono()) {
      OnApplyMono(frame);

#ifdef ENABLE_AUDIO_DUMP
      audio_dump_manager_->DumpAfterMono(output_format_, frame);
#endif  // ENABLE_AUDIO_DUMP
    }

    OnApplyFilter(frame);

#ifdef ENABLE_AUDIO_DUMP
    audio_dump_manager_->DumpAfterFilter(output_format_, frame);
#endif  // ENABLE_AUDIO_DUMP

    OnApplyVolume(frame);

#ifdef ENABLE_AUDIO_DUMP
    audio_dump_manager_->DumpAfterVolume(output_format_, frame);
#endif  // ENABLE_AUDIO_DUMP
  }
}

void AudioInput::OnApplyVolume(AudioFrame& frame) {
  const float volume = GetVolume();
  if (volume == 1.f) {
    return;
  }

  for (int i = 0; i < output_format_.GetPlane(); i++) {
    AudioMul(reinterpret_cast<float*>(frame.GetData(i)), volume,
             frame.GetCount());
  }
}

void AudioInput::OnApplyMono(AudioFrame& frame) {
  for (int i = 1; i < output_format_.GetPlane(); i++) {
    AudioAdd((float*)frame.GetData(0), (float*)frame.GetData(i),
             frame.GetCount());
  }
  float mul = 1.f / output_format_.GetPlane();
  AudioMul((float*)frame.GetData(0), mul, frame.GetCount());
  for (int i = 1; i < output_format_.GetPlane(); i++) {
    memcpy(frame.GetData(i), frame.GetData(0),
           frame.GetCount() * frame.GetBlockSize());
  }
}

void AudioInput::OnApplyMute(AudioFrame& frame) {
  for (int i = 0; i < output_format_.GetPlane(); i++) {
    AudioZero(reinterpret_cast<float*>(frame.GetData(i)), frame.GetCount());
  }
}

void AudioInput::OnApplyBalance(AudioFrame& frame) {
  if ((std::fabs)(balance_ - 0.5f) <= 1e-4f)
    return;

  if (output_format_.GetPlane() != 2) {
    DCHECK(false && "Only run with stereo audio format");
    return;
  }
  constexpr float PI = 3.14159265358979323846f;
  auto amp_l = sinf((1.0f - balance_) * (PI / 2.0f));
  auto amp_r = sinf(balance_ * (PI / 2.0f));
  AudioMul((float*)frame.GetData(0), amp_l, frame.GetCount());
  AudioMul((float*)frame.GetData(1), amp_r, frame.GetCount());
}

void AudioInput::OnApplyFilter(AudioFrame& frame) {
  GetAudioFilterChainRef().ApplyFilter(&frame);
}

void AudioInput::InputAudioFrame(const AudioFormat& format,
                                 const AudioFrame& frame) {
  ProcessAudioFrame(format, frame);
}

void AudioInput::SetVolume(float volume) {
  if (volume != volume_) {
    LOG(INFO) << "SetVolume [" << GetId() << "] plugin [" << GetName() << "] ["
              << volume << "]";
  }

  volume_ = volume;

  OnSetVolume(volume_);
}

float AudioInput::GetVolume() {
  return volume_;
}

void AudioInput::SetSyncOffset(int32_t offset) {
  if (offset != sync_offset_) {
    LOG(INFO) << "[" << GetId() << "] syn offset [" << offset << "]";
  }

  sync_offset_ = offset;
}

int32_t AudioInput::GetSyncOffset() {
  return sync_offset_;
}

void AudioInput::SetMonitorType(uint32_t monitor) {
  if (monitor != monitor_manager_.GetMonitorType()) {
    LOG(INFO) << "SetMonitorType [" << GetId() << "] plugin [" << GetName()
              << "] [" << monitor << "]";
  }

  monitor_manager_.SetMonitorType(monitor);
}

uint32_t AudioInput::GetMonitorType() {
  return monitor_manager_.GetMonitorType();
}

void AudioInput::SetMute(bool mute) {
  if (is_mute_ != mute) {
    LOG(INFO) << "SetMute [" << GetId() << "] plugin [" << GetName() << "] ["
              << mute << "]";
  }

  is_mute_ = mute;

  OnSetMute(is_mute_);
}

bool AudioInput::IsMute() {
  return is_mute_;
}

void AudioInput::SetBalance(float val) {
  if (balance_ != val) {
    LOG(INFO) << "SetBalance [" << GetId() << "] plugin [" << GetName() << "] ["
              << val << "]";
  }

  balance_ = val;
}

float AudioInput::GetBalance() {
  return balance_;
}

void AudioInput::SetMono(bool val) {
  if (is_mono_ != val) {
    LOG(INFO) << "SetMono [" << GetId() << "] plugin [" << GetName() << "] ["
              << val << "]";
  }

  is_mono_ = val;
}

bool AudioInput::IsMono() {
  return is_mono_;
}

void AudioInput::SetAudioInputParams(const AudioInputParams& param) {
  SetVolume(param.volume);
  SetBalance(param.balance);
  SetSyncOffset(param.sync_offset);
  SetInterval(param.interval);
  SetMono(param.mono);
  SetMute(param.mute);
  SetMonitorType(param.monitor_type);
}

void AudioInput::GetPerformance(MSAudioPerformance& performance) {
  AudioPerformance perform;
  performance_calc_.GetPerformance(perform);
  performance.cost = perform.cost;
  performance.offset = perform.offset;
  performance.reset_times = perform.reset_times;
  performance.valid = true;
}

void AudioInput::SetInterval(int32_t interval) {
  if (rms_calculator_.GetInterval() != interval) {
    LOG(INFO) << "SetInterval [" << GetId() << "] plugin [" << GetName()
              << "] [" << interval << "]";
  }

  rms_calculator_.SetInterval(interval);
}

void AudioInput::AddObserver(
    std::shared_ptr<AudioInputFrameObserver> observer) {
  observer_list_.AddObserver(observer);
}

void AudioInput::RemoveObserver(
    std::shared_ptr<AudioInputFrameObserver> observer) {
  observer_list_.RemoveObserver(observer);
}

void AudioInput::ChangeRenderTargetTo(const std::string& id) {
  monitor_manager_.ChangeRenderTargetTo(id);
}

void AudioInput::ComputePreAudioVolume(const AudioFormat& format,
                                       const AudioFrame& frame) {
  rms_calculator_.CalculateRMSRaw(format, frame);
}

void AudioInput::DumpAudioToFile(const std::string& path,
                                 const AudioFormat& format,
                                 const AudioFrame& frame) {
#ifdef ENABLE_AUDIO_DUMP
  audio_dump_manager_->DumpAudioToFile(path, format, frame);
#endif  // ENABLE_AUDIO_DUMP
}

std::string AudioInput::GetAudioInputCalcId() {
  return GetId();
}

MSAudioPerformance AudioInput::CalcPerformance() {
  MSAudioPerformance performance{};
  GetPerformance(performance);
  return performance;
}

std::string AudioInput::ToString() {
  auto conf = GetId() + base::StringPrintf(" mute %s vol[%.2f] mon[%u]",
                                           IsMute() ? "y" : "no", volume_,
                                           monitor_manager_.GetMonitorType());
  return conf;
}

}  // namespace mediasdk
