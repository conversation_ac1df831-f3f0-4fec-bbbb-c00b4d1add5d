import argparse
import socket
import json
import os
from enum import Enum


class Platform(Enum):
    X64 = "x64"
    WIN32 = "Win32"

    def __str__(self):
        return self.value


class BuildConfiguration(Enum):
    RELEASE = "Release"
    DEBUG = "Debug"

    def __str__(self):
        return self.value


class Config:
    def __init__(self):
        self.__cmdline_args = {}
        pass

    def parse_args(self):
        parser = argparse.ArgumentParser(description="MediaSDK_TT_2024 Package Script")
        parser.add_argument("-b", "--branch", type=str, help="branch name to build")
        parser.add_argument("-v", "--version", type=str, help="version number to build")
        parser.add_argument("-u", "--user", type=str, help="user who sponse the job")
        parser.add_argument("-j", "--job_id", type=str, help="job id")
        parser.add_argument(
            "-r", "--release", action="store_true", help="is release build"
        )
        parser.add_argument(
            "-ds", "--depsladar", action="store_true", help="does upload pdb in Dependencies dir to sladar"
        )
        parser.add_argument(
            "-npb",
            "--no_publish_branch",
            action="store_true",
            help="disable the action create and push publish branch",
        )
        parser.add_argument("-c", "--cmake_params", type=str, help="extra cmake params")
        self.__cmdline_args = parser.parse_args()
        print("args: ", self.__cmdline_args)
        print(self.code_dir)

    @property
    def args(self):
        return self.__cmdline_args

    @property
    def publish_branch_name(self):
        str = "v{}.publish".format(self.args.version)
        return str

    @property
    def build_num(self):
        num = self.args.version.split(".")[-1]
        return num

    @property
    def host_ip(self):
        str = socket.gethostbyname_ex(socket.gethostname())[2][0]
        return str

    @property
    def extra_cmake_params(self):
        str = self.args.cmake_params
        if str is None:
            return []
        params_list = str.split(";")
        # Remove empty strings from the list
        params_list = list(filter(None, params_list))
        params_list = ['-D' + param for param in params_list]
        return params_list

    @property
    def user_open_id(self):
        current_file_path = os.path.abspath(__file__)
        current_dir = os.path.dirname(current_file_path)
        with open(os.path.join(current_dir, "config.json"), "r") as f:
            data = json.load(f)
            user_id = data["lark_open_id_map"].get(self.args.user)

        if not user_id:
            print("Failed: lark open id for {} not found".format(self.args.user))
            return ""

        return user_id

    @property
    def code_dir(self):
        # If the relative position of the script file changes, change the calculation method
        str = "\\".join(os.path.abspath(__file__).split("\\")[0:-2])
        return str

    @property
    def symstore_dir(self):
        return R"D:\SymbolsCache\mediasdk"

    @property
    def build_dir(self):
        str = os.path.join(self.code_dir, "build")
        return str
    
    @property
    def ci_dir(self):
        str = os.path.join(self.code_dir, "CI")
        return str
    
    @property
    def symbol_temp_dir(self):
        str = os.path.join(self.code_dir, "symbols")
        return str

    @property
    def output_bins_dir(self):
        str = os.path.join(self.code_dir, "bins")
        return str
    
    @property
    def curl_path(self):
        str = os.path.join(self.ci_dir, "curl.exe")
        return str
    
    @property
    def dump_sym_path(self):
        str = os.path.join(self.ci_dir, "dump_syms.exe")
        return str

    def exec_dir(self, platform):
        str = os.path.join(self.output_bins_dir, "exec64")
        if platform is Platform.WIN32:
            str = os.path.join(self.output_bins_dir, "exec32")
        return str

    @property
    def job_log_url(self):
        str = "http://{}:8080/job/mediasdk_tt_2024/{}/console".format(
            CONFIG.host_ip, self.args.job_id
        )
        return str

    @property
    def jenkins_download_url(self):
        str = "http://{}:8080/job/mediasdk_tt_2024/{}/artifact/target/{}.zip".format(
            CONFIG.host_ip, CONFIG.build_num, CONFIG.args.version
        )
        return str

    @property
    def get_feihui_chat_id(self):
        str = "oc_7b8948afd9250aa74f0c777380a8b982"
        return str

    @property
    def get_feihui_app_id(self):
        str = "cli_a18d1f407a79d00c"
        return str

    @property
    def get_feihui_app_secret(self):
        str = "Sdj6ZhWWRDGqUCdQXuFyncD4ortlZ1wl"
        return str
CONFIG = Config()
