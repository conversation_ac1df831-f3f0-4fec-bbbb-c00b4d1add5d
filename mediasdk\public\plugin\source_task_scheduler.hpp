#pragma once

#include "concurrentqueue/blockingconcurrentqueue.h"

#include <base/bind.h>
#include <base/threading/sequenced_task_runner_handle.h>

///////////////////////////////////////////////////////////////////////////////
//
// OVERVIEW:
//
//   SourceTaskScheduler is a class designed to manage and execute tasks in a
//   thread-safe manner. It uses a BlockingConcurrentQueue to store tasks,
//   ensuring that tasks can be posted and executed safely across multiple
//   threads.
//
// TYPICAL USAGE:
//
//   SourceTaskScheduler scheduler;
//   scheduler.PostPrepareTask(base::BindOnce([] { /* Task logic here */ }));
//   scheduler.ExecutePrepareTask();
//
// WARNING:
//   To use this class, it is necessary to link the plugin to a base.dll that is
//   consistent with the MediaSDK
///////////////////////////////////////////////////////////////////////////////

namespace mediasdk {

class SourceTaskScheduler {
 public:
  // Posts a task to the queue for preparation.
  void PostPrepareTask(base::OnceClosure closure) {
    task_queue_.enqueue(std::move(closure));
  }

  // Posts a task that can be discarded if a newer task is posted.
  // Only the last task in the queue will be executed.
  void PostDiscardablePrepareTask(base::OnceClosure closure) {
    discardable_task_queue_.enqueue(std::move(closure));
  }

  void ExecutePrepareTask() {
    ExecutePrepareTaskInner();
    ExecuteDiscardablePrepareTask();
  }

 private:
  // Executes all tasks currently in the queue.
  void ExecutePrepareTaskInner() {
    base::OnceClosure task;
    while (task_queue_.try_dequeue(task)) {
      std::move(task).Run();
    }
  }

  // Executes the last task in the discardable queue and discards others.
  void ExecuteDiscardablePrepareTask() {
    base::OnceClosure last_task;
    base::OnceClosure temp_task;
    while (discardable_task_queue_.try_dequeue(temp_task)) {
      last_task = std::move(temp_task);
    }
    if (last_task) {
      std::move(last_task).Run();
    }
  }

 private:
  moodycamel::BlockingConcurrentQueue<base::OnceClosure> task_queue_;
  moodycamel::BlockingConcurrentQueue<base::OnceClosure>
      discardable_task_queue_;
};

}  // namespace mediasdk
