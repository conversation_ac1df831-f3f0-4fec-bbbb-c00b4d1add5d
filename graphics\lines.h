#pragma once
#include "device.h"

namespace graphics {
class GRAPHICS_EXPORT Lines {
 public:
  struct GRAPHICS_EXPORT LinesConfig {
    DirectX::XMFLOAT2* points = nullptr;
    int32_t count = 0;
    const DirectX::XMFLOAT4* colors = nullptr;
    int32_t color_cnt = 0;
  };

 public:
  virtual bool DrawTo(const DirectX::XMFLOAT2& vp_size,
                      const DirectX::XMMATRIX& world,
                      const DirectX::XMMATRIX& view,
                      const DirectX::XMMATRIX& projection) = 0;
  virtual void UpdateLineConf(const LinesConfig& config) = 0;
  virtual int32_t GetIndexCnt() = 0;
  virtual void Destroy() = 0;

  virtual ~Lines() {}
};

GRAPHICS_EXPORT std::shared_ptr<Lines> CreateLines(Device& inst);

}  // namespace graphics
