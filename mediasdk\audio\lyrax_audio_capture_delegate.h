#pragma once

#include "lyrax/lyrax_audio_device_delegate.h"

namespace mediasdk {

enum class AudioDeviceState {
  kStart,
  kStop,
};

class LyraxAudioInput;

class LyraxAudioCaptureDelegate : public lyrax::ILyraxAudioDeviceDelegate {
 public:
  explicit LyraxAudioCaptureDelegate(std::weak_ptr<LyraxAudioInput> input);

  ~LyraxAudioCaptureDelegate() override;

  // if delegate add to lyrax audio, this function will be called
  // handler is used to send event or update parameters to lyrax audio
  void onAttach(std::shared_ptr<lyrax::ILyraxAudioDeviceDelegateHandler>
                    handler) override;

  // receive event from mediasdk audio source, translate to lyrax audio
  void SignalEvent(const std::string& json_str);

  // receive events from lyrax audio, and translate to mediasdk audio source
  lyrax::LyraxErrorCode onSetParameters(
      const std::unordered_map<lyrax::LyraxAudioDeviceDelegateOptionKey,
                               lyrax::LyraxValue>& params) override;

  bool IsRawDataModeEnabled() const;

 private:
  std::shared_ptr<lyrax::ILyraxAudioDeviceDelegateHandler> handler_;
  AudioDeviceState state_;
  std::weak_ptr<LyraxAudioInput> input_;
  float last_system_volume_ = -1;
  bool raw_data_mode_enabled_ = false;
};

}  // namespace mediasdk