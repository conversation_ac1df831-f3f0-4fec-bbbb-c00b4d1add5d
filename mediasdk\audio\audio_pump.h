#pragma once

#include <base/observer_list.h>
#include <base/synchronization/waitable_event.h>
#include <base/threading/thread.h>
#include <mediasdk/audio/audio_input_manager.h>
#include <mediasdk/utils/time_duration.h>

namespace mediasdk {

class AudioPump {
 public:
  class Observer {
   public:
    virtual ~Observer() = default;

    virtual void OnPump(const TimeDuration&,
                        std::shared_ptr<AudioInputManager>) = 0;
  };

  explicit AudioPump(std::shared_ptr<AudioInputManager> audio_input_manager);

  AudioPump(const AudioPump&) = delete;

  AudioPump& operator=(const AudioPump&) = delete;

  void Start(uint32_t sample_unit = kAudioPumpUnitFrames);

  void UpdateFps(uint32_t sample_unit);

  void Stop();

  bool IsRunning();

  void AddObserver(Observer* observer);

  void RemoveObserver(Observer* observer);

 private:
  void Pump(const TimeDuration&);

  void TimingLoop();

 private:
  int32_t sample_unit_ = kAudioPumpUnitFrames;
  std::uint32_t frame_interval_ns_ = 0;
  std::atomic_bool stop_{true};
  base::WaitableEvent render_done_event_{
      base::WaitableEvent::ResetPolicy::AUTOMATIC,
      base::WaitableEvent::InitialState::SIGNALED};
  base::WaitableEvent stop_event_;
  std::shared_ptr<base::Thread> timing_thread_;
  base::ObserverList<Observer>::Unchecked observer_list_;
  std::shared_ptr<AudioInputManager> audio_input_manager_;
};

}  // namespace mediasdk
