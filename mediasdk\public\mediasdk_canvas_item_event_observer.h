#pragma once

#include "mediasdk_defines.h"
#include "mediasdk_defines_visual.h"

namespace mediasdk {

class MediaSDKCanvasEventObserver {
 public:
  virtual ~MediaSDKCanvasEventObserver() = default;

  virtual void OnCurrentCanvasChanged(uint32_t video_model_id,
                                      MediaSDKString canvas_id) = 0;

  virtual void OnCanvasCurrentItemChanged(uint32_t video_model_id,
                                          MediaSDKString canvas_id,
                                          bool manuel,
                                          MediaSDKString canvas_item_id) = 0;

  virtual void OnHittestCanvasItemChanged(int video_model_id,
                                          MediaSDKString canvas_item_id) = 0;

  virtual void OnCanvasItemTransformChanged(MediaSDKString canvas_item_id,
                                            const MSTransform& transform) = 0;

  virtual void OnBeginTrack(int video_model_id,
                            MediaSDKString canvas_item_id,
                            HittestCursorPos pos) = 0;

  virtual void OnEndTrack(int video_model_id,
                          MediaSDKString canvas_item_id,
                          HittestCursorPos pos) = 0;

  virtual void OnCanvasItemFilterNotify(MediaSDKString canvas_item_id,
                                        MediaSDKString filter_id,
                                        MediaSDKString notify,
                                        MediaSDKString json_data) = 0;

  virtual void OnCanvasItemInvalidArea(int video_model_id,
                                       MediaSDKString canvas_item_id) = 0;

  virtual void OnCanvasItemValidArea(int video_model_id,
                                     MediaSDKString canvas_item_id) = 0;

  virtual void OnCanvasItemShortcutActionNotify(
      MediaSDKString canvas_item_id,
      ShortcutAction shortcut_action) = 0;

  virtual void OnCanvasItemClipMaskEnd(MediaSDKString canvas_item_id) = 0;

  // Destroy all items in canvas when canvas is destroyed
  virtual void OnCanvasItemsDestroyedByDestroyCanvas(
      MediaSDKString destory_canvas_id,
      MediaSDKStringArray canvas_item_ids) = 0;

  virtual void OnTransitionFinished(const std::string& transition_id) = 0;
};

}  // namespace mediasdk
