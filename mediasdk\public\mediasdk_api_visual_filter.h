#pragma once

#include "mediasdk_callback_defines.h"
#include "mediasdk_export.h"

#ifdef __cplusplus
extern "C" {
#endif

namespace mediasdk {

// Enum SDK current filter's plugins can support canvas_item filter.
// Asynchronous callback result need cast to mediasdk::PluginInfoArray
MEDIASDK_EXPORT void MS_API EnumVisualFilter(Closure closure);

// To create a filter for the canvas_item specified by canvas_item_id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API CreateVisualFilter(const char* filter_id,
                                               const char* filter_name,
                                               const char* canvas_item_id,
                                               const char* json_params,
                                               Closure closure);

// Destroy a specified filter based on the filter filter_id.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
DestroyVisualFilter(const char* filter_id,
                    const char* owner_canvas_item_id,
                    Closure closure);

// Set the filter properties using 'json_params'.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
SetVisualFilterProperty(const char* filter_id,
                        const char* owner_canvas_item_id,
                        const char* key,
                        const char* json_params,
                        Closure closure);

// Get the filter properties.
// Asynchronous callback result, MediaSDKString
MEDIASDK_EXPORT void MS_API
GetVisualFilterProperty(const char* filter_id,
                        const char* owner_canvas_item_id,
                        const char* key,
                        Closure closure);

// Make the filter perform certain actions.
// Asynchronous callback result, MediaSDKString
MEDIASDK_EXPORT void MS_API VisualFilterAction(const char* filter_id,
                                               const char* owner_canvas_item_id,
                                               const char* action,
                                               const char* action_param,
                                               Closure closure);

// Set canvas_item filter active state.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
VisualFilterSetActive(const char* filter_id,
                      const char* owner_canvas_item_id,
                      bool active,
                      Closure closure);

// Get canvas_item filter active state.
// Asynchronous callback result, ResultBoolBool(exec if success, active state)
MEDIASDK_EXPORT void MS_API
VisualFilterGetActive(const char* filter_id,
                      const char* owner_canvas_item_id,
                      Closure closure);

// Reset the filters for the canvas_item specified by owner_canvas_item_id
// according to the order specified in filter_id_array. If filter_id_array
// contains filters of different levels, the specified order applies only to
// filters within the same level. The priority of the levels is determined
// internally by the MediaSDK.
//  Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
SetVisualFiltersPriority(const char* owner_canvas_item_id,
                         const MediaSDKStringArray& filter_id_array,
                         Closure closure);

}  // namespace mediasdk

#ifdef __cplusplus
}
#endif

#define MS_VISUAL_FILTER mediasdk::visual_filter
