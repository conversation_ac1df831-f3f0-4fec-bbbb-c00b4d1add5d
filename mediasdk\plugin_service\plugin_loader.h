#pragma once

#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <vector>

#include "base/memory/weak_ptr.h"
#include "base/threading/thread.h"
#include "public/plugin/plugin_global_proxy.h"
#include "source_factory.h"

namespace mediasdk {

class PluginLoaderDelegate {
 public:
  virtual void OnPluginLoaded(std::shared_ptr<SourceFactory> factory) = 0;

  virtual void OnPluginInitializeResult(
      std::shared_ptr<SourceFactory> factory) = 0;

  virtual PluginGlobalProxy* GetPluginGlobalProxy() = 0;

  virtual void OnLoadFinished() = 0;

  virtual void OnSubTypePluginsLoadFinished(PluginType type) = 0;
};

class PluginLoader : public base::SupportsWeakPtr<PluginLoader> {
 public:
  static std::unique_ptr<PluginLoader> Create(PluginLoaderDelegate* delegate);

  ~PluginLoader();

  void Load();

  void ReportLoadEvent();

 private:
  PluginLoader(PluginLoaderDelegate* delegate);

  base::NativeLibrary LoadPluginDll(const base::FilePath& file_path);

  std::shared_ptr<PluginInfo> GetPluginInfo(base::NativeLibrary library);

  std::shared_ptr<SourceFactory> CreateSourceFactory(
      base::NativeLibrary library,
      std::shared_ptr<PluginInfo> info);

  void InitAllPlugins(
      std::vector<std::pair<const base::FilePath,
                            std::shared_ptr<SourceFactory>>>&& factories);

  void OnSourceFactoryGlobalInitResult(std::shared_ptr<SourceFactory> fac,
                                       bool success);

  bool InitPlugin(const base::FilePath& file_path,
                  std::shared_ptr<SourceFactory> fac);

  void CheckAllPluginsInitialized();

 private:
  struct PluginCost {
    uint64_t load_cost = 0;
    uint64_t init_cost = 0;
  };

  PluginLoaderDelegate* delegate_ = nullptr;
  int64_t start_ts_ms_ = 0;
  std::vector<std::unique_ptr<base::Thread>> init_threads_;
  std::map<mediasdk::PluginType, size_t> pending_plugins_;
  std::mutex lock_load_failed_libraries_;
  std::vector<std::pair<base::FilePath, base::NativeLibraryLoadError>>
      load_failed_libraries_;
  std::mutex lock_load_library_costs_;
  std::unordered_map<base::FilePath, PluginCost> load_library_costs_;
  std::int64_t load_start_time_ = 0;
};

}  // namespace mediasdk
