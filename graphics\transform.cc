﻿#include "transform.h"
#include <sstream>
#include "DXSimpleMath.h"
#include "graphics_impl.h"
#include "nlohmann/json.hpp"

namespace graphics {
namespace {
Transform EMPTY_TRANSFORM = {};
}

// static
const Transform& Transform::Empty() {
  return EMPTY_TRANSFORM;
}

graphics::Transform Transform::FromTranslate(const DirectX::XMFLOAT2& trans) {
  Transform ret;
  ret.SetTranslate(trans);
  return ret;
}

bool Transform::operator!=(const Transform& other) const {
  return !(*this == other);
}

bool Transform::operator==(const Transform& other) const {
  return flipH_ == other.flipH_ && flipV_ == other.flipV_ &&
         IsNearEqual(angle_, other.angle_) && scale_ == other.scale_ &&
         translate_ == other.translate_ && clip_ == other.clip_ &&
         transparent_ == other.transparent_ &&
         shear_ == other.shear_ &&
         IsNearEqual(shear_angle_, other.shear_angle_);
}

Transform& Transform::operator=(const Transform& other) {
  if (this != &other) {
    Assign(other);
  }
  return *this;
}

Transform Transform::FromScale(const DirectX::XMFLOAT2& scale) {
  Transform ret;
  ret.SetScale(scale);
  return ret;
}

Transform Transform::FromMsTransform(const mediasdk::MSTransform& ms_trans) {
  Transform trans;

  trans.SetRotate(ms_trans.angle);
  trans.SetFlipH(ms_trans.flip_h);
  trans.SetFlipV(ms_trans.flip_v);

  XMFLOAT4 clip;
  clip.x = ms_trans.clip.x;
  clip.y = ms_trans.clip.y;
  clip.z = ms_trans.clip.z;
  clip.w = ms_trans.clip.w;
  trans.SetClip(clip);

  trans.SetScale({ms_trans.scale.x, ms_trans.scale.y});

  trans.SetTranslate({ms_trans.translate.x, ms_trans.translate.y});

  return trans;
}

Transform::Transform(const Transform& other) {
  Assign(other);
}

Transform::Transform() = default;

bool Transform::IsEmpty() const {
  return *this == Transform();
}

void Transform::Reset() {
  *this = Transform();
}

bool Transform::IsFlipH() const {
  return flipH_;
}

bool Transform::SetFlipH(bool value) {
  if (flipH_ != value) {
    flipH_ = value;
    return true;
  }
  return false;
}

bool Transform::IsFlipV() const {
  return flipV_;
}

bool Transform::SetFlipV(bool value) {
  if (flipV_ != value) {
    flipV_ = value;
    return true;
  }
  return false;
}

bool Transform::SetTransParent(bool value) {
  if (transparent_ != value) {
    transparent_ = value;
    return true;
  }
  return false;
}

bool Transform::GetTransParent() const {
  return transparent_;
}

float Transform::GetRotate() const {
  return angle_;
}

bool Transform::SetRotate(float value) {
  if (value > 360.0 || value < -360.0) {
    return false;
  }

  if (!IsNearEqual(angle_, value)) {
    angle_ = value;
    return true;
  }
  return false;
}

DirectX::XMFLOAT2 Transform::GetScale() const {
  return scale_;
}

bool Transform::SetScale(const DirectX::XMFLOAT2& value) {
  if (value.x < 0 || value.y < 0) {
    return false;
  }

  if (scale_ != value) {
    scale_ = value;
    return true;
  }
  return false;
}

DirectX::XMFLOAT2 Transform::GetTranslate() const {
  return translate_;
}

bool Transform::SetTranslate(const DirectX::XMFLOAT2& value) {
  if (translate_ != value) {
    translate_ = value;
    return true;
  }
  return false;
}

bool Transform::AddTranslate(const DirectX::XMFLOAT2& delta) {
  if (delta != XMFLOAT2_EMPTY) {
    translate_ += delta;
    return true;
  }
  return false;
}

DirectX::XMFLOAT4 Transform::GetClip() const {
  return clip_;
}

bool Transform::SetClip(const DirectX::XMFLOAT4& value) {
  if (clip_ != value) {
    clip_ = value;
    return true;
  }
  return false;
}

bool Transform::AddClip(const DirectX::XMFLOAT4& value) {
  if (value != XMFLOAT4_EMPTY) {
    clip_ += value;
    if (clip_.x < 0.f) {
      clip_.x = 0.f;
    }
    if (clip_.y < 0.f) {
      clip_.y = 0.f;
    }
    if (clip_.z < 0.f) {
      clip_.z = 0.f;
    }
    if (clip_.w < 0.f) {
      clip_.w = 0.f;
    }
    return true;
  }
  return false;
}

float Transform::GetShearAngle() const {
  return shear_angle_;
}

bool Transform::SetShearAngle(float value) {
  if (!IsNearEqual(shear_angle_, value)) {
    shear_angle_ = value;
    return true;
  }
  return false;
}

DirectX::XMFLOAT2 Transform::GetShear() const {
  return shear_;
}

bool Transform::SetShear(DirectX::XMFLOAT2& value) {
  if (shear_ != value) {
    shear_ = value;
    return true;
  }
  return false;
}

std::string Transform::ToString() const {
  try {
    nlohmann::json json_root, json_item;
    json_item["flip_h"] = flipH_;
    json_item["flip_v"] = flipV_;
    json_item["angle"] = angle_;
    json_item["scale_x"] = scale_.x;
    json_item["scale_y"] = scale_.y;
    json_item["translate_x"] = translate_.x;
    json_item["translate_y"] = translate_.y;
    json_item["clip_x"] = clip_.x;
    json_item["clip_y"] = clip_.y;
    json_item["clip_z"] = clip_.z;
    json_item["clip_w"] = clip_.w;
    json_item["shear_x"] = shear_.x;
    json_item["shear_y"] = shear_.y;
    json_item["shear_angle"] = shear_angle_;
    json_root["transform"] = json_item;
    return json_root.dump();
  } catch (...) {
  }
  return "";
}

mediasdk::MSTransform Transform::ToMSTransform() const {
  mediasdk::MSTransform ret{};

  ret.angle = GetRotate();
  ret.flip_h = IsFlipH();
  ret.flip_v = IsFlipV();

  auto clipData = GetClip();
  ret.clip.x = clipData.x;
  ret.clip.y = clipData.y;
  ret.clip.z = clipData.z;
  ret.clip.w = clipData.w;

  auto scale = GetScale();
  ret.scale = {scale.x, scale.y};

  auto translate = GetTranslate();
  ret.translate = {translate.x, translate.y};

  return ret;
}

void Transform::Assign(const Transform& other) {
  transparent_ = other.transparent_;
  flipH_ = other.flipH_;
  flipV_ = other.flipV_;
  angle_ = other.angle_;
  scale_ = other.scale_;
  translate_ = other.translate_;
  clip_ = other.clip_;
  shear_ = other.shear_;
  shear_angle_ = other.shear_angle_;
}

}  // namespace graphics
