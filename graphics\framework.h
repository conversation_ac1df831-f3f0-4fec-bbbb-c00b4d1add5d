﻿#pragma once

#include <stdint.h>
#include <algorithm>
#include "base/check.h"

namespace graphics {
inline void VideoPlanerCopy(uint8_t* dst,
                            const uint32_t stride_dst,
                            const uint32_t y_dst,
                            const uint8_t* src,
                            const uint32_t stride_src,
                            const uint32_t y_src) {
  if (stride_dst == stride_src && y_src == y_dst) {
    // quick copy
    std::memcpy(dst, src, stride_src * y_src);
  } else {
    size_t y_min = (std::min)(y_dst, y_src);
    size_t stride_min = (std::min)(stride_dst, stride_src);
    for (size_t i = 0; i < y_min; i++) {
      std::memcpy(dst + i * stride_dst, src + i * stride_src, stride_min);
    }
  }
}

}  // namespace graphics
