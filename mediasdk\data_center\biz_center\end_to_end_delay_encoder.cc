#include "end_to_end_delay_encoder.h"
#include "base/logging.h"
#include "mediasdk/utils/time_helper.h"

namespace mediasdk {
EndtoEndDelayEncoder::EndtoEndDelayEncoder(const std::string& name)
    : name_(name) {
  LOG(INFO) << "[EndtoEnd] set e2e delay:" << name << "," << std::hex << this;
}

EndtoEndDelayEncoder::~EndtoEndDelayEncoder() {
  LOG(INFO) << "[EndtoEnd] rm e2e delay:" << name_ << "," << std::hex << this;
}

void EndtoEndDelayEncoder::CacheEndtoEndMgr(int64_t index,
                                            const EndtoEndMgr& e2e_mgr) {
  if (!e2e_mgr.IsValid()) {
    // not collect e2e;
    return;
  }

  std::lock_guard<std::mutex> lg(mgr_mutex_);
  e2e_mgrs_.insert_or_assign(index, e2e_mgr);
  before_encode_ns_map_.insert_or_assign(index, nano_now());
}

void EndtoEndDelayEncoder::RemoveEndtoEndMgr(int64_t index) {
  int64_t before_encode_ns = 0;
  {
    std::lock_guard<std::mutex> lg(mgr_mutex_);
    e2e_mgrs_.erase(index);
    auto it_ns = before_encode_ns_map_.find(index);
    if (it_ns != before_encode_ns_map_.end()) {
      before_encode_ns = it_ns->second;
      before_encode_ns_map_.erase(it_ns);
    }
  }
  if (before_encode_ns != 0) {
    CollectCostUS((nano_now() - before_encode_ns) / 1000);
  }
}

EndtoEndMgr EndtoEndDelayEncoder::PopEndtoEndMgr(int64_t index) {
  EndtoEndMgr e2e_mgr;
  int64_t before_encode_ns = 0;

  {
    std::lock_guard<std::mutex> lg(mgr_mutex_);
    auto it = e2e_mgrs_.find(index);
    auto it_ns = before_encode_ns_map_.find(index);

    // not find, return empty EndtoEndMgr
    if (it == e2e_mgrs_.end() || it_ns == before_encode_ns_map_.end()) {
      if (it != e2e_mgrs_.end()) {
        e2e_mgrs_.erase(it);
      }
      if (it_ns != before_encode_ns_map_.end()) {
        before_encode_ns_map_.erase(it_ns);
      }
      return e2e_mgr;
    }

    // found
    e2e_mgr = it->second;
    before_encode_ns = it_ns->second;
    e2e_mgrs_.erase(it);
    before_encode_ns_map_.erase(it_ns);
  }

  int64_t after_encode_ns = nano_now();
  e2e_mgr.SetEncodeEndNS(after_encode_ns);
  CollectCostUS((after_encode_ns - before_encode_ns) / 1000);
  return e2e_mgr;
}

void EndtoEndDelayEncoder::CollectCostUS(int64_t cost_us) {
  DCHECK(cost_us > 0);
  std::lock_guard<std::mutex> lg(cost_mutex_);
  cost_data_.total_us += cost_us;
  cost_data_.cnt += 1;
}

bool EndtoEndDelayEncoder::FillCostData(
    std::map<std::string, int64_t>& cost_map) {
  EndtoEndDelayCostData cost_data;
  {
    std::lock_guard<std::mutex> lg(cost_mutex_);
    cost_data = cost_data_;
    cost_data_ = EndtoEndDelayCostData();
  }
  if (0 == cost_data.cnt) {
    return false;
  }
  int64_t cost_ms = cost_data.total_us / 1000 / cost_data.cnt;
  cost_map.insert_or_assign(name_, cost_ms);
  return true;
}

}  // namespace mediasdk
