#include "color_revert_shader.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>

using namespace Microsoft::WRL;

namespace graphics {

static const char* CONST_PIXEL_SHADER() {
  return R"(
Texture2D shaderTexture : register(t0);
SamplerState sampleType : register(s0);

struct PS_Pos
{
    float4 pos : SV_POSITION;
};
cbuffer PS_Param : register(b0)
{
    float revert_r;
    float revert_g;
    float revert_b;
    float revert_a;
};

float4 PS_MAIN(PS_Pos input) : SV_TARGET
{
    float4 color = shaderTexture.Load(int3(input.pos.xy,0));
	
    if(revert_r > 0.f)
    {
        color.r = 1.f - color.r;
    }
    
    if(revert_g > 0.f)
    {
        color.g = 1.f - color.g;
    }
    
    if(revert_b > 0.f)
    {
        color.b = 1.f - color.b;
    }
    
    if(revert_a > 0.f)
    {
        color.a = 1.f - color.a;
    }

    return color;
}
)";
}

static const char* CONST_VERTEX_SHADER() {
  return R"(

struct PS_Pos
{
    float4 pos : SV_POSITION;
};

PS_Pos VS_MAIN(uint vid : SV_VERTEXID)
{
	bool right = vid == 2;
	bool top = vid == 1;
	float x = - 1.0 + (right ? 4.0 : 0.0);
	float y = - 1.0 + (top ? 4.0 : 0.0);
	PS_Pos ret;
    ret.pos = float4(x,y,0.f,1.f);
    return ret;
}
)";
}

bool ColorRevertShader::Init(const std::shared_ptr<Device>& ins) {
  if (!try_init_) {
    init_suc_ = _Init(ins);
    try_init_ = true;
  }

  return init_suc_;
}

bool ColorRevertShader::_Init(const std::shared_ptr<Device>& ins) {
  device_ = ins;
  Device::CompileShaderParam param = {};
  param.ps = CONST_PIXEL_SHADER();
  param.vs = CONST_VERTEX_SHADER();
  param.ps_name = "PS_MAIN";
  param.vs_name = "VS_MAIN";

  if (!device_->CompileShader(param)) {
    return false;
  }
  vs_shader_ = param.vs_shader_;
  ps_shader_ = param.ps_shader_;
  D3D11SetDebugObjectName(vs_shader_.Get(), "revert_shader_vs");
  D3D11SetDebugObjectName(ps_shader_.Get(), "revert_shader_ps");
  D3D11_SAMPLER_DESC desc;
  desc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
  desc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.MipLODBias = 0.0F;
  desc.MaxAnisotropy = 1;
  desc.ComparisonFunc = D3D11_COMPARISON_ALWAYS;
  desc.BorderColor[0] = 0;
  desc.BorderColor[1] = 0;
  desc.BorderColor[2] = 0;
  desc.BorderColor[3] = 0;
  desc.MinLOD = 0;
  desc.MaxLOD = D3D11_FLOAT32_MAX;
  HRESULT hRes = GetDevice_()->CreateSamplerState(&desc, &sampler_);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateSamplerState(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  D3D11SetDebugObjectName(sampler_.Get(), "revert_shader_sampler");
  return true;
}

void ColorRevertShader::RenderTexture(ID3D11ShaderResourceView* pSRView,
                                      ComPtr<ID3D11Buffer>& psBuffer) {
  auto context = GetContext_();
  context->PSSetConstantBuffers(0, 1, psBuffer.GetAddressOf());
  context->PSSetShaderResources(0, 1, &pSRView);

  context->IASetInputLayout(nullptr);

  context->VSSetShader(vs_shader_.Get(), NULL, 0);
  context->PSSetShader(ps_shader_.Get(), NULL, 0);
  context->PSSetSamplers(0, 1, sampler_.GetAddressOf());
  context->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);
  context->Draw(3, 0);
}

ComPtr<ID3D11Device> ColorRevertShader::GetDevice_() {
  return device_->GetDevice();
}

ID3D11DeviceContext* ColorRevertShader::GetContext_() {
  return device_->GetContext().Get();
}

void ColorRevertShader::Destroy() {
  if (sampler_) {
    sampler_.Reset();
  }

  if (ps_shader_) {
    ps_shader_.Reset();
  }
  if (vs_shader_) {
    vs_shader_.Reset();
  }
}

ColorRevertShader::~ColorRevertShader() {
  ColorRevertShader::Destroy();
}
}  // namespace graphics