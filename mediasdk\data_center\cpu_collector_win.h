#pragma once
#include <atomic>
#include <mutex>
#include "cpu_collector.h"
#include "cpu_information.h"
#include "pdh_collector.h"
#include "process_collector_win.h"

namespace cpu_collector {

class CpuCollectorImpl final : public CpuCollector {
 public:
  CpuCollectorImpl();

  ~CpuCollectorImpl() override = default;

  double ProcessUsage() override;

  bool SystemUsage(double& system_usage, double& cpu_time) override;

  bool CpuInfo(CpuHardwareInfo& info) override;

 private:
  ProcessCollector process_collector_;
  PdhCollector pdh_collector_;
  std::shared_ptr<CpuInformation> cpu_information_ = nullptr;

  int64_t last_query_process_time_ = 0;
  double last_process_usage_ = 0.0;
  int64_t last_query_system_time_ = 0;
  double last_system_usage_ = 0.0;
  double last_cpu_time_ = 0.0;

  std::mutex mutex_;
};
}  // namespace cpu_collector