#include "rtc_audio_input_source_factory.h"
#include "mediasdk/component_proxy.h"
#include "mediasdk/rtc/rtc_audio_input_source.h"
#include "mediasdk/rtc/rtc_controller.h"

namespace mediasdk {

RTCAudioInputSourceFactory::RTCAudioInputSourceFactory(
    std::shared_ptr<PluginInfo> info)
    : AudioInputSourceFactory(nullptr, info) {
  SetInitialized(true);
}

RTCAudioInputSourceFactory::~RTCAudioInputSourceFactory() {
  DestroyAll();
  LOG(INFO) << "[RTCAudioInputSourceFactory] Destroy";
}

std::shared_ptr<AudioInputSource> RTCAudioInputSourceFactory::CreateSource(
    std::shared_ptr<AudioInputProxy> proxy,
    const std::string& json_params) {
  auto source = RTCAudioInputSource::Create(proxy.get(), json_params);
  if (!source) {
    LOG(ERROR) << "[RTCAudioInputSourceFactory] Create source failed";
    return nullptr;
  }

  // Register audio listening with RTC component and use weak_ptr to maintain
  // the listening relationship. When AudioInputSource dies, this listening
  // automatically becomes invalid
  RTCControllerProxy::Call(
      FROM_HERE, MSCallback<int>(), &RTCController::SetRemoteAudioRender,
      source->GetUserID(), source->GetStreamIndex(), source->GetRender());

  sources_.insert(source);
  LOG(INFO) << "[RTCAudioInputSourceFactory] Create source success";
  return std::dynamic_pointer_cast<AudioInputSource>(source);
}

void RTCAudioInputSourceFactory::Destroy(
    std::shared_ptr<AudioInputSource> source) {
  LOG(INFO) << "[RTCAudioInputSourceFactory] Will destroy source";

  auto rtc_source = std::dynamic_pointer_cast<RTCAudioInputSource>(source);
  if (!rtc_source) {
    return;
  }

  auto it = sources_.find(rtc_source);
  if (it != sources_.end()) {
    bool need_release_render = false;
    std::string user_id;
    int stream_index = -1;
    if (*it) {
      need_release_render = true;
      user_id = (*it)->GetUserID();
      stream_index = (*it)->GetStreamIndex();
    }
    sources_.erase(it);
    if (need_release_render) {
      RTCControllerProxy::Call(FROM_HERE, MSCallback<int>(),
                               &RTCController::SetRemoteAudioRender, user_id,
                               stream_index, nullptr);
    }
  } else {
    NOTREACHED();
    LOG(WARNING) << "[RTCAudioInputSourceFactory] can not find rtc_source:"
                 << std::hex << rtc_source.get();
  }
}

void RTCAudioInputSourceFactory::DestroyAll() {
  auto it = sources_.begin();
  while (it != sources_.end()) {
    bool need_release_render = false;
    std::string user_id;
    int stream_index = -1;
    if (*it) {
      need_release_render = true;
      user_id = (*it)->GetUserID();
      stream_index = (*it)->GetStreamIndex();
    }
    it = sources_.erase(it);
    if (need_release_render) {
      RTCControllerProxy::Call(FROM_HERE, MSCallback<int>(),
                               &RTCController::SetRemoteAudioRender, user_id,
                               stream_index, nullptr);
    }
  }
  LOG(INFO) << "[RTCAudioInputSourceFactory] clear all source, left:"
            << sources_.size();
}

MediaSDKStringData RTCAudioInputSourceFactory::EnumAudioInput() {
  return MediaSDKString().Detach();
}

MediaSDKStringData RTCAudioInputSourceFactory::GetDefaultAudioInput() {
  return MediaSDKString().Detach();
}

MediaSDKStringData RTCAudioInputSourceFactory::GetDefaultAudioOutput() {
  return MediaSDKString().Detach();
}

MediaSDKStringData RTCAudioInputSourceFactory::EnumCaptureAudio() {
  return MediaSDKString().Detach();
}

MediaSDKStringData RTCAudioInputSourceFactory::EnumRenderAudio() {
  return MediaSDKString().Detach();
}

}  // namespace mediasdk