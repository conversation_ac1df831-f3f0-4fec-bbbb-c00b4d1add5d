#include "custom_audio_input.h"

#include "audio/audio_frame_utils.h"
#include "base/mediasdk/thread_safe_deleter.h"
#include "frame_convert.h"

namespace {
static const char kCustomAudioInputName[] = "CustomAudioInput";
}

namespace mediasdk {

std::shared_ptr<CustomAudioInput> CustomAudioInput::Create(
    const std::string& id,
    const AudioFormat& output_format,
    hook_api::CustomAudioInputDelegate* delegate) {
  DCHECK(delegate);

  if (!delegate) {
    LOG(ERROR) << "Invalid delegate for CustomAudioInput";
    return nullptr;
  }

  return std::shared_ptr<CustomAudioInput>(
      new CustomAudioInput(id, output_format, delegate),
      base::ThreadSafeDeleter<CustomAudioInput>());
}

CustomAudioInput::CustomAudioInput(const std::string& id,
                                   const AudioFormat& output_format,
                                   hook_api::CustomAudioInputDelegate* delegate)
    : AudioInput(id, output_format), delegate_(delegate) {
  if (delegate_) {
    delegate_->AttachProxy(this);
  }
}

CustomAudioInput::~CustomAudioInput() noexcept {
  if (delegate_) {
    delegate_->DetachProxy();
  }

#if ENABLE_HOOK_API_AUDIO_DUMP
  if (hook_api_audio_dump_helper_) {
    hook_api_audio_dump_helper_.reset();
  }
#endif  // ENABLE_HOOK_API_AUDIO_DUMP
}

void CustomAudioInput::AttachSource(std::shared_ptr<AudioInputSource> source) {}

std::shared_ptr<mediasdk::AudioInputSource> CustomAudioInput::DetachSource() {
  return nullptr;
}

std::string CustomAudioInput::GetDevName() {
  return kCustomAudioInputName;
}

std::string CustomAudioInput::GetName() {
  return kCustomAudioInputName;
}

void CustomAudioInput::OnSetMute(bool mute) {
  if (delegate_) {
    delegate_->OnSetMute(mute);
  }
}

void CustomAudioInput::OnSetVolume(float volume) {
  if (delegate_) {
    delegate_->OnSetVolume(volume);
  }
}

void CustomAudioInput::InputCustomAudioData(const AudioSourceFrame& frame) {
  // Supported audio output format as default defined by AudioInput
  AudioFormat format = GetOutputFormat();
  AudioFrame audio_frame = AudioSourceFrameToAudioFrame(frame);

#if ENABLE_HOOK_API_AUDIO_DUMP
  DumpAudioToFile(format, audio_frame);
#endif  // ENABLE_HOOK_API_AUDIO_DUMP

  ProcessAudioFrame(format, audio_frame);
}

#if ENABLE_HOOK_API_AUDIO_DUMP
void CustomAudioInput::DumpAudioToFile(const AudioFormat& format,
                                       const AudioFrame& frame) {
  if (!hook_api_audio_dump_helper_) {
    hook_api_audio_dump_helper_ =
        std::make_unique<HookApiAudioDumpHelper>(GetId() + "_custom");
  }

  if (hook_api_audio_dump_helper_) {
    hook_api_audio_dump_helper_->DumpAudioFrame(format, frame);
  }
}
#endif  // ENABLE_HOOK_API_AUDIO_DUMP

}  // namespace mediasdk
