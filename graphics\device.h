#pragma once

#include <DirectXMath.h>
#include <d3d11.h>
#include <dxgi.h>
#include <windows.h>
#include <wrl/client.h>
#include <cstdint>
#include <memory>
#include <string>
#include "d3d11_device_context_debug_layer.h"
#include "graphics_export.h"
#include "mediasdk/public/mediasdk_defines.h"

namespace graphics {

struct ScopedBlendHelper;
class ShaderManager;
class Device;

struct GRAPHICS_EXPORT CreateDeviceOption {
  mediasdk::MSLUID adapter_id;
};

class GRAPHICS_EXPORT DeviceLostObserver {
 public:
  struct DevLostEvent {
    int32_t error;
    int32_t remove_reason;
    bool need_restart;
    std::string driver_date;
    std::string driver_name;
    std::string driver_ver;
  };

  virtual ~DeviceLostObserver() = default;
  virtual void OnPreDevLost(Device*) = 0;
  virtual void OnDevLost(Device*, const DevLostEvent& event) = 0;
};

class GRAPHICS_EXPORT Device : public std::enable_shared_from_this<Device> {
 public:
  struct GRAPHICS_EXPORT CompileShaderParam {
    const char* ps = nullptr;
    const char* vs = nullptr;
    const char* ps_name = nullptr;
    const char* vs_name = nullptr;
    Microsoft::WRL::ComPtr<ID3D11VertexShader> vs_shader_;
    Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_shader_;
    D3D11_INPUT_ELEMENT_DESC* layout_descs_ = nullptr;
    UINT32 layout_cnt_ = 0;
    Microsoft::WRL::ComPtr<ID3D11InputLayout> layout_;
  };

  virtual bool D3D11CreateVertexShader(
      const char* shader,
      const char* name,
      Microsoft::WRL::ComPtr<ID3D11VertexShader>& vs,
      Microsoft::WRL::ComPtr<ID3D10Blob>& vsblob) = 0;

  virtual bool CompileShader(CompileShaderParam& param) = 0;

  virtual bool D3D11CompileShader(const char* shader,
                                  const char* name,
                                  const char* target,
                                  Microsoft::WRL::ComPtr<ID3D10Blob>& vs) = 0;

  virtual void GetViewport(DirectX::XMFLOAT2& pos, DirectX::XMFLOAT2& size) = 0;

  virtual void SetViewport(const DirectX::XMFLOAT2& pos,
                           const DirectX::XMFLOAT2& size) = 0;

  virtual void SignalDeviceLostEventObserver(
      const graphics::DeviceLostObserver::DevLostEvent& event) = 0;

  virtual void AddDeviceLostEventObserver(DeviceLostObserver*) = 0;

  virtual void RemoveDeviceLostEventObserver(DeviceLostObserver*) = 0;

  virtual Microsoft::WRL::ComPtr<IDXGIFactory1> GetDXGIFactory() = 0;

  virtual Microsoft::WRL::ComPtr<IDXGIAdapter> GetAdapter() = 0;

  virtual LUID GetAdapterLUID() = 0;

  virtual Microsoft::WRL::ComPtr<ID3D11Device> GetDevice() = 0;

  virtual Microsoft::WRL::ComPtr<ID3D11DeviceContext> GetContext() = 0;

  virtual ShaderManager* GetShaderManager() = 0;

  virtual void AllowBlend(bool bAllow) = 0;

  virtual bool D3D11CreatePixelShader(
      const char* shader,
      const char* name,
      Microsoft::WRL::ComPtr<ID3D11PixelShader>& ps) = 0;

  virtual void Destroy() = 0;

  virtual void ReportLiveObjects() = 0;

  virtual bool SetGPUThreadPriority(int32_t priority) = 0;

  virtual std::string GetDriverDate() const = 0;

  virtual std::string GetDriverVersion() const = 0;

  virtual void CheckDevLost(HRESULT res) = 0;

  virtual ~Device() {}
};

struct GRAPHICS_EXPORT ScopedBlendHelper {
  ScopedBlendHelper(Device& dev) : dev_(dev) {
    dev_.GetContext()->OMGetBlendState(&pre_allow_, pre_blend_factor_,
                                       &pre_sample_mask_);
  }

  ~ScopedBlendHelper() {
    // recover blend state
    dev_.GetContext()->OMSetBlendState(pre_allow_.Get(), pre_blend_factor_,
                                       pre_sample_mask_);
  }

  Device& dev_;
  Microsoft::WRL::ComPtr<ID3D11BlendState> pre_allow_;
  float pre_blend_factor_[4] = {};
  UINT pre_sample_mask_ = 0xFFFFFFFF;
};

GRAPHICS_EXPORT void LogGraphicsEnvironment();

GRAPHICS_EXPORT std::string GetErrorString(HRESULT);

GRAPHICS_EXPORT std::string GetAdapterName(
    Microsoft::WRL::ComPtr<IDXGIAdapter> adapter);

GRAPHICS_EXPORT std::shared_ptr<Device> CreateDevice(
    const CreateDeviceOption& opt);

GRAPHICS_EXPORT void D3D11SetDebugObjectName(ID3D11DeviceChild* object,
                                             const char* name);

GRAPHICS_EXPORT bool GetPCIDriverDate(uint32_t vendorId,
                                      uint32_t deviceId,
                                      uint32_t subSysId,
                                      std::string& driver_date,
                                      std::string& driver_version);

}  // namespace graphics
