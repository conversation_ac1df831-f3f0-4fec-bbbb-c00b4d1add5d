﻿#include "texture_impl.h"

#include <base/check.h>
#include <base/files/file.h>
#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <base/strings/sys_string_conversions.h>
#include <comdef.h>
#include <dxgi.h>
#include <dxgitype.h>
#include <stdio.h>
#include <windef.h>
#include <windows.h>
#include <wrl/client.h>
#include "base/notreached.h"
#include "framework.h"
#include "mediasdk/debug_helper.h"
#include "third_party/WICTextureLoader/WICTextureLoader11.h"
#include "third_party/WICTextureSaveAS/ScreenGrab11.h"
#include "wincodec.h"
#include "yuv_to_bgra_graphics.h"

using namespace Microsoft::WRL;

namespace graphics {

int32_t GetLineByteSize(const DXGI_FORMAT format) {
  switch (format) {
    case DXGI_FORMAT::DXGI_FORMAT_B8G8R8A8_UNORM:
    case DXGI_FORMAT::DXGI_FORMAT_R8G8B8A8_UNORM:
    case DXGI_FORMAT::DXGI_FORMAT_R8G8B8A8_UNORM_SRGB:
    case DXGI_FORMAT::DXGI_FORMAT_B8G8R8A8_UNORM_SRGB:
    case DXGI_FORMAT::DXGI_FORMAT_B8G8R8X8_TYPELESS:

      return 4;
    case DXGI_FORMAT::DXGI_FORMAT_NV12: {
      return 1;
    }
    case DXGI_FORMAT::DXGI_FORMAT_R16G16B16A16_FLOAT:
      return 8;
    case DXGI_FORMAT::DXGI_FORMAT_R8_UNORM:
      return 1;
    case DXGI_FORMAT::DXGI_FORMAT_R32G32B32_FLOAT:
      return 12;

    default:
      DCHECK(false);
      break;
  }
  return 0;
}

DirectX::XMFLOAT2 TextureImpl::GetSize() const {
  DirectX::XMFLOAT2 ret = {};
  ret.x = static_cast<float>(desc_.Width);
  ret.y = static_cast<float>(desc_.Height);

  return ret;
}

bool TextureImpl::IsEmpty() const {
  return !texture_;
}

bool TextureImpl::CopyTextureFrom(ID3D11Texture2D* texture) {
  if (!texture)
    return false;
  GetContext()->CopyResource(texture_.Get(), texture);
  return true;
}

bool TextureImpl::CopySubFrom(ID3D11Texture2D* texture,
                              const DirectX::XMFLOAT2& start,
                              const DirectX::XMFLOAT2& end) {
  D3D11_BOX box = {};
  box.left = start.x;
  box.top = start.y;
  box.right = end.x;
  box.bottom = end.y;
  box.front = 0;
  box.back = 1;
  GetContext()->CopySubresourceRegion(texture_.Get(), 0, 0, 0, 0, texture, 0,
                                      &box);
  return true;
}

bool TextureImpl::CopySubTo(ID3D11Texture2D* texture,
                            const DirectX::XMFLOAT2& start,
                            DirectX::XMFLOAT2& end) {
  D3D11_BOX box = {};
  box.left = start.x;
  box.top = start.y;
  box.right = end.x;
  box.bottom = end.y;
  box.front = 0;
  box.back = 1;
  GetContext()->CopySubresourceRegion(texture, 0, 0, 0, 0, texture_.Get(), 0,
                                      &box);
  return true;
}

bool TextureImpl::CopyFrom(uint8_t* data, int32_t line_size, int cy) {
  DCHECK((GetUsage() & Texture::TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_WRITE) !=
         0);
  D3D11_MAPPED_SUBRESOURCE ms = {};
  if (!Map(ms, false))
    return false;

  uint8_t* ps = static_cast<uint8_t*>(ms.pData);
  VideoPlanerCopy(ps, ms.RowPitch, this->desc_.Height, data, line_size, cy);

  UnMap();
  return true;
}

bool TextureImpl::CopyTo(uint8_t* data, int32_t line_size, int cy) {
  DCHECK((GetUsage() & Texture::TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_READ) !=
         0);
  D3D11_MAPPED_SUBRESOURCE ms = {};
  if (!Map(ms, true))
    return false;

  {
    uint8_t* ps = static_cast<uint8_t*>(ms.pData);
    if (GetDesc().Format == DXGI_FORMAT::DXGI_FORMAT_NV12) {
      VideoPlanerCopy(data, line_size, cy, ps, ms.RowPitch, cy);
    } else {
      VideoPlanerCopy(data, line_size, cy, ps, ms.RowPitch, GetDesc().Height);
    }
  }

  UnMap();

  return true;
}

bool TextureImpl::CopyTo(ID3D11Texture2D* texture) const {
  GetContext()->CopyResource(texture, texture_.Get());
  return true;
}

bool TextureImpl::Map(D3D11_MAPPED_SUBRESOURCE& ms,
                      bool readOnly,
                      unsigned int flag) {
  DCHECK((!!texture_));
  if (readOnly) {
    DCHECK(GetUsage() & TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_READ);
  } else {
    DCHECK(GetUsage() & TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_WRITE);
  }
  if (texture_ != NULL) {
    HRESULT hRes = GetContext()->Map(
        texture_.Get(), 0, readOnly ? D3D11_MAP_READ : D3D11_MAP_WRITE_DISCARD,
        flag, &ms);
    if (flag == 0 && hRes != S_OK) {
        LOG(ERROR) << base::StringPrintf("Failed to Map({%s})",
                                         GetErrorString(hRes).c_str());
        device_.CheckDevLost(hRes);
    }
    if (hRes == S_OK) {
        need_un_map_ = true;
    }
    return SUCCEEDED(hRes);
  }
  return false;
}

bool TextureImpl::NeedUnMap() const {
  return need_un_map_;
}

void TextureImpl::UnMap() {
  DCHECK((!!texture_));
  if (texture_ != NULL) {
    GetContext()->Unmap(texture_.Get(), 0);
  }
  need_un_map_ = false;
}

bool TextureImpl::IsKeyedMutex() const {
  if (!texture_)
    return false;
  if (desc_.MiscFlags & D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX) {
    return true;
  }
  return false;
}

void TextureImpl::ReleaseKeyedAccess(uint64_t key) {
  if (texture_) {
    ComPtr<IDXGIKeyedMutex> pDXGIKeyedMutex;
    texture_.As(&pDXGIKeyedMutex);
    if (pDXGIKeyedMutex) {
      pDXGIKeyedMutex->ReleaseSync(key);
    }
  }
}

bool TextureImpl::AcquireKeyedAccess(uint64_t key, uint32_t milliseconds) {
  if (texture_) {
    ComPtr<IDXGIKeyedMutex> pDXGIKeyedMutex;

    // Check the keyed mutex
    texture_.As(&pDXGIKeyedMutex);
    if (pDXGIKeyedMutex) {
      HRESULT hr = pDXGIKeyedMutex->AcquireSync(key, milliseconds);
      switch (hr) {
        case WAIT_OBJECT_0:
          // The state of the object is signaled.
          return true;
        case WAIT_ABANDONED:
          break;
        case WAIT_TIMEOUT:  // The time-out interval elapsed, and the object's
                            // state is non-signaled.
          break;
        default:
          break;
      }
      {
        LOG(ERROR) << "Failed to Acquire Sync[" << GetErrorString(hr) << "]";
      }
    }
  }
  return false;
}

bool TextureImpl::SetupVertex() {
  DCHECK((GetUsage() & Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE) !=
         0);
  if (IsKeyedMutex()) {
    if (!AcquireKeyedAccess(0)) {
      return false;
    }
  }

  return true;
}

ID3D11Texture2D* TextureImpl::GetTexture() const {
  return texture_.Get();
}

ID3D11ShaderResourceView* TextureImpl::GetSRV(int plane) const {
  DCHECK((GetUsage() & Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE) !=
         0);
  auto ret = resource_views_[plane].Get();
  return ret;
}

D3D11_TEXTURE2D_DESC TextureImpl::GetDesc() const {
  return desc_;
}

HRESULT TextureImpl::BuildFromTexture(ID3D11Texture2D* texture,
                                      uint64_t usage) {
  usage_ = usage;
  HRESULT res = BuildFromTextureView(texture, nullptr, usage);
  if (FAILED(res)) {
    device_.CheckDevLost(res);
    return res;
  }
  res = BuildResourceView();
  return res;
}

HRESULT TextureImpl::BuildFromTextureView(ID3D11Texture2D* texture,
                                          ID3D11ShaderResourceView* view,
                                          uint64_t usage) {
  DCHECK(usage != 0);
  usage_ = usage;
  texture_ = texture;
  resource_views_[0] = view;
  texture_->GetDesc(&desc_);

  return S_OK;
}

HRESULT TextureImpl::BuildFromSharedHandle(HANDLE handle, uint64_t usage) {
  usage_ = usage;
  if (!handle) {
    DCHECK(false);
    return E_INVALIDARG;
  }
  HRESULT res = S_FALSE;
  ComPtr<ID3D11Resource> resource;
  if (FAILED(res = device_.GetDevice()->OpenSharedResource(
                 handle, __uuidof(ID3D11Resource), (void**)&resource))) {
    LOG(ERROR) << base::StringPrintf("Failed to Open Shared Handle {%s}",
                                     GetErrorString(res).c_str());
    return res;
  }

  if (FAILED(res = resource->QueryInterface(__uuidof(ID3D11Texture2D),
                                            (void**)&texture_))) {
    LOG(ERROR) << base::StringPrintf("Failed to QueryInterface {%s}",
                                     GetErrorString(res).c_str());
    return res;
  }
  texture_->GetDesc(&desc_);
  shared_handle_ = handle;
  return BuildResourceView();
}

HRESULT TextureImpl::BuildResourceView() {
  if (!(GetUsage() & Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE)) {
    return S_OK;
  }
  HRESULT res = S_OK;
  switch (desc_.Format) {
    case DXGI_FORMAT_NV12: {
      D3D11_SHADER_RESOURCE_VIEW_DESC dsrvd;
      ::ZeroMemory(&dsrvd, sizeof(dsrvd));
      dsrvd.Format = DXGI_FORMAT_R8_UNORM;
      dsrvd.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
      dsrvd.Texture2D.MipLevels = desc_.MipLevels;
      if (FAILED(res = device_.GetDevice()->CreateShaderResourceView(
                     texture_.Get(), &dsrvd, &resource_views_[0]))) {
        LOG(ERROR) << base::StringPrintf(
            "Failed to CreateShaderResourceView(%s)",
            GetErrorString(res).c_str());
        device_.CheckDevLost(res);
        return res;
      }
      ::ZeroMemory(&dsrvd, sizeof(dsrvd));
      dsrvd.Format = DXGI_FORMAT_R8G8_UNORM;
      dsrvd.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
      dsrvd.Texture2D.MipLevels = desc_.MipLevels;
      if (FAILED(res = device_.GetDevice()->CreateShaderResourceView(
                     texture_.Get(), &dsrvd, &resource_views_[1]))) {
        LOG(ERROR) << base::StringPrintf(
            "Failed to CreateShaderResourceView(%s)",
            GetErrorString(res).c_str());
        device_.CheckDevLost(res);
        return res;
      }
      return res;
    } break;
    case DXGI_FORMAT_P010:
    case DXGI_FORMAT_P016: {
      DCHECK(false);
    } break;
    default: {
      D3D11_SHADER_RESOURCE_VIEW_DESC dsrvd;
      ::ZeroMemory(&dsrvd, sizeof(dsrvd));
      dsrvd.Format = desc_.Format;
      dsrvd.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
      dsrvd.Texture2D.MipLevels = desc_.MipLevels;
      res = device_.GetDevice()->CreateShaderResourceView(
          texture_.Get(), &dsrvd, &resource_views_[0]);
      if (res != S_OK) {
        LOG(ERROR) << base::StringPrintf(
            "Failed to CreateShaderResourceView(%s)",
            GetErrorString(res).c_str());
        return res;
      }
      return res;
    } break;
  }
  return E_NOTIMPL;
}

HRESULT TextureImpl::Build3DResourceView() {
  if (!(GetUsage() & Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE)) {
    return S_OK;
  }
  HRESULT res = S_OK;
  switch (desc3d_.Format) {
    case DXGI_FORMAT_B8G8R8A8_UNORM:
    default: {
      D3D11_SHADER_RESOURCE_VIEW_DESC dsrvd;
      ::ZeroMemory(&dsrvd, sizeof(dsrvd));
      dsrvd.Format = desc3d_.Format;
      dsrvd.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE3D;
      dsrvd.Texture3D.MostDetailedMip = 0;
      dsrvd.Texture3D.MipLevels = -1;
      res = device_.GetDevice()->CreateShaderResourceView(
          texture3d_.Get(), &dsrvd, &resource_views_[0]);
      if (res != S_OK) {
        LOG(ERROR) << base::StringPrintf(
            "Failed to CreateShaderResourceView(%s)",
            GetErrorString(res).c_str());
        return res;
      }
      return res;
    } break;
  }
  return E_NOTIMPL;
}

HRESULT TextureImpl::BuildFromDesc(int cx,
                                   int cy,
                                   DXGI_FORMAT format,
                                   uint64_t usage) {
  DCHECK(usage != 0);
  usage_ = usage;
  HRESULT res = S_OK;
  desc_ = {};
  desc_.Width = cx;
  desc_.Height = cy;
  desc_.MipLevels = 1;
  desc_.ArraySize = 1;
  desc_.Format = format;
  desc_.SampleDesc.Count = 1;
  desc_.SampleDesc.Quality = 0;
  if (usage & TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_READ) {
    desc_.BindFlags = 0;
    desc_.CPUAccessFlags |= D3D11_CPU_ACCESS_READ;
    desc_.Usage = D3D11_USAGE_STAGING;
  } else if (usage & TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_WRITE) {
    desc_.BindFlags = 0;
    desc_.CPUAccessFlags |= D3D11_CPU_ACCESS_WRITE;
    desc_.Usage = D3D11_USAGE_DYNAMIC;
  }
  if (usage & TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE) {
    desc_.BindFlags |= D3D11_BIND_SHADER_RESOURCE;
  }
  if (usage & Texture::TEXTURE_USAGE::TEXTURE_USAGE_GDI) {
    desc_.MiscFlags |= D3D11_RESOURCE_MISC_GDI_COMPATIBLE;
    DCHECK(!(usage & Texture::TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_WRITE) &&
           "GDI can not write by CPU");
    if (!(usage & Texture::TEXTURE_USAGE::TEXTURE_USAGE_RENDER_TARGET)) {
      DCHECK(false && "GDI need  RenderTarget Flag");
      LOG(ERROR) << "GDI need TEXTURE_USAGE_RENDER_TARGET flag";
      return E_INVALIDARG;
    }
  }
  if (usage & Texture::TEXTURE_USAGE::TEXTURE_USAGE_RENDER_TARGET) {
    desc_.BindFlags |= D3D11_BIND_RENDER_TARGET;
  }

  if (usage & Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHARED) {
    desc_.MiscFlags |= D3D11_RESOURCE_MISC_SHARED;
    // desc_.BindFlags |= D3D11_BIND_SHADER_RESOURCE;
  } else if (usage & Texture::TEXTURE_USAGE::TEXTURE_USAGE_KEYED_MUTEXED) {
    desc_.MiscFlags |= D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX;
  }

  res = device_.GetDevice()->CreateTexture2D(&desc_, nullptr, &texture_);
  if (res != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateTexture2D(0x%s): ",
                                     GetErrorString(res).c_str());
    device_.CheckDevLost(res);
    return res;
  }
  if (usage & TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE) {
    if (FAILED(res = BuildResourceView())) {
      return res;
    }
  }

  return res;
}

HRESULT TextureImpl::BuildTextureFromBuffer(const uint8_t* buffer,
                                            int width,
                                            int height,
                                            DXGI_FORMAT format,
                                            uint64_t usage) {
  usage_ = usage;
  HRESULT hRes = S_OK;
  desc_ = {};
  desc_.Width = width;
  desc_.Height = height;
  desc_.MipLevels = 1;
  desc_.ArraySize = 1;
  desc_.Format = format;
  desc_.SampleDesc.Count = 1;
  desc_.SampleDesc.Quality = 0;
  desc_.BindFlags = D3D11_BIND_SHADER_RESOURCE;
  desc_.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  desc_.Usage = D3D11_USAGE_DYNAMIC;
  D3D11_SUBRESOURCE_DATA dsd;
  D3D11_SUBRESOURCE_DATA* lpSRD = NULL;
  dsd.pSysMem = static_cast<void*>(const_cast<uint8_t*>(buffer));
  UINT32 linesize = width * GetLineByteSize(format);
  UINT32 totalsize = height * linesize;
  dsd.SysMemPitch = linesize;
  dsd.SysMemSlicePitch = totalsize;
  lpSRD = &dsd;
  hRes = device_.GetDevice()->CreateTexture2D(&desc_, lpSRD, &texture_);
  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateTexture2D(0x%X): ", hRes);
    device_.CheckDevLost(hRes);
    return hRes;
  }
  return BuildResourceView();
}

HRESULT TextureImpl::Build3DTextureFromBuffer(const uint8_t* buffer,
                                              int width,
                                              int height,
                                              int depth,
                                              DXGI_FORMAT format,
                                              uint64_t usage) {
  usage_ = usage;
  HRESULT hRes = S_OK;
  desc3d_ = {};
  desc3d_.Width = width;
  desc3d_.Height = height;
  desc3d_.Depth = depth;
  desc3d_.MipLevels = 1;
  desc3d_.Format = format;
  desc3d_.BindFlags = D3D11_BIND_SHADER_RESOURCE;
  desc3d_.CPUAccessFlags = 0;  // D3D11_CPU_ACCESS_WRITE;
  desc3d_.Usage = D3D11_USAGE_DEFAULT;
  D3D11_SUBRESOURCE_DATA dsd;
  D3D11_SUBRESOURCE_DATA* lpSRD = NULL;
  dsd.pSysMem = static_cast<void*>(const_cast<uint8_t*>(buffer));
  UINT32 linesize = width * GetLineByteSize(format);
  dsd.SysMemPitch = linesize;
  dsd.SysMemSlicePitch = linesize * height;
  lpSRD = &dsd;
  hRes = device_.GetDevice()->CreateTexture3D(&desc3d_, lpSRD, &texture3d_);
  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateTexture3D(0x%X): ", hRes);
    device_.CheckDevLost(hRes);
    return hRes;
  }

  return Build3DResourceView();
}

HRESULT TextureImpl::BuildFromFrameBuffer(const TextureFrame& frame,
                                          uint64_t usage) {
  usage_ = usage;
  HRESULT hRes = S_OK;
  desc_ = {};
  desc_.Width = frame.width;
  desc_.Height = frame.height;
  desc_.MipLevels = 1;
  desc_.ArraySize = 1;
  desc_.Format = PixelFormatToDXGIFormat(frame.format);
  desc_.SampleDesc.Count = 1;
  desc_.SampleDesc.Quality = 0;
  desc_.BindFlags = D3D11_BIND_SHADER_RESOURCE;
  desc_.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
  desc_.Usage = D3D11_USAGE_DYNAMIC;
  D3D11_SUBRESOURCE_DATA dsd;
  D3D11_SUBRESOURCE_DATA* lpSRD = NULL;
  dsd.pSysMem = static_cast<void*>(const_cast<uint8_t*>(frame.data[0]));
  UINT32 linesize = frame.width * 4;  // BGRA 4 BYTE
  UINT32 totalsize = frame.height * linesize;
  dsd.SysMemPitch = linesize;
  dsd.SysMemSlicePitch = totalsize;
  lpSRD = &dsd;
  hRes = device_.GetDevice()->CreateTexture2D(&desc_, lpSRD, &texture_);
  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateTexture2D(0x%X): ", hRes);
    device_.CheckDevLost(hRes);
    return hRes;
  }

  return BuildResourceView();
}

std::shared_ptr<TextureImpl> TextureImpl::CreateTexture2D(
    Device& inst,
    const TextureFrame& frame,
    uint64_t usage) {
  auto ret = std::make_shared<TextureImpl>(inst);
  HRESULT hRes = ret->BuildFromFrameBuffer(frame, usage);
  if (FAILED(hRes)) {
    return nullptr;
  }

  return ret;
}

std::shared_ptr<TextureImpl> TextureImpl::CreateTexture2D(Device& inst,
                                                          const wchar_t* file,
                                                          uint64_t usage) {
  ComPtr<ID3D11Resource> texture = nullptr;
  DirectX::CreateWICTextureFromFile(inst.GetDevice().Get(), file, &texture,
                                    nullptr);
  return AdapterWICResourceToTexture(inst, texture.Get(), usage);
}

std::shared_ptr<TextureImpl> TextureImpl::CreateTexture2D(Device& ins,
                                                          int cx,
                                                          int cy,
                                                          DXGI_FORMAT format,
                                                          uint64_t usage) {
  if (cx <= 0 || cy <= 0) {
    NOTREACHED();
    return nullptr;
  }
  auto ret = std::make_shared<TextureImpl>(ins);
  HRESULT hRes = ret->BuildFromDesc(cx, cy, format, usage);
  if (FAILED(hRes)) {
    return nullptr;
  }

  return ret;
}

std::shared_ptr<TextureImpl> TextureImpl::CreateTexture2D(
    Device& ins,
    ID3D11Texture2D* texture,
    uint64_t usage) {
  auto ret = std::make_shared<TextureImpl>(ins);
  HRESULT hRes = ret->BuildFromTexture(texture, usage);
  if (FAILED(hRes)) {
    return nullptr;
  }

  return ret;
}

std::shared_ptr<TextureImpl> TextureImpl::CreateTexture2D(
    Device& ins,
    ID3D11Texture2D* texture,
    ID3D11ShaderResourceView* view,
    uint64_t usage) {
  DCHECK(usage != 0);
  auto ret = std::make_shared<TextureImpl>(ins);
  HRESULT hRes = ret->BuildFromTextureView(texture, view, usage);
  if (FAILED(hRes)) {
    return nullptr;
  }

  return ret;
}

std::shared_ptr<TextureImpl> TextureImpl::CreateTexture2D(Device& ins,
                                                          HANDLE handle,
                                                          uint64_t usage) {
  auto ret = std::make_shared<TextureImpl>(ins);
  HRESULT hRes = ret->BuildFromSharedHandle(handle, usage);
  if (FAILED(hRes)) {
    return nullptr;
  }

  return ret;
}

std::shared_ptr<TextureImpl> TextureImpl::CreateTexture2D(
    Device& inst,
    const uint8_t* file_mem_data,
    int32_t file_mem_data_size,
    uint64_t usage) {
  ComPtr<ID3D11Resource> texture = nullptr;
  DirectX::CreateWICTextureFromMemory(inst.GetDevice().Get(), file_mem_data,
                                      file_mem_data_size, &texture, nullptr);
  return AdapterWICResourceToTexture(inst, texture.Get(), usage);
}

std::shared_ptr<graphics::TextureImpl> TextureImpl::AdapterWICResourceToTexture(
    Device& inst,
    ID3D11Resource* texture,
    uint64_t usage) {
  std::shared_ptr<TextureImpl> ret;
  do {
    if (!texture) {
      break;
    }
    ComPtr<ID3D11Texture2D> texture_2d;
    texture->QueryInterface(IID_PPV_ARGS(&texture_2d));
    if (!texture_2d) {
      break;
    }
    D3D11_TEXTURE2D_DESC desc = {};
    texture_2d->GetDesc(&desc);
    if (!desc.Width || !desc.Height) {
      break;
    }

    if (desc.Format == DXGI_FORMAT::DXGI_FORMAT_B8G8R8A8_UNORM ||
        desc.Format == DXGI_FORMAT::DXGI_FORMAT_R8G8B8A8_UNORM) {
      ret = CreateTexture2D(inst, desc.Width, desc.Height, desc.Format, usage);
      ret->CopyTextureFrom(texture_2d.Get());
      break;
    } else if (desc.Format == DXGI_FORMAT::DXGI_FORMAT_B8G8R8A8_UNORM_SRGB) {
      ret = CreateTexture2D(inst, desc.Width, desc.Height,
                            DXGI_FORMAT_B8G8R8A8_UNORM, usage);
      ret->CopyTextureFrom(texture_2d.Get());
    } else if (desc.Format == DXGI_FORMAT::DXGI_FORMAT_R8G8B8A8_UNORM_SRGB) {
      ret = CreateTexture2D(inst, desc.Width, desc.Height,
                            DXGI_FORMAT_R8G8B8A8_UNORM, usage);
      ret->CopyTextureFrom(texture_2d.Get());
    } else if (desc.Format == DXGI_FORMAT::DXGI_FORMAT_R8_UNORM) {
      ret = CreateTexture2D(inst, desc.Width, desc.Height,
                            DXGI_FORMAT_B8G8R8A8_UNORM, usage);
      auto read_texture =
          CreateTexture2D(inst, desc.Width, desc.Height, desc.Format,
                          Texture::TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_READ);
      read_texture->CopyTextureFrom(texture_2d.Get());
      std::unique_ptr<uint8_t[]> mem =
          std::make_unique<uint8_t[]>(desc.Width * desc.Height * 4);
      read_texture->CopyTo(mem.get(), desc.Width * 4, desc.Height);
      TextureFrame frame = {};
      frame.format = (desc.Format == DXGI_FORMAT::DXGI_FORMAT_R8G8B8A8_UNORM
                          ? mediasdk::PixelFormat::kPixelFormatRGBA
                          : mediasdk::PixelFormat::kPixelFormatY8);
      frame.width = desc.Width;
      frame.height = desc.Height;
      frame.data[0] = mem.get();
      frame.line_size[0] = desc.Width * 4;
      frame.color_space = mediasdk::ColorSpace::kColorSpaceBT709;
      frame.video_range = mediasdk::VideoRange::kVideoRangeFull;
      auto yuv_to_bgra = CreateYUVToBGRAGraphics(inst);
      yuv_to_bgra->ConvertMemoryToBGRAPrepare(frame);
      yuv_to_bgra->ConvertMemoryToBGRADraw();
      if (yuv_to_bgra->GetOutputTexture()) {
        ret->CopyTextureFrom(yuv_to_bgra->GetOutputTexture()->GetTexture());
      }
    } else {
      ret = CreateTexture2D(inst, desc.Width, desc.Height, desc.Format, usage);
      ret->CopyTextureFrom(texture_2d.Get());
    }
  } while (false);
  return ret;
}

std::shared_ptr<graphics::TextureImpl> TextureImpl::CreateTexture2D(
    Device& inst,
    const uint8_t* buffer,
    int width,
    int height,
    DXGI_FORMAT format,
    uint64_t usage) {
  auto ret = std::make_shared<TextureImpl>(inst);
  HRESULT hRes =
      ret->BuildTextureFromBuffer(buffer, width, height, format, usage);

  if (FAILED(hRes)) {
    return nullptr;
  }

  return ret;
}

std::shared_ptr<graphics::TextureImpl> TextureImpl::CreateTexture3D(
    Device& inst,
    const uint8_t* buffer,
    int width,
    int height,
    int depth,
    DXGI_FORMAT format,
    uint64_t usage) {
  auto ret = std::make_shared<TextureImpl>(inst);
  HRESULT hRes = ret->Build3DTextureFromBuffer(buffer, width, height, depth,
                                               format, usage);

  if (FAILED(hRes)) {
    return nullptr;
  }

  return ret;
}

TextureImpl::TextureImpl(Device& ins) : device_(ins) {}

ComPtr<ID3D11DeviceContext> TextureImpl::GetContext() const {
  return device_.GetContext();
}

bool TextureImpl::GetDC(bool discard, void** hDC) {
  if (!texture_)
    return FALSE;
  texture_for_HDC_.Reset();
  auto pDXGISurface = texture_for_HDC_.ReleaseAndGetAddressOf();
  HRESULT hRes = texture_.As<IDXGISurface1>(&texture_for_HDC_);
  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf(
        "Failed to QueryInterface(IDXGISurface1,0x%s): ",
        GetErrorString(hRes).c_str());
    return false;
  }
  HDC* pHDC = reinterpret_cast<HDC*>(hDC);
  hRes = texture_for_HDC_->GetDC(discard, pHDC);
  if (hRes != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to GetDC(%s): ",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  return true;
}

bool TextureImpl::ReleaseDC() {
  if (texture_for_HDC_ != NULL) {
    HRESULT hRes = texture_for_HDC_->ReleaseDC(NULL);
    texture_for_HDC_.Reset();
    if (hRes != S_OK) {
      LOG(ERROR) << base::StringPrintf("Failed to ReleaseDC(%s): ",
                                       GetErrorString(hRes).c_str());
    }
    return SUCCEEDED(hRes);
  }
  return false;
}

HANDLE TextureImpl::GetSharedHandle() const {
  ComPtr<IDXGIResource> resource;
  HRESULT res =
      GetTexture()->QueryInterface(__uuidof(IDXGIResource), (void**)&resource);
  if (FAILED(res)) {
    LOG(ERROR) << base::StringPrintf(
        "Failed to QueryInterface(IDXGIResource,0x%X)", res);
    return NULL;
  }
  HANDLE shared_handle = NULL;
  res = resource->GetSharedHandle(&shared_handle);
  if (FAILED(res)) {
    LOG(ERROR) << base::StringPrintf("Failed to GetSharedHandle(0x%X)", res);
    return NULL;
  }
  return shared_handle;
}

bool TextureImpl::SaveAS(const char* path) const {
  auto desc = GetDesc();
  int32_t line_size = GetLineByteSize(desc.Format) * desc.Width;
  int32_t data_height =
      desc.Format == DXGI_FORMAT_NV12 ? desc.Height * 3 / 2 : desc.Height;
  if (!line_size)
    return false;

  std::unique_ptr<uint8_t[]> mem =
      std::make_unique<uint8_t[]>(line_size * data_height);
  auto stage_texture = GenerateReadTexture();
  if (!stage_texture)
    return false;

  if (!stage_texture->CopyTo(mem.get(), line_size, data_height)) {
    return false;
  }
  {
    base::File fWrite(
        base::FilePath(base::SysUTF8ToWide(base::StringPiece{path})),
        base::File::Flags::FLAG_CREATE_ALWAYS | base::File::Flags::FLAG_WRITE);
    if (fWrite.IsValid()) {
      fWrite.Write(0, reinterpret_cast<const char*>(mem.get()),
                   line_size * data_height);
    }
  }
  return true;
}

std::shared_ptr<Texture> TextureImpl::CopyTexture() {
  auto desc = GetDesc();
  if (!desc.Width || !desc.Height) {
    LOG(ERROR) << "Empty,can not save";
    DCHECK(false);
    return nullptr;
  }

  auto copy = CreateTexture2D(device_, desc.Width, desc.Height, desc.Format,
                              GetUsage());

  copy->CopyTextureFrom(this->GetTexture());

  return copy;
}

std::shared_ptr<Texture> TextureImpl::GenerateReadTexture() const {
  const auto desc = GetDesc();
  if (!desc.Width || !desc.Height) {
    LOG(ERROR) << "Empty,can not save";
    DCHECK(false);
    return nullptr;
  }

  auto stage_texture =
      CreateTexture2D(device_, desc.Width, desc.Height, desc.Format,
                      Texture::TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_READ);

  stage_texture->CopyTextureFrom(GetTexture());

  return stage_texture;
}

uint64_t TextureImpl::GetUsage() const {
  return usage_;
}

void TextureImpl::SetDebugName(const char* name) {
  name_ = name;
  D3D11SetDebugObjectName(texture_.Get(), name);
}

void TextureImpl::Destroy() {
  desc_ = {};
  for (int i = 0; i < kMaxVideoPlanes; i++) {
    if (resource_views_[i]) {
      resource_views_[i].Reset();
    }
  }
  if (texture_for_HDC_) {
    texture_for_HDC_.Reset();
  }
  if (texture_) {
    auto ret = texture_.Reset();
  }
}

TextureImpl::~TextureImpl() {
  Destroy();
}

REFGUID GetWICCodec(ImageFileFormat format) {
  switch (format) {
    case kImageFileFormatJPG:
      return GUID_ContainerFormatJpeg;
      break;
    case kImageFileFormatPNG:
      return GUID_ContainerFormatPng;
      break;
    case kImageFileFormatTIFF:
      return GUID_ContainerFormatTiff;
      break;
    case kImageFileFormatGIF:
      return GUID_ContainerFormatGif;
      break;
    case kImageFileFormatWMP:
      return GUID_ContainerFormatWmp;
      break;
    case kImageFileFormatBMP:
      return GUID_ContainerFormatBmp;
  }
  return GUID_ContainerFormatBmp;
}

bool SaveBGRATextureToFile(ID3D11DeviceContext* pContext,
                           ID3D11Resource* pSource,
                           const wchar_t* fileName,
                           const ImageFileFormat targetFormat) {
  auto target_wic_format = GetWICCodec(targetFormat);
  auto ret = DirectX::SaveWICTextureToFile(pContext, pSource, target_wic_format,
                                           fileName);

  if (SUCCEEDED(ret)) {
    return true;
  } else {
    DCHECK(false);
    return false;
  }
}
}  // namespace graphics
