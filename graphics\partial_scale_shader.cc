#include "partial_scale_shader.h"

#include "graphics_utils.h"
#include "pixel_shader.h"
#include "vertex_shader.h"
#include <base/logging.h>

namespace graphics {

namespace {
struct VSConstantBuffer {
    DirectX::XMMATRIX world;
    DirectX::XMMATRIX view;
    DirectX::XMMATRIX projection;
};

struct VertexType {
    DirectX::XMFLOAT3 position;
    DirectX::XMFLOAT2 texture;
};

}  // namespace

PartialScaleShader::PartialScaleShader()
    : context_(nullptr), index_count_(0), try_init_(false), init_suc_(false) {}

PartialScaleShader::~PartialScaleShader() {
    Destroy();
}

bool PartialScaleShader::Init(const std::shared_ptr<Device>& device) {
    if (try_init_) {
        return init_suc_;
    }
    try_init_ = true;
    init_suc_ = DoInit(device);
    return init_suc_;
}

bool PartialScaleShader::DoInit(const std::shared_ptr<Device>& ins) {
    device_ = ins;
    context_ = device_->GetContext().Get();

    D3D11_INPUT_ELEMENT_DESC polygon_layout[2];
    polygon_layout[0].SemanticName = "POSITION";
    polygon_layout[0].SemanticIndex = 0;
    polygon_layout[0].Format = DXGI_FORMAT_R32G32B32_FLOAT;
    polygon_layout[0].InputSlot = 0;
    polygon_layout[0].AlignedByteOffset = 0;
    polygon_layout[0].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
    polygon_layout[0].InstanceDataStepRate = 0;

    polygon_layout[1].SemanticName = "TEXCOORD";
    polygon_layout[1].SemanticIndex = 0;
    polygon_layout[1].Format = DXGI_FORMAT_R32G32_FLOAT;
    polygon_layout[1].InputSlot = 0;
    polygon_layout[1].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
    polygon_layout[1].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
    polygon_layout[1].InstanceDataStepRate = 0;

    Device::CompileShaderParam param;
    param.vs = TEXTURE_VERTEX_SHADER();
    param.ps = SCALE_PIXEL_SHADER();
    param.vs_name = "VS_MAIN";
    param.ps_name = "SCALE_PS_MAIN";
    param.layout_descs_ = polygon_layout;
    param.layout_cnt_ = ARRAYSIZE(polygon_layout);

    if (!device_->CompileShader(param)) {
        LOG(ERROR) << "Failed to compile partial scale shader";
        return false;
    }
    vertex_shader_ = param.vs_shader_;
    pixel_shader_ = param.ps_shader_;
    input_layout_ = param.layout_;

    D3D11_BUFFER_DESC matrix_buffer_desc;
    matrix_buffer_desc.Usage = D3D11_USAGE_DYNAMIC;
    matrix_buffer_desc.ByteWidth = sizeof(VSConstantBuffer);
    matrix_buffer_desc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
    matrix_buffer_desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
    matrix_buffer_desc.MiscFlags = 0;
    matrix_buffer_desc.StructureByteStride = 0;
    HRESULT hr = device_->GetDevice()->CreateBuffer(&matrix_buffer_desc, nullptr, &vs_matrix_buffer_);
    if (FAILED(hr)) {
        LOG(ERROR) << "Failed to create matrix constant buffer: " << hr;
        return false;
    }
    D3D11SetDebugObjectName(vs_matrix_buffer_.Get(), "PartialScaleVSMatrixBuffer");

    // Sampler state
    D3D11_SAMPLER_DESC sampler_desc;
    sampler_desc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
    sampler_desc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
    sampler_desc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
    sampler_desc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
    sampler_desc.MipLODBias = 0.0f;
    sampler_desc.MaxAnisotropy = 1;
    sampler_desc.ComparisonFunc = D3D11_COMPARISON_ALWAYS;
    sampler_desc.BorderColor[0] = 0;
    sampler_desc.BorderColor[1] = 0;
    sampler_desc.BorderColor[2] = 0;
    sampler_desc.BorderColor[3] = 0;
    sampler_desc.MinLOD = 0;
    sampler_desc.MaxLOD = D3D11_FLOAT32_MAX;
    hr = device_->GetDevice()->CreateSamplerState(&sampler_desc, &sampler_state_);
    if (FAILED(hr)) {
        LOG(ERROR) << "Failed to create sampler state: " << hr;
        return false;
    }
    D3D11SetDebugObjectName(sampler_state_.Get(), "PartialScaleSamplerState");

    if (!CreateVertexAndIndexBuffers()) {
        return false;
    }

    return true;
}

bool PartialScaleShader::CreateVertexAndIndexBuffers() {
    VertexType vertices[4];
    unsigned long indices[6];

    // Load the vertex array with data.
    // Quad covering screen with CCW winding
    vertices[0].position = DirectX::XMFLOAT3(-1.0f, 1.0f, 0.0f);  // Top left
    vertices[0].texture = DirectX::XMFLOAT2(0.0f, 0.0f);

    vertices[1].position = DirectX::XMFLOAT3(1.0f, 1.0f, 0.0f);  // Top right
    vertices[1].texture = DirectX::XMFLOAT2(1.0f, 0.0f);

    vertices[2].position = DirectX::XMFLOAT3(-1.0f, -1.0f, 0.0f);  // Bottom left
    vertices[2].texture = DirectX::XMFLOAT2(0.0f, 1.0f);

    vertices[3].position = DirectX::XMFLOAT3(1.0f, -1.0f, 0.0f);  // Bottom right
    vertices[3].texture = DirectX::XMFLOAT2(1.0f, 1.0f);

    // Load the index array with data.
    indices[0] = 0;
    indices[1] = 1;
    indices[2] = 2;
    indices[3] = 1;
    indices[4] = 3;
    indices[5] = 2;

    index_count_ = ARRAYSIZE(indices);

    D3D11_BUFFER_DESC vertex_buffer_desc;
    vertex_buffer_desc.Usage = D3D11_USAGE_DEFAULT;
    vertex_buffer_desc.ByteWidth = sizeof(VertexType) * 4;
    vertex_buffer_desc.BindFlags = D3D11_BIND_VERTEX_BUFFER;
    vertex_buffer_desc.CPUAccessFlags = 0;
    vertex_buffer_desc.MiscFlags = 0;
    vertex_buffer_desc.StructureByteStride = 0;

    D3D11_SUBRESOURCE_DATA vertex_data;
    vertex_data.pSysMem = vertices;
    vertex_data.SysMemPitch = 0;
    vertex_data.SysMemSlicePitch = 0;

    HRESULT hr = device_->GetDevice()->CreateBuffer(&vertex_buffer_desc, &vertex_data, &vertex_buffer_);
    if (FAILED(hr)) {
        LOG(ERROR) << "Failed to create vertex buffer: " << hr;
        return false;
    }
    D3D11SetDebugObjectName(vertex_buffer_.Get(), "PartialScaleVertexBuffer");

    D3D11_BUFFER_DESC index_buffer_desc;
    index_buffer_desc.Usage = D3D11_USAGE_DEFAULT;
    index_buffer_desc.ByteWidth = sizeof(unsigned long) * index_count_;
    index_buffer_desc.BindFlags = D3D11_BIND_INDEX_BUFFER;
    index_buffer_desc.CPUAccessFlags = 0;
    index_buffer_desc.MiscFlags = 0;
    index_buffer_desc.StructureByteStride = 0;

    D3D11_SUBRESOURCE_DATA index_data;
    index_data.pSysMem = indices;
    index_data.SysMemPitch = 0;
    index_data.SysMemSlicePitch = 0;

    hr = device_->GetDevice()->CreateBuffer(&index_buffer_desc, &index_data, &index_buffer_);
    if (FAILED(hr)) {
        LOG(ERROR) << "Failed to create index buffer: " << hr;
        return false;
    }
    D3D11SetDebugObjectName(index_buffer_.Get(), "PartialScaleIndexBuffer");

    return true;
}

void PartialScaleShader::Render(ID3D11ShaderResourceView* view,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& vertex_buffer,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& index_buffer,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& crop_buffer,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& matrix_buffer,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_buffer) {
        UINT stride = sizeof(PartialScaleShader::VERTEXTYPE);
        UINT offset = 0;
        // auto context = device_->GetContext().Get();; // context_ is already a member
        context_->IASetVertexBuffers(0, 1, vertex_buffer.GetAddressOf(), &stride,
            &offset);
        context_->IASetIndexBuffer(index_buffer.Get(), DXGI_FORMAT_R32_UINT, 0);
        context_->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);
        context_->VSSetConstantBuffers(0, 1, matrix_buffer.GetAddressOf()); // This should be vs_matrix_buffer_ if it's the class member, or the passed one if intended.
                                                                          // Assuming matrix_buffer is the one passed for world/view/projection, and crop_buffer for scale/offset params.
        context_->VSSetConstantBuffers(1, 1, crop_buffer.GetAddressOf()); // crop_buffer is for VS b1 (scale params)
        context_->PSSetConstantBuffers(0, 1, ps_buffer.GetAddressOf()); // ps_buffer is for PS b0 (other params if any)
        context_->PSSetShaderResources(0, 1, &view);
        context_->IASetInputLayout(input_layout_.Get());
        context_->VSSetShader(vertex_shader_.Get(), NULL, 0);
        context_->PSSetShader(pixel_shader_.Get(), NULL, 0);
        context_->PSSetSamplers(0, 1, sampler_state_.GetAddressOf());
        context_->DrawIndexed(index_count_, 0, 0);
}

void PartialScaleShader::Destroy() {
    vertex_shader_.Reset();
    pixel_shader_.Reset();
    input_layout_.Reset();
    sampler_state_.Reset();
    vs_matrix_buffer_.Reset();
    vertex_buffer_.Reset();
    index_buffer_.Reset();
    init_suc_ = false;
    try_init_ = false;
    context_ = nullptr;
    device_ = nullptr;
}

}  // namespace graphics