#include "cpu_information_win.h"
#include <windows.h>
#include "base\strings\utf_string_conversions.h"

namespace {
const std::wstring kRegPath =
    L"HARDWARE\\DESCRIPTION\\System\\CentralProcessor\\0";
}

namespace cpu_collector {

std::shared_ptr<CpuInformation> CpuInformation::Create() {
  HKEY key = {0};
  auto r =
      RegOpenKeyEx(HKEY_LOCAL_MACHINE, kRegPath.c_str(), 0, KEY_READ, &key);
  if (r != ERROR_SUCCESS) {
    return nullptr;
  }
  DWORD type = 0;
  WCHAR cpu_name[1024] = {0};
  DWORD size = 500;

  r = RegQueryValueEx(key, L"ProcessorNameString", NULL, &type,
                      (LPBYTE)&cpu_name[0], &size);
  if (ERROR_SUCCESS != r) {
    return nullptr;
  }
  std::wstring cpu_name_str = cpu_name;
  CpuInformationImpl* impl = new CpuInformationImpl;
  impl->SetCpuName(base::WideToUTF8(cpu_name_str.c_str()));

  // speed
  type = REG_DWORD;
  DWORD value;
  r = RegQueryValueEx(key, L"~MHz", NULL, &type, (LPBYTE)&value, &size);
  if (ERROR_SUCCESS == r) {
    impl->SetClockSpeed(value);
  }

  // processor num
  SYSTEM_INFO si;
  memset(&si, 0, sizeof(SYSTEM_INFO));
  ::GetSystemInfo(&si);
  impl->SetNum(si.dwNumberOfProcessors);

  return std::shared_ptr<CpuInformation>(impl);
}

std::string CpuInformationImpl::GetCpuName() {
  return cpu_name_;
}

uint32_t CpuInformationImpl::GetClockSpeed() {
  return clock_speed_;
}

uint32_t CpuInformationImpl::GetProcessorNum() {
  return num_;
}

}  // namespace cpu_collector