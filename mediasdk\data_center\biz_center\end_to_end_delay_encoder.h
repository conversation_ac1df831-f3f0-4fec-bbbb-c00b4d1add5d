#pragma once

#include <mutex>
#include "mediasdk/data_center/biz_center/end_to_end_delay.h"
#include "mediasdk/data_center/biz_center/end_to_end_mgr.h"

namespace mediasdk {

class EndtoEndDelayEncoder : public EndtoEndDelay {
 public:
  explicit EndtoEndDelayEncoder(const std::string& name);

  ~EndtoEndDelayEncoder() override;

  void CacheEndtoEndMgr(int64_t index, const EndtoEndMgr& e2e_mgr);

  void RemoveEndtoEndMgr(int64_t index);

  EndtoEndMgr PopEndtoEndMgr(int64_t index);

  // EndtoEndDelay interface
  bool FillCostData(std::map<std::string, int64_t>& cost_map) override;

 private:
  void CollectCostUS(int64_t cost_us);

 private:
  const std::string name_;
  std::mutex cost_mutex_;
  EndtoEndDelayCostData cost_data_;

  std::mutex mgr_mutex_;
  std::map<int64_t, EndtoEndMgr> e2e_mgrs_;
  std::map<int64_t, int64_t> before_encode_ns_map_;
};

}  // namespace mediasdk
