#include "chroma_key_params.h"
#include "graphics_utils.h"

namespace graphics {

ChromaKeyParams::ChromaKeyParams(const ChromaKeyParams& other) {
  Assign(other);
}

ChromaKeyParams& ChromaKeyParams::operator=(const ChromaKeyParams& other) {
  if (this != &other) {
    Assign(other);
  }

  return *this;
}

bool ChromaKeyParams::operator==(const ChromaKeyParams& other) const {
  return chroma_key_ == other.ChromaKey() &&
         IsNearEqual(pixel_size_.x, other.PixelSizeX()) &&
         IsNearEqual(pixel_size_.y, other.PixelSizeY()) &&
         IsNearEqual(similarity_, other.Similarity()) &&
         IsNearEqual(smoothness_, other.Smoothness()) &&
         IsNearEqual(spill_, other.Spill()) &&
         IsNearEqual(extrude_, other.Extrude()) &&
         IsNearEqual(opacity_, other.Opacity()) &&
         IsNearEqual(gamma_, other.Gamma()) &&
         IsNearEqual(brightness_, other.Brightness()) &&
         IsNearEqual(contrast_, other.Contrast());
}

bool ChromaKeyParams::operator!=(const ChromaKeyParams& other) const {
  return !(*this == other);
}

bool ChromaKeyParams::IsEmpty() const {
  return *this == ChromaKeyParams();
}

void ChromaKeyParams::Reset() {
  *this = ChromaKeyParams();
}

float ChromaKeyParams::ChromaKey() const {
  return chroma_key_;
}

void ChromaKeyParams::SetChromaKey(uint32_t chroma_key) {
  chroma_key_ = chroma_key;
}

float ChromaKeyParams::PixelSizeX() const {
  return pixel_size_.x;
}

float ChromaKeyParams::PixelSizeY() const {
  return pixel_size_.y;
}

void ChromaKeyParams::SetPixelSize(PixelSize pixel_size) {
  pixel_size_.x = pixel_size.x;
  pixel_size_.y = pixel_size.y;
}

float ChromaKeyParams::Similarity() const {
  return similarity_;
}

void ChromaKeyParams::SetSimilarity(float similarity) {
  similarity_ = similarity;
}

float ChromaKeyParams::Smoothness() const {
  return smoothness_;
}

void ChromaKeyParams::SetSmoothness(float smoothness) {
  smoothness_ = smoothness;
}

float ChromaKeyParams::Spill() const {
  return spill_;
}

void ChromaKeyParams::SetSpill(float spill) {
  spill_ = spill;
}

float ChromaKeyParams::Extrude() const {
  return extrude_;
}

void ChromaKeyParams::SetExtrude(float extrude) {
  extrude_ = extrude;
}

float ChromaKeyParams::Opacity() const {
  return opacity_;
}

void ChromaKeyParams::SetOpacity(float opacity) {
  opacity_ = opacity;
}

float ChromaKeyParams::Gamma() const {
  return gamma_;
}

void ChromaKeyParams::SetGamma(float gamma) {
  gamma_ = gamma;
}

float ChromaKeyParams::Brightness() const {
  return brightness_;
}

void ChromaKeyParams::SetBrightness(float brightness) {
  brightness_ = brightness;
}

float ChromaKeyParams::Contrast() const {
  return contrast_;
}

void ChromaKeyParams::SetContrast(float contrast) {
  contrast_ = contrast;
}

void ChromaKeyParams::Assign(const ChromaKeyParams& other) {
  chroma_key_ = other.ChromaKey();
  pixel_size_.x = other.PixelSizeX();
  pixel_size_.y = other.PixelSizeY();
  similarity_ = other.Similarity();
  smoothness_ = other.Smoothness();
  spill_ = other.Spill();
  extrude_ = other.Extrude();
  opacity_ = other.Opacity();
  gamma_ = other.Gamma();
  brightness_ = other.Brightness();
  contrast_ = other.Contrast();
}

}  // namespace graphics