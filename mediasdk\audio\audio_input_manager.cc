#include "audio_input_manager.h"

#include <set>

#include "audio/audio_common.h"
#include "data_center/vqos_data.h"
#include "mediasdk/audio/audio_input.h"
#include "mediasdk/audio/audio_track.h"
#include "mediasdk/audio/lyrax_audio_input.h"
#include "mediasdk/callback_utils.h"
#include "mediasdk/component_center.h"
#include "mediasdk/plugin_service/plugin_service.h"
#include "mediasdk/rtc/rtc_controller.h"

#include "mediasdk/component_proxy.h"
#include "mediasdk/debug_helper.h"
#include "mediasdk/mediasdk_thread.h"
#include "mediasdk/public/mediasdk_trace_event.h"
#include "mediasdk/public/plugin/audio_filter.h"
#include "mediasdk/public/plugin/audio_input_source.h"

namespace {
constexpr int kAudioLayoutTimerIntervaS = 10;
}

namespace mediasdk {

AudioInputManager::AudioInputManager() : output_format_(kAudioOutputFormat) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  output_audio_layout_timer_.Start(
      FROM_HERE, base::Seconds(kAudioLayoutTimerIntervaS),
      base::BindRepeating(&AudioInputManager::OnOutputAudioLayoutTimer,
                          base::Unretained(this)));
}

AudioFormat AudioInputManager::GetOutputFormat() const {
  return output_format_;
}

AudioInputManager::~AudioInputManager() {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  output_audio_layout_timer_.Stop();
  DestroyAllAudioInput(MSCallback<bool>());
}

void AudioInputManager::CreateAudioInput(const std::string& id,
                                         const CreateAudioParams& params,
                                         MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (IsAudioInputExist(id)) {
    NOTREACHED() << "duplicate audio id";
    std::move(callback).Resolve(false);
    return;
  }

  auto audio_input = SourceAudioInput::Create(id, this->GetOutputFormat());
  CreateAudioInputWithParam(audio_input, params, std::move(callback));
}

void AudioInputManager::CreateLyraxAudioInput(const std::string& id,
                                              const CreateAudioParams& params,
                                              MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  if (IsAudioInputExist(id)) {
    NOTREACHED() << "duplicate audio id";
    std::move(callback).Resolve(false);
    return;
  }

  auto audio_input = LyraxAudioInput::Create(id, this->GetOutputFormat());
  RTCControllerProxy::PostTaskAndReplyWithResult(
      FROM_HERE,
      base::BindOnce(&AudioInputManager::OnLyraxAudioInputCreated,
                     base::Unretained(this), std::move(callback), params,
                     audio_input),
      &RTCController::AllocLyraxAudioProcessor, audio_input);
}

ResultBoolBool AudioInputManager::AddAudioInputToTrack(
    const std::string& id,
    const uint32_t& track_id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  const auto audio_input = GetAudioInput(id);
  if (!audio_input) {
    LOG(ERROR) << "can not find audio input [" << id << "]";
    return {false, false};
  }

  const bool ret = AddAudioInput(track_id, audio_input);
  return {true, ret};
}

void AudioInputManager::OnLyraxAudioInputCreated(
    MSCallbackBool callback,
    const CreateAudioParams& params,
    std::shared_ptr<LyraxAudioInput> audio_input,
    std::shared_ptr<LyraxAudioProcessor> processor) {
  LOG(INFO) << "[AudioInputManager][OnLyraxAudioInputCreated], audio input id: "
            << (audio_input == nullptr ? "" : audio_input->GetId());
  auto input = std::dynamic_pointer_cast<LyraxAudioInput>(audio_input);
  input->SetAudioProcessor(processor);
  CreateAudioInputWithParam(audio_input, params, std::move(callback));
}

void AudioInputManager::OnAudioInputSourceCreated(
    const uint32_t track_id,
    const std::string& plugin_name,
    MSCallbackBool callback,
    std::shared_ptr<SourceAudioInput> audio_input,
    std::shared_ptr<AudioInputSource> source) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  DCHECK(audio_input);

  if (!source) {
    VqosData data;
    data.ReportSourceEvent({static_cast<int>(SOURCE_CATEGORY::kAudio),
                            plugin_name,
                            static_cast<int>(SOURCE_EVENT_TYPE::kCreate),
                            static_cast<int>(SOURCE_EXTRA_CODE::kFailed), ""});
    std::move(callback).Resolve(false);
    return;
  }

  VqosData data;
  data.ReportSourceEvent({static_cast<int>(SOURCE_CATEGORY::kAudio),
                          plugin_name,
                          static_cast<int>(SOURCE_EVENT_TYPE::kCreate),
                          static_cast<int>(SOURCE_EXTRA_CODE::kSuccess), ""});
  audio_input->AttachSource(std::move(source));
  std::move(callback).Resolve(AddAudioInput(track_id, std::move(audio_input)));
}

void AudioInputManager::DestroyAudioInput(const std::string& id,
                                          MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  auto audio_input = GetAudioInput(id);
  if (!audio_input) {
    NOTREACHED() << "duplicate audio id";
    return std::move(callback).Resolve(false);
  }

  // Remove from all tracks
  RemoveAudioInput(id);

  DCHECK(!audio_input->IsFromVisual() &&
         "If AudioInput is from Visual, its internal source is managed by "
         "Visual. use RemoveAudioInput directly");

  DestroyAudioInputAndSource(audio_input, std::move(callback));
}

void AudioInputManager::DestroyAllAudioInput(MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  auto audio_input_list = GetAudioInputList();
  if (audio_input_list.empty()) {
    return;
  }

  for (const auto& audio_input : audio_input_list) {
    // Detach audio input source
    audio_input->DetachSource();
  }

  PluginServiceProxy::PostTaskAndReply(
      FROM_HERE,
      base::BindOnce(&AudioInputManager::OnAllAudioInputSourceDestroyed,
                     base::Unretained(this), std::move(audio_input_list),
                     std::move(callback)),
      &PluginService::DestroyAllAudioInputSource);
}

bool AudioInputManager::IsAudioInputExist(const std::string& id) {
  if (GetAudioInput(id)) {
    return true;
  }
  return false;
}

ResultBoolBool AudioInputManager::RemoveAudioInputFromTrack(
    const std::string& id,
    const uint32_t track_id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  const auto track = GetAudioTrack(track_id);
  if (!track) {
    LOG(ERROR) << "Can not find audio [" << id << "] in [" << track_id << "]";
    return {false, false};
  }

  const auto audio_input = track->GetAudioInput(id);
  if (!audio_input) {
    LOG(ERROR) << "Can not find audio [" << id << "] in [" << track_id << "]";
    return {false, false};
  }
  track->RemoveAudioInput(id);

  // If other audio tracks do not have the audio input reference, destroy it
  if (const auto last = GetAudioInput(id); !last) {
    DestroyAudioInputAndSource(audio_input, MSCallbackBool());
    LOG(INFO) << "DestroyAudioInput cause by RemoveAudioInputFromAudioTrack";
  }
  return {true, true};
}

std::shared_ptr<AudioTrack> AudioInputManager::GetAudioTrack(
    const uint32_t track_id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  const auto it = tracks_.find(track_id);
  return it != tracks_.end() ? it->second : nullptr;
}

std::vector<std::shared_ptr<AudioInput>>
AudioInputManager::GetAudioInputList() {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  std::set<std::shared_ptr<AudioInput>> ret;
  for (const auto& [_, audio_track] : tracks_) {
    if (audio_track) {
      const auto audio_input_list = audio_track->GetAudioInputs();
      for (const auto& [_, audio_input] : audio_input_list) {
        if (audio_input) {
          ret.insert(audio_input->audio_input);
        }
      }
    }
  }
  return std::vector<std::shared_ptr<AudioInput>>(ret.begin(), ret.end());
}

std::vector<std::string> AudioInputManager::GetAudioInputIds() {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  const auto audio_list = GetAudioInputList();

  std::set<std::string> ret;
  for (const auto& input : audio_list) {
    ret.insert(input->GetId());
  }
  return std::vector<std::string>(ret.begin(), ret.end());
}

// for performance reason, reduce memory operation
AudioInputManager::TracksType& AudioInputManager::GetTracksRef() {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  return tracks_;
}

std::shared_ptr<AudioInput> AudioInputManager::GetAudioInput(
    const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  for (const auto& [_, audio_track] : tracks_) {
    if (const auto ret = audio_track->GetAudioInput(id)) {
      return ret;
    }
  }
  return nullptr;
}

bool AudioInputManager::AddAudioInput(const uint32_t track_id,
                                      std::shared_ptr<AudioInput> audio_input) {
  if (!audio_input)
    return false;

  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  LOG(INFO) << "Add Audio [" << audio_input->GetId() << " to  track ["
            << track_id << "]";

  if (tracks_.find(track_id) == tracks_.end()) {
    tracks_[track_id] = std::make_shared<AudioTrack>(track_id);
  }

  const auto ret = tracks_[track_id]->AddAudioInput(std::move(audio_input));
  return ret;
}

void AudioInputManager::DestroyAudioInputFromVisual(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  if (const auto audio_input = GetAudioInput(id)) {
    DCHECK(audio_input->IsFromVisual());
    audio_input->DetachSource();
  }
  RemoveAudioInput(id);
}

void AudioInputManager::CheckAudioTrackExisted(const uint32_t track_id) {
  if (tracks_.find(track_id) == tracks_.end()) {
    // create a new mute pusher track when it is observed but not create by app
    tracks_[track_id] = std::make_shared<AudioTrack>(track_id);
  }
}

void AudioInputManager::GetAudioInputPerformance(
    const std::string& audio_input_id,
    MSAudioPerformance& performance) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  auto audio_input = GetAudioInput(audio_input_id);
  if (!audio_input) {
    return;
  }
  audio_input->GetPerformance(performance);
  return;
}

MediaSDKString AudioInputManager::EnumAudioInputsInTrack(uint32_t track_id) {
  try {
    if (auto audio_track = GetAudioTrack(track_id)) {
      nlohmann::json json_root;
      auto& audio_input_list = audio_track->GetAudioInputs();
      for (auto& iter : audio_input_list) {
        nlohmann::json json_item;
        json_item["volume"] = iter.second->audio_input->GetVolume();
        json_item["sync_offset"] = iter.second->audio_input->GetSyncOffset();
        json_item["monitor_type"] = iter.second->audio_input->GetMonitorType();
        json_item["is_mute"] = iter.second->audio_input->IsMute();
        json_item["balance"] = iter.second->audio_input->GetBalance();
        json_item["is_mono"] = iter.second->audio_input->IsMono();
        json_item["source_name"] = iter.second->audio_input->GetName();
        json_item["dev_name"] = iter.second->audio_input->GetDevName();
        json_item["id"] = iter.first;
        json_root.push_back(json_item);
      }
      return json_root.dump();
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "EnumAudioInputsInTrack Catch exception: " << e.what();
  }
  return {};
}

void AudioInputManager::CreateAudioInputWithParam(
    std::shared_ptr<SourceAudioInput> audio_input,
    const CreateAudioParams& params,
    MSCallbackBool callback) {
  PluginServiceProxy::PostTaskAndReplyWithResult(
      FROM_HERE,
      base::BindOnce(&AudioInputManager::OnAudioInputSourceCreated,
                     base::Unretained(this), params.track_id,
                     params.plugin_name.ToString(), std::move(callback),
                     audio_input),
      &PluginService::CreateAudioInputSource, audio_input,
      params.plugin_name.ToString(), params.json_params.ToString());
}

void AudioInputManager::OnOutputAudioLayoutTimer() {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  auto new_audio_lay_out = GetLayoutString();
  if (new_audio_lay_out != pre_layout_str_) {
    pre_layout_str_ = new_audio_lay_out;
    LOG(INFO) << pre_layout_str_;
  }
}

std::string AudioInputManager::GetLayoutString() {
  std::string ret = "audio layout [";
  for (auto& it : tracks_) {
    if (!it.second)
      continue;
    ret += "\n" + std::to_string(it.first) + " " + it.second->ToString();
    ret += ",";
  }
  return ret;
}

void AudioInputManager::DestroyAudioInputAndSource(
    const std::shared_ptr<AudioInput> audio_input,
    MSCallbackBool callback) {
  auto source = audio_input->DetachSource();
  if (!source) {
    return std::move(callback).Resolve(true);
  }

  // Destroy the audio input after the source destroyed
  PluginServiceProxy::PostTaskAndReply(
      FROM_HERE,
      base::BindOnce(&AudioInputManager::OnAudioInputSourceDestroyed,
                     base::Unretained(this), audio_input, std::move(callback)),
      &PluginService::DestroyAudioInputSource, std::move(source));
}

void AudioInputManager::RemoveAudioInput(const std::string& id) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  for (const auto& [_, audio_track] : tracks_) {
    if (audio_track) {
      audio_track->RemoveAudioInput(id);
    }
  }
}

void AudioInputManager::OnAudioInputSourceDestroyed(
    std::shared_ptr<AudioInput> audio_input,
    MSCallbackBool callback) {
  // Keep the audio_input alive before source destroyed
  LOG(INFO) << "audio [" << audio_input->GetId()
            << "] OnAudioInputSourceDestroyed";

  audio_input.reset();

  std::move(callback).Resolve(true);
}

void AudioInputManager::OnAllAudioInputSourceDestroyed(
    std::vector<std::shared_ptr<AudioInput>> audio_input_list,
    MSCallbackBool callback) {
  audio_input_list.clear();
  std::move(callback).Resolve(true);
}

}  // namespace mediasdk
