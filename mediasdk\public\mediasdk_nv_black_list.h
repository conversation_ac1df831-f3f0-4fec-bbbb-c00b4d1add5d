#include <map>
#include <string>

namespace {

// https://developer.nvidia.com/video-encode-and-decode-gpu-support-matrix-new
// https://download.nvidia.com/XFree86/Linux-x86_64/410.73/README/supportedchips.html
const std::multimap<uint32_t, std::string> kNvidiaGraphicCardBlackList = {
    {0x1140, "NVIDIA GeForce 610M"},
    {0x1140, "NVIDIA GeForce 710M"},
    {0x1140, "NVIDIA GeForce 810M"},
    {0x1140, "NVIDIA GeForce 820M"},
    {0x1140, "NVIDIA GeForce 620M"},
    {0x1140, "NVIDIA GeForce 625M"},
    {0x1140, "NVIDIA GeForce 630M"},
    {0x1140, "NVIDIA GeForce 720M"},
    {0x1293, "NVIDIA GeForce 730M"},
    {0x1290, "NVIDIA GeForce 730M"},
    {0x0fe1, "NVIDIA GeForce 730M"},
    {0x0fdf, "NVIDIA GeForce 740M"},
    {0x1294, "NVIDIA GeForce 740M"},
    {0x1292, "NVIDIA GeForce 740M"},
    {0x0fe2, "NVIDIA GeForce 745M"},
    {0x0fe3, "NVIDIA GeForce 745M"},
    {0x1140, "NVIDIA GeForce 610M"},
    {0x1140, "NVIDIA GeForce 710M"},
    {0x1140, "NVIDIA GeForce 810M"},
    {0x1140, "NVIDIA GeForce 820M"},
    {0x1140, "NVIDIA GeForce 620M"},
    {0x1140, "NVIDIA GeForce 610M"},
    {0x1140, "NVIDIA GeForce 625M"},
    {0x1140, "NVIDIA GeForce 630M"},
    {0x1140, "NVIDIA GeForce 720M"},
    {0x0fed, "NVIDIA GeForce 820M"},
    {0x1340, "NVIDIA GeForce 830M"},
    {0x1393, "NVIDIA GeForce 840M"},
    {0x1341, "NVIDIA GeForce 840M"},
    {0x1398, "NVIDIA GeForce 845M"},
    {0x1390, "NVIDIA GeForce 845M"},
    {0x1344, "NVIDIA GeForce 845M"},
    {0x1299, "NVIDIA GeForce 920M"},
    {0x1299, "NVIDIA GeForce 920M"},
    {0x1349, "NVIDIA GeForce 930M"},
    {0x1346, "NVIDIA GeForce 930M"},
    {0x139c, "NVIDIA GeForce 940M"},
    {0x1347, "NVIDIA GeForce 940M"},
    {0x1399, "NVIDIA GeForce 945M"},
    {0x1348, "NVIDIA GeForce 945A"},
    {0x174e, "NVIDIA GeForce MX110"},
    {0x174d, "NVIDIA GeForce MX130"},
    {0x1d10, "NVIDIA GeForce MX150"},
    {0x1d12, "NVIDIA GeForce MX150"},
    {0x1d11, "NVIDIA GeForce MX230"},
    {0x1d13, "NVIDIA GeForce MX250"},
    {0x1d52, "NVIDIA GeForce MX250"},
    {0x1d52, "NVIDIA GeForce MX250"},
    {0x1c94, "NVIDIA GeForce MX350"},
    {0x1f97, "NVIDIA GeForce MX450"},
    {0x1348, "NVIDIA GeForce 930MX"},
    {0x134f, "NVIDIA GeForce 920MX"},
    {0x134e, "NVIDIA GeForce 930MX"},
    {0x134b, "NVIDIA GeForce 940MX"},
    {0x134d, "NVIDIA GeForce 940MX"},
    {0x179c, "NVIDIA GeForce 940MX"},
    {0x1d01, "NVIDIA GeForce GT 1030"},
    {0x0fc5, "NVIDIA GeForce GT 1030"},
    {0x1298, "NVIDIA GeForce GT 720M"},
    {0x137b, "NVIDIA Quadro M520 Mobile"},
    {0x1d33, "NVIDIA Quadro M520 Mobile"},
    {0x137a, "NVIDIA Quadro Quadro K620M"},
    {0x137a, "NVIDIA Quadro Quadro M500M"}};

}  // namespace
