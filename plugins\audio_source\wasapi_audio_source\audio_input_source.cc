#include "mediasdk/public/plugin/audio_input_source.h"

// clang-format off
#include <audiopolicy.h>
#include <mediasdk/public/plugin/plugin_defines.h>
#include <base/strings/utf_string_conversions.h>
#include <nlohmann/json.hpp>
#include "mediasdk/public/mediasdk_defines.h"
#include "wasapi_audio_input_loopback_source.h"
#include "wasapi_audio_input_microphone_source.h"
#include "wasapi_audio_input_source.h"
#include "wasapi_audio_source_helper.h"
#include "wasapi_audio_device_monitor.h"

// clang-format on

namespace mediasdk {

const PluginInfo* GetPluginInfo() {
  static PluginInfo info;
  info.id = wasapi_audio_source::GetPluginID();
  info.type = PluginType::kAudio;
  info.name = wasapi_audio_source::GetPluginName();
  info.desc = wasapi_audio_source::GetPluginDesc();

  return &info;
}

AudioInputSource* CreateAudioInputSource(AudioInputProxy* proxy,
                                         const char* json_params) {
  WASApiAudioInputSource* ret = nullptr;
  int32_t type = mediasdk::kAudioInputNone;
  if (json_params && json_params[0]) {
    nlohmann::json json_root;
    try {
      json_root = nlohmann::json::parse(json_params);
      type = json_root.value("audio_input_type", mediasdk::kAudioInputNone);
    } catch (const std::exception& e) {
      LOG(ERROR) << "Catch exception: " << e.what()
                 << " json string:" << json_params;
      return nullptr;
    }
    switch (type) {
      case mediasdk::kAudioInputMicrophone:
        ret = new WASApiAudioInputMicrophoneSource(proxy);
        break;
      case mediasdk::kAudioInputLoopback:
        ret = new WASApiAudioInputLoopBackSource(proxy);
        break;
      default:
        DCHECK(false);
        break;
    }
    if (ret && !ret->Create(json_root)) {
      ret->Destroy();
      delete ret;
      ret = nullptr;
    }
  }
  return ret;
}

void DestroyAudioInputSource(AudioInputSource* source) {
  WASApiAudioInputSource* source_tmp =
      dynamic_cast<WASApiAudioInputSource*>(source);
  if (source_tmp) {
    source_tmp->Destroy();
    delete source_tmp;
  }
}

MediaSDKStringData EnumAudioInputDevice() {
  std::list<WASAPIDevice::DeviceName> input_info = WASAPIDevice::GetInput();
  std::list<WASAPIDevice::DeviceName> output_info = WASAPIDevice::GetOutput();
  WASAPIDevice::DeviceName default_output_info =
      WASAPIDevice::GetDefaultOutput();
  WASAPIDevice::DeviceName default_input_info = WASAPIDevice::GetDefaultInput();
  nlohmann::json json_root, json_item, json_input, json_output;

  for (auto iter : input_info) {
    json_item["device_id"] = base::WideToUTF8(iter.id.c_str());
    json_item["device_name"] = base::WideToUTF8(iter.name.c_str());
    json_item["default"] = bool(iter.id == default_input_info.id);
    json_input.push_back(json_item);
  }
  for (auto iter : output_info) {
    json_item["device_id"] = base::WideToUTF8(iter.id.c_str());
    json_item["device_name"] = base::WideToUTF8(iter.name.c_str());
    json_item["default"] = bool(iter.id == default_output_info.id);
    json_output.push_back(json_item);
  }
  json_root["audio_input"] = json_input;
  json_root["audio_output"] = json_output;
  std::string json = json_root.dump();
  LOG(INFO) << "ret: " << json;
  return MediaSDKString(json).Detach();
}

MediaSDKStringData GetDefaultAudioInputDevice() {
  WASAPIDevice::DeviceName default_info = WASAPIDevice::GetDefaultInput();
  nlohmann::json json_root;
  json_root["device_id"] = base::WideToUTF8(default_info.id.c_str());
  json_root["device_name"] = base::WideToUTF8(default_info.name.c_str());
  std::string json = json_root.dump();
  LOG(INFO) << "GetDefaultAudioInputDevice ret: " << json;
  return MediaSDKString(json).Detach();
}

MediaSDKStringData GetDefaultAudioOutDevice() {
  WASAPIDevice::DeviceName default_info = WASAPIDevice::GetDefaultOutput();
  nlohmann::json json_root;
  json_root["device_id"] = base::WideToUTF8(default_info.id.c_str());
  json_root["device_name"] = base::WideToUTF8(default_info.name.c_str());
  std::string json = json_root.dump();
  LOG(INFO) << "GetDefaultAudioOutDevice ret: " << json;
  return MediaSDKString(json).Detach();
}

MediaSDKStringData EnumCaptureAudioDevice() {
  std::list<WASAPIDevice::DeviceName> input_info = WASAPIDevice::GetInput();
  nlohmann::json json_root, json_item;
  for (auto iter : input_info) {
    json_item["device_id"] = base::WideToUTF8(iter.id.c_str());
    json_item["device_name"] = base::WideToUTF8(iter.name.c_str());
    json_root.push_back(json_item);
  }
  std::string json = json_root.dump();
  LOG(INFO) << "ret: " << json;
  return MediaSDKString(json).Detach();
}

MediaSDKStringData EnumRenderAudioDevice() {
  std::list<WASAPIDevice::DeviceName> output_info = WASAPIDevice::GetOutput();
  nlohmann::json json_root, json_item;
  for (auto iter : output_info) {
    json_item["device_id"] = base::WideToUTF8(iter.id.c_str());
    json_item["device_name"] = base::WideToUTF8(iter.name.c_str());
    json_root.push_back(json_item);
  }
  std::string json = json_root.dump();
  LOG(INFO) << "EnumRenderAudioDevice ret: " << json;
  return MediaSDKString(json).Detach();
}

bool PluginGlobalInit(PluginGlobalProxy* global_proxy) {
  WASAPIAudioDeviceMonitor::GetInstance()->Init(global_proxy);
  return true;
}

void PluginGlobalUnInit() {
  WASAPIAudioDeviceMonitor::GetInstance()->Uninit();
}

int32_t AudioClientHungTest(const wchar_t* device_id, int32_t stream_flags) {
  if (!device_id || !device_id[0]) {
    return E_INVALIDARG;
  }
  WASAPIDevice::DeviceName device;
  device.id = device_id;
  WASAPICaptureStream test_tmp(nullptr, nullptr);
  if (!test_tmp.Create(device, stream_flags)) {
    return E_UNEXPECTED;
  }
  int32_t ret = E_FAIL;
  if (test_tmp.StuckTest()) {
    ret = S_OK;
  }
  test_tmp.Destroy();
  return ret;
}

}  // namespace mediasdk
