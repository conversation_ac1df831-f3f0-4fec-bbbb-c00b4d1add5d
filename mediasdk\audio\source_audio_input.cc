#include "source_audio_input.h"

#include "audio/audio_frame_utils.h"
#include "base/mediasdk/thread_safe_deleter.h"
#include "component_center.h"
#include "data_center/vqos_data.h"
#include "frame_convert.h"
#include "mediasdk/public/plugin/visual_source.h"
#include "notify_center.h"
#include "public/mediasdk_audio_status_observer.h"

namespace mediasdk {

std::shared_ptr<SourceAudioInput> SourceAudioInput::Create(
    const std::string& id,
    const AudioFormat& output_format) {
  return std::shared_ptr<SourceAudioInput>(
      new SourceAudioInput(id, output_format),
      base::ThreadSafeDeleter<SourceAudioInput>());
}

SourceAudioInput::SourceAudioInput(const std::string& id,
                                   const AudioFormat& output_format)
    : AudioInput(id, output_format) {}

SourceAudioInput::~SourceAudioInput() {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  LOG(INFO) << "~SourceAudioInput [" << GetId() << "]";
}

void SourceAudioInput::AttachSource(std::shared_ptr<AudioInputSource> source) {
  if (source) {
    monitor_manager_.SetNeedSync(source->NeedSyn());
  }
  source_ = std::move(source);
}

std::shared_ptr<AudioInputSource> SourceAudioInput::DetachSource() {
  filter_chain_.DestroyAllFilter();
  return std::move(source_);
}

bool SourceAudioInput::IsFromVisual() {
  auto visual_source = std::dynamic_pointer_cast<VisualSource>(source_);
  return !!visual_source;
}

std::string SourceAudioInput::GetDevName() {
  if (source_) {
    auto dev_name = source_->GetProperty("dev_name");
    return dev_name.ToString();
  }
  return "";
}

std::string SourceAudioInput::GetName() {
  if (source_) {
    const char* name = source_->GetAudioSourceName();
    if (name && name[0]) {
      return name;
    } else {
      NOTREACHED();
    }
  }
  return "";
}

std::shared_ptr<AudioInputSource> SourceAudioInput::source() {
  return source_;
}

void SourceAudioInput::OnAudio(const AudioFormat& format,
                               const AudioSourceFrame& input_source_frame) {
  auto input_frame = AudioSourceFrameToAudioFrame(input_source_frame);
  ProcessAudioFrame(format, input_frame);
}

void SourceAudioInput::SignalSourceEvent(const char* name) {
  if (!name) {
    LOG(ERROR) << "SignalEvent error!";
    return;
  }
  auto* nc = com::GetNotifyCenter();
  nc->AudioEvent()->Notify(FROM_HERE,
                           &MediaSDKAudioStatusObserver::OnAudioEvent, GetId(),
                           std::string(name));
}

void SourceAudioInput::ReportSourceEvent(const AudioSourceEvent& event) {
  VqosData data;
  data.ReportSourceEvent({static_cast<int>(SOURCE_CATEGORY::kAudio),
                          event.source_name, event.event_type, event.extra_code,
                          event.extra_msg.ToString()});
}

MediaSDKString SourceAudioInput::GetInitParams() {
  return MediaSDKString();
}

}  // namespace mediasdk