#include "audio_filter_chain.h"

#include <mediasdk/audio/audio_input.h>
#include <mediasdk/component_proxy.h>
#include <mediasdk/mediasdk_thread.h>
#include "mediasdk/plugin_service/plugin_service.h"
#include "notify_center.h"
#include "public/mediasdk_audio_status_observer.h"

namespace mediasdk {

AudioFilterChain::AudioFilterChain(AudioInput* audio_input)
    : audio_input_(audio_input) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  DCHECK(audio_input_);
}

AudioFilterChain::~AudioFilterChain() {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  std::vector<std::shared_ptr<AudioFilter>> filters;
  filters.reserve(filters_.size());
  for (auto& [_, filter] : std::move(filters_)) {
    filters.push_back(std::move(filter));
  }
  PluginServiceProxy::Call(FROM_HERE, &PluginService::DestroyAudioFilter,
                           std::move(filters));
}

void AudioFilterChain::CreateFilter(const std::string& filter_id,
                                    const std::string& plugin_name,
                                    const std::string& json_params,
                                    MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  std::lock_guard lock(filters_mutex_);

  if (FindFilterByIdInner(filter_id)) {
    LOG(ERROR) << "CreateFilter with exist filter_id";
    return std::move(callback).Resolve(false);
  }

  PluginServiceProxy::PostTaskAndReplyWithResult(
      FROM_HERE,
      base::BindOnce(&AudioFilterChain::OnAudioFilterCreated, AsWeakPtr(),
                     std::move(callback), filter_id),
      &PluginService::CreateAudioFilter, plugin_name, json_params);
}

void AudioFilterChain::DestroyFilter(const std::string& filter_id,
                                     MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);

  std::lock_guard lock(filters_mutex_);
  if (const auto it = std::find_if(filters_.begin(), filters_.end(),
                                   [&filter_id](const FilterItem& item) {
                                     return item.id == filter_id;
                                   });
      it != filters_.end()) {
    std::vector<std::shared_ptr<AudioFilter>> filter_list;
    filter_list.push_back(std::move(it->filter));
    filters_.erase(it);
    PluginServiceProxy::PostTaskAndReply(
        FROM_HERE,
        base::BindOnce(
            [](MSCallbackBool final_callback) {
              std::move(final_callback).Resolve(true);
            },
            std::move(callback)),
        &PluginService::DestroyAudioFilter, std::move(filter_list));
    return;
  }
  std::move(callback).Resolve(false);
}

void AudioFilterChain::SetFilterEnable(const std::string& filter_id,
                                       bool enable,
                                       MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  std::lock_guard lock(filters_mutex_);
  auto audio_filter = FindFilterByIdInner(filter_id);
  if (!audio_filter) {
    LOG(ERROR) << "Can not find the filter";
    return std::move(callback).Resolve(false);
  }
  PluginServiceProxy::Call(FROM_HERE, std::move(callback),
                           &PluginService::SetAudioFilterEnable,
                           std::move(audio_filter), enable);
}

void AudioFilterChain::IsFilterEnable(const std::string& filter_id,
                                      MSCallback<ResultBoolBool> callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  std::lock_guard lock(filters_mutex_);
  auto audio_filter = FindFilterByIdInner(filter_id);
  if (!audio_filter) {
    LOG(ERROR) << "Can not find the filter";
    return std::move(callback).Resolve({false, false});
  }
  PluginServiceProxy::Call(FROM_HERE, std::move(callback),
                           &PluginService::IsAudioFilterEnable,
                           std::move(audio_filter));
}

void AudioFilterChain::SetFilterProperty(const std::string& filter_id,
                                         const std::string& key,
                                         const std::string& json_params,
                                         MSCallbackBool callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  std::lock_guard lock(filters_mutex_);
  auto audio_filter = FindFilterByIdInner(filter_id);
  if (!audio_filter) {
    LOG(ERROR) << "Can not find the filter";
    return std::move(callback).Resolve(false);
  }

  PluginServiceProxy::Call(FROM_HERE, std::move(callback),
                           &PluginService::SetAudioFilterProperty,
                           std::move(audio_filter), key, json_params);
}

void AudioFilterChain::GetFilterProperty(
    const std::string& filter_id,
    const std::string& key,
    MSCallback<ResultBoolString> callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  std::lock_guard lock(filters_mutex_);
  auto filter = FindFilterByIdInner(filter_id);
  if (!filter) {
    LOG(ERROR) << "Can not find the filter";
    return std::move(callback).Resolve({false, ""});
  }

  PluginServiceProxy::Call(FROM_HERE, std::move(callback),
                           &PluginService::GetAudioFilterProperty,
                           std::move(filter), key);
}

void AudioFilterChain::FilterAction(const std::string& filter_id,
                                    const std::string& action,
                                    const std::string& param,
                                    MSCallback<ResultBoolString> callback) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  std::lock_guard lock(filters_mutex_);
  auto filter = FindFilterByIdInner(filter_id);
  if (!filter) {
    LOG(ERROR) << "Can not find the filter";
    return std::move(callback).Resolve({false, ""});
  }

  PluginServiceProxy::Call(FROM_HERE, std::move(callback),
                           &PluginService::AudioFilterAction, std::move(filter),
                           action, param);
}

void AudioFilterChain::ApplyFilter(AudioFrame* frame) {
  if (!frame) {
    return;
  }

  std::lock_guard lock(filters_mutex_);
  for (const auto& [_, filter] : filters_) {
    if (filter) {
      // todo: use return value
      filter->Process(frame);
    }
  }
}

void AudioFilterChain::DestroyAllFilter() {
  std::lock_guard lock(filters_mutex_);
  std::vector<std::shared_ptr<AudioFilter>> filters;
  filters.reserve(filters_.size());
  for (auto& [_, filter] : std::move(filters_)) {
    filters.push_back(std::move(filter));
  }
  PluginServiceProxy::Call(FROM_HERE, &PluginService::DestroyAudioFilter,
                           std::move(filters));
}

void AudioFilterChain::OnAudioFilterCreated(
    MSCallbackBool callback,
    const std::string& filter_id,
    std::shared_ptr<AudioFilter> filter) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  if (!filter) {
    std::move(callback).Resolve(false);
    return;
  }
  std::lock_guard lock(filters_mutex_);
  if (FindFilterByIdInner(filter_id)) {
    std::vector<std::shared_ptr<AudioFilter>> filter_list;
    filter_list.push_back(std::move(filter));
    LOG(ERROR) << "AddFilter with exist filter_id";
    PluginServiceProxy::PostTaskAndReply(
        FROM_HERE,
        base::BindOnce(
            [](MSCallbackBool finish_callback) {
              std::move(finish_callback).Resolve(false);
            },
            std::move(callback)),
        &PluginService::DestroyAudioFilter, std::move(filter_list));
    return;
  }
  PluginServiceProxy::PostTaskAndReplyWithResult(
      FROM_HERE,
      base::BindOnce(&AudioFilterChain::OnAudioFilterInited, AsWeakPtr(),
                     std::move(callback), filter_id, filter),
      &PluginService::InitAudioFilter, filter, audio_input_->GetOutputFormat(),
      this);
}

void AudioFilterChain::OnAudioFilterInited(MSCallbackBool callback,
                                           const std::string& filter_id,
                                           std::shared_ptr<AudioFilter> filter,
                                           const bool success) {
  DCHECK_CURRENTLY_ON(ThreadID::AUDIO);
  if (!filter) {
    std::move(callback).Resolve(false);
    return;
  }
  if (!success) {
    std::vector<std::shared_ptr<AudioFilter>> filter_list;
    filter_list.push_back(std::move(filter));
    PluginServiceProxy::PostTaskAndReply(
        FROM_HERE,
        base::BindOnce(
            [](MSCallbackBool finish_callback) {
              std::move(finish_callback).Resolve(false);
            },
            std::move(callback)),
        &PluginService::DestroyAudioFilter, std::move(filter_list));
    return;
  }

  {
    std::lock_guard lock(filters_mutex_);
    filters_.push_back({filter_id, std::move(filter)});
  }

  std::move(callback).Resolve(true);
}

std::shared_ptr<AudioFilter> AudioFilterChain::FindFilterByIdInner(
    const std::string& filter_id) {
  const auto it = std::find_if(
      filters_.begin(), filters_.end(),
      [&filter_id](const auto& item) { return item.id == filter_id; });
  return it != filters_.end() ? it->filter : nullptr;
}

MediaSDKString AudioFilterChain::GetFilterId(const AudioFilter* filter) const {
  if (!filter) {
    return {};
  }

  std::lock_guard lock(filters_mutex_);
  const auto it = std::find_if(
      filters_.begin(), filters_.end(),
      [filter](const FilterItem& item) { return item.filter.get() == filter; });
  if (it == filters_.end()) {
    return {};
  }
  return {it->id};
}

void AudioFilterChain::SendNotify(const AudioFilter* filter,
                                  const char* event) {
  if (!event) {
    LOG(ERROR) << "SendNotify error!";
    return;
  }

  if (audio_input_) {
    auto* nc = com::GetNotifyCenter();
    nc->AudioEvent()->Notify(FROM_HERE,
                             &MediaSDKAudioStatusObserver::OnAudioEvent,
                             audio_input_->GetId(), std::string(event));
  }
}

}  // namespace mediasdk