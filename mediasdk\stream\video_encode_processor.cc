#include "video_encode_processor.h"

#include <magic_enum.hpp>
#include <nlohmann/json.hpp>
#include <utility>
#include "audio_encode_processor.h"
#include "mediasdk/component_proxy.h"
#include "mediasdk/data_center/biz_center/end_to_end_delay_encoder.h"
#include "mediasdk/debug_helper.h"
#include "mediasdk/public/plugin/video_encoder_source.h"
#include "mediasdk/random_hung_mock_center.h"
#include "shared_video_encode_texture.h"
#include "video_encode_fallback.h"

namespace mediasdk {
namespace {

#define LOG_TAG(level) LOG(level) << "[Video Encode Processor] "

constexpr char kThreadName[] = "ENCODE_Thread";

constexpr char kEmptyCodecName[] = "";

std::string GenerateThreadName() {
  static int s_count = 0;
  return std::to_string(++s_count) + kThreadName;
}

G_CONST_STRING(encoder, MAX_SINK_COUNT);

// When the encoding speed is slower than the production speed, the produced
// quantity is faster than the encoded quantity. When the threshold
// kDropThreshold is exceeded, the produced video frame data is discarded
constexpr int kDropThreshold = 6;

constexpr int kPtsBufferThreshold = 150;

constexpr int kMaxReBuild = 3;

std::shared_ptr<VideoPacket> BuildVideoPacketFromEncoderSourcePacket(
    const VideoEncoderSourcePacket& source_packet) {
  auto video_packet = std::make_shared<VideoPacket>();
  // Set the properties of the VideoPacket from the source_packet
  video_packet->SetPTS(source_packet.pts);
  video_packet->SetDTS(source_packet.dts);
  video_packet->SetStreamType(source_packet.stream_type);
  video_packet->SetFrameType(source_packet.frame_type);
  video_packet->SetPriority(source_packet.priority);
  video_packet->SetKeyFrame(source_packet.is_key_frame);
  video_packet->SetRefferred(source_packet.is_refferred);

  // Handle the NAL units
  int pay_load_size = 0;
  for (uint32_t i = 0; i < source_packet.nals_count; ++i) {
    pay_load_size += source_packet.nals[i].payload_size;
  }
  video_packet->ReserveDataCapacity(pay_load_size);

  for (uint32_t i = 0; i < source_packet.nals_count; ++i) {
    video_packet->AppendData(source_packet.nals[i].payload,
                             source_packet.nals[i].payload_size);
  }

  return video_packet;
}
}  // namespace

VideoEncodeProcessor::VideoEncodeProcessor(
    std::shared_ptr<VideoEncoderConfig> config)
    : config_(std::move(config)),
      e2d_delay_(
          std::make_shared<EndtoEndDelayEncoder>("anchor_encode_delay")) {
  LOG(INFO) << "VideoEncodeProcessor created[" << this << "]";
  DCHECK(config_);

  input_adaptive_ = std::make_unique<VideoEncodeInputAdaptive>(
      VideoEncodeAdaptiveConfig{config_->GetOutputWidth(),
                                config_->GetOutputHeight(),
                                config_->GetTimeBase()},
      this);

  thread_ = std::make_shared<base::Thread>(GenerateThreadName());
  thread_->Start();

  frame_rate_statistics_ = std::make_unique<VideoEncodeProcessorStatistics>();

  if (auto dc = com::GetDataCenter(); dc) {
    use_new_version_pushing_timestamp_ = dc->IsUseNewVersionPushingTimeStamp();
  }
}

VideoEncodeProcessor::~VideoEncodeProcessor() {
  TRACE_ACTION_DURATION(
      TypeToString(trace::TASK_COST_TYPE::StopStream_VideoEncoderDestroyed));
  LOG_TAG(INFO) << "VideoEncodeProcessor Destroyed[" << this << "]";

  if (thread_) {
    thread_->Stop();
  }
  LOG_TAG(INFO) << "Thread stoped";
}

void VideoEncodeProcessor::SetEncoder(
    std::shared_ptr<VideoEncoderSource> encoder) {
  DCHECK(encoder);

  encoder_ = std::move(encoder);
  if (encoder_) {
    const auto encoder_name = encoder_->GetName();
    encoder_rate_calc_.SetCodecName(encoder_name);
  }
}

void VideoEncodeProcessor::SetSinkId(const uint32_t sink_id) {
  LOG_TAG(INFO) << "Set sink id: " << sink_id;
  sink_id_ = sink_id;
  encoder_rate_calc_.SetSinkId(sink_id);
}

std::string VideoEncodeProcessor::GetId() const {
  if (config_) {
    return config_->GetVideoEncoderId();
  }
  return std::string();
}

void VideoEncodeProcessor::SetConfig(
    const std::shared_ptr<VideoEncoderConfig>& config) {
  config_ = config;
}

void VideoEncodeProcessor::UpdateConfig() {
  if (!encoder_ || !config_) {
    return;
  }
  config_->SetIsHardWare(IsHardWareEncoder());
}

std::shared_ptr<std::vector<uint8_t>> VideoEncodeProcessor::GetExtraData()
    const {
  return latest_extra_data_;
}

void VideoEncodeProcessor::AddObserver(
    std::shared_ptr<VideoEncodeProcessorObserver> observer) {
  if (observer_list_.AddObserver(std::move(observer)) ==
      base::ObserverListThreadSafeWeak<
          VideoEncodeProcessorObserver>::AddObserverResult::kBecameNonEmpty) {
    Start();
  }
}

void VideoEncodeProcessor::RemoveObserver(
    std::shared_ptr<VideoEncodeProcessorObserver> observer) {
  if (observer_list_.RemoveObserver(std::move(observer)) ==
      base::ObserverListThreadSafeWeak<VideoEncodeProcessorObserver>::
          RemoveObserverResult::kWasOrBecameEmpty) {
    Stop();
  }
}

void VideoEncodeProcessor::SetAudioEncoderProcessor(
    std::shared_ptr<AudioEncodeProcessor> ptr) {
  if (ptr) {
    LOG(INFO) << "VideoEncodeProcessor bind [" << this << "] to [" << ptr.get()
              << "]";
  }
  audio_encoder_ = ptr;
}

int64_t VideoEncodeProcessor::GetVideoEncoderStartVideoFrameTS() const {
  return begin_video_frame_timestamp_ns_;
}

StreamType VideoEncodeProcessor::GetStreamType() const {
  std::shared_lock lock(encoder_lock_);
  DCHECK(encoder_);
  return encoder_->GetStreamType();
}

uint32_t VideoEncodeProcessor::GetCurrentTargetBitrate() const {
  std::shared_lock lock(encoder_lock_);
  DCHECK(encoder_);
  if (!encoder_) {
    return 0;
  }
  return encoder_->CurrentTargetBitrate();
}

bool VideoEncodeProcessor::OptimizeBitrate(
    const uint32_t bitrate_kbps,
    ReconfigReasonType reason,
    base::OnceCallback<void(uint32_t, bool)> callback) {
  bool need_reconfig = true;
  uint32_t target_bitrate = bitrate_kbps;
  if (encoder_optimizer_) {
    encoder_optimizer_->ReconfigTargetBitrate(bitrate_kbps, reason,
                                              target_bitrate, need_reconfig);
  }

  if (need_reconfig) {
    const auto task_runner = thread_->task_runner();
    task_runner->PostTaskAndReplyWithResult(
        FROM_HERE,
        base::BindOnce(&VideoEncodeProcessor::ReconfigBitrateInternal,
                       base::Unretained(this), target_bitrate),
        base::BindOnce(std::move(callback), target_bitrate));
  } else {
    std::move(callback).Run(bitrate_kbps, false);
  }
  return true;
}

void VideoEncodeProcessor::Adaptive(
    VideoEncodeAdaptiveConfig config,
    base::OnceCallback<void(VideoEncodeAdaptiveResult)> callback) {
  thread_->task_runner()->PostTaskAndReplyWithResult(
      FROM_HERE,
      base::BindOnce(&VideoEncodeProcessor::AdaptiveInternal,
                     base::Unretained(this), config),
      std::move(callback));
}

bool VideoEncodeProcessor::HasRecordObserver() {
  bool has_rec_observer = false;
  observer_list_.ForEachObserver(
      [&has_rec_observer](
          const std::shared_ptr<VideoEncodeProcessorObserver>& observer) {
        has_rec_observer |= observer->IsRecord();
      });

  return has_rec_observer;
}

void VideoEncodeProcessor::ResetOptimizer() {
  if (!encoder_optimizer_) {
    return;
  }
  encoder_optimizer_->ResetBitrate();
}

std::string VideoEncodeProcessor::GetEncoderConfigPrettyString(
    const int32_t indent) const {
  if (config_) {
    return config_->ToString(indent);
  }
  return std::string();
}

const char* VideoEncodeProcessor::GetVideoEncoderName() const {
  std::shared_lock lock(encoder_lock_);
  DCHECK(encoder_);
  if (!encoder_) {
    return kEmptyCodecName;
  }
  return encoder_->GetName();
}

bool VideoEncodeProcessor::IsHardWareEncoder() {
  std::shared_lock lock(encoder_lock_);
  DCHECK(encoder_);
  return encoder_ &&
         encoder_->GetEncoderType() == kVideoEncoderSourceTypeHardWare;
}

void VideoEncodeProcessor::OnTargetBitrate(
    const int bitrate_kbps,
    base::OnceCallback<void(uint32_t, bool)> callback) {
  const auto task_runner = thread_->task_runner();
  task_runner->PostTaskAndReplyWithResult(
      FROM_HERE,
      base::BindOnce(&VideoEncodeProcessor::ReconfigBitrateInternal,
                     base::Unretained(this), bitrate_kbps),
      base::BindOnce(std::move(callback), bitrate_kbps));
}

void VideoEncodeProcessor::OnEncoderOptimizerEvent(const std::string& event) {
  if (event.empty()) {
    return;
  }
  LOG(INFO) << "[VideoEncodeProcessor] OnEncoderOptimizerEvent: " << event;
  DispatchEncodeEvent(event);
}

VideoModelOutputObserverType VideoEncodeProcessor::RequiredBufferType() const {
  std::shared_lock lock(encoder_lock_);
  DCHECK(encoder_);
  return encoder_->GetInputType() == kVideoEncoderSourceInputTypeBuffer
             ? VideoModelOutputObserverType::
                   kVideoModelOutputObserverTypeVideoFrame
             : VideoModelOutputObserverType::
                   kVideoModelOutputObserverTypeTexture;
}

PixelFormat VideoEncodeProcessor::OnGetRequestedPixelFormat() const {
  std::shared_lock lock(encoder_lock_);
  DCHECK(encoder_);
  return encoder_->GetRequestedPixelFormat();
}

void VideoEncodeProcessor::OnAppendExtraData(const uint8_t* data, size_t size) {
  if (encoder_fallback_) {
    encoder_fallback_->OnAppendExtraData(data, size);
    return;
  }
  if (!latest_extra_data_) {
    latest_extra_data_ = std::make_shared<std::vector<uint8_t>>();
  }
  latest_extra_data_->insert(latest_extra_data_->end(), data, data + size);
}

void VideoEncodeProcessor::OnResetExtraData(const uint8_t* data, size_t size) {
  if (encoder_fallback_) {
    encoder_fallback_->OnResetExtraData(data, size);
    return;
  }
  latest_extra_data_.reset();
  latest_extra_data_ = std::make_shared<std::vector<uint8_t>>();
  latest_extra_data_->insert(latest_extra_data_->end(), data, data + size);
}

void VideoEncodeProcessor::OnVideoFrame(std::shared_ptr<VideoFrame> frame) {
  DCHECK(frame);

  CheckEncodeFreezing();

  if (frame->GetFormat() != encoder_->GetRequestedPixelFormat()) {
    return;
  }

  DCHECK(frame->GetFormat() == encoder_->GetRequestedPixelFormat());

  DispatchVideoFrame();

  if (NeedDiscardCurrentFrame()) {
    return;
  }

  AddOnePendingFrameForHandle();
  const auto task_runner = thread_->task_runner();
  task_runner->PostTask(
      FROM_HERE, base::BindOnce(&VideoEncodeProcessor::HandleVideoFrame,
                                base::Unretained(this), std::move(frame)));
}

void VideoEncodeProcessor::OnModelTexture(const VideoTexture& texture) {
  DCHECK(encoder_);

  if (input_adaptive_) {
    input_adaptive_->InputTexture(texture);
  }

  // The following process will proceed to OnAdaptiveTexture
}

void VideoEncodeProcessor::OnAdaptiveTexture(const VideoTexture& texture) {
  CheckEncodeFreezing();

  DispatchVideoFrame();

  if (NeedDiscardCurrentFrame()) {
    return;
  }

  if (!texture.GetTexture()) {
    NOTREACHED();
    return;
  }

  if (!shared_texture_factory_) {
    if (!config_) {
      return;
    }
    shared_texture_factory_ = std::make_unique<SharedVideoEncodeTextureFactory>(
        texture.GetTexture()->GetDevice(), config_->GetVideoRange(),
        config_->GetColorSpace());
  }

  auto shared_tex = shared_texture_factory_->Build(texture);
  if (!shared_tex) {
    return;
  }

  if (encoder_optimizer_) {
    encoder_optimizer_->OnSharedTexture(shared_tex);
  }

  AddOnePendingFrameForHandle();
  const auto task_runner = thread_->task_runner();
  task_runner->PostTask(
      FROM_HERE, base::BindOnce(&VideoEncodeProcessor::HandleSharedTexture,
                                base::Unretained(this), std::move(shared_tex)));
}

void VideoEncodeProcessor::OnEncodedData(
    const VideoEncoderSourcePacket& packet) {
  uint64_t last_encoded_timestamp_ms = encoded_timestamp_ms_;
  encoded_timestamp_ms_ = low_precision_milli_now();

  bool is_first_key_frame = false;
  if (!CheckFirstEncodedDataKeyFrameAppeared(packet, is_first_key_frame)) {
    return;
  }
  if (is_first_key_frame && last_encoded_timestamp_ms > 0) {
    LOG(INFO) << "[VideoEncodeProcessor] first_key_frame encode cost: "
              << encoded_timestamp_ms_ - last_encoded_timestamp_ms << " ms";
    DispatchEncodeEvent(BuildFirstKeyFrameCost(encoded_timestamp_ms_ -
                                               last_encoded_timestamp_ms));
  }

  gop_checker_.OnVideoPacket(packet.frame_type,
                             encoder_ ? encoder_->GetName() : "");

  auto video_packet = BuildVideoPacketFromEncoderSourcePacket(packet);
  if (video_packet) {
    video_packet->SetIsFirstKeyFrame(is_first_key_frame);
    if (use_new_version_pushing_timestamp_) {
      auto width = config_->GetOutputWidth();
      if (auto adaptive_width = config_->GetAdaptiveOutputWidth();
          adaptive_width) {
        width = *adaptive_width;
      }
      auto height = config_->GetOutputHeight();
      if (auto adaptive_height = config_->GetAdaptiveOutputHeight();
          adaptive_height) {
        height = *adaptive_height;
      }
      auto fps = config_->GetTimeBase();
      if (auto adaptive_timebase = config_->GetAdaptiveTimeBase();
          adaptive_timebase) {
        fps = *adaptive_timebase;
      }
      video_packet->SetWidth(width);
      video_packet->SetHeight(height);
      video_packet->SetFps(fps);
      if (is_first_key_frame) {
        video_packet->SetExtraData(GetExtraData());
      }
    }
  }

  AdjustEncodedPacketTimestampIndex(video_packet);

  // Record output frame for statistics
  if (frame_rate_statistics_) {
    frame_rate_statistics_->OnOutputFrame();
  }

  video_packet->SetEndtoEndMgr(
      e2d_delay_->PopEndtoEndMgr(video_packet->GetPTS()));
  DispatchVideoPacket(std::move(video_packet));
}

void VideoEncodeProcessor::OnAdaptiveCompleted(
    bool successed,
    std::shared_ptr<std::vector<uint8_t>> extra_data) {
  // must on encoder thread
  adaptive_director_.reset();
  if (successed) {
    ResetPtsStatusAfterReconfig(
        input_adaptive_ ? input_adaptive_->GetConfig().fps : 0);
    if (input_adaptive_) {
      auto config = input_adaptive_->GetConfig();
      config_->SetAdaptiveOutputWidth(config.width);
      config_->SetAdaptiveOutputHeight(config.height);
      config_->SetAdaptiveTimeBase(config.fps);
      if (encoder_optimizer_ && encoder_) {
        encoder_optimizer_->ResetCae(sink_id_);
      }
    }
    smoothing_.OnPacketCaptureDtsTimeStampNSBeforeReconfig(
        last_capture_dts_timestamp_);
  }
  if (!latest_extra_data_ && extra_data) {
    latest_extra_data_ = extra_data;
  }
  LOG(ERROR) << "[VideoEncodeProcessor] OnReconfigureCompleted "
             << (successed ? "Successes" : "Failed");
}

bool VideoEncodeProcessor::OnBeforeReconfig(
    std::shared_ptr<std::vector<uint8_t>>& extra_data) {
  FlushEncoder();
  extra_data = latest_extra_data_;
  latest_extra_data_.reset();
  return true;
}

bool VideoEncodeProcessor::SupportReconfigSizeFps() {
  return encoder_ ? encoder_->SupportReconfigBaseInfo() : false;
}

bool VideoEncodeProcessor::ReconfigEncoderWithParam(uint32_t width,
                                                    uint32_t height,
                                                    uint32_t fps) {
  DCHECK(encoder_);

  if (!encoder_) {
    return false;
  }

  reconfig_num_++;

  MSSize size = {(int32_t)width, (int32_t)height};
  return encoder_->ReconfigBaseInfo(size, fps);
}

bool VideoEncodeProcessor::RebuildEncoderWithParam(uint32_t width,
                                                   uint32_t height,
                                                   uint32_t fps) {
  DCHECK(encoder_);

  if (!encoder_) {
    return false;
  }

  rebuild_num_++;

  MSSize size = {(int32_t)width, (int32_t)height};
  return encoder_->RebuildEncoder(size, fps);
}

VideoEncodeInputAdaptive* VideoEncodeProcessor::GetInputAdaptive() {
  return input_adaptive_.get();
}

void VideoEncodeProcessor::HandleVideoFrame(std::shared_ptr<VideoFrame> frame) {
  if (config_) {
    if (frame->GetWidth() != config_->GetOutputWidth() ||
        frame->GetHeight() != config_->GetOutputHeight()) {
      // output size not equal to encoder size
      return;
    }
  }
  DCHECK(encoder_);
  if (!encoder_) {
    return;
  }

  if (encoder_->GetInputType() != kVideoEncoderSourceInputTypeBuffer) {
    return;
  }

  if (frame->GetFormat() != encoder_->GetRequestedPixelFormat()) {
    return;
  }

  if (encoder_optimizer_) {
    encoder_optimizer_->OnFrame(frame);
  }

  HandleFrame(frame->GetTimeStampNS(), [&frame, this](bool is_force_idr) {
    VideoEncoderSourceFrame encoder_frame = {};
    for (int32_t plane = 0; plane < graphics::kMaxVideoPlanes; ++plane) {
      encoder_frame.data[plane] = frame->GetPlaneData(plane);
      encoder_frame.line_size[plane] = frame->GetLineSize()[plane];
    }

    encoder_frame.pixel_format = frame->GetFormat();
    encoder_frame.pts_index = latest_video_in_encoder_pts_index_;
    encoder_frame.is_force_idr = is_force_idr;

    e2d_delay_->CacheEndtoEndMgr(latest_video_in_encoder_pts_index_,
                                 frame->GetEndtoEndMgr());
    bool encode_ret = false;
    {
      ScopedProfilerCostName cost(G_CONST_GET_STRING(encoder, sink_id_));
      ScopedProfilerName name(encoder_->GetName());
      encode_ret = encoder_->Encode(&encoder_frame);
    }
    if (!encode_ret) {
      e2d_delay_->RemoveEndtoEndMgr(latest_video_in_encoder_pts_index_);
    }
    return encode_ret;
  });

  OnePendingFrameHasBeenHandled();
}

void VideoEncodeProcessor::HandleSharedTexture(
    std::shared_ptr<SharedVideoEncodeTexture> shared_tex) {
  if (input_adaptive_) {
    auto config = input_adaptive_->GetConfig();
    auto tex = shared_tex->GetTexture();
    auto tex_size = tex->GetSize();
    if (static_cast<uint32_t>(tex_size.x) != config.width ||
        static_cast<uint32_t>(tex_size.y) != config.height) {
      // output size not equal to encoder size
      return;
    }
  }

  DCHECK(encoder_);
  if (!encoder_) {
    return;
  }

  if (encoder_->GetInputType() != kVideoEncoderSourceInputTypeTexture) {
    return;
  }

  HandleFrame(
      shared_tex->GetTimestampNS(), [&shared_tex, this](bool is_force_idr) {
        VideoEncoderSourceSharedTexture encode_shared_tex{
            shared_tex->GetSharedHandle(), latest_video_in_encoder_pts_index_,
            is_force_idr};

        e2d_delay_->CacheEndtoEndMgr(latest_video_in_encoder_pts_index_,
                                     shared_tex->GetEndtoEndMgr());
        bool encode_ret = false;
        {
          ScopedProfilerCostName cost(G_CONST_GET_STRING(encoder, sink_id_));
          ScopedProfilerName name(encoder_->GetName());
          encode_ret = encoder_->EncodeTexture(&encode_shared_tex);
        }
        if (!encode_ret) {
          e2d_delay_->RemoveEndtoEndMgr(latest_video_in_encoder_pts_index_);
        }
        return encode_ret;
      });

  OnePendingFrameHasBeenHandled();
}

void VideoEncodeProcessor::HandleFrame(
    int64_t timestamp_ns,
    std::function<bool(bool)> encode_process) {
  DCHECK(encoder_);

  if (!IsRunning()) {
    return;
  }

  if (!CheckAudioEncoderStartUP(timestamp_ns)) {
    LOG_TAG(WARNING) << "Waiting for audio buffer startup";
    return;
  }

  bool is_first_frame = false;
  if (!GenerateCurrentPts(timestamp_ns, is_first_frame)) {
    return;
  }

#ifdef ENABLE_RANDOM_HUNG_TEST
  mock_video_render_hung();
#endif  // ENABLE_RANDOM_HUNG_TEST

  // The encoding result will be obtained by OnEncodedData
  EncodeFrame(is_first_frame, encode_process);
}

void VideoEncodeProcessor::EncodeFrame(
    bool is_first_frame,
    std::function<bool(bool)> encode_process) {
  DCHECK(pts_generator_);

  encoder_rate_calc_.StartEncode();
  // Need to force first frame is key frame
  bool encode_success = encode_process(is_first_frame);
  if (!encode_success) {
    LOG_TAG(ERROR) << "VideoEncoderHolder encode failed";
    encoder_rate_calc_.OnEncodeResult(false);

    bool check_rebuild = false;
    if (use_new_version_pushing_timestamp_) {
      check_rebuild = CheckRebuild();
    }

    if (!encode_error_handled_) {
      encode_error_handled_ = true;

      VqosData data;
      data.ReportFatalError(
          {static_cast<int>(mediasdk::FATAL_ERROR_TYPE::kVideoEncodeFailed),
           encoder_ ? encoder_->GetName() : "", reconfig_num_, rebuild_num_});

      DispatchEncodeError();
      if (use_new_version_pushing_timestamp_ && !check_rebuild) {
        DispatchEncodeEvent(BuildEncodeFreezing());
      }
    }
    pts_generator_->PopBack();
    return;
  }
  encoder_rate_calc_.OnEncodeResult(true);
  // Record input frame for statistics
  if (frame_rate_statistics_) {
    frame_rate_statistics_->OnInputFrame();
  }
  return;
}

VideoEncodeAdaptiveResult VideoEncodeProcessor::AdaptiveInternal(
    VideoEncodeAdaptiveConfig config) {
  if (!adaptive_director_) {
    adaptive_director_ = std::make_shared<VideoEncoderAdaptiveDirector>(*this);
  }
  if (HasRecordObserver()) {
    LOG_TAG(ERROR)
        << "VideoEncoderReconfig res|fps failed because now is recording";
    return kVideoEncodeAdaptiveResultNotSupport;
  }
  return adaptive_director_->Direct(config);
}

bool VideoEncodeProcessor::ReconfigBitrateInternal(uint32_t bitrate_kbps) {
  DCHECK(encoder_);
  if (!encoder_) {
    return false;
  }
  return encoder_->ReconfigBitrate(bitrate_kbps);
}

bool VideoEncodeProcessor::GenerateCurrentPts(int64_t timestamp_ns,
                                              bool& is_first_frame) {
  DCHECK(pts_generator_);

  if (!begin_video_frame_timestamp_ns_) {
    LOG_TAG(INFO)
        << "stream phase  Video stream phase First Video Frame Received["
        << GetId() << "] [" << timestamp_ns << "]";
    begin_video_frame_timestamp_ns_ = timestamp_ns;
    is_first_frame = true;
  }
  latest_video_in_pts_ns_ = timestamp_ns - begin_video_frame_timestamp_ns_;
  auto pts_index =
      pts_generator_->Generate(begin_video_frame_timestamp_ns_, timestamp_ns);
  if (auto gop = config_->GetGOP(); gop > 0) {
    auto pts_buffer_count = pts_generator_->PtsBufferCount();
    if (pts_buffer_count > 2 * gop) {
      if (!pts_overflow_reported_) {
        pts_overflow_reported_ = true;
        DispatchEncodeEvent(BuildPtsOverFlow(pts_buffer_count));
      }
    }
  }
  if (MEDIASDK_INVALID_DTS == pts_index) {
    return false;
  }
  latest_video_in_encoder_pts_index_ = pts_index;
  auto pts_buffer_count = pts_generator_->PtsBufferCount();
  if (pts_buffer_count > kPtsBufferThreshold) {
    if (!pts_overflow_reported_) {
      pts_overflow_reported_ = true;
      DispatchEncodeEvent(BuildPtsOverFlow(pts_buffer_count));
    }
  }
  return true;
}

bool VideoEncodeProcessor::NeedDiscardCurrentFrame() {
  if (!IsRunning()) {
    return true;
  }

  if (pending_count_ > kDropThreshold) {
    auto now = nano_now();
    // If the processing threshold is exceeded, discard it
    LOG_TAG(ERROR) << "Threshold is exceeded, discard a frame ["
                   << now - pre_drop_ts_;
    pre_drop_ts_ = now;
    return true;
  }
  return false;
}

bool VideoEncodeProcessor::CheckFirstEncodedDataKeyFrameAppeared(
    const VideoEncoderSourcePacket& packet,
    bool& is_first_key_frame) {
  if (!checked_first_is_i_packet_) {
    if (!packet.is_key_frame) {
      LOG_TAG(WARNING) << "Force check first packet is i packet["
                       << magic_enum::enum_name<VideoFrameType>(
                              packet.frame_type)
                       << "] pts [" << packet.pts << "] dts [" << packet.dts
                       << "]";
      return false;
    }
    is_first_key_frame = true;
    checked_first_is_i_packet_ = true;
  }
  return true;
}

void VideoEncodeProcessor::AdjustEncodedPacketTimestampIndex(
    std::shared_ptr<VideoPacket>& video_packet) {
  DCHECK(encoder_);
  DCHECK(pts_generator_);

  const auto pts = pts_generator_->PopFront();
  const int64_t dts = pts - encoder_->GetBFrameCount();
  if (video_packet->GetDTS() == MEDIASDK_INVALID_DTS) {
    // adjust dts if illegal
    video_packet->SetDTS(dts);
    if (video_packet->GetPTS() < dts) {
      video_packet->SetPTS(dts);
    }
  } else {
    auto cur_dts = pts - encoder_->GetBFrameCount();
    while (pts_generator_->PtsBufferCount() > 0 &&
           cur_dts < video_packet->GetDTS()) {
      LOG(INFO) << "[VideoEncodeProcessor] dts must skip, cur_dts is: "
                << cur_dts << " dts is: " << video_packet->GetDTS();
      cur_dts = pts_generator_->PopFront();
      if (cur_dts == MEDIASDK_INVALID_DTS) {
        break;
      }
      cur_dts -= encoder_->GetBFrameCount();
    }
  }
  DCHECK(pre_dts_ <= video_packet->GetDTS());
  if (MEDIASDK_INVALID_DTS == pre_dts_) {
    LOG_TAG(INFO) << "Video stream phase receive first video packet[" << GetId()
                  << "]";
  } else if (pre_dts_ >= video_packet->GetDTS()) {
    LOG(ERROR) << "[VideoEncodeProcessor] dts must increase, pre_dts is "
               << pre_dts_ << " current dts is " << video_packet->GetDTS();
  }

  if (video_packet->GetDTS() > video_packet->GetPTS()) {
    LOG(ERROR) << "[VideoEncodeProcessor] invalid dts: "
               << video_packet->GetDTS()
               << " with pts: " << video_packet->GetPTS()
               << " frame type: " << (int)video_packet->GetFrameType();
  }

  if (int pts_diff = video_packet->GetPTS() - video_packet->GetDTS();
      pts_diff > kPtsBufferThreshold) {
    LOG(ERROR) << "[VideoEncodeProcessor] dts and pts too far: " << pts_diff;
  }

  pre_dts_ = video_packet->GetDTS();
  int time_base = config_->GetTimeBase();
  if (auto adaptive_time_base = config_->GetAdaptiveTimeBase();
      adaptive_time_base) {
    time_base = *adaptive_time_base;
  }
  if (use_new_version_pushing_timestamp_) {
    video_packet->SetStartUpTimeStampNS(
        smoothing_.AdjustedBeginCaptureDtsTimeStampNS(
            video_packet->GetDTS(), encoder_->GetBFrameCount(), time_base,
            begin_video_frame_timestamp_ns_));
  } else {
    video_packet->SetStartUpTimeStampNS(begin_video_frame_timestamp_ns_);
  }
  video_packet->SetTimeBase(time_base);

  latest_encoded_video_pts_ns_ = video_packet->GetCapturePTSTimeStampNS() -
                                 begin_video_frame_timestamp_ns_;
  DCHECK(video_packet->GetCaptureDTSTimeStampNS() >
         last_capture_dts_timestamp_);
  last_capture_dts_timestamp_ = video_packet->GetCaptureDTSTimeStampNS();
  latest_encoded_video_dts_ns_ =
      last_capture_dts_timestamp_ - begin_video_frame_timestamp_ns_;
}

bool VideoEncodeProcessor::Start() {
  if (IsRunning()) {
    LOG_TAG(WARNING) << "Encode processor is not running.";
    return true;
  }

  ResetStatus();

  if (!pts_generator_ && config_) {
    auto time_base = config_->GetTimeBase();
    if (config_->GetAdaptiveTimeBase()) {
      time_base = *config_->GetAdaptiveTimeBase();
    }
    pts_generator_ = std::make_unique<VideoEncodePtsGenerator>(
        config_->GetEncoderName(), time_base);
  }

  LOG_TAG(INFO) << "stream phase Add video encode to video model[" << sink_id_
                << "] [" << GetId() << "]";
  // observer the output of video model
  DCHECK_NE(sink_id_, -1);
  VideoControllerProxy::Call(FROM_HERE,
                             &VideoController::AddModelOutputObserver, sink_id_,
                             shared_from_this());

  is_running_ = true;

  if (auto dc = com::GetDataCenter(); dc) {
    if (dc->GetCaeConfig().enable) {
      bool no_use_cae = config_->GetAdaptiveOutputHeight() ||
                        config_->GetAdaptiveOutputWidth() ||
                        config_->GetAdaptiveTimeBase();
      if (encoder_ && !encoder_optimizer_) {
        encoder_optimizer_ = std::make_unique<EncoderOptimizer>(
            this, sink_id_, encoder_->GetRequestedPixelFormat(),
            encoder_->GetInputType(), encoder_->GetName(), !no_use_cae);
        if (encoder_optimizer_ && config_) {
          encoder_optimizer_->Start(
              config_->GetOutputWidth(), config_->GetOutputHeight(),
              config_->GetBitRateKbps(), config_->GetTimeBase(), sink_id_);
        }
      }
    }
  }

  LOG_TAG(INFO) << "Processor started.";
  return true;
}

bool VideoEncodeProcessor::Stop() {
  if (!IsRunning()) {
    LOG_TAG(WARNING)
        << "There's no need to stop the processor because it's not running.";
    return true;
  }
  is_running_ = false;

  LOG_TAG(INFO) << "Remove video encode to video model[" << sink_id_ << "] ["
                << GetId() << "]";
  DCHECK_NE(sink_id_, -1);
  VideoControllerProxy::Call(FROM_HERE,
                             &VideoController::RemoveModelOutputObserver,
                             sink_id_, shared_from_this());

  PostFlushTaskToEncodeThread();
  if (encoder_optimizer_) {
    encoder_optimizer_->Stop(sink_id_);
    encoder_optimizer_.reset();
  }

  LOG_TAG(INFO) << "Processor stoped.";
  return true;
}

void VideoEncodeProcessor::FlushEncoder() {
  DCHECK(encoder_);

#ifdef _DEBUG
  auto extra = GetExtraData();
#endif

  encoder_->Flush();

#ifdef _DEBUG
  auto after_extra = GetExtraData();
  DCHECK(extra == after_extra);
#endif
}

void VideoEncodeProcessor::PostFlushTaskToEncodeThread() {
  auto task_runner = thread_->task_runner();
  task_runner->PostTask(FROM_HERE,
                        base::BindOnce(&VideoEncodeProcessor::FlushEncoder,
                                       base::Unretained(this)));
}

void VideoEncodeProcessor::DispatchVideoFrame() {
  observer_list_.Notify(&VideoEncodeProcessorObserver::OnVideoFrame);
}

void VideoEncodeProcessor::DispatchVideoPacket(
    std::shared_ptr<VideoPacket> video_packet) {
  observer_list_.Notify(&VideoEncodeProcessorObserver::OnVideoPacket,
                        video_packet);
}

void VideoEncodeProcessor::DispatchEncodeError() {
  observer_list_.Notify(&VideoEncodeProcessorObserver::OnEncodeError);
}

void VideoEncodeProcessor::DispatchEncodeEvent(const std::string& event) {
  observer_list_.Notify(&VideoEncodeProcessorObserver::OnEncodeEvent, event);
}

void VideoEncodeProcessor::DispatchVideoEncoderFallback() {
  observer_list_.Notify(&VideoEncodeProcessorObserver::OnVideoEncoderFallback);
}

void VideoEncodeProcessor::OnePendingFrameHasBeenHandled() {
  --pending_count_;
}

void VideoEncodeProcessor::AddOnePendingFrameForHandle() {
  ++pending_count_;
}

bool VideoEncodeProcessor::CheckAudioEncoderStartUP(
    const int64_t video_timestamp_ns) const {
  const auto audio_encoder = audio_encoder_.lock();

  if (!audio_encoder) {
    DCHECK(false);
    return false;
  }

  if (!audio_encoder->GetBeginAudioFrameBufferTs()) {
    LOG_TAG(WARNING) << "stream phase Audio encoder not start up";
    return false;
  }

  if (video_timestamp_ns <= audio_encoder->GetBeginAudioFrameBufferTs()) {
    LOG_TAG(WARNING)
        << "stream phase Video frame too new for audio encoder start up ts";
    return false;
  }

  return true;
}

void VideoEncodeProcessor::ResetStatus() {
  LOG_TAG(INFO) << "stream phase VideoEncodeProcessor ResetStatus.";
  ResetPtsStatusAfterReconfig(0);
  pending_count_ = 0;
  pts_generator_ = nullptr;
  shared_texture_factory_ = nullptr;
  encode_error_handled_ = false;
  pts_overflow_reported_ = false;
  encode_freeze_reported_ = false;
  encoded_timestamp_ms_ = 0;
  gop_checker_.Reset();
  rebuild_count_ = 0;
  reconfig_num_ = 0;
  rebuild_num_ = 0;

  // Reset frame rate statistics
  if (frame_rate_statistics_) {
    frame_rate_statistics_->Reset();
  }
}

void VideoEncodeProcessor::ResetPtsStatusAfterReconfig(int64_t timebase) {
  pre_dts_ = MEDIASDK_INVALID_DTS;
  if (pts_generator_) {
    pts_generator_->Reset();
    pts_generator_->UpdateTimeBase(timebase);
  }
  if (encoder_) {
    encoder_->ResetDtsGenerater();
  }
  begin_video_frame_timestamp_ns_ = 0;
  checked_first_is_i_packet_ = false;
  pending_count_ = 0;
}

std::string VideoEncodeProcessor::BuildPtsOverFlow(uint32_t size) {
  try {
    nlohmann::json json_root;
    json_root["event"] = "pts_overflow";
    json_root["size"] = size;
    json_root["codec_name"] = encoder_ ? encoder_->GetName() : "";
    return json_root.dump();
  } catch (const std::exception& e) {
    LOG(ERROR) << "[VideoEncodeProcessor] BuildPtsOverFlow Failed: "
               << e.what();
  }
  return std::string();
}

std::string VideoEncodeProcessor::BuildFirstKeyFrameCost(uint64_t diff_ms) {
  try {
    nlohmann::json json_root;
    json_root["event"] = "first_key_frame_cost";
    json_root["duration"] = diff_ms;
    json_root["codec_name"] = encoder_ ? encoder_->GetName() : "";
    return json_root.dump();
  } catch (const std::exception& e) {
    LOG(ERROR) << "[VideoEncodeProcessor] BuildFirstKeyFrameCost Failed: "
               << e.what();
  }
  return std::string();
}

void VideoEncodeProcessor::CheckEncodeFreezing() {
  constexpr uint64_t threshold = 10 * 1000;
  if (encoded_timestamp_ms_ <= 0) {
    return;
  }
  if (!encode_freeze_reported_ &&
      low_precision_milli_now() - encoded_timestamp_ms_ >= threshold) {
    encode_freeze_reported_ = true;
    DispatchEncodeEvent(BuildEncodeFreezing());
  }
}

std::string VideoEncodeProcessor::BuildEncodeFreezing() const {
  try {
    nlohmann::json json_root;
    json_root["event"] = "encode_freeze";
    json_root["codec_name"] = encoder_ ? encoder_->GetName() : "";
    return json_root.dump();
  } catch (const std::exception& e) {
    LOG(ERROR) << "[VideoEncodeProcessor] BuildEncodeFreezing Failed: "
               << e.what();
  }
  return std::string();
}

std::shared_ptr<VideoEncoderConfig> VideoEncodeProcessor::BuildFallbackConfig(
    const std::string config) {
  std::shared_ptr<VideoEncoderConfig> fallback_config =
      std::make_shared<VideoEncoderConfig>();
  try {
    if (!fallback_config->FromJsonString(config)) {
      LOG(ERROR) << "[StreamControllerImpl] FallbackVideoEncoder Failed for "
                    "parse json";
      return nullptr;
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "[StreamControllerImpl] FallbackVideoEncoder Failed for "
                  "parse json : "
               << e.what();
    return nullptr;
  }
  fallback_config->SetOutputHeight(config_->GetOutputHeight());
  fallback_config->SetOutputWidth(config_->GetOutputWidth());
  fallback_config->SetTimeBase(config_->GetTimeBase());
  fallback_config->SetAdapterLUID(config_->GetAdapterLUID());
  fallback_config->SetVideoRange(config_->GetVideoRange());
  fallback_config->SetColorSpace(config_->GetColorSpace());
  return fallback_config;
}

bool VideoEncodeProcessor::CheckRebuild() {
  LOG(INFO) << "[VideoEncodeProcessor] CheckRebuild";
  if (!encoder_ || !config_) {
    return false;
  }
  if (rebuild_count_ >= kMaxReBuild) {
    return false;
  }
  rebuild_count_++;
  auto width = config_->GetOutputWidth();
  if (auto adapt_width = config_->GetAdaptiveOutputWidth(); adapt_width) {
    width = *adapt_width;
  }
  auto height = config_->GetOutputHeight();
  if (auto adapt_height = config_->GetAdaptiveOutputHeight(); adapt_height) {
    height = *adapt_height;
  }
  auto fps = config_->GetTimeBase();
  if (auto adapt_fps = config_->GetAdaptiveTimeBase(); adapt_fps) {
    fps = *adapt_fps;
  }
  MSSize size = {(int32_t)width, (int32_t)height};
  auto extra_data = latest_extra_data_;
  latest_extra_data_.reset();

  bool r = encoder_->RebuildEncoder(size, fps);
  if (!r) {
    latest_extra_data_ = extra_data;
    LOG(INFO) << "[VideoEncodeProcessor] CheckRebuild Failed";
    return false;
  }
  OnAdaptiveCompleted(r, nullptr);
  LOG(INFO) << "[VideoEncodeProcessor] CheckRebuild Success";
  return r;
}

std::pair<int, int> VideoEncodeProcessor::PopCurrentFrameRate() {
  if (frame_rate_statistics_) {
    return frame_rate_statistics_->PopCurrentFrameRate();
  }
  return std::make_pair(0, 0);
}

bool VideoEncodeProcessor::FallBackEncoder(const std::string config) {
  if (encoder_fallback_) {
    LOG(ERROR) << "[VideoEncodeProcessor] FallBackEncoder fallback is "
                  "already running";
    try {
      nlohmann::json json_root;
      json_root["event"] = "encoder_fallback";
      json_root["success"] = false;
      json_root["reason"] = "is already running";
      DispatchEncodeEvent(json_root.dump());
    } catch (const std::exception& e) {
      LOG(ERROR) << e.what();
    }
    return false;
  }
  auto fallback_config = BuildFallbackConfig(config);
  if (!fallback_config) {
    LOG(ERROR)
        << "[VideoEncodeProcessor] FallBackEncoder fallback_config is null";
    try {
      nlohmann::json json_root;
      json_root["event"] = "encoder_fallback";
      json_root["success"] = false;
      json_root["reason"] = "fallback config is null";
      DispatchEncodeEvent(json_root.dump());
    } catch (const std::exception& e) {
      LOG(ERROR) << e.what();
    }
    return false;
  }
  if (HasRecordObserver()) {
    LOG(ERROR) << "[VideoEncodeProcessor] FallBackEncoder cannot fallback";
    try {
      nlohmann::json json_root;
      json_root["event"] = "encoder_fallback";
      json_root["success"] = false;
      json_root["reason"] = "cannot fallback";
      DispatchEncodeEvent(json_root.dump());
    } catch (const std::exception& e) {
      LOG(ERROR) << e.what();
    }
    return false;
  }

  encoder_fallback_ = std::make_unique<VideoEncoderFallback>(
      shared_from_this(), shared_from_this());

  auto task_runner = thread_->task_runner();
  task_runner->PostTask(
      FROM_HERE,
      base::BindOnce(
          [](const VideoEncodeProcessor* processor,
             const std::shared_ptr<VideoEncoderConfig>& fallback_config) {
            if (processor->encoder_fallback_) {
              processor->encoder_fallback_->Start(fallback_config);
            }
          },
          base::Unretained(this), fallback_config));
  return true;
}

void VideoEncodeProcessor::OnFallbackResult(
    std::shared_ptr<VideoEncoderSource> encoder,
    const std::shared_ptr<VideoEncoderConfig>& config) {
  if (!encoder) {
    LOG(ERROR) << "[VideoEncodeProcessor] OnFallbackResult encoder is null";
    try {
      nlohmann::json json_root;
      json_root["event"] = "encoder_fallback";
      json_root["success"] = false;
      json_root["reason"] = "fall back failed";
      DispatchEncodeEvent(json_root.dump());
    } catch (const std::exception& e) {
      LOG(ERROR) << e.what();
    }
    return;
  }
  auto bitrate = encoder_->CurrentTargetBitrate();
  {
    std::unique_lock lck(encoder_lock_);
    encoder_ = encoder;
  }
  smoothing_.OnPacketCaptureDtsTimeStampNSBeforeReconfig(
      last_capture_dts_timestamp_);
  config_ = config;
  if (input_adaptive_) {
    VideoEncodeAdaptiveConfig config{config_->GetOutputWidth(),
                                     config_->GetOutputHeight(),
                                     config_->GetTimeBase()};
    input_adaptive_->UpdateConfig(config);
  }
  if (config) {
    if (config->GetBitRateKbps() != bitrate) {
      ReconfigBitrateInternal(bitrate);
    }
  }
  if (encoder_optimizer_ && encoder_) {
    encoder_optimizer_->ResetCae(sink_id_);
  }
  ResetStatus();
  latest_extra_data_ = encoder_fallback_->GetExtraData();
  encoder_fallback_.reset();
  if (!pts_generator_) {
    pts_generator_ = std::make_unique<VideoEncodePtsGenerator>(
        config_->GetEncoderName(), config_->GetTimeBase());
  }

  DispatchVideoEncoderFallback();

  try {
    nlohmann::json json_root;
    json_root["event"] = "encoder_fallback";
    json_root["success"] = true;
    json_root["reason"] = config_->ToString();
    DispatchEncodeEvent(json_root.dump());
  } catch (const std::exception& e) {
    LOG(ERROR) << e.what();
  }
}

}  // namespace mediasdk