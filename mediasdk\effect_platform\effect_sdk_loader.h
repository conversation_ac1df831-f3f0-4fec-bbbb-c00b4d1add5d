#pragma once

#include "base/files/file_path.h"
#include "base/native_library.h"
#include "mediasdk/public/effect_platform/effect_bef_api.h"

namespace angle {
class Library;
}

namespace mediasdk::ep {

class EffectSdkLoader {
 public:
  static EffectSdkLoader* GetInstance();
  bool Load();
  void Clear();
  bool IsLoaded();

  // used internal
  void* GetEGLProc(const char* symbol);
  void* GetGLESV2Proc(const char* symbol);

  std::string GetBEFVersion() const;
  std::string GetBEFCommit() const;
  std::string GetBEFVersionFull() const;

 private:
  EffectSdkLoader() = default;
  bool LoadEffect(const base::FilePath& file_path);
  bool LoadEGL(const base::FilePath& file_path);
  bool LoadGLESv2(const base::FilePath& file_path);

 private:
  base::NativeLibrary effect_library_ = nullptr;
  angle::Library* egl_library_ = nullptr;
  angle::Library* glesv2_library_ = nullptr;
  bool effect_load_success_ = false;
  bool egl_load_success_ = false;
  bool glesv2_load_success_ = false;
};

}  // namespace mediasdk::ep
