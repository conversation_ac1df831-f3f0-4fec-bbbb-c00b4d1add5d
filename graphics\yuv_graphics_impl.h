#pragma once
#include <array>
#include "graphics.h"
#include "yuv_graphics_shader.h"
#include "yuv_to_bgra_graphics.h"

namespace graphics {

class YUVToBGRAGraphicsImpl : public YUVToBGRAGraphics {
 public:
  YUVToBGRAGraphicsImpl(Device& ins);

 public:
  bool ConvertMemoryToBGRAPrepare(const TextureFrame& frame) override;
  bool ConvertMemoryToBGRADraw() override;
  bool ConvertTextureToBGRA(std::shared_ptr<Texture>,
                            mediasdk::ColorSpace cs,
                            mediasdk::VideoRange cr) override;
  std::shared_ptr<Texture> GetOutputTexture() override;
  void Destroy() override;

  ~YUVToBGRAGraphicsImpl() override;

 private:
  bool TryCreateResource(const TextureFrame& frame,
                         const VIDEO_CONVERT_FORMAT& info);
  bool DrawNV12ToTexture(const Texture& texture,
                         mediasdk::ColorSpace cs,
                         mediasdk::VideoRange cr,

                         const VIDEO_CONVERT_FORMAT& info);
  bool DrawFrameToTexture(const TextureFrame& frame,
                          const VIDEO_CONVERT_FORMAT& info);
  bool DrawYUVTextureToBGRA(const TextureFrame& frame,
                            const VIDEO_CONVERT_FORMAT& info);
  bool DrawNV12TextureToBGRA(const Texture& texture,
                             mediasdk::ColorSpace cs,
                             mediasdk::VideoRange cr,
                             const VIDEO_CONVERT_FORMAT& info);

  bool MapYUVToTexture(const TextureFrame& frame,
                       const VIDEO_CONVERT_FORMAT& info);
  bool TryCreatePSBuffer();
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice();
  Microsoft::WRL::ComPtr<ID3D11DeviceContext> GetContext();

 private:
  std::array<std::shared_ptr<Texture>, kMaxVideoPlanes> tex_for_map_;
  std::shared_ptr<Graphics> graphics_;
  YUVGraphicsShader::PS_BUFFER const_buffer_ps_ = {};
  YUVGraphicsShader::VS_BUFFER const_buffer_v_ = {};
  Microsoft::WRL::ComPtr<ID3D11Buffer> vs_buffer_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> ps_buffer_;
  TextureFrame pre_frame_ = {};
  Device& device_;
  bool new_ = false;
};

}  // namespace graphics
