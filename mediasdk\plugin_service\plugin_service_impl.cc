#include "plugin_service_impl.h"

#include "mediasdk/debug_helper.h"
#include "mediasdk/mediasdk_thread.h"
#include "mediasdk/plugin_service/audio_encoder_source_factory.h"
#include "mediasdk/plugin_service/audio_filter_factory.h"
#include "mediasdk/plugin_service/audio_input_source_factory_impl.h"
#include "mediasdk/plugin_service/stream_service_source_factory.h"
#include "mediasdk/plugin_service/video_encoder_source_factory.h"
#include "mediasdk/plugin_service/visual_filter_factory.h"
#include "mediasdk/plugin_service/visual_source_factory.h"
#include "mediasdk/public/mediasdk_trace_event.h"
#include "mediasdk/public/plugin/audio_filter.h"
#include "mediasdk/public/plugin/stream_service_proxy.h"
#include "mediasdk/public/plugin/visual_filter.h"
#include "mediasdk/public/plugin/visual_source.h"

namespace mediasdk {

PluginServiceImpl::PluginServiceImpl() = default;

PluginServiceImpl::~PluginServiceImpl() = default;

void PluginServiceImpl::ReportLoadEvent() {
  PostToPlugin(FROM_HERE, base::BindOnce(
                              [](base::WeakPtr<PluginServiceImpl> service) {
                                if (service && service->plugin_manager_) {
                                  service->plugin_manager_->ReportLoadEvent();
                                }
                              },
                              base::AsWeakPtr(this)));
}

bool PluginServiceImpl::Initialize() {
  plugin_manager_ = std::make_unique<PluginManager>();

  // PostToPlugin is designed to avoid the impact of plugin loading on
  // initialization speed
  LOG(INFO) << "[PluginServiceImpl] begin to initialize.";
  PostToPlugin(FROM_HERE, base::BindOnce(
                              [](base::WeakPtr<PluginServiceImpl> service) {
                                if (service && service->plugin_manager_) {
                                  service->plugin_manager_->Load();
                                }
                              },
                              base::AsWeakPtr(this)));
  LOG(INFO) << "[PluginServiceImpl] initialized.";
  return true;
}

void PluginServiceImpl::Uninitialize() {
  LOG(INFO) << "[PluginServiceImpl] Begin Uninitialize.";
  if (plugin_manager_) {
    plugin_manager_->UnLoadAll();
    plugin_manager_.reset();
  }
  LOG(INFO) << "[PluginServiceImpl] uninitialized.";
}

std::shared_ptr<PluginInfoArray> PluginServiceImpl::EnumSource(
    PluginType type) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (plugin_manager_) {
    return plugin_manager_->EnumSource(type);
  }
  return std::make_shared<PluginInfoArray>();
}

std::shared_ptr<MediaSDKString> PluginServiceImpl::EnumVisualInput(
    const std::string& plugin_name,
    const std::string& json_config) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  auto visual_device_info = std::make_shared<MediaSDKString>();
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetVisualSourceFactory(plugin_name);
    if (fac) {
      *visual_device_info = fac->EnumVideoInput(json_config);
    }
  }
  return visual_device_info;
}

std::shared_ptr<MediaSDKString> PluginServiceImpl::EnumVisualFormat(
    const std::string& plugin_name,
    const std::string& device_id) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  auto visual_formats_info = std::make_shared<MediaSDKString>();
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetVisualSourceFactory(plugin_name);
    if (fac) {
      *visual_formats_info = fac->EnumFormat(device_id, kEnumFormatTypeVideo);
    }
  }
  return visual_formats_info;
}

std::shared_ptr<MediaSDKString> PluginServiceImpl::EnumAudioFormat(
    const std::string& plugin_name,
    const std::string& device_id) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  auto visual_formats_info = std::make_shared<MediaSDKString>();
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetVisualSourceFactory(plugin_name);
    if (fac) {
      *visual_formats_info = fac->EnumFormat(device_id, kEnumFormatTypeAudio);
    }
  }
  return visual_formats_info;
}

std::shared_ptr<VisualSource> PluginServiceImpl::CreateVisualSource(
    std::shared_ptr<VisualProxy> proxy,
    const std::string& plugin_name,
    const std::string& json_params) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  TRACE_ACTION_DURATION(trace::TypeToString(
      trace::TASK_COST_TYPE::CreateVisual_CreateInputSource));

  std::shared_ptr<VisualSource> source;
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetVisualSourceFactory(plugin_name);
    if (fac) {
      source = fac->CreateSource(proxy.get(), json_params);
    }
  }
  return source;
}

bool PluginServiceImpl::ReopenVisual(std::shared_ptr<VisualSource> source,
                                     const std::string& json_params) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (source) {
    return source->Reopen(json_params.c_str());
  }
  return false;
}

bool PluginServiceImpl::PauseVisualCapture(
    std::shared_ptr<VisualSource> source) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (source) {
    return source->Pause();
  }
  return false;
}

ResultBoolBool PluginServiceImpl::IsVisualCapturePause(
    std::shared_ptr<VisualSource> source) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (source) {
    return {true, source->IsPaused()};
  }
  return {false, false};
}

bool PluginServiceImpl::ContinueVisualCapture(
    std::shared_ptr<VisualSource> source) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (source) {
    return source->Continue();
  }
  return false;
}

void PluginServiceImpl::DestroyVisualSource(
    std::shared_ptr<VisualSource> source) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  TRACE_ACTION_DURATION(
      TypeToString(trace::TASK_COST_TYPE::DestroyVisual_DestroyVideoSource));

  if (plugin_manager_) {
    plugin_manager_->DestroyVisualSource(source);
  }

  DCHECK_EQ(source.use_count(), 1);
}

std::shared_ptr<MediaSDKString> PluginServiceImpl::GetVisualProperty(
    std::shared_ptr<VisualSource> source,
    const std::string& key) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  auto visual_input_property = std::make_shared<MediaSDKString>();
  if (source) {
    if (auto info = source->GetProperty(key.c_str()); !info.empty()) {
      *visual_input_property = std::move(info);
    }
  }
  return visual_input_property;
}

bool PluginServiceImpl::DoVisualAction(std::shared_ptr<VisualSource> source,
                                       const std::string& json) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  bool ret = false;
  if (source) {
    ret = source->Action(json.c_str());
  }
  return ret;
}

bool PluginServiceImpl::SetVisualProperty(std::shared_ptr<VisualSource> source,
                                          const std::string& key,
                                          const std::string& json) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  bool ret = false;
  if (source) {
    ret = source->SetProperty(key.c_str(), json.c_str());
  }
  return ret;
}

void PluginServiceImpl::DestroyAllVisualSource() {
  if (plugin_manager_) {
    plugin_manager_->DestroyAllVisualSource();
  }
}

bool PluginServiceImpl::RegisterExternalVisualSourceFactory(
    const PluginInfo& info,
    std::shared_ptr<VisualSourceFactory> factory) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (plugin_manager_) {
    return plugin_manager_->RegisterExternalVisualSourceFactory(info, factory);
  }
  return false;
}

bool PluginServiceImpl::RegisterExternalAudioInputSourceFactory(
    const PluginInfo& info,
    std::shared_ptr<AudioInputSourceFactory> factory) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (plugin_manager_) {
    return plugin_manager_->RegisterExternalAudioInputSourceFactory(info,
                                                                    factory);
  }
  return false;
}

std::shared_ptr<MediaSDKString> PluginServiceImpl::GetVisualSourceProperty(
    const std::string& plugin_name,
    const std::string& json) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  auto property_info = std::make_shared<MediaSDKString>();

  if (plugin_manager_) {
    auto fac = plugin_manager_->GetVisualSourceFactory(plugin_name);
    if (fac) {
      *property_info = fac->GetVisualSourceProperty(json);
    }
  }
  return property_info;
}

bool PluginServiceImpl::SetVisualSourceProperty(const std::string& plugin_name,
                                                const std::string& key,
                                                const std::string& json) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (plugin_manager_) {
    auto fac = plugin_manager_->GetVisualSourceFactory(plugin_name);
    if (fac) {
      return fac->SetVisualSourceProperty(key, json);
    }
  }
  return false;
}

std::shared_ptr<VisualFilter> PluginServiceImpl::CreateVisualFilter(
    const std::string& plugin_name,
    const std::string& json_params) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  std::shared_ptr<VisualFilter> filter;
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetVisualFilterFactory(plugin_name);
    if (fac) {
      filter = fac->CreateFilter(json_params);
    }
  }
  return filter;
}

std::shared_ptr<MediaSDKString> PluginServiceImpl::GetVisualFilterProperty(
    std::shared_ptr<VisualFilter> filter,
    const std::string& key) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  auto visual_filter_property = std::make_shared<MediaSDKString>();
  if (filter) {
    if (auto info = filter->GetProperty(key.c_str()); !info.empty()) {
      *visual_filter_property = std::move(info);
    }
  }
  return visual_filter_property;
}

bool PluginServiceImpl::SetVisualFilterProperty(
    std::shared_ptr<VisualFilter> filter,
    const std::string& key,
    const std::string& json) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  bool ret = false;
  if (filter) {
    ret = filter->SetProperty(key.c_str(), json.c_str());
  }
  return ret;
}

std::shared_ptr<MediaSDKString> PluginServiceImpl::VisualFilterAction(
    std::shared_ptr<VisualFilter> filter,
    const std::string& action,
    const std::string& param) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  auto action_ret = std::make_shared<MediaSDKString>();
  if (filter) {
    *action_ret = filter->Action(action.c_str(), param.c_str());
  }
  return action_ret;
}

std::shared_ptr<MediaSDKString> PluginServiceImpl::EnumAudioInput(
    const std::string& plugin_name) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  auto audio_device_info = std::make_shared<MediaSDKString>();
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetAudioInputSourceFactory(plugin_name);
    if (fac) {
      *audio_device_info = fac->EnumAudioInput();
    }
  }
  return audio_device_info;
}

std::shared_ptr<MediaSDKString> PluginServiceImpl::GetDefaultCaptureAudio(
    const std::string& plugin_name) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  auto audio_device_info = std::make_shared<MediaSDKString>();
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetAudioInputSourceFactory(plugin_name);
    if (fac) {
      *audio_device_info = fac->GetDefaultAudioInput();
    }
  }
  return audio_device_info;
}

std::shared_ptr<MediaSDKString> PluginServiceImpl::GetDefaultRenderAudio(
    const std::string& plugin_name) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  auto audio_device_info = std::make_shared<MediaSDKString>();
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetAudioInputSourceFactory(plugin_name);
    if (fac) {
      *audio_device_info = fac->GetDefaultAudioOutput();
    }
  }
  return audio_device_info;
}

std::shared_ptr<MediaSDKString> PluginServiceImpl::EnumCaptureAudio(
    const std::string& plugin_name) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  TRACE_ACTION_DURATION(
      trace::TypeToString(trace::TASK_COST_TYPE::kEnumCaptureAudios));

  auto audio_device_info = std::make_shared<MediaSDKString>();
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetAudioInputSourceFactory(plugin_name);
    if (fac) {
      *audio_device_info = fac->EnumCaptureAudio();
    }
  }
  return audio_device_info;
}

std::shared_ptr<MediaSDKString> PluginServiceImpl::EnumRenderAudio(
    const std::string& plugin_name) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  TRACE_ACTION_DURATION(
      trace::TypeToString(trace::TASK_COST_TYPE::kEnumRenderAudios));

  auto audio_device_info = std::make_shared<MediaSDKString>();
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetAudioInputSourceFactory(plugin_name);
    if (fac) {
      *audio_device_info = fac->EnumRenderAudio();
    }
  }
  return audio_device_info;
}

std::shared_ptr<AudioInputSource> PluginServiceImpl::CreateAudioInputSource(
    std::shared_ptr<AudioInputProxy> proxy,
    const std::string& plugin_name,
    const std::string& json_params) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  TRACE_ACTION_DURATION(trace::TypeToString(
      trace::TASK_COST_TYPE::CreateAudioInput_CreateInputSource));

  std::shared_ptr<AudioInputSource> source;
  if (plugin_manager_) {
    if (const auto fac =
            plugin_manager_->GetAudioInputSourceFactory(plugin_name)) {
      source = fac->CreateSource(std::move(proxy), json_params);
    }
  }
  return source;
}

void PluginServiceImpl::DestroyAudioInputSource(
    std::shared_ptr<AudioInputSource> source) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  TRACE_ACTION_DURATION(trace::TypeToString(
      trace::TASK_COST_TYPE::DestroyAudioInput_DestroyAudioSource));

  if (plugin_manager_) {
    plugin_manager_->DestroyAudioInputSource(source);
  }
  if (source) {
    LOG(INFO) << "Destroy AudioInputSource[" << source->GetAudioSourceName()
              << "]";
  }

  DCHECK_EQ(source.use_count(), 1);
}

void PluginServiceImpl::DestroyAudioFilter(
    std::vector<std::shared_ptr<AudioFilter>> filters) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);
  if (plugin_manager_) {
    for (const auto& filter : filters) {
      plugin_manager_->DestroyAudioFilter(filter);
    }
  }
}

void PluginServiceImpl::DestroyAllAudioInputSource() {
  if (plugin_manager_) {
    plugin_manager_->DestroyAllAudioInputSource();
  }
}

void PluginServiceImpl::DestroyAllAudioFilter() {
  if (plugin_manager_) {
    plugin_manager_->DestroyAllAudioFilter();
  }
}

std::shared_ptr<AudioFilter> PluginServiceImpl::CreateAudioFilter(
    const std::string& plugin_name,
    const std::string& json_params) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (plugin_manager_) {
    if (const auto fac = plugin_manager_->GetAudioFilterFactory(plugin_name)) {
      return fac->CreateFilter(json_params);
    }
  }
  return nullptr;
}

bool PluginServiceImpl::InitAudioFilter(std::shared_ptr<AudioFilter> filter,
                                        const AudioFormat& audio_format,
                                        AudioFilterProxy* proxy) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);
  if (!filter) {
    return false;
  }
  return filter->Init(audio_format, proxy);
}

void PluginServiceImpl::UninitAudioFilter(std::shared_ptr<AudioFilter> filter) {
  if (filter) {
    filter->Uninit();
  }
}

bool PluginServiceImpl::SetAudioFilterEnable(
    std::shared_ptr<AudioFilter> filter,
    const bool enable) {
  if (filter) {
    filter->SetEnable(enable);
    return true;
  }
  return false;
}

ResultBoolBool PluginServiceImpl::IsAudioFilterEnable(
    std::shared_ptr<AudioFilter> filter) {
  if (filter) {
    return {true, filter->IsEnable()};
  }
  return {false, false};
}

ResultBoolString PluginServiceImpl::GetAudioFilterProperty(
    std::shared_ptr<AudioFilter> filter,
    const std::string& key) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);
  if (filter) {
    return {true, filter->GetProperty(key.c_str())};
  }
  return {false, {}};
}

bool PluginServiceImpl::SetAudioFilterProperty(
    std::shared_ptr<AudioFilter> filter,
    const std::string& key,
    const std::string& json) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);
  if (filter) {
    return filter->SetProperty(key.c_str(), json.c_str());
  }
  return false;
}

ResultBoolString PluginServiceImpl::AudioFilterAction(
    std::shared_ptr<AudioFilter> filter,
    const std::string& action,
    const std::string& param) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);
  if (filter) {
    return {true, filter->Action(action.c_str(), param.c_str())};
  }
  return {false, {}};
}

std::shared_ptr<StreamServiceSource>
PluginServiceImpl::CreateStreamServiceSource(
    std::shared_ptr<StreamServiceProxy> proxy,
    const std::string& plugin_name,
    const std::string& json_params) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  std::shared_ptr<StreamServiceSource> source;
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetServiceSourceFactory(plugin_name);
    if (fac) {
      source = fac->CreateSource(proxy, json_params);
    }
  }
  return source;
}

void PluginServiceImpl::DestroyStreamServiceSource(
    std::shared_ptr<StreamServiceSource> source) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (plugin_manager_) {
    plugin_manager_->DestroyStreamServiceSource(source);
  }

  DCHECK_EQ(source.use_count(), 1);
}

void PluginServiceImpl::DestroyAllStreamServiceSource() {
  if (plugin_manager_) {
    plugin_manager_->DestroyAllStreamServiceSource();
  }
}

std::shared_ptr<VideoEncoderSource> PluginServiceImpl::CreateVideoEncoderSource(
    std::shared_ptr<VideoEncoderProxy> proxy,
    const std::string& plugin_name,
    const std::string& json_params) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  std::shared_ptr<VideoEncoderSource> source;
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetVideoEncoderSourceFactory(plugin_name);
    if (fac) {
      source = fac->CreateSource(proxy.get(), json_params);
    }
  }
  return source;
}

bool PluginServiceImpl::TestEncoderSessionCountSupported(
    const std::string& plugin_name,
    uint32_t count) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (plugin_manager_) {
    auto fac = plugin_manager_->GetVideoEncoderSourceFactory(plugin_name);
    if (fac) {
      return fac->TestEncoderSessionCountSupported(count);
    }
  }
  return false;
}

std::shared_ptr<AudioEncoderSource> PluginServiceImpl::CreateAudioEncoderSource(
    std::shared_ptr<AudioEncoderProxy> proxy,
    const std::string& plugin_name,
    const std::string& json_params) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  std::shared_ptr<AudioEncoderSource> source;
  if (plugin_manager_) {
    auto fac = plugin_manager_->GetAudioEncoderSourceFactory(plugin_name);
    if (fac) {
      source = fac->CreateSource(proxy.get(), json_params);
    }
  }
  return source;
}

}  // namespace mediasdk
