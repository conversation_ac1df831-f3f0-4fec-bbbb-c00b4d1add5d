import json
import os
import utils
import requests
from json import <PERSON><PERSON><PERSON>nco<PERSON>
from subprocess import Popen, PIPE, STDOUT
from upload_tos import upload_tos


class MyEncoder(JSONEncoder):
    def default(self, o):
        return o.__dict__

biz_name_tt = "tt_v2"

class SDKFileCheck:
    # 是否是发版分支
    is_release_br = False

    # 对比重点
    sdk_file_dic = {}

    check_change = False

    check_files = {}

    get_config_url = "https://cloudapi.bytedance.net/faas/services/tt2k4m/invoke/config_file_download"

    # 检查的测试文件后缀
    test_file_suffixs = []

    # 检查的变动文件后缀
    check_file_suffixs = []

    # 单个文件变动阈值: xxKB
    single_size_threshold = ""

    # 压缩包大小变动阈值 xxMB
    package_size_threshold = ""

    # 包大小基准
    package_size_basic = {}

    # 检查文件大小变动
    file_size_details = {}

    # 业务方
    biz_name = ""

    # 比较的版本号
    basic_compare_version = ""

    # 需要检查的签名文件后缀
    check_sign_file_suffixs = []

    # 已经签名的文件
    already_signed_files = []

    need_check_pack_size = False


    now_dir = "\\".join(os.path.abspath(__file__).split("\\")[0:-1])
 
    # 之前版本的完整配置文件数据
    old_config_data = None
 

    def __init__(self, package_branch, is_release):
        SDKFileCheck.is_release_br = is_release
        try:
            resp = requests.get(self.get_config_url)
            resp = requests.get(resp.text)
            data = json.loads(resp.text)
            SDKFileCheck.old_config_data = data
            SDKFileCheck.check_file_suffixs = data.get('check_file_suffixs')
            SDKFileCheck.test_file_suffixs = data.get('test_file_suffix')
            SDKFileCheck.single_size_threshold = data.get('single_size_threshold')
            SDKFileCheck.package_size_threshold = data.get('package_size_threshold')
            SDKFileCheck.package_size_basic = data.get('package_size_basic')
            SDKFileCheck.file_size_details = data.get('file_size_details')
            SDKFileCheck.check_sign_file_suffixs = data.get('check_sign_file_suffixs')
            print("old  version check data:", data)
        except Exception as e:
            print("error!" + str(e))
        print("init SDKFileCheck end")

    @classmethod
    def get_compare_package_details(cls, version: str, package_dir_path: str):
        cls.biz_name = biz_name_tt
        if cls.biz_name:
            cls.basic_compare_version = SDKFileCheck.file_size_details.get(cls.biz_name).get("version")
        test_files = []

        size = cls.get_sdk_files(package_dir_path, test_files)
        size = str(round(size / 1024 / 1024, 2)) + "MB"
        return test_files, size

    @classmethod
    def _get_target_files(cls, filename_set, gpl_path=None):
        res = []
        for item in filename_set:
            for suffix in cls.check_file_suffixs:
                if item.endswith(suffix):
                    if gpl_path:
                        item_path = os.path.join(gpl_path, item)
                        if not os.path.exists(item_path):
                            file_size = str(SDKFileCheck.file_size_details.get(cls.biz_name).get("files").get(item).get("size"))
                            res.append(item + " 对比版本中的该文件的大小为：" + file_size + " KB")
                        else:
                            file_size = str(int(os.path.getsize(item_path) / float(1024)))
                            res.append(item + " 大小为：" + file_size + " KB")
                    else:
                        res.append(item)
        return res

    @classmethod
    def run_file_check(cls,platform,version, package_dir_path: str, new_target_br_name: str):

        print("start file check")
        test_files, dir_size = cls.get_compare_package_details(version, package_dir_path)
        if not dir_size:
            return "检测脚本有误，请检查"
        print("check version:", version, "check biz:", cls.biz_name, "test_files:", test_files, "dir_size:", dir_size)
        new_file_set = SDKFileCheck.sdk_file_dic.keys()
        old_file_dic = SDKFileCheck.file_size_details.get(cls.biz_name).get("files")
        old_file_set = old_file_dic.keys()
        print("new_file_set:",new_file_set)
        print("old_file_set:",old_file_set)
        # 获取老的已签名的文件
        old_already_sign_set = set()
        for item in old_file_set:
            sign_code = old_file_dic.get(item).get("sign_code")
            if sign_code == 2:
                old_already_sign_set.add(item)

        # 新增的目标文件
        add_files = new_file_set - old_file_set
        add_files = cls._get_target_files(add_files, package_dir_path)
        add_files.sort()
        # 删除的目标文件
        del_files = old_file_set - new_file_set
        del_files = cls._get_target_files(del_files, package_dir_path)
        del_files.sort()
        # 文件大小发生变化的文件
        size_check_files = new_file_set & old_file_set
        size_check_files = cls._get_target_files(size_check_files)
        size_check_files.sort()
        size_add_files = cls.check_files.get("size_add")
        if size_add_files is None:
            size_add_files = []
        size_reduce_files = cls.check_files.get("size_reduce")
        if size_reduce_files is None:
            size_reduce_files = []

        single_size_threshold = int(float(cls.single_size_threshold[:-2]))
        for item in size_check_files:
            old_file_size = old_file_dic.get(item).get("size")
            new_file_size = SDKFileCheck.sdk_file_dic.get(item).get("size")
            diff_size_val = new_file_size - old_file_size
            item_path = os.path.join(package_dir_path, item)
            if not os.path.exists(item_path):
                print("file not exists:",item_path)
                continue
            else:
                file_size = str(int(os.path.getsize(item_path) / float(1024)))

            if diff_size_val > single_size_threshold:
                info_str = item + " 大小为：" + file_size + " KB," + "增大了：" + str(diff_size_val) + "KB"
                size_add_files.append(info_str)
                SDKFileCheck.check_files.update({"size_add": size_add_files})

            elif diff_size_val < -single_size_threshold:
                info_str = item + " 大小为：" + file_size + " KB," + "减小了：" + str(-diff_size_val) + "KB"
                size_reduce_files.append(info_str)
                SDKFileCheck.check_files.update({"size_reduce": size_reduce_files})

        # 压缩文件夹
        zip_package_path = os.path.join(os.path.dirname(package_dir_path), new_target_br_name + '.zip')
        print("start zip: %s to %s" % (package_dir_path, zip_package_path))
        if not os.path.exists(zip_package_path):
            # 压缩
            utils.zip_folder(package_dir_path, zip_package_path)
        print("end zip ,and start upload zip")
        tos_download_url, error = upload_tos(zip_package_path)
        print("end upload zip and start size check")
        size_mes,package_size = cls.check_package_size(zip_package_path, version)
        check_size_str = "各个检测项比对的基准包版本号：" + cls.basic_compare_version + "\n本次打包产物文件夹总大小 ：" + dir_size + "," + size_mes + '\n'
        os.remove(zip_package_path)

        res_str = ""
        if cls.check_file_suffixs:
            res_str += "\n检查文件后缀为：%s" % ",".join(cls.check_file_suffixs) + ",单个文件大小变动阈值为：" + cls.single_size_threshold

        if add_files:
            res_str += "\n以下为新包新增的文件：\n"
            if platform == "ALL":
                res_str +=  "\n".join(add_files)
            elif "32" in platform:
                for item in add_files:
                    if "exec32" in item:
                        res_str +=  "\n" + item
            elif "64" in platform:
                for item in add_files:
                    if "exec64" in item:
                        res_str +=  "\n" + item
        if del_files:
            res_str += "\n以下为新包删除的文件：\n"
            if platform == "ALL":
                for item in del_files:
                    if "exec32" in item and cls.biz_name == biz_name_tt:
                        continue
                    else:
                        res_str +=  "\n" + item
            elif "32" in platform:
                for item in del_files:
                    if "exec32" in item and cls.biz_name == biz_name_tt:
                        continue
                    elif "exec32" in item:
                        res_str +=  "\n" + item
            elif "64" in platform:
                for item in del_files:
                    if "exec64" in item:
                        res_str +=  "\n" + item
        size_add_files = cls.check_files.get("size_add")
        size_reduce_files = cls.check_files.get("size_reduce")

        if size_add_files:
            res_str += "\n以下为新包大小增加的文件：\n" + "\n".join(size_add_files)
        if size_reduce_files:
            res_str += "\n以下为新包大小减小的文件：\n" + "\n".join(size_reduce_files)
        if test_files:
            res_str += "\n【文件检查】\n检查文件后缀为：%s的文件是否存在，以下文件存在：\n" % ",".join(cls.test_file_suffixs) + "\n".join(
                test_files)
        sign_str = ""
        if cls.is_release_br:
            # 仅发版分支进行比较
            # 本次的已签名文件列表是否与上次的已签名文件列表相同
            already_signed_files_set = set(cls.already_signed_files)
            change_sign_set1 = already_signed_files_set - old_already_sign_set
            change_sign_set2 = old_already_sign_set - already_signed_files_set
            # 过滤掉 tt 32位的签名检查
            change_sign_list1 = list(change_sign_set1)
            change_sign_list2 = list(change_sign_set2)
            for item in change_sign_list1[::-1]:
                if "exec32" in item and cls.biz_name == biz_name_tt:
                    change_sign_list1.remove(item)
            for item in change_sign_list2[::-1]:
                if "exec32" in item and cls.biz_name == biz_name_tt:
                    change_sign_list2.remove(item)

            if change_sign_list1:
                sign_str += "\n以下文件签名变动：已签名，不需要再次签名：%s" % (",".join(change_sign_list1))
            if change_sign_list2:
                sign_str += "以下文件签名变动：没有签名，需要再次签名：%s" % (",".join(change_sign_list2))
            if sign_str:
                sign_str = "【SDK签名检查】" + sign_str
            else:
                sign_str = "【SDK签名检查】无签名文件发送变化"
        if not sign_str:
            sign_str = "【SDK签名检查】非发版分支不进行签名检查"
        #发版分支更新签名config文件，用于打包对比
        if cls.is_release_br:
            # 打包完成后 更新签名文件
            # sdk 完整的 老的 配置文件数据 cls.old_config_data
            # sdk 本次打包的 各个文件的签名及大小数据 cls.sdk_file_dic

            # 将要更新的 配置文件存储到本地
            check_config_path = os.path.join(cls.now_dir, 'check_config.json')
            if os.path.exists(check_config_path):
                os.remove(check_config_path)

            old_file_size_details = cls.old_config_data.get("file_size_details")
            old_package_size_basic = cls.old_config_data.get("package_size_basic")

            if cls.biz_name == biz_name_tt:
                print("update check config file ...")
                # 更新包大小
                old_package_size_basic.update({cls.biz_name:package_size})
                cls.old_config_data.update({"package_size_basic": old_package_size_basic})

                need_updata_data = {cls.biz_name: {"version": version, "files": cls.sdk_file_dic}}
                old_file_size_details.update(need_updata_data)
                cls.old_config_data.update({"file_size_details": old_file_size_details})
                with open(check_config_path, 'w', encoding='utf-8') as f:
                    json.dump(cls.old_config_data, f)

                # 更新并上传 sdk 最新打包的配置文件
                config_path = SDKFileCheck.upload_config(check_config_path)
                print("last config file url:",config_path)
            else:
                print("other biz ")

        if res_str:
            return check_size_str + sign_str + "\n【单个文件变动检查】" + res_str, tos_download_url, error
        return check_size_str + sign_str, tos_download_url, error

    @classmethod
    def upload_config(cls, config_path):
        request_url = 'https://cloudapi.bytedance.net/faas/services/tt2k4m/invoke/config_file_upload'
        files = {'file': open(config_path, 'rb')}
        resp = requests.post(request_url, files=files)
        print(u"信息上传：%s" % resp.text)
        return resp.text

    @classmethod
    def download_config(cls, download_url):
        try:
            resp = requests.get(download_url)
            return json.loads(resp.text)
        except Exception as e:
            print("error!" + str(e))

    @classmethod
    def check_file_sign(cls, sign_file_path):
        no_need_check_signs = [".proto", ".md"]
        for item in no_need_check_signs:
            if sign_file_path.endswith(item):
                return 0
        check_line = os.popen(os.path.join("\\".join(os.path.abspath(__file__).split("\\")[0:-1]), "check_sign.exe") + " %s" % sign_file_path)
        check_line = check_line.read()
        if "Error" in check_line:
            print(sign_file_path, "No signature required")
            return 0
        if "is not signed" in check_line:
            print(sign_file_path, "No Signature")
            return 1
        if "is signed and the signature was verified" in check_line:
            print(sign_file_path, "Signed and legal")
            return 2
        print(sign_file_path, "other case")
        # 其他情况
        return 3

    @classmethod
    def get_sdk_files(cls, path: str, test_files):
        size = 0
        filenames_list = os.listdir(path)
        for item in filenames_list:
            new_item = os.path.join(path,item)
            if os.path.isdir(new_item):
                size += cls.get_sdk_files(new_item, test_files)
            else:
                if "exec" in new_item:
                    bits_index = new_item.index("exec")
                    # 文件相对路径
                    abs_filepath = new_item[bits_index:]
                    # 检查测试文件
                    for suffix in cls.test_file_suffixs:
                        if item.endswith(suffix):
                            test_files.append(abs_filepath)
                    sign_code = 0
                    if cls.is_release_br:
                        # 是发版分支才校验签名
                        # 返回已签名文件，用于对比是否有变动
                        sign_code = cls.check_file_sign(new_item)
                        if sign_code == 2:
                            cls.already_signed_files.append(abs_filepath)
                    # 计算文件夹总大小
                    size += os.path.getsize(new_item)
                    # 文件大小转换为KB,向下取整
                    file_size = int(os.path.getsize(new_item) / float(1024))
                    SDKFileCheck.sdk_file_dic.update({abs_filepath: {"size": file_size, "sign_code": sign_code}})
        return size

    @classmethod
    def check_package_size(cls, zip_package_path, version):
        package_size = int(os.path.getsize(zip_package_path) / 1024 / 1024)

        package_size_threshold = int(float(cls.package_size_threshold[:-2]))

        package_size_basic = int(float(cls.package_size_basic.get(cls.biz_name)[:-2]))
        mes = '压缩包大小：' + str(package_size) + ' MB'
        if package_size < (package_size_basic - package_size_threshold) or package_size > (
                package_size_basic + package_size_threshold):
            if package_size < package_size_basic:
                mes += ',基准值：%s MB,新包包大小减小 %s MB,' % (package_size_basic, round(package_size_basic - package_size, 0))
            else:
                mes += ',基准值：%s MB,新包包大小增加 %s MB,' % (package_size_basic, round(package_size - package_size_basic, 0))

            mes += '准出结果：大小变动待确认'
            cls.need_check_pack_size = True
        else:
            mes += '准出结果：准出'

        return mes,str(package_size) + 'MB'