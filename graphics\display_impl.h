#pragma once
#include <dxgi1_2.h>
#include "display.h"

namespace graphics {

class DisplayImpl : public Display {
 public:
  static std::shared_ptr<DisplayImpl> CreateDisplay2D(Device& ins,
                                                      HWND hWnd,
                                                      int cx,
                                                      int cy);

  DisplayImpl(Device& ins);

  bool IsReady(uint32_t wait_time = 0) override;

  bool Present() override;

  Graphics& GetGraphics() override;

  void Destroy() override;

  bool Resize(uint32_t cx, uint32_t cy) override;

  virtual ~DisplayImpl() override;

 private:
  bool _OpenWithHWND(HWND hWnd, int cx, int cy);
  bool _CreateTexture();

 protected:
  std::shared_ptr<Graphics> graphics_;
  HWND hWnd_ = NULL;
  DXGI_SWAP_CHAIN_DESC swap_chain_desc_ = {};
  Microsoft::WRL::ComPtr<IDXGISwapChain> swap_chain_;
  Device& device_;
};

}  // namespace graphics
