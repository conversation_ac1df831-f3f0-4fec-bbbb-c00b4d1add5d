#pragma once
#include "bgra_calc_graphics.h"
#include "bgra_calc_shader.h"
#include "device.h"
#include "graphics.h"

namespace graphics {

class BGRACalcGraphicsImpl : public BGRACalcGraphics {
 public:
  BGRACalcGraphicsImpl(Device& ins);

 public:
  bool AlphaMask(std::shared_ptr<Texture> texture,
                 std::shared_ptr<Texture> mask,
                 CalcType type,
                 const XMFLOAT2 texture_trans = {},
                 const XMFLOAT2 texture_mask = {}) override;
  std::shared_ptr<Texture> GetOutputTexture() override;
  ~BGRACalcGraphicsImpl() override;

 private:
  void Destroy();
  bool DoAlphaMaskCalc(const Texture& left, const Texture& right);
  bool TryCreateResource(const Texture& left, bool half);
  bool TryUpdateParam(const Texture& texutre,
                      const XMFLOAT2& texture_move,
                      const XMFLOAT2& mask_move);
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice();
  Microsoft::WRL::ComPtr<ID3D11DeviceContext> GetContext();

 private:
  std::shared_ptr<Graphics> graphics_;
  BGRACalcShader::PS_BUFFER const_buffer_p_ = {};

  Microsoft::WRL::ComPtr<ID3D11Buffer> ps_buffer_;
  Device& device_;
};
}  // namespace graphics
