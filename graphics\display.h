#pragma once

#include "device.h"
#include "graphics.h"

namespace graphics {

class GRAPHICS_EXPORT Display {
 public:
  virtual bool IsReady(uint32_t wait_time = 0) = 0;

  virtual bool Present() = 0;

  virtual Graphics& GetGraphics() = 0;

  virtual bool Resize(uint32_t width, uint32_t height) = 0;

  virtual void Destroy() = 0;

  virtual ~Display() {}
};

enum class DISPLAY_MODE {
  DISPLAY_MODE_BITBLT = 0,
  DISPLAY_MODE_FLIP = 1,
  DISPLAY_MODE_FLIP_WAITABLE = 2,
  DISPLAY_MODE_FLIP_ALPHA = 3,
};

struct GRAPHICS_EXPORT DisplayModeOpt {
  DISPLAY_MODE mode = DISPLAY_MODE::DISPLAY_MODE_BITBLT;
};

GRAPHICS_EXPORT std::shared_ptr<Display> CreateDisplay2D(Device& ins,
                                                         HW<PERSON> hwnd,
                                                         int cx,
                                                         int cy,
                                                         DisplayModeOpt mode);

}  // namespace graphics
