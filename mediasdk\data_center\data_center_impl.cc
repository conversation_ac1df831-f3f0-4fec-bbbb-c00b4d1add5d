#include "data_center_impl.h"

#include <base/process/process_handle.h>
#include "audio/audio_format.h"
#include "base/strings/stringprintf.h"
#include "base/strings/utf_string_conversions.h"
#include "component_proxy.h"
#include "mediasdk/component_center.h"
#include "mediasdk/data_center/profiler.h"
#include "mediasdk/mediasdk_version.h"
#include "mediasdk/notify_center.h"
#include "stream/connection_type.h"
#include "uuid_generater.h"
#include "vqos_data.h"

namespace {
constexpr int kDefaultFps = 60;
// Q: why named "global_config"
// A: Backward compatibility
constexpr char kABKey[] = "global_config";

graphics::AdapterInfo g_current_adapter_info = {};

void InitializeParams() {
  std::thread th([] { net_collector::DnsCollector::GetDnsIp(); });
  th.detach();
}

std::string GenerateUUID(const std::string& default_id) {
  if (auto res = mediasdk::UuidGenerater::GenerateUuid(); res.empty()) {
    return default_id;
  } else {
    return res;
  }
}

}  // namespace

namespace mediasdk {

const char kCameraType[] = "DShowVisualSource";

const char kWindowType[] = "DesktopCaptureVisualWindowSource";

const char kMonitorType[] = "DesktopCaptureVisualScreenSource";

const char kCastType[] = "BytelinkVisualSource";

const char kGameType[] = "GameVisualSource";

DataCenterImpl::DataCenterImpl(const std::string& init_config) {
  LOG(INFO) << "[DataCenterImpl] init config: \n\t" << init_config;
  try {
    raw_config_json_ = nlohmann::json::parse(init_config);
    config_ = raw_config_json_;
  } catch (const std::exception& e) {
    LOG(ERROR) << "[DataCenterImpl] Init Parse Failed: " << e.what();
    DCHECK(false);
  }
  cpu_collector_ = cpu_collector::CpuCollector::CreateCollector();
  gpu_collector_ = gpu_collector::GpuCollector::CreateCollector();
  memory_collector_ = memory_collector::MemoryCollector::CreateCollector();

  meta_fps_ = kDefaultFps;
}

DataCenterImpl::~DataCenterImpl() = default;

bool DataCenterImpl::Initialize() {
  LOG(INFO) << "[DataCenterImpl] begin to initialize.";
  ProfilerConsumer::GetInstance().SetHandler(
      [this](const char* name, const ProfilerDataItem& data) {
        OnProfiler(name, data);
      });
  ProfilerConsumer::GetInstance().SetCostProfilerHandler(
      [this](const char* name, const CostProfilerDataItem& data) {
        OnCostProfiler(name, data);
      });

  if (!graphics::GetFirstAdapterInfo(&g_current_adapter_info)) {
    LOG(ERROR) << "[DataCenterImpl] Failed to get preferred adapter info.";
    return false;
  }
  EnumAdapterInfo(gpu_adapters_);
  InitializeParams();
  LOG(INFO) << "[DataCenterImpl] initialized.";
  return true;
}

void DataCenterImpl::Uninitialize() {
  LOG(INFO) << "[DataCenterImpl] uninitialized";

  ProfilerConsumer::GetInstance().SetHandler(nullptr);
  ProfilerConsumer::GetInstance().SetCostProfilerHandler(nullptr);
}

bool DataCenterImpl::UpdateGlobalConfig(const std::string& json_info) {
  LOG(INFO) << "update global config[" << json_info << "]";
  std::unique_lock lock(config_mutex_);
  try {
    const auto new_config = nlohmann::json::parse(json_info);
    raw_config_json_.merge_patch(new_config);
    config_ = raw_config_json_;
  } catch (const std::exception& e) {
    LOG(ERROR) << "update ab config get error: " << e.what();
    return false;
  }
  return true;
}

bool DataCenterImpl::UpdateABConfig(const std::string& json_info) {
  LOG(INFO) << "update ab config[" << json_info << "]";
  std::unique_lock lock(config_mutex_);
  try {
    const auto new_config = nlohmann::json::parse(json_info);
    nlohmann::json& existing_global_config = raw_config_json_["global_config"];
    existing_global_config.merge_patch(new_config["global_config"]);
    config_ = raw_config_json_;
  } catch (const std::exception& e) {
    LOG(ERROR) << "update ab config get error: " << e.what();
    return false;
  }
  return true;
}

std::string DataCenterImpl::GetVersion() {
  std::shared_lock lock(config_mutex_);
  return config_.version;
}

std::string DataCenterImpl::GetSdkVersion() {
  return MEDIASDK_VERSION;
}

std::string DataCenterImpl::GetParfaitHost() {
  std::shared_lock lock(config_mutex_);
  if (!config_.parfait_host.empty() && config_.parfait_host.back() == '/') {
    config_.parfait_host.pop_back();
  }
  return config_.parfait_host;
}

std::string DataCenterImpl::GetUserId() {
  std::shared_lock lock(config_mutex_);
  return config_.uid;
}

config::LyraxAudioConfig DataCenterImpl::GetLyraxConfig() {
  return config_.lyrax_audio_config;
}

int DataCenterImpl::GetAudioChannel() {
  return mediasdk::kAudioOutputFormat.GetChannel();
}

int DataCenterImpl::GetAudioSampleRate() {
  return mediasdk::kAudioOutputFormat.GetSampleRate();
}

int DataCenterImpl::GetMicAddCount() {
  return 0;
}

int DataCenterImpl::GetMicRemoveCount() {
  return 0;
}

void DataCenterImpl::UpdateRenderFPS(uint32_t fps) {
  meta_fps_ = fps;
}

int DataCenterImpl::GetRenderFPS() {
  return meta_fps_;
}

CpuInfo DataCenterImpl::GetCpuInfo() {
  CpuInfo info{};
  if (!cpu_collector_) {
    return info;
  }
  cpu_collector::CpuHardwareInfo hardware;
  cpu_collector_->CpuInfo(hardware);
  info.name = hardware.name;
  info.clock_speed = hardware.clock_speed;
  info.processor_num = hardware.num;

  info.process_usage = cpu_collector_->ProcessUsage();
  double system_usage = 0.0;
  double cpu_time = 0.0;
  cpu_collector_->SystemUsage(system_usage, cpu_time);
  info.system_usage = system_usage;
  info.system_time = cpu_time;
  return info;
}

bool DataCenterImpl::GetGpuInfo(std::vector<GpuInformation>& infos) {
  if (!gpu_collector_) {
    return false;
  }
  if (gpu_adapters_.empty() && !EnumAdapterInfo(gpu_adapters_)) {
    return false;
  }
  if (gpu_adapters_.empty()) {
    return false;
  }

  const int process_id = base::GetCurrentProcId();
  std::vector<gpu_collector::GpuUsage> process_usage;
  gpu_collector_->ProcessUsage(process_id, process_usage);
  std::vector<gpu_collector::GpuUsage> system_usage;
  gpu_collector_->SystemUsage(system_usage);

  for (const auto& gpu_adapter_info : gpu_adapters_) {
    GpuInformation info;
    info.gpu_name = gpu_adapter_info.model_;
    info.id_high = gpu_adapter_info.luid_high_;
    info.id_low = gpu_adapter_info.luid_low_;
    info.dedicate_mem = gpu_adapter_info.dedicated_memory_;
    info.vendor_id = gpu_adapter_info.vendor_id_;
    info.device_id = gpu_adapter_info.device_id_;
    info.shared_mem = gpu_adapter_info.shared_memory_;
    infos.emplace_back(std::move(info));
  }

  for (const auto& gpu_usage : process_usage) {
    for (auto& info : infos) {
      if (gpu_usage.device_info.id_1 == info.id_low &&
          gpu_usage.device_info.id_2 == info.id_high) {
        info.gpu_3d_usage = gpu_usage.usage_3d;
        info.dedicate_usage = gpu_usage.dedicate_usage;
      }
    }
  }
  for (const auto& gpu_usage : system_usage) {
    for (auto& info : infos) {
      if (gpu_usage.device_info.id_1 == info.id_low &&
          gpu_usage.device_info.id_2 == info.id_high) {
        info.system_gpu_3d_usage = gpu_usage.usage_3d;
        info.total_mem = gpu_usage.dedicate_usage;
      }
    }
  }

  return true;
}

MemoryInfo DataCenterImpl::GetMemoryInfo() {
  MemoryInfo info{};
  memset(&info, 0, sizeof(MemoryInfo));
  if (!memory_collector_) {
    return info;
  }
  memory_collector_->ProcessMemInMB(info.process_memory_size);
  memory_collector_->TotalMemInMB(info.memory_size);
  memory_collector_->TotalMemUsedInMB(info.system_used_memory_size);
  memory_collector_->ProcessPageFaultNum(info.page_fault);
  info.memory_usage = info.memory_size > 0
                          ? (float)info.system_used_memory_size * 100.0f /
                                (float)info.memory_size
                          : 0.0f;
  return info;
}

bool DataCenterImpl::GetDiskInfo(int& free_in_mb, int& total_in_mb) {
  if (!disk_collector_) {
    disk_collector_ = disk_collector::DiskCollector::Create();
  }
  if (!disk_collector_) {
    return false;
  }
  return disk_collector_->GetDiskSpace(free_in_mb, total_in_mb);
}

EncoderStatisticInfo DataCenterImpl::GetEncoderStatisticInfo(
    const std::string& stream_id) {
  EncoderStatisticInfo info = {"", 0.0, 0.0};
  auto start_json = GetStreamStart(stream_id);

  if (!start_json) {
    return info;
  }
  auto sink_id = start_json->sink_id;

  auto pinfo = GetEncoderStatistics(sink_id);
  if (pinfo) {
    info.video_enc_fps = std::round(pinfo->encode_fps);
    info.video_encode_err_fps = pinfo->error_fps;
    info.video_codec_name = pinfo->name;
  }
  return info;
}

StatisticInfo DataCenterImpl::GetStatisticInfo(const std::string& stream_id) {
  StatisticInfo info;
  memset(&info, 0, sizeof(StatisticInfo));
  info.video_codec_name = MediaSDKString{};
  if (const auto statistic = GetStreamStatistics(stream_id)) {
    info.video_send_fps =
        std::min(statistic->send_fps, static_cast<double>(GetRenderFPS()));
    info.video_enc_bitrate = statistic->enc_video_bitrate;
    info.audio_enc_bitrate = statistic->enc_audio_bitrate;
    info.send_bitrate = statistic->send_bitrate;
    info.video_send_bitrate = statistic->send_video_bitrate;
    info.audio_send_bitrate = statistic->send_audio_bitrate;
    info.total_drops = statistic->total_drops;
    info.total_video_packets = statistic->total_video_packets;
    info.total_send_bitrate = statistic->total_send_bitrate;
  }
  if (const auto package_delay = GetStreamSendPackageDelay(stream_id)) {
    info.package_delay_ms = package_delay.value();
  }

  return info;
}

void DataCenterImpl::StartRenderProfiler() {
  ResetProfiler();

  ProfilerConsumer::GetInstance().Calc();

  ReportProfiler();
}

void DataCenterImpl::StartCollectPerformanceMatrics(
    const std::vector<CostThresholdParam>& params) {
  ProfilerConsumer::GetInstance().UpdateCostThreshold(params);
  ProfilerConsumer::GetInstance().CalcCost();

  ReportCostProfiler();
}

void DataCenterImpl::SetRealRenderFPS(const float fps) {
  render_fps_ = fps;
}

float DataCenterImpl::GetRealRenderFPS() {
  return render_fps_;
}

void DataCenterImpl::SetNoReadyFPS(const float fps) {
  std::unique_lock lock(statistics_mutex_);
  no_ready_fps_ = fps;
}

float DataCenterImpl::GetNoReadyFPS() {
  std::shared_lock lock(statistics_mutex_);
  return no_ready_fps_;
}

void DataCenterImpl::SetGpuTaskNum(const int num) {
  std::unique_lock lock(statistics_mutex_);
  gpu_task_ = num;
}

int DataCenterImpl::GetGpuTaskNum() {
  std::shared_lock lock(statistics_mutex_);
  return gpu_task_;
}

void DataCenterImpl::SetStreamAdaptiveGearStrategy(
    const std::string& stream_id,
    const AdaptiveGearStrategy& strategy) {
  std::unique_lock lock(adaptive_mutex_);
  stream_adaptive_map_[stream_id] = strategy;
}

std::optional<AdaptiveGearStrategy> DataCenterImpl::GetAdaptiveGearStrategy(
    const std::string& stream_id) {
  std::shared_lock lock(adaptive_mutex_);
  if (const auto it = stream_adaptive_map_.find(stream_id);
      it == stream_adaptive_map_.end()) {
    return {};
  } else {
    return std::make_optional<AdaptiveGearStrategy>(it->second);
  }
}

void DataCenterImpl::OnAdaptiveSwitchSuccess(const std::string& stream_id) {
  std::unique_lock lock(statistics_mutex_);
  if (stream_info_map_.count(stream_id) <= 0) {
    return;
  }
  if (stream_info_map_[stream_id].adaptive_stat) {
    stream_info_map_[stream_id].adaptive_stat->count++;
    return;
  }
  StreamAdaptiveStatistics stat{};
  stat.count = 1;
  stream_info_map_[stream_id].adaptive_stat = stat;
  return;
}

int DataCenterImpl::GetAdaptiveSwitchSuccessCount(
    const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (stream_info_map_.count(stream_id) <= 0) {
    return 0;
  }
  if (!stream_info_map_[stream_id].adaptive_stat) {
    return 0;
  }
  return stream_info_map_[stream_id].adaptive_stat->count;
}

void DataCenterImpl::OnAdaptiveAbnormalUp(const std::string& stream_id) {
  if (!GetAdaptiveGearStrategy(stream_id)) {
    return;
  }
  std::unique_lock lock(statistics_mutex_);
  if (stream_info_map_.count(stream_id) <= 0) {
    return;
  }
  auto current = low_precision_milli_now();
  if (stream_info_map_[stream_id].adaptive_stat) {
    if (stream_info_map_[stream_id].adaptive_stat->abnormal_timestamp == 0) {
      stream_info_map_[stream_id].adaptive_stat->abnormal_timestamp = current;
    }
    if (stream_info_map_[stream_id].adaptive_stat->abnormal_up_timestamp == 0) {
      stream_info_map_[stream_id].adaptive_stat->abnormal_up_timestamp =
          current;
    }
    return;
  }
  StreamAdaptiveStatistics stat{};
  stat.abnormal_timestamp = current;
  stat.abnormal_up_timestamp = current;
  stream_info_map_[stream_id].adaptive_stat = stat;
  return;
}

void DataCenterImpl::OnAdaptiveAbnormalDown(const std::string& stream_id) {
  if (!GetAdaptiveGearStrategy(stream_id)) {
    return;
  }
  std::unique_lock lock(statistics_mutex_);
  if (stream_info_map_.count(stream_id) <= 0) {
    return;
  }
  auto current = low_precision_milli_now();
  if (stream_info_map_[stream_id].adaptive_stat) {
    if (stream_info_map_[stream_id].adaptive_stat->abnormal_timestamp == 0) {
      stream_info_map_[stream_id].adaptive_stat->abnormal_timestamp = current;
    }
    if (stream_info_map_[stream_id].adaptive_stat->abnormal_down_timestamp ==
        0) {
      stream_info_map_[stream_id].adaptive_stat->abnormal_down_timestamp =
          current;
    }
    return;
  }
  StreamAdaptiveStatistics stat{};
  stat.abnormal_timestamp = current;
  stat.abnormal_down_timestamp = current;
  stream_info_map_[stream_id].adaptive_stat = stat;
  return;
}

void DataCenterImpl::OnAdaptiveNormal(const std::string& stream_id) {
  if (!GetAdaptiveGearStrategy(stream_id)) {
    return;
  }
  std::unique_lock lock(statistics_mutex_);
  if (stream_info_map_.count(stream_id) <= 0) {
    return;
  }
  if (!stream_info_map_[stream_id].adaptive_stat) {
    StreamAdaptiveStatistics stat{};
    stream_info_map_[stream_id].adaptive_stat = stat;
    return;
  }
  auto current = low_precision_milli_now();
  if (stream_info_map_[stream_id].adaptive_stat->abnormal_timestamp > 0) {
    stream_info_map_[stream_id].adaptive_stat->abnormal_duration +=
        current - stream_info_map_[stream_id].adaptive_stat->abnormal_timestamp;
    stream_info_map_[stream_id].adaptive_stat->abnormal_timestamp = 0;
  }
  if (stream_info_map_[stream_id].adaptive_stat->abnormal_down_timestamp > 0) {
    stream_info_map_[stream_id].adaptive_stat->abnormal_down_duration +=
        current -
        stream_info_map_[stream_id].adaptive_stat->abnormal_down_timestamp;
    stream_info_map_[stream_id].adaptive_stat->abnormal_down_timestamp = 0;
  }
  if (stream_info_map_[stream_id].adaptive_stat->abnormal_up_timestamp > 0) {
    stream_info_map_[stream_id].adaptive_stat->abnormal_up_duration +=
        current -
        stream_info_map_[stream_id].adaptive_stat->abnormal_up_timestamp;
    stream_info_map_[stream_id].adaptive_stat->abnormal_up_timestamp = 0;
  }
  return;
}

int64_t DataCenterImpl::GetAdaptiveAbnormalDuration(
    const std::string& stream_id) {
  if (!GetAdaptiveGearStrategy(stream_id)) {
    return 0;
  }
  std::shared_lock lock(statistics_mutex_);
  if (stream_info_map_.count(stream_id) <= 0) {
    return 0;
  }
  if (!stream_info_map_[stream_id].adaptive_stat) {
    return 0;
  }
  return stream_info_map_[stream_id].adaptive_stat->abnormal_duration +
         (low_precision_milli_now() -
          stream_info_map_[stream_id].adaptive_stat->abnormal_timestamp);
}

int64_t DataCenterImpl::GetAdaptiveAbnormalUpDuration(
    const std::string& stream_id) {
  if (!GetAdaptiveGearStrategy(stream_id)) {
    return 0;
  }
  std::shared_lock lock(statistics_mutex_);
  if (stream_info_map_.count(stream_id) <= 0) {
    return 0;
  }
  if (!stream_info_map_[stream_id].adaptive_stat) {
    return 0;
  }
  return stream_info_map_[stream_id].adaptive_stat->abnormal_up_duration +
         (low_precision_milli_now() -
          stream_info_map_[stream_id].adaptive_stat->abnormal_up_timestamp);
}

int64_t DataCenterImpl::GetAdaptiveAbnormalDownDuration(
    const std::string& stream_id) {
  if (!GetAdaptiveGearStrategy(stream_id)) {
    return 0;
  }
  std::shared_lock lock(statistics_mutex_);
  if (stream_info_map_.count(stream_id) <= 0) {
    return 0;
  }
  if (!stream_info_map_[stream_id].adaptive_stat) {
    return 0;
  }
  return stream_info_map_[stream_id].adaptive_stat->abnormal_down_duration +
         (low_precision_milli_now() -
          stream_info_map_[stream_id].adaptive_stat->abnormal_down_timestamp);
}

void DataCenterImpl::AddStreamReconnectCnt(const std::string& stream_id) {
  std::unique_lock lock(statistics_mutex_);
  stream_info_map_[stream_id].reconnect_count++;
}

int32_t DataCenterImpl::GetStreamReconnectCnt(const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return 0;
  } else {
    return it->second.reconnect_count;
  }
}

void DataCenterImpl::SetEncoderStatistics(
    const uint32_t sink_id,
    const EncoderStatistics& encoder_statistics) {
  std::unique_lock lock(statistics_mutex_);
  sink_info_map_[sink_id].encoder_statistics = encoder_statistics;
}

std::optional<EncoderStatistics> DataCenterImpl::GetEncoderStatistics(
    const uint32_t sink_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = sink_info_map_.find(sink_id);
      it == sink_info_map_.end()) {
    return {};
  } else {
    return it->second.encoder_statistics;
  }
}

void DataCenterImpl::SetStreamStatistics(const std::string& stream_id,
                                         const StreamStatistics& statistics) {
  std::unique_lock lock(statistics_mutex_);
  stream_info_map_[stream_id].statistics = statistics;
}

std::optional<StreamStatistics> DataCenterImpl::GetStreamStatistics(
    const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return {};
  } else {
    return it->second.statistics;
  }
}

void DataCenterImpl::ResetStreamStatus(const std::string& stream_id) {
  StreamInfo info{};
  info.push_session_id = GenerateUUID(stream_id);
  info.connect_session_id = GenerateUUID(stream_id);
  std::unique_lock lock(statistics_mutex_);
  stream_info_map_[stream_id] = info;
}

void DataCenterImpl::SetStreamStart(const std::string& stream_id,
                                    const StreamStart& start) {
  std::unique_lock lock(statistics_mutex_);
  stream_info_map_[stream_id].start = start;
}

std::optional<StreamStart> DataCenterImpl::GetStreamStart(
    const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return {};
  } else {
    return it->second.start;
  }
}

std::optional<std::string> DataCenterImpl::GetStreamPushSessionId(
    const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return {};
  } else {
    return std::make_optional(it->second.push_session_id);
  }
}

std::optional<std::string> DataCenterImpl::GetStreamConnectSessionId(
    const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return {};
  } else {
    return std::make_optional(it->second.connect_session_id);
  }
}

void DataCenterImpl::UpdateStreamConnectSessionId(
    const std::string& stream_id) {
  std::unique_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return;
  } else {
    it->second.connect_session_id = GenerateUUID(stream_id);
  }
  return;
}

bool DataCenterImpl::UpdateStreamFirstConnectStartEventTimeStampMS(
    const std::string& stream_id,
    int64_t ms) {
  std::unique_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    stream_info_map_[stream_id].first_connected_timestamp_ms = ms;
    return true;
  } else if (!it->second.first_connected_timestamp_ms) {
    it->second.first_connected_timestamp_ms = ms;
    return true;
  }

  return false;
}

std::optional<int64_t>
DataCenterImpl::GetStreamFirstConnectStartEventTimeStampMS(
    const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return {};
  } else {
    return it->second.first_connected_timestamp_ms;
  }
}

void DataCenterImpl::OnFirstVideoFrame(const std::string& stream_id) {
  std::unique_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return;
  } else {
    it->second.first_video_frame = false;
  }
}

bool DataCenterImpl::GetFirstVideoFrame(const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return false;
  } else {
    return it->second.first_video_frame;
  }
}

void DataCenterImpl::OnFirstAudioFrame(const std::string& stream_id) {
  std::unique_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return;
  } else {
    it->second.first_audio_frame = false;
  }
}

bool DataCenterImpl::GetFirstAudioFrame(const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return false;
  } else {
    return it->second.first_audio_frame;
  }
}

void DataCenterImpl::OnFirstVideoPacket(const std::string& stream_id) {
  std::unique_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return;
  } else {
    it->second.first_video_packet = false;
  }
}

bool DataCenterImpl::GetFirstVideoPacket(const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return false;
  } else {
    return it->second.first_video_packet;
  }
}

void DataCenterImpl::OnFirstAudioPacket(const std::string& stream_id) {
  std::unique_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return;
  } else {
    it->second.first_audio_packet = false;
  }
}

bool DataCenterImpl::GetFirstAudioPacket(const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return false;
  } else {
    return it->second.first_audio_packet;
  }
}

void DataCenterImpl::OnFirstVideoSend(const std::string& stream_id) {
  std::unique_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return;
  } else {
    it->second.first_video_send = false;
  }
}

bool DataCenterImpl::GetFirstVideoSend(const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return false;
  } else {
    return it->second.first_video_send;
  }
}

void DataCenterImpl::OnFirstAudioSend(const std::string& stream_id) {
  std::unique_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return;
  } else {
    it->second.first_audio_send = false;
  }
}

bool DataCenterImpl::GetFirstAudioSend(const std::string& stream_id) {
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return false;
  } else {
    return it->second.first_audio_send;
  }
}

void DataCenterImpl::SetSourceFPS(const SourceFPS& fps) {
  std::unique_lock<std::shared_mutex> lock(statistics_mutex_);
  source_pfs_ = fps;
}

std::optional<SourceFPS> DataCenterImpl::GetSourceFPS() {
  std::shared_lock lock(statistics_mutex_);
  return source_pfs_;
}

void DataCenterImpl::SetVideoEncoderBw(const std::string& stream_id,
                                       const int32_t& v) {
  std::unique_lock lock(statistics_mutex_);
  stream_info_map_[stream_id].encode_bw = v;
}

std::optional<int32_t> DataCenterImpl::GetVideoEncoderBw(
    const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return {};
  } else {
    return it->second.encode_bw;
  }
}

void DataCenterImpl::SetStreamSendDelay(const std::string& stream_id,
                                        const int64_t& delay) {
  std::unique_lock lock(statistics_mutex_);
  stream_info_map_[stream_id].delay = delay;
}

std::optional<int64_t> DataCenterImpl::GetStreamSendDelay(
    const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return {};
  } else {
    return it->second.delay;
  }
}

void DataCenterImpl::AddStreamTransportReconnectDuration(
    const std::string& stream_id,
    const int64_t duration) {
  DCHECK(duration > 0);
  if (duration <= 0) {
    return;
  }
  std::unique_lock lock(statistics_mutex_);
  if (!stream_info_map_[stream_id].transport_reconnect) {
    stream_info_map_[stream_id].transport_reconnect =
        std::make_optional<StreamTransportReconnect>({});
  }
  stream_info_map_[stream_id].transport_reconnect->transport_reconnect_time +=
      duration;
}

void DataCenterImpl::AddStreamTransportReconnectCount(
    const std::string& stream_id) {
  std::unique_lock lock(statistics_mutex_);
  if (!stream_info_map_[stream_id].transport_reconnect) {
    stream_info_map_[stream_id].transport_reconnect =
        std::make_optional<StreamTransportReconnect>({});
  }
  stream_info_map_[stream_id].transport_reconnect->transport_reconnect_count++;
}

void DataCenterImpl::SetStreamTransportReconnect(
    const std::string& stream_id,
    const StreamTransportReconnect& transport_reconnect) {
  std::unique_lock lock(statistics_mutex_);
  stream_info_map_[stream_id].transport_reconnect = transport_reconnect;
}

std::optional<StreamTransportReconnect>
DataCenterImpl::GetStreamTransportReconnect(const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return {};
  } else {
    return it->second.transport_reconnect;
  }
}

void DataCenterImpl::SetStreamSendPackageDelay(const std::string& stream_id,
                                               const int64_t& delay) {
  std::unique_lock lock(statistics_mutex_);
  stream_info_map_[stream_id].package_delay_ms = delay;
}

std::optional<int64_t> DataCenterImpl::GetStreamSendPackageDelay(
    const std::string& stream_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = stream_info_map_.find(stream_id);
      it == stream_info_map_.end()) {
    return {};
  } else {
    return it->second.package_delay_ms;
  }
}

void DataCenterImpl::SetSinkMapAndPresentStatistics(
    const uint32_t& sink_id,
    const SinkMapAndPresentStatistics& sink_map_and_present_statistics) {
  std::unique_lock lock(statistics_mutex_);
  sink_info_map_[sink_id].sink_map_and_present_statistics =
      sink_map_and_present_statistics;
}

std::optional<SinkMapAndPresentStatistics>
DataCenterImpl::GetSinkMapAndPresentStatistics(const uint32_t& sink_id) {
  std::shared_lock lock(statistics_mutex_);
  if (const auto it = sink_info_map_.find(sink_id);
      it == sink_info_map_.end()) {
    return {};
  } else {
    return it->second.sink_map_and_present_statistics;
  }
}

std::optional<RtcStatus> DataCenterImpl::GetRtcStatus() {
  return rtc_status_;
}

void DataCenterImpl::SetRtcStatus(std::optional<RtcStatus> rtc_status) {
  LOG(INFO) << "[DataCenterImpl] SetRtcStatus: "
            << (rtc_status ? "value" : "null");
  rtc_status_ = rtc_status;
}

std::shared_ptr<DeviceInfoArray> DataCenterImpl::GetDeviceInfo() {
  std::vector<GpuInformation> infos;
  GetGpuInfo(infos);
  std::vector<DeviceInfo> result;
  int index = 0;
  for (const auto& it : infos) {
    DeviceInfo device_info{};
    device_info.adapter_name = it.gpu_name;
    device_info.pci_id =
        base::StringPrintf("%x:%x", it.vendor_id, it.device_id);
    device_info.driver_version = it.driver_version;
    device_info.luid =
        base::StringPrintf("0x%08x_0x%08x", it.id_high, it.id_low);
    device_info.dedicated_memory_size = it.dedicate_mem;
    device_info.shared_memory_size = it.shared_mem;
    device_info.system_3d_usage = it.system_gpu_3d_usage;
    device_info.process_3d_usage = it.gpu_3d_usage;
    device_info.current_gpu = index == 0;
    index++;
    result.emplace_back(device_info);
  }

  return std::make_shared<DeviceInfoArray>(result);
}

config::AbrConfig DataCenterImpl::GetAbrConfig() {
  std::shared_lock lock(config_mutex_);
  return config_.global_config.abr;
}

config::TraceConfig DataCenterImpl::GetTraceConfig() {
  std::shared_lock lock(config_mutex_);
  return config_.global_config.trace;
}

config::DllLoadConfig DataCenterImpl::GetDllLoadConfig() {
  std::shared_lock lock(config_mutex_);
  return config_.global_config.dll_load;
}

config::CaeConfig DataCenterImpl::GetCaeConfig() {
  std::shared_lock lock(config_mutex_);
  return config_.global_config.cae;
}

config::TcpConfig DataCenterImpl::GetTcpConfig() {
  std::shared_lock lock(config_mutex_);
  return config_.global_config.tcp;
}

config::CameraConfig DataCenterImpl::GetCameraConfig() {
  std::shared_lock lock(config_mutex_);
  return config_.global_config.camera;
}

config::RTCConfig DataCenterImpl::GetRTCConfig() {
  std::shared_lock lock(config_mutex_);
  return config_.global_config.rtc_config;
}

config::MjpegVideoRangeConfig DataCenterImpl::GetMjpegVideoRangeConfig() {
  std::shared_lock lock(config_mutex_);
  return config_.global_config.mjpeg_video_range;
}

config::QuicSleepConfig DataCenterImpl::GetQuicSleepConfig() {
  std::shared_lock lock(config_mutex_);
  return config_.global_config.quic_sleep;
}

config::QsvDtsConfig DataCenterImpl::GetQsvDtsConfig() {
  std::shared_lock lock(config_mutex_);
  return config_.global_config.qsv_dts;
}

config::PushSessionIdConfig DataCenterImpl::GetPushSessionIdConfig() {
  std::shared_lock lock(config_mutex_);
  return config_.global_config.session_id;
}

VisualFpsStore& DataCenterImpl::GetVisualFpsStore() {
  return visual_fps_store_;
}

AudioPerformanceStore& DataCenterImpl::GetAudioPerformanceStore() {
  return audio_performace_store_;
}

void DataCenterImpl::SetTTNtp(const TTNtp& tt_ntp) {
  LOG(INFO) << "[DataCenter] tt_ntp_ms:" << tt_ntp.tt_ntp_ms
            << ", sdk_ns:" << tt_ntp.sdk_ns << ", loc:" << tt_ntp.local_ms;
  std::unique_lock lock(tt_ntp_mutex_);
  tt_ntp_ = tt_ntp;
}

int64_t DataCenterImpl::ChangeToTTNtpMS(int64_t sdk_ns) {
  std::shared_lock lock(tt_ntp_mutex_);
  if (tt_ntp_) {
    return tt_ntp_->tt_ntp_ms + (sdk_ns - tt_ntp_->sdk_ns) / 1'000'000;
  }
  return 0;
}

int64_t DataCenterImpl::ChangeToLocalMS(int64_t sdk_ns) {
  std::shared_lock lock(tt_ntp_mutex_);
  if (tt_ntp_) {
    return tt_ntp_->local_ms + (sdk_ns - tt_ntp_->sdk_ns) / 1'000'000;
  }
  return 0;
}

bool DataCenterImpl::IsUseNewVersionPushingTimeStamp() {
  static std::once_flag flag;
  static bool use = false;
  std::call_once(flag, [&]() {
    std::shared_lock lock(config_mutex_);
    use = config_.global_config.new_version_pushing_time_stamp.enable;
  });
  return use;
}

std::optional<int64_t> DataCenterImpl::GetLocaltoNtpDiffMS() {
  std::shared_lock lock(tt_ntp_mutex_);
  if (tt_ntp_) {
    return std::optional(tt_ntp_->tt_ntp_ms - tt_ntp_->local_ms);
  }
  return std::nullopt;
}

bool DataCenterImpl::EnableEndtoEndDelay() {
  return true;
}

void DataCenterImpl::RegistEndtoEndDelay(EndtoEndDelay* e2e_delay) {
  if (!e2e_delay) {
    NOTREACHED();
    return;
  }
  std::unique_lock lock(e2e_delay_mutex_);
  e2e_delay_arr_.push_back(e2e_delay);
}

void DataCenterImpl::UnregistEndtoEndDelay(EndtoEndDelay* e2e_delay) {
  std::unique_lock lock(e2e_delay_mutex_);
  auto it =
      std::remove(e2e_delay_arr_.begin(), e2e_delay_arr_.end(), e2e_delay);
  e2e_delay_arr_.erase(it, e2e_delay_arr_.end());
}

void DataCenterImpl::FillEndtoEndDelay(event_tracking_data::PushStream& data) {
  int64_t cur_time = milli_now();

  std::unique_lock lock(e2e_delay_mutex_);

  // 4s
  if (cur_time - last_collect_cost_ms_ > 4000) {
    for (auto& e2e_delay : e2e_delay_arr_) {
      if (!e2e_delay) {
        NOTREACHED();
        continue;
      }
      if (!e2e_delay->FillCostData(last_cost_map_)) {
        return;
      }
    }
    last_collect_cost_ms_ = cur_time;
  }
  data.anchor_video_delay = last_cost_map_;
}

nlohmann::json DataCenterImpl::GetABConfig(const std::string& key) const {
  std::shared_lock lock(config_mutex_);
  const auto ab_key_it = raw_config_json_.find(kABKey);
  if (ab_key_it == raw_config_json_.end()) {
    return {};
  }
  const auto it = ab_key_it->find(key);
  if (it == ab_key_it->end()) {
    return {};
  }
  return *it;
}

graphics::AdapterInfo DataCenterImpl::GetCurrentAdapterInfo() const {
  return g_current_adapter_info;
}

std::vector<graphics::GpuAdapterInfo> DataCenterImpl::GetAdapters() const {
  return gpu_adapters_;
}

void DataCenterImpl::ResetProfiler() {
  std::lock_guard lock(buffered_render_data_mutex_);
  buffered_render_data_ = nlohmann::json();
}

void DataCenterImpl::ReportProfiler() {
  nlohmann::json json_object;
  {
    std::lock_guard lock(buffered_render_data_mutex_);
    json_object = std::move(buffered_render_data_);
  }

  if (const auto trace_config = GetTraceConfig();
      trace_config.report_performance) {
    std::unique_lock lock(tea_buffer_mutex_);
    try {
      tea_buffer_.merge_patch(json_object);
    } catch (const std::exception& e) {
      LOG(ERROR) << "merge tea buffer get error:" << e.what();
    }
  }
  auto nc = com::GetNotifyCenter();
  nc->GlobalEvent()->Notify(FROM_HERE, &MediaSDKGlobalEventObserver::OnTeaEvent,
                            "livesdk_mediasdk_profilter", json_object.dump());
}

void DataCenterImpl::OnProfiler(const char* name,
                                const ProfilerDataItem& data) {
  if (!data.cnt || !name || !name[0]) {
    return;
  }
  auto str =
      nlohmann::json{
          {"min", data.min_ts_us},
          {"max", data.max_ts_us},
          {"average", data.total_us / data.cnt},
      }
          .dump();

  std::lock_guard lock(buffered_render_data_mutex_);
  buffered_render_data_[name] = std::move(str);
}

void DataCenterImpl::ReportCostProfiler() {
  nlohmann::json json_object;
  {
    std::lock_guard lock(buffered_render_data_cost_mutex_);
    json_object = std::move(buffered_render_data_cost_);
  }

  if (const auto trace_config = GetTraceConfig();
      trace_config.report_performance) {
    nlohmann::json tea_buffer;
    {
      std::unique_lock lock(tea_buffer_mutex_);
      try {
        tea_buffer_.merge_patch(json_object);
      } catch (const std::exception& e) {
        LOG(ERROR) << "merge tea buffer get error:" << e.what();
      }
      tea_buffer = std::move(tea_buffer_);
    }

    event_tracking_data::Performance performance;
    performance.preview_fps = GetRealRenderFPS();
    const auto meta_fps = GetRenderFPS();
    performance.render_fps_achieving_rate =
        (meta_fps > performance.preview_fps && performance.preview_fps > 0)
            ? performance.preview_fps / meta_fps
            : 1.0;
    const auto no_ready_fps = GetNoReadyFPS();
    performance.no_ready_fps = no_ready_fps;
    performance.present_ready_fps = performance.preview_fps - no_ready_fps;
    performance.present_fps_achieving_rate =
        performance.preview_fps > 0
            ? (performance.preview_fps - no_ready_fps) / performance.preview_fps
            : 1.0;

    const auto cpu_information = GetCpuInfo();
    performance.cpu_name = cpu_information.name.ToString();
    performance.cpu_usage = cpu_information.process_usage;
    performance.cpu_total = cpu_information.system_usage;

    const auto memory_information = GetMemoryInfo();
    performance.memory = memory_information.process_memory_size;
    performance.total_mem = memory_information.system_used_memory_size;
    performance.page_fault = memory_information.page_fault;

    event_tracking_data::LegacyGpuField::FillField(performance);

    event_tracking_data::VisualFpsField::FillField(performance);
    int free_mb = 0;
    int total_mb = 0;
    if (GetDiskInfo(free_mb, total_mb)) {
      performance.disk_space_left = free_mb;
      performance.disk_space = total_mb;
    }

    VqosData data;
    data.ReportPerformance(performance, tea_buffer);
  }
  auto nc = com::GetNotifyCenter();
  nc->GlobalEvent()->Notify(FROM_HERE, &MediaSDKGlobalEventObserver::OnTeaEvent,
                            "livesdk_mediasdk_profilter_cost",
                            json_object.dump());
}

void DataCenterImpl::OnCostProfiler(const char* name,
                                    const CostProfilerDataItem& data) {
  auto str =
      nlohmann::json{
          {"data", data.cost},
          {"isFrozen", (data.last_begin_time_ms != 0 &&
                        milli_now() - data.last_begin_time_ms > 5000)},
      }
          .dump();

  std::lock_guard lock(buffered_render_data_cost_mutex_);
  buffered_render_data_cost_[name] = std::move(str);
}

}  // namespace mediasdk