#pragma once

#include "lyrax/lyrax_audio.h"
#include "lyrax/lyrax_engine.h"

namespace mediasdk {
class LyraxAudioListener : public lyrax::ILyraxAudioListener {
 public:
  LyraxAudioListener();
  ~LyraxAudioListener() = default;

 void onApiCall(const std::string& api_name, 
                lyrax::LyraxErrorCode error_code, const std::string& msg) override;

 void onEngineInfo(const std::string& msg) override;

 void onDeviceEventInfo(const lyrax::LyraxAudioDeviceEventInfo& info) override;

  void onEchoDetectionResult(float probability) override;
};

}  // namespace mediasdk