#pragma once

#include "audio_controller.h"

namespace mediasdk {
class AudioInputManager;
class AudioMixer;
class AudioPump;

class AudioControllerImpl final : public AudioController {
 public:
  AudioControllerImpl();
  ~AudioControllerImpl() override;

  // Component:
  bool Initialize() override;
  void Uninitialize() override;

  // AudioController:
  void CreateAudioInput(const std::string& id,
                        const CreateAudioParams& params,
                        MSCallbackBool callback) override;

  void CreateLyraxAudioInput(const std::string& id,
                             const CreateAudioParams& params,
                             MSCallbackBool callback) override;

  ResultBoolBool AddAudioInputToTrack(const std::string& id,
                                      uint32_t track) override;

  ResultBoolBool RemoveAudioInputFromTrack(const std::string& id,
                                           uint32_t track_id) override;

  std::shared_ptr<AudioInput> CreateAudioInputWithSource(
      const std::string& id,
      uint32_t track_id,
      std::shared_ptr<AudioInputSource> source) override;

  void DestroyAudioInput(const std::string& id,
                         MSCallbackBool callback) override;

  bool SetAudioInputParams(const std::string& id,
                           const AudioInputParams& param) override;

  void DestroyAudioInputFromVisual(const std::string& id) override;

  void DestroyMultiAudioInputFromVisual(
      const std::vector<std::string>& ids) override;

  void DestroyAllAudioInputFromVisual() override;

  bool SetVolume(const std::string& id, float volume) override;

  ResultBoolFloat GetVolume(const std::string& id) override;

  bool SetMute(const std::string& id, bool mute) override;

  ResultBoolBool GetMute(const std::string& id) override;

  bool SetDeviceMute(const std::string& id, bool mute) override;

  ResultBoolBool GetDeviceMute(const std::string& id) override;

  bool SetBalance(const std::string& id, float balance) override;

  bool SetAudioInputRenderDeviceID(
      const std::string& audio_input_id,
      const std::string& render_device_id) override;

  ResultBoolFloat GetBalance(const std::string& id) override;

  ResultBoolInt32 GetSyncOffset(const std::string& id) override;

  bool SetSyncOffset(const std::string& id, int32_t sync_offset) override;

  ResultBoolUint32 GetMonitorType(const std::string& id) override;

  bool SetMonitorType(const std::string& id, int32_t type) override;

  bool SetMono(const std::string& id, bool mono) override;

  ResultBoolBool GetMono(const std::string& id) override;

  bool SetInterval(const std::string& id, int32_t interval) override;

  MediaSDKString GetFirstAudio() override;

  void DestroyAllAudioInput(MSCallbackBool callback) override;

  // audio filter related APIs
  void CreateAudioFilter(const std::string& audio_filter_id,
                         const std::string& audio_filter_name,
                         const std::string& audio_input_id,
                         const std::string& json_params,
                         MSCallbackBool callback) override;

  void DestroyAudioFilter(const std::string& audio_filter_id,
                          const std::string& audio_input_id,
                          MSCallbackBool callback) override;

  void SetAudioFilterEnable(const std::string& audio_filter_id,
                            const std::string& audio_input_id,
                            bool enable,
                            MSCallbackBool callback) override;

  bool UpdatePCMAudioDatas(const std::string& audio_input_id,
                           const std::string& pcm_audio_datas) override;

  void IsAudioFilterEnable(const std::string& audio_filter_id,
                           const std::string& audio_input_id,
                           MSCallback<ResultBoolBool> callback) override;

  void SetAudioFilterProperty(const std::string& audio_filter_id,
                              const std::string& audio_input_id,
                              const std::string& key,
                              const std::string& value,
                              MSCallbackBool callback) override;

  void GetAudioFilterProperty(const std::string& audio_filter_id,
                              const std::string& audio_input_id,
                              const std::string& key,
                              MSCallback<ResultBoolString> callback) override;

  void AudioFilterAction(const std::string& audio_filter_id,
                         const std::string& audio_input_id,
                         const std::string& action,
                         const std::string& param,
                         MSCallback<ResultBoolString> callback) override;

  void AddMixedAudioObserver(
      uint32_t track_id,
      std::shared_ptr<MixedAudioObserver> observer) override;

  void RemoveMixedAudioObserver(
      uint32_t track_id,
      std::shared_ptr<MixedAudioObserver> observer) override;

  void AddInputAudioObserver(
      std::string audio_input_id,
      std::shared_ptr<AudioInputFrameObserver> observer) override;

  void RemoveInputAudioObserver(
      std::string audio_input_id,
      std::shared_ptr<AudioInputFrameObserver> observer) override;

  MediaSDKString EnumAudioInputsInTrack(uint32_t track_id) override;

  std::string GetRenderTargetDeviceID() override;

  void SetRenderTargetDeviceID(const std::string& id) override;

  bool SetAudioInputReferenceId(const std::string& audio_input_id,
                                const std::string& audio_ref_input_id) override;

  bool SetAudioInputAECOption(const std::string& audio_input_id,
                              const bool enable) override;

  bool SetAudioInputANSOption(const std::string& audio_input_id,
                              const int32_t level) override;

  bool SetAudioInputRawDataOption(const std::string& audio_input_id,
                                  const int32_t mode) override;

  std::shared_ptr<MediaSDKString> GetAudioInputListInfo() override;

  void SetAudioTrackDelayMs(uint32_t track_id, const int64_t ms) override;

  bool EnableAudioInputEchoDetection(const std::string& audio_input_id,
                                     const int32_t interval) override;

  void CreateCustomAudioInput(const std::string& id,
                              uint32_t track_id,
                              hook_api::CustomAudioInputDelegate* delegate,
                              MSCallbackBool callback) override;

 private:
  std::shared_ptr<AudioInputManager> audio_input_manager_;
  std::shared_ptr<AudioPump> audio_pump_;
  std::shared_ptr<AudioMixer> audio_mixer_;

  std::mutex lock_render_target_;
  std::string render_target_audio_dev_id_;
};

}  // namespace mediasdk
