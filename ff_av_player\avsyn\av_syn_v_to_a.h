#pragma once
#include "av_clock.h"

class AVSynClock {
 public:
  void Reset(int32_t serial);
  int64_t GetCurrentClock() const;
  int64_t GetVideoClock() const;
  void UpdateAudioPTS(int32_t serial, int64_t ms_pts);
  void UpdateVideoPTS(int32_t serial, int64_t ms_pts);
  static bool IsVideoTooQuick(int64_t diff);
  static bool IsVideoTooSlow(int64_t diff);
  bool IsAudioTookQuick(int64_t ms_pts, int64_t* diff = nullptr) const;
  bool GetVideoDiff(int64_t cur_pts,
                    int64_t next_pts,
                    int64_t& cur_diff,
                    int64_t* next_diff = nullptr,
                    int64_t* last_render_pts_diff = nullptr,
                    int64_t* render_diff = nullptr) const;

  void Pause();
  void Resume();
  bool IsPause() const;

  void SetStep(bool step);
  bool IsStep() const;

  bool AudioNotReady() const;
  bool VideoNotReady() const;

  void SetNoAudio();
  void SetNoVideo();
  void SetVideoPtsNotValid();

  bool IsNoVideo() const;
  bool IsNoAudio() const;

  void SetSpeed(float speed);
  float GetSpeed() const;

 private:
  AVClock audio_clock_;
  AVClock video_clock_;
  AVClock global_clock_;
  bool no_audio_ = {false};
  bool no_video_ = {false};
  bool video_pts_not_valid_ = false;
  int32_t serial_ = 0;
};