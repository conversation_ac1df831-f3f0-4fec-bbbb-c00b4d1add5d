#pragma once

#include <DirectXMath.h>
#include <map>
#include <mutex>
#include "shader.h"
#include "texture_shader.h"

using namespace DirectX;

namespace graphics {

class ClipMaskShader : public TextureShader {
 public:
  static inline const char* SHADER_ID_STRING = "clip_mask_shader";

  static std::shared_ptr<Shader> CreateClipMaskShader(const char* type) {
    if (strcmp(type, SHADER_ID_STRING) == 0) {
      return std::make_shared<ClipMaskShader>();
    }
    return nullptr;
  }

  static void EnumPluginsFunction(void* param, EnumPluginCallBack fpn) {
    fpn(param, ShaderItem{SHADER_ID_STRING,
                          "shader for draw clip mask texture to graphics",
                          ClipMaskShader::CreateClipMaskShader});
  }

 public:
  bool Init(const std::shared_ptr<Device>&) override;

  void RenderClipMask(Microsoft::WRL::ComPtr<ID3D11Buffer>& vertex_buffer,
                      Microsoft::WRL::ComPtr<ID3D11Buffer>& index_buffer,
                      Microsoft::WRL::ComPtr<ID3D11Buffer>& crop_buffer,
                      Microsoft::WRL::ComPtr<ID3D11Buffer>& matrix_buffer,
                      Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_buffer);
  void Destroy() override;
  ~ClipMaskShader() override;

 private:
  bool DoInit(const std::shared_ptr<Device>& ins);

 private:
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice_();
  ID3D11DeviceContext* GetContext_();

 private:
  bool try_init_ = false;
  bool init_suc_ = false;
  std::shared_ptr<Device> device_;

  Microsoft::WRL::ComPtr<ID3D11VertexShader> vs_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11InputLayout> layout_;
  Microsoft::WRL::ComPtr<ID3D11SamplerState> sampler_;
};

}  // namespace graphics
