#pragma once

#include <base/check.h>
#include <base/debug/stack_trace.h>
#include <base/logging.h>
#include <base/strings/stringprintf.h>

#include "DXSimpleMath.h"
#include "graphics/public/graphics_defines.h"

using namespace DirectX;

namespace graphics {
// https://en.wikipedia.org/wiki/YCbCr#ITU-R_BT.601_conversion

// color space part value range
// y [16~235]
// uv[16~240]

// color space full value range
// y [0~255]
// uv[0~255]

struct value_min_max {
  int32_t min = 0;
  int32_t max = 0;

  int32_t range() const noexcept { return max - min; }
};

constexpr value_min_max range_part[3] = {
    value_min_max{16, 235}, value_min_max{16, 240}, value_min_max{16, 240}};

constexpr value_min_max range_full[3] = {
    value_min_max{0, 255}, value_min_max{0, 255}, value_min_max{0, 255}};

constexpr XMFLOAT4 min_part = XMFLOAT4(range_part[0].min / 255.0f,
                                       range_part[0].min / 255.0f,
                                       range_part[0].min / 255.0f,
                                       0.0f);

constexpr XMFLOAT4 max_part = XMFLOAT4(range_part[1].max / 255.0f,
                                       range_part[1].max / 255.0f,
                                       range_part[1].max / 255.0f,
                                       0.0f);

constexpr XMFLOAT4 min_full = XMFLOAT4(range_full[0].min / 255.0f,
                                       range_full[0].min / 255.0f,
                                       range_full[0].min / 255.0f,
                                       0.0f);

constexpr XMFLOAT4 max_full = XMFLOAT4(range_full[1].max / 255.0f,
                                       range_full[1].max / 255.0f,
                                       range_full[1].max / 255.0f,
                                       0.0f);

inline void PrintMatrix(const XMFLOAT4X4& matrix) {
  LOG(INFO) << base::StringPrintf(
      "\n% f, % f, % f, % f"
      "\n% f, % f, % f, % f"
      "\n% f, % f, % f, % f"
      "\n% f, % f, % f, % f",
      matrix.m[0][0], matrix.m[0][1], matrix.m[0][2], matrix.m[0][3],
      matrix.m[1][0], matrix.m[1][1], matrix.m[1][2], matrix.m[1][3],
      matrix.m[2][0], matrix.m[2][1], matrix.m[2][2], matrix.m[2][3],
      matrix.m[3][0], matrix.m[3][1], matrix.m[3][2], matrix.m[3][3]);
}

inline void BuildMatrixYCbCrToRGB(const float Kb,
                                  const float Kr,
                                  const value_min_max ranges[3],
                                  XMFLOAT4X4& matrix_with_offsets) {
  const float Kg = 1.f - Kb - Kr;
  const float y_range = ranges[0].range();
  const float u_range = ranges[1].range();
  const float v_range = ranges[2].range();

  const float y_scale = 255.f / y_range;
  const float u_scale = 255.f / u_range;
  const float v_scale = 255.f / v_range;

  const DirectX::XMFLOAT3 line_r(y_scale, 0.0F, u_scale * 2.f * (1.0f - Kr));
  const DirectX::XMFLOAT3 line_g(y_scale, u_scale * 2.f * (Kb - 1.0f) * Kb / Kg,
                                 v_scale * 2.f * (Kr - 1.0f) * Kr / Kg);
  const DirectX::XMFLOAT3 line_b(y_scale, u_scale * 2.f * (1.0F - Kb), 0.0f);

  const DirectX::XMFLOAT3 begin_from_zero(-ranges[0].min / 255.0f,
                                          -128 / 255.0f, -128 / 255.0f);

  DirectX::XMFLOAT3 multiple;
  multiple.x =
      XMVectorGetX(XMVector3Dot(XMLoadFloat3((const XMFLOAT3*)&begin_from_zero),
                                XMLoadFloat3((const XMFLOAT3*)&line_r)));
  multiple.y =
      XMVectorGetX(XMVector3Dot(XMLoadFloat3((const XMFLOAT3*)&begin_from_zero),
                                XMLoadFloat3((const XMFLOAT3*)&line_g)));
  multiple.z =
      XMVectorGetX(XMVector3Dot(XMLoadFloat3((const XMFLOAT3*)&begin_from_zero),
                                XMLoadFloat3((const XMFLOAT3*)&line_b)));

  matrix_with_offsets.m[0][0] = line_r.x;
  matrix_with_offsets.m[0][1] = line_r.y;
  matrix_with_offsets.m[0][2] = line_r.z;
  matrix_with_offsets.m[0][3] = multiple.x;

  matrix_with_offsets.m[1][0] = line_g.x;
  matrix_with_offsets.m[1][1] = line_g.y;
  matrix_with_offsets.m[1][2] = line_g.z;
  matrix_with_offsets.m[1][3] = multiple.y;

  matrix_with_offsets.m[2][0] = line_b.x;
  matrix_with_offsets.m[2][1] = line_b.y;
  matrix_with_offsets.m[2][2] = line_b.z;
  matrix_with_offsets.m[2][3] = multiple.z;

  matrix_with_offsets.m[3][0] = 0.0f;
  matrix_with_offsets.m[3][1] = 0.0f;
  matrix_with_offsets.m[3][2] = 0.0f;
  matrix_with_offsets.m[3][3] = 1.0f;
  PrintMatrix(matrix_with_offsets);
}

inline void BuildPlanesBGRAToYUV(const mediasdk::ColorSpace cs,
                                 const mediasdk::VideoRange range,
                                 XMFLOAT4& Y,
                                 XMFLOAT4& U,
                                 XMFLOAT4& V) {
  XMFLOAT4X4 matrix;
  switch (cs) {
    case mediasdk::ColorSpace::kColorSpaceBT601: {
      switch (range) {
        case mediasdk::VideoRange::kVideoRangePartial:
          BuildMatrixYCbCrToRGB(0.114f, 0.299f, range_part, matrix);
          break;
        case mediasdk::VideoRange::kVideoRangeFull:
          BuildMatrixYCbCrToRGB(0.114f, 0.299f, range_full, matrix);
          break;
      }
    } break;
    case mediasdk::ColorSpace::kColorSpaceBT709: {
      switch (range) {
        case mediasdk::VideoRange::kVideoRangePartial:
          BuildMatrixYCbCrToRGB(0.0722f, 0.2126f, range_part, matrix);
          break;
        case mediasdk::VideoRange::kVideoRangeFull:
          BuildMatrixYCbCrToRGB(0.0722f, 0.2126f, range_full, matrix);
          break;
      }
    } break;
  }
  // revert
  XMMATRIX m = XMLoadFloat4x4((const XMFLOAT4X4*)&matrix);
  XMVECTOR determinant = XMMatrixDeterminant(m);
  XMStoreFloat4x4(&matrix, XMMatrixInverse(&determinant, m));
  XMFLOAT4 val = (XMFLOAT4)matrix.m[0];
  matrix.m[0][0] = matrix.m[1][0];
  matrix.m[0][1] = matrix.m[1][1];
  matrix.m[0][2] = matrix.m[1][2];
  matrix.m[0][3] = matrix.m[1][3];
  matrix.m[1][0] = val.x;
  matrix.m[1][1] = val.y;
  matrix.m[1][2] = val.z;
  matrix.m[1][3] = val.w;
  Y.x = matrix.m[1][0];
  Y.y = matrix.m[1][1];
  Y.z = matrix.m[1][2];
  Y.w = matrix.m[1][3];

  U.x = matrix.m[0][0];
  U.y = matrix.m[0][1];
  U.z = matrix.m[0][2];
  U.w = matrix.m[0][3];

  V.x = matrix.m[2][0];
  V.y = matrix.m[2][1];
  V.z = matrix.m[2][2];
  V.w = matrix.m[2][3];
}

inline void BuildPlanesYUVToBGRA(const mediasdk::ColorSpace cs,
                                 const mediasdk::VideoRange range,
                                 XMFLOAT4& Y,
                                 XMFLOAT4& U,
                                 XMFLOAT4& V,
                                 XMFLOAT4& min_range,
                                 XMFLOAT4& max_range,
                                 const mediasdk::PixelFormat format) {
  XMFLOAT4X4 matrix = {};
  switch (cs) {
    case mediasdk::ColorSpace::kColorSpaceBT601: {
      switch (range) {
        case mediasdk::VideoRange::kVideoRangePartial:
          BuildMatrixYCbCrToRGB(0.114F, 0.299F, range_part, matrix);
          min_range = min_part;
          max_range = max_part;
          break;
        case mediasdk::VideoRange::kVideoRangeFull:
          BuildMatrixYCbCrToRGB(0.114F, 0.299F, range_full, matrix);
          min_range = min_full;
          max_range = max_full;
          break;
      }
    } break;
    case mediasdk::ColorSpace::kColorSpaceBT709: {
      switch (range) {
        case mediasdk::VideoRange::kVideoRangePartial:
          BuildMatrixYCbCrToRGB(0.0722F, 0.2126F, range_part, matrix);
          min_range = min_part;
          max_range = max_part;
          break;
        case mediasdk::VideoRange::kVideoRangeFull:
          BuildMatrixYCbCrToRGB(0.0722F, 0.2126F, range_full, matrix);
          min_range = min_full;
          max_range = max_full;
          break;
      }
    } break;
    default: {
      DCHECK(false && "not impl");
    }
  }

  Y.x = matrix.m[0][0];
  Y.y = matrix.m[0][1];
  Y.z = matrix.m[0][2];
  Y.w = matrix.m[0][3];

  U.x = matrix.m[1][0];
  U.y = matrix.m[1][1];
  U.z = matrix.m[1][2];
  U.w = matrix.m[1][3];

  V.x = matrix.m[2][0];
  V.y = matrix.m[2][1];
  V.z = matrix.m[2][2];
  V.w = matrix.m[2][3];

  if (graphics::IsRGBFormat(format)) {
    if (range == mediasdk::VideoRange::kVideoRangePartial) {
      max_range.x = 255.0f / range_part[0].range();
      min_range.x = (float)range_part[0].min / (float)range_part[0].range();
    }

    if (range == mediasdk::VideoRange::kVideoRangeFull) {
      max_range.x = 1.0f;
      min_range.x = 0.0f;
    }
  }
}

}  // namespace graphics
