#pragma once

#include <stdint.h>
#include <memory>

#include "audio_input_proxy.h"
#include "mediasdk/public/mediasdk_string.hpp"
#include "mediasdk/public/plugin/plugin_export.h"

namespace mediasdk {

class AudioInputSource {
 public:
  virtual ~AudioInputSource() = default;

  virtual const char* GetAudioSourceName() = 0;

  virtual bool Action(const char* json_params) = 0;

  virtual MediaSDKString GetProperty(const char* key) = 0;

  virtual bool NeedSyn() = 0;

  virtual void SetDeviceVolume(float volume) = 0;

  virtual float GetDeviceVolume() = 0;

  virtual void SetDeviceMute(bool) = 0;

  virtual bool GetDeviceMute() = 0;

  virtual void SetDeviceUsedQPC(bool used) = 0;

  virtual bool GetDeviceUsedQPC() = 0;

  virtual void OnAudioAttached() {}
};

extern "C" PLUGIN_EXPORT AudioInputSource* CreateAudioInputSource(
    AudioInputProxy* proxy,
    const char* json_params);

extern "C" PLUGIN_EXPORT void DestroyAudioInputSource(AudioInputSource* source);

extern "C" PLUGIN_EXPORT MediaSDKStringData EnumAudioInputDevice();

// Optional
extern "C" PLUGIN_EXPORT MediaSDKStringData GetDefaultAudioInputDevice();

// Optional
extern "C" PLUGIN_EXPORT MediaSDKStringData GetDefaultAudioOutDevice();

// Optional
extern "C" PLUGIN_EXPORT MediaSDKStringData EnumCaptureAudioDevice();

// Optional
extern "C" PLUGIN_EXPORT MediaSDKStringData EnumRenderAudioDevice();

}  // namespace mediasdk
