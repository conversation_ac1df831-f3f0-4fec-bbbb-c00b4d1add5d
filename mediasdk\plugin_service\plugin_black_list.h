
#pragma once

#include <base/files/file_path.h>
#include <vector>

namespace plugin_black_list {
static const std::vector<base::FilePath::StringPieceType> black_list = {
    FILE_PATH_LITERAL("game_detour_64.dll"),
    FILE_PATH_LITERAL("game_detour_32.dll"),
    FILE_PATH_LITERAL("libttquic.dll"),
    FILE_PATH_LITERAL("ssl.dll"),
    FILE_PATH_LITERAL("crypto.dll"),
};

inline bool IsInBlackList(const base::FilePath& file_path) {
  const auto base_name = file_path.BaseName();
  return std::any_of(black_list.begin(), black_list.end(),
                     [&base_name](const auto& file_name) {
                       return base::FilePath::CompareIgnoreCase(
                                  file_name, base_name.value()) == 0;
                     });
}
}  // namespace plugin_black_list
