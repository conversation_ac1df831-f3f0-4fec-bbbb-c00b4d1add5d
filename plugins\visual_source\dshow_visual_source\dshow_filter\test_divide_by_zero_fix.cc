// 测试除零错误修复的有效性
// 这个文件用于验证 NVIDIA 虚拟摄像头过滤器除零错误的修复

#include <iostream>
#include <windows.h>
#include <dshow.h>
#include "dshow_capture_base.h"
#include "base/logging.h"

using namespace mediasdk;

// 模拟测试用例
void TestDivideByZeroFix() {
    std::cout << "=== 测试除零错误修复 ===" << std::endl;

    // 测试用例1：AvgTimePerFrame 为 0 的情况
    {
        std::cout << "\n--- 测试用例1：AvgTimePerFrame = 0 ---" << std::endl;
        
        AM_MEDIA_TYPE media_type = {};
        VIDEOINFOHEADER vih = {};
        
        // 设置正常参数
        vih.bmiHeader.biWidth = 1920;
        vih.bmiHeader.biHeight = 1080;
        vih.AvgTimePerFrame = 0;  // 这会导致除零错误
        
        media_type.formattype = FORMAT_VideoInfo;
        media_type.pbFormat = reinterpret_cast<BYTE*>(&vih);
        media_type.cbFormat = sizeof(VIDEOINFOHEADER);
        
        // 创建测试实例
        DShowCaptureBase capture_base;
        bool result = capture_base.ValidateMediaTypeParameters(&media_type);
        
        std::cout << "验证结果: " << (result ? "通过" : "失败") << std::endl;
        std::cout << "修复后的 AvgTimePerFrame: " << vih.AvgTimePerFrame << std::endl;
        
        if (vih.AvgTimePerFrame > 0) {
            float fps = 10000000.0f / vih.AvgTimePerFrame;
            std::cout << "对应帧率: " << fps << " fps" << std::endl;
        }
    }

    // 测试用例2：AvgTimePerFrame 过小的情况
    {
        std::cout << "\n--- 测试用例2：AvgTimePerFrame 过小 ---" << std::endl;
        
        AM_MEDIA_TYPE media_type = {};
        VIDEOINFOHEADER vih = {};
        
        vih.bmiHeader.biWidth = 1920;
        vih.bmiHeader.biHeight = 1080;
        vih.AvgTimePerFrame = 50000;  // 对应200fps，过高
        
        media_type.formattype = FORMAT_VideoInfo;
        media_type.pbFormat = reinterpret_cast<BYTE*>(&vih);
        media_type.cbFormat = sizeof(VIDEOINFOHEADER);
        
        DShowCaptureBase capture_base;
        bool result = capture_base.ValidateMediaTypeParameters(&media_type);
        
        std::cout << "验证结果: " << (result ? "通过" : "失败") << std::endl;
        std::cout << "修复后的 AvgTimePerFrame: " << vih.AvgTimePerFrame << std::endl;
        
        if (vih.AvgTimePerFrame > 0) {
            float fps = 10000000.0f / vih.AvgTimePerFrame;
            std::cout << "对应帧率: " << fps << " fps" << std::endl;
        }
    }

    // 测试用例3：无效的分辨率
    {
        std::cout << "\n--- 测试用例3：无效分辨率 ---" << std::endl;
        
        AM_MEDIA_TYPE media_type = {};
        VIDEOINFOHEADER vih = {};
        
        vih.bmiHeader.biWidth = 0;    // 无效宽度
        vih.bmiHeader.biHeight = 0;   // 无效高度
        vih.AvgTimePerFrame = 333333; // 正常帧率
        
        media_type.formattype = FORMAT_VideoInfo;
        media_type.pbFormat = reinterpret_cast<BYTE*>(&vih);
        media_type.cbFormat = sizeof(VIDEOINFOHEADER);
        
        DShowCaptureBase capture_base;
        bool result = capture_base.ValidateMediaTypeParameters(&media_type);
        
        std::cout << "验证结果: " << (result ? "通过" : "失败") << std::endl;
        std::cout << "这种情况应该返回失败，因为分辨率无效" << std::endl;
    }

    // 测试用例4：FORMAT_VideoInfo2 格式
    {
        std::cout << "\n--- 测试用例4：FORMAT_VideoInfo2 格式 ---" << std::endl;
        
        AM_MEDIA_TYPE media_type = {};
        VIDEOINFOHEADER2 vih2 = {};
        
        vih2.bmiHeader.biWidth = 1280;
        vih2.bmiHeader.biHeight = 720;
        vih2.AvgTimePerFrame = 0;  // 会导致除零错误
        
        media_type.formattype = FORMAT_VideoInfo2;
        media_type.pbFormat = reinterpret_cast<BYTE*>(&vih2);
        media_type.cbFormat = sizeof(VIDEOINFOHEADER2);
        
        DShowCaptureBase capture_base;
        bool result = capture_base.ValidateMediaTypeParameters(&media_type);
        
        std::cout << "验证结果: " << (result ? "通过" : "失败") << std::endl;
        std::cout << "修复后的 AvgTimePerFrame: " << vih2.AvgTimePerFrame << std::endl;
        
        if (vih2.AvgTimePerFrame > 0) {
            float fps = 10000000.0f / vih2.AvgTimePerFrame;
            std::cout << "对应帧率: " << fps << " fps" << std::endl;
        }
    }
}

void TestFrameRateCalculation() {
    std::cout << "\n=== 测试帧率计算修复 ===" << std::endl;

    // 测试各种帧率值
    float test_rates[] = {0.0f, -1.0f, 30.0f, 60.0f, 120.0f, 0.1f, 1000.0f};
    
    for (float rate : test_rates) {
        std::cout << "\n输入帧率: " << rate << " fps" << std::endl;
        
        REFERENCE_TIME requst_avg_time_per_frame = 0;
        if (rate > 0.0f) {
            requst_avg_time_per_frame = static_cast<REFERENCE_TIME>(10000000.0f / rate);
        } else {
            requst_avg_time_per_frame = 333333; // 默认30fps
            std::cout << "无效帧率，使用默认30fps" << std::endl;
        }
        
        std::cout << "计算的 AvgTimePerFrame: " << requst_avg_time_per_frame << std::endl;
        
        // 验证反向计算
        if (requst_avg_time_per_frame > 0) {
            float calculated_rate = 10000000.0f / requst_avg_time_per_frame;
            std::cout << "反向计算的帧率: " << calculated_rate << " fps" << std::endl;
        }
    }
}

void PrintFixSummary() {
    std::cout << "\n=== 修复总结 ===" << std::endl;
    
    std::cout << "1. 问题原因：" << std::endl;
    std::cout << "   - NVIDIA 虚拟摄像头过滤器在 SetFormat 时进行除法运算" << std::endl;
    std::cout << "   - AvgTimePerFrame 参数为 0 导致除零错误" << std::endl;
    std::cout << "   - 帧率计算中缺乏参数验证" << std::endl;
    
    std::cout << "\n2. 修复措施：" << std::endl;
    std::cout << "   - 添加 ValidateMediaTypeParameters 函数验证媒体类型参数" << std::endl;
    std::cout << "   - 检查并修复 AvgTimePerFrame 为 0 或过小的情况" << std::endl;
    std::cout << "   - 在帧率计算中添加除零保护" << std::endl;
    std::cout << "   - 设置合理的默认值和限制" << std::endl;
    
    std::cout << "\n3. 保护机制：" << std::endl;
    std::cout << "   - AvgTimePerFrame <= 0 时设置为默认值 333333 (30fps)" << std::endl;
    std::cout << "   - AvgTimePerFrame < 100000 时限制为 100000 (100fps)" << std::endl;
    std::cout << "   - 验证视频分辨率参数的有效性" << std::endl;
    std::cout << "   - 在日志输出前进行除零检查" << std::endl;
    
    std::cout << "\n4. 适用场景：" << std::endl;
    std::cout << "   - NVIDIA 虚拟摄像头设备" << std::endl;
    std::cout << "   - 其他可能存在类似问题的 DirectShow 过滤器" << std::endl;
    std::cout << "   - 异常的媒体格式参数" << std::endl;
}

int main() {
    std::cout << "DirectShow 除零错误修复测试" << std::endl;
    std::cout << "=============================" << std::endl;
    
    try {
        TestDivideByZeroFix();
        TestFrameRateCalculation();
        PrintFixSummary();
    } catch (const std::exception& e) {
        std::cout << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n测试完成！修复应该能够防止 NVIDIA 虚拟摄像头过滤器的除零错误。" << std::endl;
    return 0;
}

/*
使用说明：

1. 编译并运行此测试来验证修复的有效性
2. 测试涵盖了各种可能导致除零错误的场景
3. 修复后的代码应该能够：
   - 检测并修复无效的 AvgTimePerFrame 参数
   - 防止除零错误的发生
   - 提供合理的默认值
   - 记录警告信息以便调试

4. 在实际使用中，这些修复将：
   - 防止 NVIDIA 虚拟摄像头过滤器崩溃
   - 提高 DirectShow 图形的稳定性
   - 提供更好的错误处理和日志记录
*/
