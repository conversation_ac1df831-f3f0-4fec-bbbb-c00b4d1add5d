#pragma once
#include "r_g_b_a.h"
#include "texture_color_convert_matrix_builder.h"

#include <base/check.h>
#include "media/video/video_frame.h"
#include "ycbcr.h"

namespace graphics {

r_g_b_a readBGRAFromVideoFrame(const mediasdk::VideoFrame& in, int cx, int cy) {
  auto data = in.GetPlaneData(0);
  r_g_b_a ret;
  int32_t begin = cy * in.GetPlaneSize(0) + cx * 4;
  ret.b = data[begin++];
  ret.g = data[begin++];
  ret.r = data[begin++];
  ret.a = data[begin++];
  return ret;
};

float_r_g_b_a r_g_b_a_to_float_r_g_b_a(const r_g_b_a& v) {
  float_r_g_b_a ret;
  ret.a = v.a / 255.f;
  ret.r = v.r / 255.f;
  ret.g = v.g / 255.f;
  ret.b = v.b / 255.f;
  return ret;
};

y_Cb_Cr_alpha ConvertRGBAToYUV(const float_r_g_b_a& r_g_b_a,
                               const XMFLOAT4& Y,
                               const XMFLOAT4& U,
                               const XMFLOAT4& V,
                               bool limit = false) {
  // limit value range
  float r = r_g_b_a.r;
  float g = r_g_b_a.g;
  float b = r_g_b_a.b;

  float y = r * Y.x + g * Y.y + b * Y.z + Y.w;
  float u = r * U.x + g * U.y + b * U.z + U.w;
  float v = r * V.x + g * V.y + b * V.z + V.w;

  auto clamp_to_0_255 = [](float v, int32_t min, int32_t max) {
    int32_t ret = v * 255.f;
    return std::min(std::max(ret, min), max);
  };
  y_Cb_Cr_alpha ret = {};
  if (limit) {
    ret.y = clamp_to_0_255(y, range_part[0].min, range_part[0].max);
    ret.Cb = clamp_to_0_255(u, range_part[1].min, range_part[1].max);
    ret.Cr = clamp_to_0_255(v, range_part[2].min, range_part[2].max);
  } else {
    ret.y = clamp_to_0_255(y, range_full[0].min, range_full[0].max);
    ret.Cb = clamp_to_0_255(u, range_full[1].min, range_full[1].max);
    ret.Cr = clamp_to_0_255(v, range_full[2].min, range_full[2].max);
  }

  ret.alpha = 255;
  return ret;
}

void BGRAToYUVConvertI420(const mediasdk::VideoFrame& in,
                          mediasdk::VideoFrame& out) {
  DCHECK(in.GetCS() != mediasdk::ColorSpace::kColorSpaceUnspecified &&
         in.GetVR() != mediasdk::VideoRange::kVideoRangeUnspecified &&
         in.GetWidth() > 0 && in.GetHeight() > 0);
  // 1. build matrix
  // 2. get y Cb Cr alpha
  // 3. get rgb from y CbCr with(cx,cy)

  XMFLOAT4 Y;
  XMFLOAT4 U;
  XMFLOAT4 V;
  XMFLOAT4 min_v;
  XMFLOAT4 max_v;
  BuildPlanesBGRAToYUV(in.GetCS(), in.GetVR(), Y, U, V);
  out.ResizeBuffer(in.GetWidth(), in.GetHeight());
  uint8_t* y_data = out.GetPlaneData(0);
  uint8_t* u_data = out.GetPlaneData(1);
  uint8_t* v_data = out.GetPlaneData(2);

  for (int cy = 0; cy < in.GetHeight() / 2; cy++) {
    for (int cx = 0; cx < in.GetWidth() / 2; cx++) {
      int begin_cx = cx * 2;
      int begin_cy = cy * 2;

      auto rgba00 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx, begin_cy));
      auto rgba01 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx + 1, begin_cy));
      auto rgba10 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx, begin_cy + 1));
      auto rgba11 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx + 1, begin_cy + 1));

      y_data[begin_cx + begin_cy * in.GetWidth()] =
          ConvertRGBAToYUV(
              rgba00, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;
      y_data[begin_cx + 1 + begin_cy * in.GetWidth()] =
          ConvertRGBAToYUV(
              rgba01, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;
      y_data[begin_cx + (begin_cy + 1) * in.GetWidth()] =
          ConvertRGBAToYUV(
              rgba10, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;
      y_data[begin_cx + 1 + (begin_cy + 1) * in.GetWidth()] =
          ConvertRGBAToYUV(
              rgba11, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;

      float_r_g_b_a average_rgba;
      average_rgba.a = (rgba00.a + rgba01.a + rgba10.a + rgba11.a) / 4.f;
      average_rgba.r = (rgba00.r + rgba01.r + rgba10.r + rgba11.r) / 4.f;
      average_rgba.g = (rgba00.g + rgba01.g + rgba10.g + rgba11.g) / 4.f;
      average_rgba.b = (rgba00.b + rgba01.b + rgba10.b + rgba11.b) / 4.f;
      y_Cb_Cr_alpha yuv = ConvertRGBAToYUV(
          average_rgba, Y, U, V,
          out.GetVR() == mediasdk::VideoRange::kVideoRangePartial);
      u_data[cy * out.GetPlaneSize(1) + cx] = yuv.Cb;
      v_data[cy * out.GetPlaneSize(2) + cx] = yuv.Cr;
    }
  }
}

void BGRAToYUVConvertNV12(const mediasdk::VideoFrame& in,
                          mediasdk::VideoFrame& out) {
  DCHECK(in.GetCS() != mediasdk::ColorSpace::kColorSpaceUnspecified &&
         in.GetVR() != mediasdk::VideoRange::kVideoRangeUnspecified &&
         in.GetWidth() > 0 && in.GetHeight() > 0);
  // 1. build matrix
  // 2. get y Cb Cr alpha
  // 3. get rgb from y CbCr with(cx,cy)

  XMFLOAT4 Y;
  XMFLOAT4 U;
  XMFLOAT4 V;
  XMFLOAT4 min_v;
  XMFLOAT4 max_v;
  BuildPlanesBGRAToYUV(in.GetCS(), in.GetVR(), Y, U, V);
  out.ResizeBuffer(in.GetWidth(), in.GetHeight());
  uint8_t* y_data = out.GetPlaneData(0);
  uint8_t* uv_data = out.GetPlaneData(1);

  for (int cy = 0; cy < in.GetHeight() / 2; cy++) {
    for (int cx = 0; cx < in.GetWidth() / 2; cx++) {
      int begin_cx = cx * 2;
      int begin_cy = cy * 2;

      auto rgba00 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx, begin_cy));
      auto rgba01 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx + 1, begin_cy));
      auto rgba10 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx, begin_cy + 1));
      auto rgba11 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx + 1, begin_cy + 1));

      y_data[begin_cx + begin_cy * in.GetWidth()] =
          ConvertRGBAToYUV(
              rgba00, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;
      y_data[begin_cx + 1 + begin_cy * in.GetWidth()] =
          ConvertRGBAToYUV(
              rgba01, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;
      y_data[begin_cx + (begin_cy + 1) * in.GetWidth()] =
          ConvertRGBAToYUV(
              rgba10, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;
      y_data[begin_cx + 1 + (begin_cy + 1) * in.GetWidth()] =
          ConvertRGBAToYUV(
              rgba11, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;

      float_r_g_b_a average_rgba;
      average_rgba.a = (rgba00.a + rgba01.a + rgba10.a + rgba11.a) / 4.f;
      average_rgba.r = (rgba00.r + rgba01.r + rgba10.r + rgba11.r) / 4.f;
      average_rgba.g = (rgba00.g + rgba01.g + rgba10.g + rgba11.g) / 4.f;
      average_rgba.b = (rgba00.b + rgba01.b + rgba10.b + rgba11.b) / 4.f;
      y_Cb_Cr_alpha yuv = ConvertRGBAToYUV(
          average_rgba, Y, U, V,
          out.GetVR() == mediasdk::VideoRange::kVideoRangePartial);
      uv_data[cy * out.GetPlaneSize(1) + cx * 2] = yuv.Cb;
      uv_data[cy * out.GetPlaneSize(1) + cx * 2 + 1] = yuv.Cr;
    }
  }
}

void BGRAToYUVConvert422(const mediasdk::VideoFrame& in,
                         mediasdk::VideoFrame& out) {
  DCHECK(in.GetCS() != mediasdk::ColorSpace::kColorSpaceUnspecified &&
         in.GetVR() != mediasdk::VideoRange::kVideoRangeUnspecified &&
         in.GetWidth() > 0 && in.GetHeight() > 0);
  // 1. build matrix
  // 2. get y Cb Cr alpha
  // 3. get rgb from y CbCr with(cx,cy)

  XMFLOAT4 Y;
  XMFLOAT4 U;
  XMFLOAT4 V;
  XMFLOAT4 min_v;
  XMFLOAT4 max_v;
  BuildPlanesBGRAToYUV(in.GetCS(), in.GetVR(), Y, U, V);
  out.ResizeBuffer(in.GetWidth(), in.GetHeight());
  uint8_t* y_data = out.GetPlaneData(0);
  uint8_t* u = out.GetPlaneData(1);
  uint8_t* v = out.GetPlaneData(2);

  for (int cy = 0; cy < in.GetHeight(); cy++) {
    for (int cx = 0; cx < in.GetWidth() / 2; cx++) {
      int begin_cx = cx * 2;
      int begin_cy = cy;

      auto rgba00 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx, begin_cy));
      auto rgba01 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx + 1, begin_cy));

      y_data[begin_cx + begin_cy * in.GetWidth()] =
          ConvertRGBAToYUV(
              rgba00, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;
      y_data[begin_cx + 1 + begin_cy * in.GetWidth()] =
          ConvertRGBAToYUV(
              rgba01, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;

      float_r_g_b_a average_rgba;
      average_rgba.a = (rgba00.a + rgba01.a) / 2.f;
      average_rgba.r = (rgba00.r + rgba01.r) / 2.f;
      average_rgba.g = (rgba00.g + rgba01.g) / 2.f;
      average_rgba.b = (rgba00.b + rgba01.b) / 2.f;
      y_Cb_Cr_alpha yuv = ConvertRGBAToYUV(
          average_rgba, Y, U, V,
          out.GetVR() == mediasdk::VideoRange::kVideoRangePartial);
      u[cy * out.GetPlaneSize(1) + cx] = yuv.Cb;
      v[cy * out.GetPlaneSize(2) + cx] = yuv.Cr;
    }
  }
}

void BGRAToYUVConvertUYVY(const mediasdk::VideoFrame& in,
                          mediasdk::VideoFrame& out) {
  DCHECK(in.GetCS() != mediasdk::ColorSpace::kColorSpaceUnspecified &&
         in.GetVR() != mediasdk::VideoRange::kVideoRangeUnspecified &&
         in.GetWidth() > 0 && in.GetHeight() > 0);
  // 1. build matrix
  // 2. get y Cb Cr alpha
  // 3. get rgb from y CbCr with(cx,cy)

  XMFLOAT4 Y;
  XMFLOAT4 U;
  XMFLOAT4 V;
  XMFLOAT4 min_v;
  XMFLOAT4 max_v;
  BuildPlanesBGRAToYUV(in.GetCS(), in.GetVR(), Y, U, V);
  out.ResizeBuffer(in.GetWidth(), in.GetHeight());
  uint8_t* u00_y00_v00_y01_data = out.GetPlaneData(0);

  for (int cy = 0; cy < in.GetHeight(); cy++) {
    for (int cx = 0; cx < in.GetWidth() / 2; cx++) {
      int begin_cx = cx * 2;
      int begin_cy = cy;

      auto rgba00 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx, begin_cy));
      auto rgba01 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx + 1, begin_cy));
      int32_t data_begin = begin_cx * 2 + begin_cy * out.GetPlaneSize(0);

      u00_y00_v00_y01_data[data_begin + 1] =
          ConvertRGBAToYUV(
              rgba00, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;
      u00_y00_v00_y01_data[data_begin + 3] =
          ConvertRGBAToYUV(
              rgba01, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;

      float_r_g_b_a average_rgba;
      average_rgba.a = (rgba00.a + rgba01.a) / 2.f;
      average_rgba.r = (rgba00.r + rgba01.r) / 2.f;
      average_rgba.g = (rgba00.g + rgba01.g) / 2.f;
      average_rgba.b = (rgba00.b + rgba01.b) / 2.f;
      y_Cb_Cr_alpha yuv = ConvertRGBAToYUV(
          average_rgba, Y, U, V,
          out.GetVR() == mediasdk::VideoRange::kVideoRangePartial);
      u00_y00_v00_y01_data[data_begin + 0] = yuv.Cb;
      u00_y00_v00_y01_data[data_begin + 2] = yuv.Cr;
    }
  }
}

void BGRAToYUVConvertYUY2(const mediasdk::VideoFrame& in,
                          mediasdk::VideoFrame& out) {
  DCHECK(in.GetCS() != mediasdk::ColorSpace::kColorSpaceUnspecified &&
         in.GetVR() != mediasdk::VideoRange::kVideoRangeUnspecified &&
         in.GetWidth() > 0 && in.GetHeight() > 0);
  // 1. build matrix
  // 2. get y Cb Cr alpha
  // 3. get rgb from y CbCr with(cx,cy)

  XMFLOAT4 Y;
  XMFLOAT4 U;
  XMFLOAT4 V;
  XMFLOAT4 min_v;
  XMFLOAT4 max_v;
  BuildPlanesBGRAToYUV(in.GetCS(), in.GetVR(), Y, U, V);
  out.ResizeBuffer(in.GetWidth(), in.GetHeight());
  uint8_t* y00_u00__v00_y01_data = out.GetPlaneData(0);

  for (int cy = 0; cy < in.GetHeight(); cy++) {
    for (int cx = 0; cx < in.GetWidth() / 2; cx++) {
      int begin_cx = cx * 2;
      int begin_cy = cy;

      auto rgba00 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx, begin_cy));
      auto rgba01 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx + 1, begin_cy));
      int32_t data_begin = begin_cx * 2 + begin_cy * out.GetPlaneSize(0);

      y00_u00__v00_y01_data[data_begin + 0] =
          ConvertRGBAToYUV(
              rgba00, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;
      y00_u00__v00_y01_data[data_begin + 2] =
          ConvertRGBAToYUV(
              rgba01, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;

      float_r_g_b_a average_rgba;
      average_rgba.a = (rgba00.a + rgba01.a) / 2.f;
      average_rgba.r = (rgba00.r + rgba01.r) / 2.f;
      average_rgba.g = (rgba00.g + rgba01.g) / 2.f;
      average_rgba.b = (rgba00.b + rgba01.b) / 2.f;
      y_Cb_Cr_alpha yuv = ConvertRGBAToYUV(
          average_rgba, Y, U, V,
          out.GetVR() == mediasdk::VideoRange::kVideoRangePartial);
      y00_u00__v00_y01_data[data_begin + 1] = yuv.Cb;
      y00_u00__v00_y01_data[data_begin + 3] = yuv.Cr;
    }
  }
}

void BGRAToYUVConvert444(const mediasdk::VideoFrame& in,
                         mediasdk::VideoFrame& out) {
  DCHECK(in.GetCS() != mediasdk::ColorSpace::kColorSpaceUnspecified &&
         in.GetVR() != mediasdk::VideoRange::kVideoRangeUnspecified &&
         in.GetWidth() > 0 && in.GetHeight() > 0);
  // 1. build matrix
  // 2. get y Cb Cr alpha
  // 3. get rgb from y CbCr with(cx,cy)

  XMFLOAT4 Y;
  XMFLOAT4 U;
  XMFLOAT4 V;
  XMFLOAT4 min_v;
  XMFLOAT4 max_v;
  BuildPlanesBGRAToYUV(in.GetCS(), in.GetVR(), Y, U, V);
  out.ResizeBuffer(in.GetWidth(), in.GetHeight());
  uint8_t* y_data = out.GetPlaneData(0);
  uint8_t* u = out.GetPlaneData(1);
  uint8_t* v = out.GetPlaneData(2);

  for (int cy = 0; cy < in.GetHeight(); cy++) {
    for (int cx = 0; cx < in.GetWidth(); cx++) {
      int begin_cx = cx;
      int begin_cy = cy;

      auto rgba00 = r_g_b_a_to_float_r_g_b_a(
          readBGRAFromVideoFrame(in, begin_cx, begin_cy));

      y_data[begin_cx + begin_cy * in.GetWidth()] =
          ConvertRGBAToYUV(
              rgba00, Y, U, V,
              out.GetVR() == mediasdk::VideoRange::kVideoRangePartial)
              .y;

      float_r_g_b_a average_rgba = rgba00;
      y_Cb_Cr_alpha yuv = ConvertRGBAToYUV(
          average_rgba, Y, U, V,
          out.GetVR() == mediasdk::VideoRange::kVideoRangePartial);
      u[cy * out.GetPlaneSize(1) + cx] = yuv.Cb;
      v[cy * out.GetPlaneSize(2) + cx] = yuv.Cr;
    }
  }
}
}  // namespace graphics
