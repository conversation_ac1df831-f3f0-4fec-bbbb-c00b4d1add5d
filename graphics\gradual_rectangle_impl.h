#pragma once
#include <array>
#include "device.h"
#include "rectangle.h" // Includes GradualRectangle definition
// #include "gradual_rectangle_shader.h" // Will be needed when shader is implemented
#include "color_shader.h" // Placeholder, will use GradualRectangleShader
#include "transform.h"

namespace graphics {

class GradualRectangleImpl : public GradualRectangle {
 public:
  static std::shared_ptr<GradualRectangleImpl> CreateGradualRectangle(Device& inst);
  GradualRectangleImpl(Device& ins);

 public:
  bool DrawTo(const DirectX::XMFLOAT2& vp,
              const DirectX::XMMATRIX& view,
              const DirectX::XMMATRIX& projection) override;

  void UpdateRectangleConf(const GradualRectangleConfig* conf) override;
  int32_t GetIndexCnt() override;
  void Destroy() override;
  ~GradualRectangleImpl() override;

 private:
  struct GradualRectangleConfigInternal : public GradualRectangle::GradualRectangleConfig {
    bool show = false;
  };

  bool SetupIAS();

  ID3D11DeviceContext* GetContext();
  ID3D11Device* GetDevice();
  bool UpdateBufferWithVPSize(const DirectX::XMFLOAT2& vp,
                              const GradualRectangleConfigInternal& conf);
  bool UpdateVertexBuffer(const GradualRectangleConfigInternal& conf);

 private:
  Microsoft::WRL::ComPtr<ID3D11Buffer> vertex_buffer_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> index_buffer_fill_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> index_buffer_lines_;
  // This might need to change based on how gradient is handled (e.g., more vertices, different vertex type)
  std::array<ColorShader::VERTEXTYPE, 8> vertex_mem_buffer_; 
  int32_t cur_index_cnt_ = 0;
  bool fill_ = false;
  Device& instance_;

  std::unique_ptr<GradualRectangleConfigInternal> config_;
  DirectX::XMMATRIX view_;

  Microsoft::WRL::ComPtr<ID3D11Buffer> matrix_;
  GradualRectangleConfig pre_conf_ = {};
};

}  // namespace graphics