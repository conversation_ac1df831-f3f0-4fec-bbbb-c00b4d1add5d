#pragma once

#include "audio/audio_format.h"
#include "audio/audio_resample.h"
#include "audio_input.h"
#include "base/threading/thread.h"
#include "base/timer/timer.h"
#include "lyrax/lyrax_audio.h"
#include "lyrax/lyrax_audio_device_delegate.h"
#include "lyrax/lyrax_audio_frame.h"
#include "lyrax/lyrax_engine.h"
#ifdef ENABLE_AUDIO_DUMP
#include <audio/wav_writer.h>
#endif

namespace mediasdk {

bool ToLyraxAudioFormat(int sample_rate, int channel_count, lyrax::LyraxAudioFormat& format);

lyrax::LyraxAudioFrame ToLyraxAudioFrame(const AudioFrame& target_audio_frame);

class LyraxAudioProcessorDelegate {
 public:
  virtual void OnLyraxPreAudioFrame(const AudioFormat& format,
                                    const AudioFrame& frame) = 0;
  virtual void OnLyraxPostAudioFrame(const AudioFormat& format,
                                     const AudioFrame& frame) = 0;
};

class LyraxAudioProcessor {
 public:
  LyraxAudioProcessor(LyraxAudioProcessorDelegate* delegate,
                      std::shared_ptr<lyrax::ILyraxAudio> audio);
  ~LyraxAudioProcessor();
  // audio frame function

  void PushMicFrame(const AudioFormat& format, const AudioFrame& frame);
  void PushRefFrame(const AudioFormat& format, const AudioFrame& frame);

  void SetMute(bool mute);

  // 3a control
  void SetAECOption(const bool);
  void SetRawDataOption(const int32_t mode);
  void SetRawDataMode(const bool enable);
  void SetANSOption(const int32_t level);
  bool EnableEchoDetection(const int32_t interval);
  // audio device delegate
  void SetAudioDeviceDelegate(
      std::shared_ptr<lyrax::ILyraxAudioDeviceDelegate> delegate);

  bool IsAECEnable();
  bool IsEchoDetectionEnable();
  int64_t GetLastMicMSTS();
  //
  int OnLyraxPreAudioFrame(lyrax::LyraxAudioFrame& audio_frame);
  int OnLyraxPostAudioFrame(lyrax::LyraxAudioFrame& audio_frame);

 private:
  std::shared_ptr<lyrax::ILyraxAudio> GetLyraxAudio();

  void CheckAndChangeLyraxProcessor();
  bool IsNeedLyraxProcessor() const;
  void ConvertFromLyraxAudioFrame(lyrax::LyraxAudioFrame& input_frame,
                                  AudioFormat& out_format,
                                  AudioFrame& out_frame);

 private:
  LyraxAudioProcessorDelegate* delegate_ = nullptr;
  bool aec_enable_ = false;
  bool raw_data_enabled_ = false;
  int32_t ans_level_ =
      lyrax::LyraxAudioNsApiOption::kLyraxAudioNsApiOptionDisable;
  int32_t echo_detection_interval_ = 0;
  std::weak_ptr<lyrax::ILyraxAudio> lyrax_audio_;

  std::shared_ptr<lyrax::ILyraxAudioInputStream> lyrax_audio_input_stream_;

  lyrax::LyraxAudioFormat lyrax_audio_mic_input_format_;
  bool pre_enable_ = false;

  std::shared_ptr<AudioResample> resample_to_lyrax_format_mic_;

  std::shared_ptr<AudioResample> resample_to_lyrax_format_ref_;

  std::atomic_bool is_mute_ = false;
  std::atomic_int64_t last_mic_ms_ts_ = 0;
  AudioFormat lyrax_mic_target_format_ = AudioFormat{
      AUDIO_FORMAT::AUDIO_FORMAT_S16, CHANNEL_LAYOUT::CHANNEL_STEREO, 2, 48000};
  const AudioFormat lyrax_ref_target_format_ = AudioFormat{
      AUDIO_FORMAT::AUDIO_FORMAT_S16, CHANNEL_LAYOUT::CHANNEL_STEREO, 2, 48000};
  std::shared_ptr<lyrax::ILyraxAudioFilter> pre_frame_filter_;
  std::shared_ptr<lyrax::ILyraxAudioFilter> post_frame_filter_;
  std::shared_ptr<lyrax::ILyraxAudioListener> lyrax_audio_listener_;
#ifdef ENABLE_AUDIO_DUMP
  std::unique_ptr<WAVWriter> dump_mic_in_;
  std::unique_ptr<WAVWriter> dump_mic_after_resample_in_;
  std::unique_ptr<WAVWriter> dump_ref_in_;
  std::unique_ptr<WAVWriter> dump_output_;
  std::string ref_name_;
  std::string mic_name_;
#endif
};

}  // namespace mediasdk