#include "profiler.h"

#include <utility>

namespace {

// if name is encoder, match is encoder_0 or encoder_1, return true
bool MatchName(const std::string_view name, const std::string_view match) {
  return match.substr(0, name.size()) == name;
}
}  // namespace

namespace mediasdk {

ProfilerConsumer::ProfilerConsumer()
    : error_profiler_(new ProfilerData("")),
      error_cost_profiler_(new CostProfilerData("")) {}

ProfilerConsumer& ProfilerConsumer::GetInstance() {
  static ProfilerConsumer ins;
  return ins;
}

void ProfilerConsumer::SetHandler(ProcessProfilerData func) {
  std::unique_lock lock(process_profiler_data_mutex_);
  process_profiler_data_ = std::move(func);
}

void ProfilerConsumer::SetCostProfilerHandler(ProcessCostProfilerData func) {
  std::unique_lock lock(process_cost_profiler_mutex_);
  process_cost_profiler_ = std::move(func);
}

ProfilerData* ProfilerConsumer::GetProfiler(const char* name) {
  if (!name || !name[0]) {
    return error_profiler_.get();
  }

  {
    std::shared_lock lock(profiler_data_map_mutex_);
    if (const auto it = profiler_data_map_.find(name);
        it != profiler_data_map_.end()) {
      return it->second.get();
    }
  }

  {
    std::unique_lock lock(profiler_data_map_mutex_);
    const auto [it, insert_result] =
        profiler_data_map_.insert({name, std::make_unique<ProfilerData>(name)});
    return it->second.get();
  }
}

CostProfilerData* ProfilerConsumer::GetCostProfiler(const char* name) {
  if (!name || !name[0]) {
    return error_cost_profiler_.get();
  }

  {
    std::shared_lock lock(cost_profiler_data_map_mutex_);
    if (const auto it = cost_profiler_data_map_.find(name);
        it != cost_profiler_data_map_.end()) {
      return it->second.get();
    }
  }

  {
    std::unique_lock lock(cost_profiler_data_map_mutex_);
    const auto [it, insert_result] = cost_profiler_data_map_.insert(
        {name, std::make_unique<CostProfilerData>(name)});
    return it->second.get();
  }
}

void ProfilerConsumer::Calc() {
  std::shared_lock lock(profiler_data_map_mutex_);
  for (const auto& [name, data] : profiler_data_map_) {
    data->Calc();
  }
}

void ProfilerConsumer::CalcCost() {
  std::shared_lock lock(cost_profiler_data_map_mutex_);
  for (auto& [name, data] : cost_profiler_data_map_) {
    data->Calc();
  }
}

void ProfilerConsumer::UpdateCostThreshold(
    const std::vector<CostThresholdParam>& params) {
  for (const auto& param : params) {
    if (const auto val = param.threshold; val > 1000 / 60) {
      std::shared_lock lock(cost_profiler_data_map_mutex_);
      for (const auto& [name, data] : cost_profiler_data_map_) {
        if (MatchName(param.name, name)) {
          data->UpdateThreadsHold(val);
        }
      }
    }
  }
}

void ProfilerConsumer::CalcCollection() {
  for (const auto& [name, profiler_data] : profiler_data_map_) {
    profiler_data->CalcCollection();
  }
}

void ProfilerConsumer::OnProfilerDataCollection(
    const char* name,
    const ProfilerCollectDataItem& data) {
  LOG(INFO) << "\nperf [" << name << "]\n"
            << "    min[" << data.min_cost_ << "]\n"
            << "    max[" << data.max_cost_ << "]\n"
            << "    average[" << data.average_cost_ << "]\n"
            << "    median[" << data.median_cost_ << "]\n"
            << "    percentile99[" << data.percentile99_cost_ << "]\n";
}

void ProfilerConsumer::OnCostProfilerData(const char* name,
                                          const CostProfilerDataItem& data) {
  std::shared_lock lock(process_cost_profiler_mutex_);
  if (process_cost_profiler_) {
    process_cost_profiler_(name, data);
  }
}

void ProfilerConsumer::OnProfilerData(const char* name,
                                      const ProfilerDataItem& data) {
  std::shared_lock lock(process_profiler_data_mutex_);
  if (process_profiler_data_) {
    process_profiler_data_(name, data);
  }
}

}  // namespace mediasdk
