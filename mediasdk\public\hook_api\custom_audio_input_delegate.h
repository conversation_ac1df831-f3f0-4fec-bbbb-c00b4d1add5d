#pragma once

#include "custom_audio_input_proxy.h"
#include "mediasdk/public/mediasdk_defines.h"

namespace mediasdk {

namespace hook_api {

class CustomAudioInputDelegate {
 public:
  virtual ~CustomAudioInputDelegate() {}

  // `AttachProxy` and `DetachProxy` are called in audio thread.
  // The lifecycle of `CustomAudioInputProxy` is managed in audio thread. When
  // accessing proxy object, thread safety must be considered and proper locking
  // mechanism should be applied for proxy object access.
  // !!! Note: Do not perform time-consuming operations in the implementation of
  //  `AttachProxy`, `DetachProxy` and related APIs. Such operations may
  // affect the audio thread's performance.
  virtual void AttachProxy(CustomAudioInputProxy* proxy) = 0;

  // `CustomAudioInputDelegate` must maintain its lifetime until `DetachProxy`
  // is called, as `CustomAudioInput` might access `CustomAudioInputDelegate`
  // during this period.
  virtual void DetachProxy() = 0;

  // Sets whether to mute the CustomAudioInput. Returns true if the operation is
  // successful, false if failed.
  virtual bool OnSetMute(bool mute) = 0;

  // Sets the volume of CustomAudioInput. The volume range is 0~1.0.
  // Returns true if the operation succeeds, false if it fails.
  virtual bool OnSetVolume(float volume) = 0;
};

}  // namespace hook_api

}  // namespace mediasdk