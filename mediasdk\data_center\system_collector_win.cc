#include "system_collector_win.h"

#include "base\logging.h"

namespace cpu_collector {

bool SystemCollector::Initialize() {
  time_collector_.Initialize();
  return pdh_collector_.Initialize();
}

bool SystemCollector::SystemUsage(double& system_usage, double& cpu_time) {
  system_usage = 0.0;
  cpu_time = 0.0;
  if (pdh_collector_.SystemUsage(system_usage, cpu_time)) {
    return true;
  }
  auto time_usage = time_collector_.SystemUsage();
  if (system_usage <= 0.00001) {
    system_usage = time_usage;
  }
  if (cpu_time <= 0.00001) {
    cpu_time = time_usage;
  }
  return true;
}
}  // namespace cpu_collector
