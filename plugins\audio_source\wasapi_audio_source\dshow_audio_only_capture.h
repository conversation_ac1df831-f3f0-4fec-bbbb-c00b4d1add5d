#pragma once

#include <memory>
#include <string>
#include <dshow.h>
#include <wrl/client.h>
#include "plugins/visual_source/dshow_visual_source/dshow_helper/dshow_helper.h"
#include "plugins/visual_source/dshow_visual_source/dshow_filter/audio_filter.h"
#include "mediasdk/public/plugin/audio_input_source.h"
#include "nlohmann/json.hpp"

namespace mediasdk {

// 纯音频捕获类，专门处理采集卡的音频流，不涉及视频处理
class DShowAudioOnlyCapture : public AudioFilterCallBack {
 public:
  explicit DShowAudioOnlyCapture(AudioInputProxy* proxy);
  virtual ~DShowAudioOnlyCapture();

  // 创建纯音频捕获
  bool Create(const std::string& device_id, const nlohmann::json& json_params);
  void Destroy();

  // 开始/停止音频捕获
  bool Start();
  bool Stop();
  bool Pause();
  bool Resume();

  // 获取状态
  bool IsRunning() const;
  bool IsPaused() const;

  // AudioFilterCallBack 接口实现
  void OnAudioFrameReceive(BYTE* buffer, LONG buffer_size, int64_t timestamp) override;
  void OnAudioFormatChange(AM_MEDIA_TYPE* media_type) override;

 private:
  // 创建 DirectShow 图形（仅音频）
  HRESULT CreateAudioGraph(const std::string& device_id, const nlohmann::json& json_params);
  
  // 连接音频过滤器
  HRESULT ConnectAudioFilters();
  
  // 查找音频捕获设备
  HRESULT FindAudioCaptureDevice(const std::string& device_id);
  
  // 配置音频格式
  HRESULT ConfigureAudioFormat(const nlohmann::json& json_params);
  
  // 清理资源
  void CleanupGraph();
  
  // 检查设备是否为音视频复合设备
  bool IsCompositeAudioVideoDevice(const std::string& device_id);
  
  // 处理复合设备的音频提取
  HRESULT HandleCompositeDevice(const std::string& device_id);

 private:
  AudioInputProxy* proxy_;
  std::string device_id_;
  bool is_created_;
  bool is_running_;
  bool is_paused_;
  
  // DirectShow 组件
  Microsoft::WRL::ComPtr<IGraphBuilder> graph_builder_;
  Microsoft::WRL::ComPtr<ICaptureGraphBuilder2> capture_builder_;
  Microsoft::WRL::ComPtr<IMediaControl> media_control_;
  Microsoft::WRL::ComPtr<IMediaEvent> media_event_;
  
  // 音频相关过滤器
  Microsoft::WRL::ComPtr<IBaseFilter> audio_capture_filter_;
  Microsoft::WRL::ComPtr<IPin> audio_capture_output_pin_;
  
  // 自定义音频接收过滤器
  AudioFilter* audio_sink_filter_;
  Microsoft::WRL::ComPtr<IPin> audio_sink_input_pin_;
  
  // 视频相关（仅用于复合设备，但会丢弃数据）
  Microsoft::WRL::ComPtr<IBaseFilter> video_capture_filter_;
  Microsoft::WRL::ComPtr<IBaseFilter> null_renderer_;  // 用于丢弃视频数据
  
  // 音频格式信息
  AudioCaptureFormat audio_format_;
  
  // 线程安全
  mutable std::mutex state_mutex_;
};

// 空渲染器过滤器，用于丢弃视频数据
class NullVideoRenderer : public CBaseRenderer {
 public:
  NullVideoRenderer(HRESULT* phr);
  virtual ~NullVideoRenderer();

  // CBaseRenderer 接口
  HRESULT CheckMediaType(const CMediaType* pmtIn) override;
  HRESULT DoRenderSample(IMediaSample* pMediaSample) override;

 private:
  static const CLSID CLSID_NullVideoRenderer;
};

// 工厂函数
std::unique_ptr<DShowAudioOnlyCapture> CreateDShowAudioOnlyCapture(AudioInputProxy* proxy);

}  // namespace mediasdk
