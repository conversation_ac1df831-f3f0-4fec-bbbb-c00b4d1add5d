#include "audio_filter_factory.h"

#include <base/logging.h>
#include "mediasdk/mediasdk_thread.h"
#include "mediasdk/public/mediasdk_defines.h"
#include "mediasdk/public/plugin/audio_filter.h"

namespace mediasdk {

// static
std::shared_ptr<AudioFilterFactory> AudioFilterFactory::Create(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info) {
  if (auto factory =
          std::make_shared<AudioFilterFactory>(library, std::move(info));
      factory && factory->Load()) {
    return factory;
  }

  return nullptr;
}

AudioFilterFactory::AudioFilterFactory(base::NativeLibrary library,
                                       std::shared_ptr<PluginInfo> info)
    : SourceFactory(library, info) {}

std::shared_ptr<AudioFilter> AudioFilterFactory::CreateFilter(
    const std::string& json_params) {
  if (create_func_) {
    if (auto* filter = create_func_(json_params.c_str())) {
      auto destroy_func = destroy_func_;
      std::shared_ptr<AudioFilter> audio_filter(
          filter, [destroy_func](AudioFilter* audio_filter) {
            DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);
            destroy_func(audio_filter);
          });
      filters_.insert(audio_filter);
      return audio_filter;
    }
  }
  return nullptr;
}

void AudioFilterFactory::Destroy(std::shared_ptr<AudioFilter> audio_filter) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);
  filters_.erase(audio_filter);
}

void AudioFilterFactory::DestroyAll() {
  filters_.clear();
}

bool AudioFilterFactory::Load() {
  create_func_ = reinterpret_cast<CreateAudioFilterFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_, "CreateAudioFilter"));

  if (!create_func_) {
    LOG(ERROR) << "Failed to get function pointer for CreateAudioFilter";
    return false;
  }

  destroy_func_ = reinterpret_cast<DestroyAudioFilter>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "DestroyAudioFilter"));

  if (!destroy_func_) {
    LOG(ERROR) << "Failed to get function pointer for DestroyStreamService";
    return false;
  }

  return true;
}

}  // namespace mediasdk
