#pragma once

#include <d3d11.h>
#include <stdint.h>
#include <map>

#include "mediasdk/public/mediasdk_defines.h"

namespace mediasdk {

namespace hook_api {

struct RenderContext {
  ID3D11Device* device;
  ID3D11DeviceContext* context;
};

class VideoHookPoint {
 public:
  virtual void OnRenderLoopBegin(RenderContext* render_ctx) = 0;

  virtual void OnRenderLoopEnd() = 0;

  virtual void OnPrepareBegin(uint32_t sink_id) = 0;

  virtual void OnPrepareEnd(uint32_t sink_id) = 0;

  virtual void OnCanvasItemOriginTexture(const char* item_id,
                                         ID3D11Texture2D* texture) = 0;

  virtual void OnCanvasItemFilteredTexture(const char* item_id,
                                           ID3D11Texture2D* texture) = 0;

  // After the preview generation is complete, share the BackBuffer of
  // the preview window's swap chain to make it customizable, supporting
  // in-place modifications only.
  virtual void OnPreviewTextureComplete(uint32_t sink_id,
                                        ID3D11Texture2D* texture) = 0;

  virtual ID3D11Texture2D* OnOutputTextureComplete(
      uint32_t sink_id,
      ID3D11Texture2D* texture) = 0;

  // Receives information about the hierarchical structure of sinks, canvases,
  // and canvas items in JSON format. JSON format:
  // {
  //   "sinks": [
  //     {
  //       "sink_id": 1,
  //       "canvases": ["canvas_id_1", "canvas_id_2"]
  //     }
  //   ],
  //   "canvases": [
  //     {
  //       "canvas_id": "canvas_id_1",
  //       "items": ["item_id_1", "item_id_2"]
  //     }
  //   ]
  // }
  virtual void OnCanvasItemIdInfo(const char* json_data) = 0;
};

}  // namespace hook_api

}  // namespace mediasdk