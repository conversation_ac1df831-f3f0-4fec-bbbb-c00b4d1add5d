#pragma once

#include <d3d11.h>
#include <stdint.h>

#include "component_center.h"
#include "mediasdk/public/mediasdk_defines.h"
#include "public/hook_api/audio_hook_point.h"
#include "public/hook_api/video_hook_point.h"

namespace mediasdk {

// Generalized template function to call a hook function without a return value
template <typename HookType, typename FuncType, typename... Args>
void HookCall(HookType* hook_pt, FuncType func, Args... args) {
  if (hook_pt) {
    (hook_pt->*func)(args...);
  }
}

// Generalized template function to call a hook function with a return value
template <typename HookType, typename FuncType, typename... Args>
auto HookCallWithResult(HookType* hook_pt, FuncType func, Args... args)
    -> decltype((std::declval<HookType>().*func)(args...)) {
  if (hook_pt) {
    return (hook_pt->*func)(args...);
  }
  using ReturnType = decltype((std::declval<HookType>().*func)(args...));
  return ReturnType();
}

// Helper functions to get specific HookPoint instances
inline hook_api::VideoHookPoint* GetVideoHookPoint() {
  auto* hook_api = com::GetHookApi();
  return hook_api ? hook_api->GetVideoHookPoint() : nullptr;
}

inline hook_api::AudioHookPoint* GetAudioHookPoint() {
  auto* hook_api = com::GetHookApi();
  return hook_api ? hook_api->GetAudioHookPoint() : nullptr;
}

}  // namespace mediasdk

// Macros for VideoHookPoint
#define HOOK_CALL_VIDEO(func, ...)                  \
  mediasdk::HookCall(mediasdk::GetVideoHookPoint(), \
                     &hook_api::VideoHookPoint::func, ##__VA_ARGS__)

#define HOOK_CALL_VIDEO_WITH_RESULT(func, ...)                \
  mediasdk::HookCallWithResult(mediasdk::GetVideoHookPoint(), \
                               &hook_api::VideoHookPoint::func, ##__VA_ARGS__)

// Macros for AudioHookPoint
#define HOOK_CALL_AUDIO(func, ...)                  \
  mediasdk::HookCall(mediasdk::GetAudioHookPoint(), \
                     &hook_api::AudioHookPoint::func, ##__VA_ARGS__)

#define HOOK_CALL_AUDIO_WITH_RESULT(func, ...)                \
  mediasdk::HookCallWithResult(mediasdk::GetAudioHookPoint(), \
                               &hook_api::AudioHookPoint::func, ##__VA_ARGS__)
