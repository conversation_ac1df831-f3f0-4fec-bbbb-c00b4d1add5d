#pragma once

#include <memory>
#include <string>
#include "audio/audio_format.h"
#include "audio/audio_frame.h"

namespace mediasdk {

class WAVWriter;

// AudioDumpManager is responsible for managing audio dump operations
class AudioDumpManager {
 public:
  explicit AudioDumpManager(const std::string& id);
  ~AudioDumpManager();

  // Initialize dump file names
  void InitDumpFileNames();

  // Dump audio frame to file based on the processing stage
  void DumpAudioToFile(const std::string& path,
                       const AudioFormat& format,
                       const AudioFrame& frame);

  // Dump audio at different processing stages
  void DumpInputAudio(const AudioFormat& format, const AudioFrame& frame);
  void DumpAfterMute(const AudioFormat& format, const AudioFrame& frame);
  void DumpAfterBalance(const AudioFormat& format, const AudioFrame& frame);
  void DumpAfterMono(const AudioFormat& format, const AudioFrame& frame);
  void DumpAfterFilter(const AudioFormat& format, const AudioFrame& frame);
  void DumpAfterVolume(const AudioFormat& format, const AudioFrame& frame);

 private:
  std::string id_;
  std::string dump_file_name_in_;
  std::string dump_file_name_after_mute_;
  std::string dump_file_name_after_balance_;
  std::string dump_file_name_after_mono_;
  std::string dump_file_name_after_filter_;
  std::string dump_file_name_after_volume_;

  std::unique_ptr<WAVWriter> dump_audio_in_;
  std::unique_ptr<WAVWriter> dump_audio_after_mute_;
  std::unique_ptr<WAVWriter> dump_audio_after_balance_;
  std::unique_ptr<WAVWriter> dump_audio_after_mono_;
  std::unique_ptr<WAVWriter> dump_audio_after_filter_;
  std::unique_ptr<WAVWriter> dump_audio_after_volume_;
};

}  // namespace mediasdk