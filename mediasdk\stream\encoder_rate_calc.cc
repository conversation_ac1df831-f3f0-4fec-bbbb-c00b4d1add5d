#include "encoder_rate_calc.h"
#include <nlohmann/json.hpp>
#include "mediasdk/component_center.h"
#include "mediasdk/data_center/data_center.h"
#include "mediasdk/utils/time_helper.h"

namespace {
constexpr int kIntervalOneSecond = 1000;
}  // namespace

namespace mediasdk {

void EncoderRateCalc::StartEncode() {
  start_encode_time_ms_ = milli_now();
}

void EncoderRateCalc::OnEncodeResult(bool success) {
  const auto now_ms = milli_now();
  if (const auto cost_ms = now_ms - start_encode_time_ms_;
      max_encode_cost_ < cost_ms) {
    max_encode_cost_ = cost_ms;
  }
  if (success) {
    OnEncodeSuccess();
  } else {
    OnEncodeError();
  }
  if (last_time_stamp_ms_ <= 0) {
    last_time_stamp_ms_ = now_ms;
  }
  if (const auto duration = now_ms - last_time_stamp_ms_;
      duration > kIntervalOneSecond) {
    CalcAndReport();
  }
}

void EncoderRateCalc::OnEncodeSuccess() {
  encode_num_++;
}

void EncoderRateCalc::OnEncodeError() {
  encode_err_num_++;
}

void EncoderRateCalc::CalcAndReport() {
  const auto now_ms = milli_now();
  double duration = (double)(now_ms - last_time_stamp_ms_) / 1000.0;
  auto num = encode_num_ - last_encode_num_;
  double encode_fps = num / duration;
  double encode_err_fps = encode_err_num_ - last_encode_err_num_;

  Report(encode_fps, encode_err_fps, max_encode_cost_);
  max_encode_cost_ = 0;
  last_encode_err_num_ = encode_err_num_;
  last_encode_num_ = encode_num_;
  last_time_stamp_ms_ = now_ms;
}

void EncoderRateCalc::Report(double fps, double err_fps, int max_cost) {
  EncoderStatistics info;
  info.encode_fps = fps;
  info.error_fps = err_fps;
  info.encode_process_time = max_cost;
  info.name = codec_name_;
  auto dc = com::GetDataCenter();
  if (dc) {
    dc->SetEncoderStatistics(sink_id_, info);
  }
}
}  // namespace mediasdk