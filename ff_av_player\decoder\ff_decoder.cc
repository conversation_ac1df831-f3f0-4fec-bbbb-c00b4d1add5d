#include "ff_decoder.h"
#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <mediasdk/utils/time_helper.h>
#include "ffmpeg_common.h"

extern "C" {
#include <libavutil/dict.h>
#include <libavutil/mem.h>
}

static void null_av_buffer_free_callback(void* opaque, uint8_t* data) {
  // do nothing now, reserve for debug
  // LOG(INFO) << "temporary av_buffer free callback :"
  //           << " opaque [" << opaque << "]"
  //           << " data [" << data << "]";
}

AVPacket* FFDecoder::BuildPacketFromMemory(
    std::shared_ptr<AVPacketRAIIFree>& buffered_packet,
    const uint8_t* data,
    int32_t data_size,
    bool fake_buffer) {
  if (!buffered_packet) {
    AVPacket* packet = av_packet_alloc();
    buffered_packet = std::make_shared<AVPacketRAIIFree>(packet);
  }
  auto packet = buffered_packet->packet();
  packet->pts = 0;
  packet->dts = 0;
  if (fake_buffer) {
    if (packet->buf) {
      packet->buf = av_buffer_create(nullptr, 0, null_av_buffer_free_callback,
                                     NULL, AV_BUFFER_FLAG_READONLY);
    } else {
    }
  }
  packet->data = const_cast<uint8_t*>(data);
  packet->size = data_size;
  return buffered_packet->packet();
}

FFDecoder::~FFDecoder() {
  Close(nullptr);
}

bool FFDecoder::Open(AVCodecID id, const std::vector<uint8_t>& extra) {
  codec_ = avcodec_find_decoder(id);
  if (!codec_) {
    LOG(ERROR) << "Failed to avcodec_find_decoder[" << id << "]";
    return false;
  }

  codec_context_ = avcodec_alloc_context3(codec_);
  if (!codec_context_) {
    LOG(ERROR) << "Failed to avcodec_alloc_context3";
    return false;
  }
  int32_t ret = 0;
  if (!extra.empty()) {
    if (codec_->capabilities & AV_CODEC_CAP_TRUNCATED)
      codec_context_->flags |= AV_CODEC_FLAG_TRUNCATED;
    codec_context_->codec_id = AV_CODEC_ID_AAC;
    codec_context_->codec_type = AVMEDIA_TYPE_AUDIO;
    codec_context_->extradata_size = extra.size();
    codec_context_->extradata = static_cast<uint8_t*>(
        av_mallocz(extra.size() + AV_INPUT_BUFFER_PADDING_SIZE));
    memcpy(codec_context_->extradata, extra.data(), extra.size());
    ret = avcodec_open2(codec_context_, codec_, NULL);
  } else {
    ret = avcodec_open2(codec_context_, codec_, NULL);
  }
  if (ret != 0) {
    LOG(ERROR) << base::StringPrintf("Failed to avcodec_open2(%d, %s)", ret,
                                     FFErrorToString(ret).c_str());
    return false;
  }
  return true;
}

bool FFDecoder::Open(AVFormatContext* context,
                     int index,
                     bool hardware /* = false*/,
                     bool pure_hw /*= false*/,
                     const char* device_id /* = nullptr*/) {
  codec_context_ = avcodec_alloc_context3(nullptr);
  if (const auto result = avcodec_parameters_to_context(
          codec_context_, context->streams[index]->codecpar);
      result < 0) {
    LOG(ERROR) << "failed to avcodec_open2 [" << FFErrorToString(result);
    DCHECK(false);
    return false;
  }
  codec_context_->pkt_timebase = context->streams[index]->time_base;
  if (codec_context_->codec_id == AV_CODEC_ID_VP8 ||
      codec_context_->codec_id == AV_CODEC_ID_VP9) {
    if (const auto* entry =
            av_dict_get(context->streams[index]->metadata, "alpha_mode",
                        nullptr, AV_DICT_IGNORE_SUFFIX);
        entry && strcmp(entry->value, "1") == 0) {
      const char* name = (codec_context_->codec_id == AV_CODEC_ID_VP8)
                             ? "libvpx"
                             : "libvpx-vp9";
      codec_ = avcodec_find_decoder_by_name(name);
    }
  }
  if (!codec_) {
    codec_ = avcodec_find_decoder(codec_context_->codec_id);
  }
  if (!codec_) {
    LOG(ERROR) << "failed to find codec";
    DCHECK(false);
    return false;
  }
  AVDictionary* opts = nullptr;

  codec_context_->codec_id = codec_->id;
  codec_context_->flags2 |= AV_CODEC_FLAG2_FAST;

  if (hardware) {
    auto TryHwType = [](const AVCodec* codec, AVHWDeviceType type,
                        AVPixelFormat& format) {
      for (int i = 0;; i++) {
        const auto config = avcodec_get_hw_config(codec, i);
        if (!config) {
          return false;
        }
        if (config->methods & AV_CODEC_HW_CONFIG_METHOD_HW_DEVICE_CTX &&
            config->device_type == type) {
          format = config->pix_fmt;
          return true;
        }
      }
      return false;
    };
    constexpr AVHWDeviceType kHWList[] = {
        AV_HWDEVICE_TYPE_DXVA2, AV_HWDEVICE_TYPE_D3D11VA,
        AV_HWDEVICE_TYPE_VAAPI, AV_HWDEVICE_TYPE_VDPAU,
        AV_HWDEVICE_TYPE_QSV,   AV_HWDEVICE_TYPE_NONE};

    constexpr AVHWDeviceType kHWTexture[] = {
        AV_HWDEVICE_TYPE_D3D11VA,
    };
    AVHWDeviceType hw_type = AVHWDeviceType::AV_HWDEVICE_TYPE_NONE;
    if (pure_hw) {
      for (AVHWDeviceType val : kHWTexture) {
        if (val == AV_HWDEVICE_TYPE_NONE)
          break;
        if (TryHwType(codec_, val, hw_pixel_format_)) {
          {
            hw_type = val;
            break;
          }
        }
      }

    } else {
      for (const auto val : kHWList) {
        if (val == AV_HWDEVICE_TYPE_NONE)
          break;
        if (TryHwType(codec_, val, hw_pixel_format_)) {
          {
            hw_type = val;
            break;
          }
        } else {
          LOG(WARNING) << "Try hw type [" << val << "] Failed";
        }
      }
    }

    if (hw_type != AVHWDeviceType::AV_HWDEVICE_TYPE_NONE) {
#ifdef _DEBUG
      AVDictionary* hw_device_opt = {};
      av_dict_set(&hw_device_opt, "debug", "", 0);
#else
#endif
      if (const auto r = av_hwdevice_ctx_create(&hw_context_, hw_type, nullptr,
                                                nullptr, 0);
          r != 0) {
        LOG(ERROR) << "Failed to Open [" << hw_type << "] ret"
                   << " [" << FFErrorToString(r) << "]";
      }
    }
    if (hw_context_) {
      codec_context_->hw_device_ctx = av_buffer_ref(hw_context_);
      LOG(INFO) << "Open Hardware Decode Context Success[" << hw_type << "]";
    }
  } else {
    av_dict_set(&opts, "threads", "auto", 0);
  }

  if (const auto r = avcodec_open2(codec_context_, codec_, &opts); r != 0) {
    LOG(ERROR) << "failed to avcodec_open2 [" << FFErrorToString(r) << "]";
    DCHECK(false);
    return false;
  }
  context->streams[index]->discard = AVDISCARD_DEFAULT;
  for (auto* obs : obs_list_) {
    obs->OnDecodeOpened(this, !!hw_context_);
  }
  return true;
}

void FFDecoder::DecodeTask() {
  if (disable_post_driver_task_) {
    task_filter_.OnTask();
  }

  SynGetFrame();  // receive pending frames
  if (stop_)
    return;

  AVPacket* pkt = packet_list_.FrontPacket();
  if (!pkt)
    return;

  AVPacketRAIIFree v(pkt);

  if (stop_)
    return;
  if (flushing_) {
    packet_list_.Clear();
    return;
  }

  while (!stop_ && !flushing_) {
    if (bool retry = false; SynDecode(*pkt, retry)) {
      SynGetFrame();
      break;
    } else if (retry) {
      continue;
    } else {
      break;
    }
  }
}

void FFDecoder::SynGetFrame() {
  while (!stop_) {
    if (GetPendingReceivePacket() == 0)
      return;
    AVFrame frame = {};
    if (Receive(frame)) {
      packet_list_.PopPendingReceive();
      for (auto* observer : obs_list_) {
        observer->OnFrame(this, frame);
      }
      av_frame_unref(&frame);
    } else {
      break;
    }
  }
}

void FFDecoder::AsnGetFrame(const base::Thread& thread) {
  if (const auto runner = thread.task_runner()) {
    runner->PostTask(FROM_HERE, base::BindOnce(&FFDecoder::SynGetFrame,
                                               base::Unretained(this)));
  }
}

void FFDecoder::AsnDecode(const base::Thread& thread) {
  flushing_ = false;
  if (disable_post_driver_task_ && task_filter_.TaskOverflow()) {
    return;
  }
  if (const auto runner = thread.task_runner()) {
    if (disable_post_driver_task_) {
      task_filter_.OnPostTask();
    }
    runner->PostTask(FROM_HERE, base::BindOnce(&FFDecoder::DecodeTask,
                                               base::Unretained(this)));
  }
}

void FFDecoder::AsnFlushForEnd(const base::Thread& thread) {
  packet_list_.PushFlushPacket();
  AsnDecode(thread);
}

void FFDecoder::AsnDecode(const base::Thread& thread, const AVPacket& packet) {
  packet_list_.PushPacket(packet);
  AsnDecode(thread);
}

bool FFDecoder::SynDecode(const AVPacket& packet, bool& retry) {
  return SynDecodeInternal(packet, retry);
}

bool FFDecoder::SynDecodeInternal(const AVPacket& packet, bool& retry) {
  retry = false;
  if (const int ret = avcodec_send_packet(codec_context_, &packet); ret == 0) {
    return true;
  } else if (ret == AVERROR(EAGAIN)) {
    return false;  // need more input packet
  } else if (ret == AVERROR_EOF) {
    // LOG(INFO) << "decoder EOF";
    return false;  // no more frames
  } else if (ret == AVERROR(ENOMEM)) {
    // no memory ,for example
    // hardware video decode should keep buffered video frame count limit in
    // fixed size
    retry = true;
    return false;
  } else {
    auto now = mediasdk::low_precision_milli_now();
    if (now - pre_log_ms_ > 1000) {  // limit log every seconds
      pre_log_ms_ = now;
      LOG(ERROR) << "failed to avcodec_send_packet [" << FFErrorToString(ret);
    }
    return false;
  }
  return false;
}

void FFDecoder::Close(base::Thread* thread) {
  if (thread) {
    stop_ = true;
    thread->Stop();
  }
  if (codec_context_) {
    avcodec_free_context(&codec_context_);
  }
  if (hw_context_) {
    av_buffer_unref(&hw_context_);
  }
  packet_list_.Clear();
}

// flush last input packet, we can call this before close;
void FFDecoder::Flush(const base::Thread& thread) {
  if (const auto runner = thread.task_runner()) {
#ifdef DEBUG_FAV_PLAYER
    LOG(INFO) << "Send flush task to decode thread";
#endif
    runner->PostTask(FROM_HERE,
                     base::BindOnce(
                         [](FFDecoder* pThis) {
                           pThis->packet_list_.Clear();
                           if (pThis->codec_context_) {
                             avcodec_flush_buffers(pThis->codec_context_);
                           }
                           for (auto* obs : pThis->obs_list_) {
                             obs->OnFlushSuccess(pThis);
                           }
                         },
                         base::Unretained(this)));
  }
}

void FFDecoder::AddObserver(DecoderObserver* observer) {
  obs_list_.push_back(observer);
}

void FFDecoder::RemoveObserver(DecoderObserver* observer) {
  obs_list_.erase(
      std::remove_if(obs_list_.begin(), obs_list_.end(),
                     [observer](auto it) { return it == observer; }),
      obs_list_.end());
}

bool FFDecoder::Receive(AVFrame& frame) {
  return ReceiveInternal(frame);
}

bool FFDecoder::ReceiveInternal(AVFrame& frame) {
  if (const int ret = avcodec_receive_frame(codec_context_, &frame); ret == 0) {
    return true;
  } else if (ret == AVERROR_EOF) {
    packet_list_.Clear();
    for (auto* observer : obs_list_) {
      observer->OnDecoderEOF(this);
    }
    return false;
  } else if (ret == AVERROR(EAGAIN)) {
    for (auto* obs : obs_list_) {
      obs->OnNeedMoreData(this);
    }
    return false;  // need more data
  } else {
    LOG(ERROR) << "failed to avcodec_receive_frame [" << FFErrorToString(ret);
    DCHECK(false);
    return false;
  }
}

int32_t FFDecoder::GetPendingSendPacket() const {
  return packet_list_.GetPendingSendPacket();
}

int32_t FFDecoder::GetPendingReceivePacket() const {
  return packet_list_.GetPendingReceivePacket();
}

void FFDecoder::ClearAndFlushPendingPacket(const base::Thread& thread) {
  flushing_ = true;
  Flush(thread);
}

AVPixelFormat FFDecoder::GetHWPixelFormat() const {
  return hw_pixel_format_;
}