import json
import socket
from urllib import request
from config import CONFIG

def get_tenant_access_token():
    APP_ID = CONFIG.get_feihui_app_id
    APP_SECRET = CONFIG.get_feihui_app_secret
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
    headers = {"Content-Type": "application/json"}
    req_body = {"app_id": APP_ID, "app_secret": APP_SECRET}

    data = bytes(json.dumps(req_body), encoding="utf8")
    req = request.Request(url=url, data=data, headers=headers, method="POST")
    try:
        response = request.urlopen(req)
    except Exception as e:
        print(e.read().decode())
        return ""

    rsp_body = response.read().decode("utf-8")
    rsp_dict = json.loads(rsp_body)
    code = rsp_dict.get("code", -1)
    if code != 0:
        print("get tenant_access_token error, code =", code)
        return ""
    return rsp_dict.get("tenant_access_token", "")


def report(msg_str):
    chat_id = CONFIG.get_feihui_chat_id
    token = get_tenant_access_token()
    url = "https://open.feishu.cn/open-apis/message/v4/send/"
    headers = {
        "Authorization": "Bearer " + token,
        "Content-Type": "application/json; charset=utf-8",
    }
    req_body = {"chat_id": chat_id, "msg_type": "text", "content": {"text": msg_str}}

    data = bytes(json.dumps(req_body), encoding="utf8")
    req = request.Request(url=url, data=data, headers=headers, method="POST")

    try:
        response = request.urlopen(req)
        rsp_body = response.read().decode("utf-8")
        rsp_dict = json.loads(rsp_body)
        print(" send robot mes response: ", rsp_dict, "")
    except Exception as e:
        print("----------Failed to send data to robot, %s ----------" % str(e))

def get_user_id(user_name):
    # TODO: 
    return user_name

def report_start(at_user, version):
    msg_str = "start compile...\r\n"
    msg_str += '<at user_id="' + get_user_id(at_user) + '"></at>\r\n'
    
    current_host_ip = socket.gethostbyname_ex(socket.gethostname())[2][0]
    # TODO: version get build num
    build_num = version
    msg_str += "log url: {}:8080/job/mediasdk_tt_2024/{}/console".format(current_host_ip, build_num)

    msg_str += "\r\n"
    report(msg_str)
