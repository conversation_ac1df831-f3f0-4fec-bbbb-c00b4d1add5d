#pragma once

#include "audio/audio_resample.h"
#include "audio_input.h"
#include "base/threading/thread.h"
#include "base/timer/timer.h"
#include "lyrax/lyrax_audio.h"
#include "lyrax/lyrax_engine.h"
#include "lyrax_audio_processor.h"
#include "lyrax_audio_capture_delegate.h"
#include "source_audio_input.h"
#ifdef ENABLE_AUDIO_DUMP
#include <audio/wav_writer.h>
#endif // ENABLE_AUDIO_DUMP

namespace mediasdk {

class LyraxAudioInput final
    : public SourceAudioInput,
      public AudioInputFrameObserver,
      public LyraxAudioProcessorDelegate,
      public base::SupportsWeakPtr<LyraxAudioInput>,
      public std::enable_shared_from_this<LyraxAudioInput> {
 public:
  static std::shared_ptr<LyraxAudioInput> Create(
      const std::string& id,
      const AudioFormat& output_format);

  LyraxAudioInput(const std::string& id, const AudioFormat& output_format);
  ~LyraxAudioInput();

  void OnAudio(const AudioFormat&, const AudioSourceFrame&) override;
  
  void SetMute(bool) override;

  // 3a control
  bool SetAECOption(const bool enable);
  bool SetANSOption(const int32_t level);
  // mode: 0(auto), 1(disable), 2(enable)
  bool SetRawDataOption(const int32_t mode);

  bool EnableEchoDetection(const int32_t interval);

  void SetRefId(const std::string& id);
  //
  void OnLyraxPreAudioFrame(const AudioFormat& format,
                           const AudioFrame& frame) override;
  void OnLyraxPostAudioFrame(const AudioFormat& format,
                           const AudioFrame& frame) override;

  void OnInputAudioFrame(const std::string& input_id,
                         const AudioFrame& frame,
                         const AudioFormat& format) override;
  void SetAudioProcessor(std::shared_ptr<LyraxAudioProcessor> processor);

  void SignalSourceEvent(const char* json_str) override;

  bool SendActionToAudioSource(const std::string& json_str);

  void AttachSource(std::shared_ptr<AudioInputSource> source) override;

  MediaSDKString GetInitParams() override;

 private:
  std::shared_ptr<LyraxAudioProcessor> GetProcessor();
  bool InterceptAudioEvent(const char* json_str);
  void CheckReferenceAudioInput(bool pre_aec_enable,
                                bool curr_aec_enable,
                                bool pre_echo_detection_enable,
                                bool curr_echo_detection_enable);

 private:
  std::shared_ptr<LyraxAudioProcessor> processor_;
  std::shared_ptr<LyraxAudioCaptureDelegate> capture_delegate_;
  std::mutex pending_action_lock_;
  std::string pending_action_;
  std::mutex lock_processor_;

  std::string ref_id_;
  std::string seted_ref_id_;
  int raw_data_api_option_ = 0; // default is 0(auto)
};

}  // namespace mediasdk