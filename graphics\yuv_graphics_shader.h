#pragma once

#include <array>
#include <mutex>
#include "public/graphics_defines.h"
#include "shader.h"

namespace graphics {

struct YUV_PLANE_DATA_DESC {
  int sub_x = 1;
  int sub_y = 1;
  DXGI_FORMAT format = DXGI_FORMAT::DXGI_FORMAT_UNKNOWN;
  int mul = 0;
};

struct VIDEO_CONVERT_FORMAT {
  mediasdk::PixelFormat format = mediasdk::PixelFormat::kPixelFormatUnspecified;
  int32_t planes = -1;
  const char* vs_name = nullptr;
  const char* ps_name = nullptr;

  std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes> planes_desc;
};

VIDEO_CONVERT_FORMAT GetStaticInfo(mediasdk::PixelFormat format);

struct SHADER_RESOURCE {
  VIDEO_CONVERT_FORMAT format_info;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps;
  Microsoft::WRL::ComPtr<ID3D11VertexShader> vs;
  Microsoft::WRL::ComPtr<ID3D11InputLayout>
      input_layout;  // for GPU Texture to GPU Texture Convert
  Microsoft::WRL::ComPtr<ID3D11Buffer> vertex;  // for texture convert
  Microsoft::WRL::ComPtr<ID3D11Buffer> index;   // for texture convert
  void Destroy();
};

class YUVGraphicsShader : public Shader {
 public:
  static inline const char* SHADER_ID_STRING = "yuv_graphics_shader";

  static std::shared_ptr<Shader> CreateYUVShader(const char* type) {
    if (strcmp(type, SHADER_ID_STRING) == 0) {
      return std::make_shared<YUVGraphicsShader>();
    }
    return nullptr;
  }

  static void EnumPluginsFunction(void* param, EnumPluginCallBack fpn) {
    fpn(param,
        ShaderItem{SHADER_ID_STRING, "shader for draw texture to graphics",
                   YUVGraphicsShader::CreateYUVShader});
  }

 public:
  struct VERTEXTYPE {
    DirectX::XMFLOAT3 position;
    DirectX::XMFLOAT2 texture;
  };

  __declspec(align(16)) struct VS_BUFFER {
    float cx;
    float cy;
  };

  __declspec(align(16)) struct PS_BUFFER {
    DirectX::XMFLOAT4 Y;
    DirectX::XMFLOAT4 U;
    DirectX::XMFLOAT4 V;
    DirectX::XMFLOAT4 min_range;
    DirectX::XMFLOAT4 max_range;
  };

  bool Init(const std::shared_ptr<Device>&) override;
  void Render(mediasdk::PixelFormat format,
              Microsoft::WRL::ComPtr<ID3D11Buffer>& vs,
              Microsoft::WRL::ComPtr<ID3D11Buffer>& ps,
              ID3D11ShaderResourceView* views[kMaxVideoPlanes]);
  ~YUVGraphicsShader() override;
  void Destroy() override;

 private:
  std::shared_ptr<SHADER_RESOURCE> Init(mediasdk::PixelFormat format);
  bool CreateIAS();

 protected:
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice();
  Microsoft::WRL::ComPtr<ID3D11DeviceContext> GetContext();

  bool try_init_ = false;
  bool init_suc_ = false;
  std::shared_ptr<Device> instance_;

  Microsoft::WRL::ComPtr<ID3D11SamplerState> sampler_;
  std::array<std::shared_ptr<SHADER_RESOURCE>, mediasdk::kPixelFormatMax>
      convert_shader_;
};
}  // namespace graphics
