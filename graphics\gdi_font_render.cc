#include "gdi_font_render.h"

#include <base/logging.h>
#include <base/strings/sys_string_conversions.h>

namespace graphics {

#ifndef SAFE_DELETE_OBJECT
#define SAFE_DELETE_OBJECT(p) \
  {                           \
    if (p) {                  \
      ::DeleteObject(p);      \
      (p) = NULL;             \
    }                         \
  }
#endif  // SAFE_DELETE_OBJECT

constexpr int32_t kMinSizeCX = 2;

constexpr int32_t kMinSizeCY = 2;

constexpr int32_t kMaxSizeCX = 16384;

constexpr int32_t kMaxSizeCY = 16384;

constexpr float kEpsilon = 1e-4f;

static inline double clamp(double value, double low, double high) {
  return value < low ? low : (value > high ? high : value);
}

GDIFontRender::GDIFontRender()
    : hdc_(CreateCompatibleDC(NULL)), font_(NULL), gdi_graphics_(hdc_) {}

GDIFontRender::~GDIFontRender() {
  if (hdc_ != NULL) {
    DeleteDC(hdc_);
    hdc_ = NULL;
  }
  font_ptr_ = nullptr;
  if (font_) {
    ::DeleteObject(font_);
    font_ = NULL;
  }
  LOG(INFO) << "[GDIFontRenderImpl] destructor";
}

bool GDIFontRender::UpdateSetting(Text::TextOPT opt) {
  if (!opt.text || !opt.text_face) {
    DCHECK(false);
    return false;
  }
  opt_ = opt;
  MeasureString();

  return true;
}

// https://docs.microsoft.com/zh-cn/windows/desktop/api/gdiplustypes/ne-gdiplustypes-status
inline std::string GDIPlusStatusMessage(Gdiplus::Status status) {
  std::string msg;
  switch (status) {
    case Gdiplus::Ok:
      msg = "Indicates that the method call was successful.";
      break;
    case Gdiplus::GenericError:
      msg =
          "Indicates that there was an error on the method call, which is "
          "identified as something other than those defined by the other "
          "elements of this enumeration.";
      break;
    case Gdiplus::InvalidParameter:
      msg =
          "Indicates that one of the arguments passed to the method was "
          "not valid.";
      break;
    case Gdiplus::OutOfMemory:
      msg =
          "Indicates that the operating system is out of memory and could "
          "not allocate memory to process the method call. For an "
          "explanation of how constructors use the OutOfMemory status, see "
          "the Remarks section at the end of this topic.";
      break;
    case Gdiplus::ObjectBusy:
      msg =
          "Indicates that one of the arguments specified in the API call "
          "is already in use in another thread.";
      break;
    case Gdiplus::InsufficientBuffer:
      msg =
          "Indicates that a buffer specified as an argument in the API "
          "call is not large enough to hold the data to be received.";
      break;
    case Gdiplus::NotImplemented:
      msg = "Indicates that the method is not implemented.";
      break;
    case Gdiplus::Win32Error:
      msg = "Indicates that the method generated a Win32 error.";
      break;
    case Gdiplus::WrongState:
      msg =
          "Indicates that the object is in an invalid state to satisfy the "
          "API call. For example, calling Pen::GetColor from a pen that is "
          "not a single, solid color results in a WrongState status.";
      break;
    case Gdiplus::Aborted:
      msg = "Indicates that the method was aborted.";
      break;
    case Gdiplus::FileNotFound:
      msg =
          "Indicates that the specified image file or metafile cannot be "
          "found.";
      break;
    case Gdiplus::ValueOverflow:
      msg =
          "Indicates that the method performed an arithmetic operation "
          "that produced a numeric overflow.";
      break;
    case Gdiplus::AccessDenied:
      msg =
          "Indicates that a write operation is not allowed on the "
          "specified file.";
      break;
    case Gdiplus::UnknownImageFormat:
      msg = "Indicates that the specified image file format is not known.";
      break;
    case Gdiplus::FontFamilyNotFound:
      msg =
          "Indicates that the specified font family cannot be found. "
          "Either the font family name is incorrect or the font family is "
          "not installed.";
      break;
    case Gdiplus::FontStyleNotFound:
      msg =
          "Indicates that the specified style is not available for the "
          "specified font family.";
      break;
    case Gdiplus::NotTrueTypeFont:
      msg =
          "Indicates that the font retrieved from an HDC or LOGFONT is not "
          "a TrueType font and cannot be used with GDI + .";
      break;
    case Gdiplus::UnsupportedGdiplusVersion:
      msg =
          "Indicates that the version of GDI+ that is installed on the "
          "system is incompatible with the version with which the "
          "application was compiled.";
      break;
    case Gdiplus::GdiplusNotInitialized:
      msg =
          "Indicates that the GDI+API is not in an initialized state. To "
          "function, all GDI+ objects require that GDI+ be in an "
          "initialized state. Initialize GDI+ by calling GdiplusStartup.";
      break;
    case Gdiplus::PropertyNotFound:
      msg =
          "Indicates that the specified property does not exist in the "
          "image.";
      break;
    case Gdiplus::PropertyNotSupported:
      msg =
          "Indicates that the specified property is not supported by the "
          "format of the image and, therefore, cannot be set.";
      break;
#if (GDIPVER >= 0x0110)
    case Gdiplus::ProfileNotFound:
      msg =
          "Indicates that the color profile required to save an image in "
          "CMYK format was not found.";
      break;
#endif  //(GDIPVER >= 0x0110)
    default:
      msg = "Indicates an unknown status was returned.";
      break;
  }
  return msg;
}

bool GDIFontRender::MeasureString() {
  LOGFONTW s = {};
  wcscpy_s(s.lfFaceName, 32, opt_.text_face);
  s.lfHeight = opt_.size;
  s.lfWeight = opt_.bold ? FW_BOLD : FW_DONTCARE;
  s.lfItalic = false;
  s.lfUnderline = false;
  s.lfStrikeOut = false;
  s.lfQuality = ANTIALIASED_QUALITY;
  s.lfCharSet = DEFAULT_CHARSET;
  SAFE_DELETE_OBJECT(font_);
  font_ = CreateFontIndirectW(&s);
  font_ptr_ = std::make_unique<Gdiplus::Font>(hdc_, font_);
  Gdiplus::StringFormat string_format(
      Gdiplus::StringFormat::GenericTypographic());
  Gdiplus::Status status = Gdiplus::Ok;
  Gdiplus::RectF rect;
  std::wstring text = opt_.text;
  text += L'\n';
  GetStringFormat(string_format, false);
  if (!MeasureString(text, string_format, s, rect, size_, opt_.outline,
                     opt_.outline_size))
    return false;

  std::unique_ptr<uint8_t[], base::AlignedFreeDeleter> bitmap_buffer(
      (uint8_t*)base::AlignedAlloc(GetBufferSize(), 32));
  Gdiplus::Bitmap gdi_bitmap((int32_t)size_.x, (int32_t)size_.y,
                             4 * (int32_t)size_.x, PixelFormat32bppARGB,
                             bitmap_buffer.get());
  Gdiplus::Graphics graphics_for_draw(&gdi_bitmap);
  Gdiplus::SolidBrush brush(
      Gdiplus::Color(opt_.text_opacity, GetRValue(opt_.text_color),
                     GetGValue(opt_.text_color), GetBValue(opt_.text_color)));
  if (status != Gdiplus::Ok) {
    LOG(ERROR) << GDIPlusStatusMessage(status);
    return false;
  }
  status = graphics_for_draw.Clear(
      Gdiplus::Color(opt_.bk_opacity, GetRValue(opt_.bk_color),
                     GetGValue(opt_.bk_color), GetBValue(opt_.bk_color)));
  if (status != Gdiplus::Ok) {
    LOG(ERROR) << GDIPlusStatusMessage(status);
    return false;
  }
  status = graphics_for_draw.SetTextRenderingHint(
      Gdiplus::TextRenderingHintAntiAlias);
  if (status != Gdiplus::Ok) {
    LOG(ERROR) << GDIPlusStatusMessage(status);
    return false;
  }
  status =
      graphics_for_draw.SetCompositingMode(Gdiplus::CompositingModeSourceOver);
  if (status != Gdiplus::Ok) {
    LOG(ERROR) << GDIPlusStatusMessage(status);
    return false;
  }
  status = graphics_for_draw.SetSmoothingMode(Gdiplus::SmoothingModeAntiAlias);
  if (status != Gdiplus::Ok) {
    LOG(ERROR) << GDIPlusStatusMessage(status);
    return false;
  }
  if (!text.empty()) {
    if (opt_.outline) {
      DrawStringWithOutline(text, string_format, brush, rect,
                            graphics_for_draw);
    } else {
      status = graphics_for_draw.DrawString(text.c_str(), text.size(),
                                            font_ptr_.get(), rect,
                                            &string_format, &brush);
      if (status != Gdiplus::Ok) {
        LOG(ERROR) << GDIPlusStatusMessage(status);
        return FALSE;
      }
    }
  }
  std::swap(bitmap_buffer_, bitmap_buffer);
  return true;
}

void GDIFontRender::GetStringFormat(Gdiplus::StringFormat& format,
                                    bool vertical) {
  uint32_t flags = Gdiplus::StringFormatFlagsNoFitBlackBox |
                   Gdiplus::StringFormatFlagsMeasureTrailingSpaces;
  format.SetFormatFlags(flags);
  format.SetTrimming(Gdiplus::StringTrimmingWord);
  format.SetAlignment(Gdiplus::StringAlignmentCenter);
  format.SetLineAlignment(Gdiplus::StringAlignmentCenter);
}

bool GDIFontRender::DrawStringWithOutline(
    const std::wstring& text,
    const Gdiplus::StringFormat& string_format,
    const Gdiplus::SolidBrush& brush,
    Gdiplus::RectF& rect,
    Gdiplus::Graphics& graphics_for_draw) {
  rect.Offset(static_cast<Gdiplus::REAL>(opt_.outline_size / 2),
              static_cast<Gdiplus::REAL>(opt_.outline_size / 2));

  Gdiplus::FontFamily family;
  font_ptr_->GetFamily(&family);

  Gdiplus::GraphicsPath path;
  auto status =
      path.AddString(text.c_str(), text.size(), &family, font_ptr_->GetStyle(),
                     font_ptr_->GetSize(), rect, &string_format);
  if (status != Gdiplus::Ok) {
    LOG(ERROR) << GDIPlusStatusMessage(status);
    return false;
  }

  Gdiplus::Pen pen(
      Gdiplus::Color(opt_.outline_opacity, GetRValue(opt_.outline_color),
                     GetGValue(opt_.outline_color),
                     GetBValue(opt_.outline_color)),
      static_cast<Gdiplus::REAL>(opt_.outline_size));
  status = pen.SetLineJoin(Gdiplus::LineJoinRound);
  if (status != Gdiplus::Ok) {
    LOG(ERROR) << GDIPlusStatusMessage(status);
    return false;
  }
  status = graphics_for_draw.DrawPath(&pen, &path);
  if (status != Gdiplus::Ok) {
    LOG(ERROR) << GDIPlusStatusMessage(status);
    return false;
  }
  status = graphics_for_draw.FillPath(&brush, &path);
  if (status != Gdiplus::Ok) {
    LOG(ERROR) << GDIPlusStatusMessage(status);
    return false;
  }
  return true;
}

bool GDIFontRender::MeasureString(const std::wstring& wstr,
                                  const Gdiplus::StringFormat& format,
                                  const LOGFONTW& s,
                                  Gdiplus::RectF& rectF,
                                  DirectX::XMFLOAT2& size,
                                  bool outline,
                                  int32_t outline_size) {
  Gdiplus::Status status = Gdiplus::Ok;
  Gdiplus::RectF boxF;

  status = gdi_graphics_.MeasureString(
      wstr.c_str(), wstr.size() + 1, font_ptr_.get(),
      Gdiplus::PointF(0.0F, 0.0F), &format, &rectF);
  if (status != Gdiplus::Ok) {
    LOG(ERROR) << GDIPlusStatusMessage(status);
    return false;
  }
  boxF = rectF;

  float width_diff = 0.f;
  float height_diff = 0.f;
  auto get_diff = [&]() {
    Gdiplus::RectF rect_char;
    Gdiplus::RectF rect_double_line;
    status = gdi_graphics_.MeasureString(L"W", 2, font_ptr_.get(),
                                         Gdiplus::PointF(0.0F, 0.0F), &format,
                                         &rect_char);
    if (status != Gdiplus::Ok) {
      LOG(ERROR) << GDIPlusStatusMessage(status);
      return false;
    }
    status = gdi_graphics_.MeasureString(L"W\n", 3, font_ptr_.get(),
                                         Gdiplus::PointF(0.0F, 0.0F), &format,
                                         &rect_double_line);
    if (status != Gdiplus::Ok) {
      LOG(ERROR) << GDIPlusStatusMessage(status);
      return false;
    }
    width_diff = rect_double_line.Width - rect_char.Width;

    height_diff = rect_double_line.Height - rect_char.Height;
    return true;
  };
  auto calc_size = [&]() {
    Gdiplus::RectF rect_to_size = rectF;
    rectF.X = 0.0F;
    rectF.Y = 0.0F;

    if (width_diff >= 1.0F)
      width_diff -= 1.0F;
    rectF.Y -= height_diff * 0.5F;
    rect_to_size.Width -= width_diff;
    rect_to_size.Height -= height_diff;

    if (outline) {
      rect_to_size.Width += outline_size;
      rect_to_size.Height += outline_size;
    }

    if (rect_to_size.Height < s.lfHeight) {
      size.y = s.lfHeight;
      rect_to_size.Height = float(s.lfHeight);
    } else {
      size.y = int32_t(rect_to_size.Height + kEpsilon);
    }
    size.x = int32_t(rect_to_size.Width + kEpsilon);
    size.x += (int32_t)size.x % 2;
    size.y += (int32_t)size.y % 2;
    size.x = clamp(size.x, kMinSizeCX, kMaxSizeCX);
    size.y = clamp(size.y, kMinSizeCY, kMaxSizeCY);
  };
  get_diff();
  calc_size();
  rectF.Width = boxF.Width;
  rectF.Height = boxF.Height;
  return true;
}

}  // namespace graphics
