#pragma once

#include <cstdint>

#include <audio/audio_input_change_observer.h>
#include <mediasdk/audio/mixed_audio_observer.h>
#include <mediasdk/callback_utils.h>
#include <mediasdk/component.h>
#include <mediasdk/public/mediasdk_defines_audio.h>
#include "public/hook_api/custom_audio_input_delegate.h"

namespace mediasdk {

class AudioInput;
class AudioInputFrameObserver;
class AudioInputSource;

class AudioController : public Component,
                        public base::SupportsWeakPtr<AudioController> {
 public:
  static constexpr char kComponentName[] = "AudioController";

  // Component:
  ThreadID GetExecThreadId() override { return ThreadID::AUDIO; }

  virtual void CreateAudioInput(const std::string& id,
                                const CreateAudioParams& params,
                                MSCallbackBool callback) = 0;

  virtual void CreateLyraxAudioInput(const std::string& id,
                                     const CreateAudioParams& params,
                                     MSCallbackBool callback) = 0;

  virtual ResultBoolBool AddAudioInputToTrack(const std::string& id,
                                              uint32_t track) = 0;

  virtual ResultBoolBool RemoveAudioInputFromTrack(const std::string& id,
                                                   uint32_t track) = 0;

  virtual void DestroyAudioInputFromVisual(const std::string& ids) = 0;

  virtual void DestroyMultiAudioInputFromVisual(
      const std::vector<std::string>& ids) = 0;

  virtual void DestroyAllAudioInputFromVisual() = 0;

  virtual std::shared_ptr<AudioInput> CreateAudioInputWithSource(
      const std::string& id,
      uint32_t track_id,
      std::shared_ptr<AudioInputSource> source) = 0;

  virtual void DestroyAudioInput(const std::string& id,
                                 MSCallbackBool callback) = 0;

  virtual bool SetAudioInputParams(const std::string& id,
                                   const AudioInputParams& param) = 0;

  virtual bool SetVolume(const std::string& id, float volume) = 0;

  virtual ResultBoolFloat GetVolume(const std::string& id) = 0;

  virtual bool SetBalance(const std::string& id, float balance) = 0;

  virtual ResultBoolFloat GetBalance(const std::string& id) = 0;

  virtual bool SetMute(const std::string& id, bool mute) = 0;

  virtual ResultBoolBool GetMute(const std::string& id) = 0;

  virtual bool SetDeviceMute(const std::string& id, bool mute) = 0;

  virtual ResultBoolBool GetDeviceMute(const std::string& id) = 0;

  virtual ResultBoolInt32 GetSyncOffset(const std::string& id) = 0;

  virtual bool SetSyncOffset(const std::string& id, int32_t sync_offset) = 0;

  virtual ResultBoolUint32 GetMonitorType(const std::string& id) = 0;

  virtual bool SetMonitorType(const std::string& id, int32_t type) = 0;

  virtual bool SetMono(const std::string& id, bool mono) = 0;

  virtual ResultBoolBool GetMono(const std::string& id) = 0;

  virtual bool SetInterval(const std::string& id, int32_t interval) = 0;

  virtual bool SetAudioInputRenderDeviceID(const std::string& audio_input_id,
                                          const std::string& render_device_id) = 0;

  virtual MediaSDKString GetFirstAudio() = 0;

  virtual void DestroyAllAudioInput(MSCallbackBool common) = 0;

  // audio filter related APIs
  virtual void CreateAudioFilter(const std::string& audio_filter_id,
                                 const std::string& audio_filter_name,
                                 const std::string& audio_input_id,
                                 const std::string& json_params,
                                 MSCallbackBool callback) = 0;

  virtual void DestroyAudioFilter(const std::string& audio_filter_id,
                                  const std::string& audio_input_id,
                                  MSCallbackBool callback) = 0;

  virtual void SetAudioFilterEnable(const std::string& audio_filter_id,
                                    const std::string& audio_input_id,
                                    bool enable,
                                    MSCallbackBool callback) = 0;

  virtual bool UpdatePCMAudioDatas(const std::string& audio_input_id,
                                   const std::string& pcm_audio_datas) = 0;

  virtual void IsAudioFilterEnable(const std::string& audio_filter_id,
                                   const std::string& audio_input_id,
                                   MSCallback<ResultBoolBool> callback) = 0;

  virtual void SetAudioFilterProperty(const std::string& audio_filter_id,
                                      const std::string& audio_input_id,
                                      const std::string& key,
                                      const std::string& value,
                                      MSCallbackBool callback) = 0;

  virtual void GetAudioFilterProperty(
      const std::string& audio_filter_id,
      const std::string& audio_input_id,
      const std::string& key,
      MSCallback<ResultBoolString> callback) = 0;

  virtual void AudioFilterAction(const std::string& audio_filter_id,
                                 const std::string& audio_input_id,
                                 const std::string& action,
                                 const std::string& param,
                                 MSCallback<ResultBoolString> callback) = 0;

  virtual void AddMixedAudioObserver(
      uint32_t track_id,
      std::shared_ptr<MixedAudioObserver> observer) = 0;

  virtual void RemoveMixedAudioObserver(
      uint32_t track_id,
      std::shared_ptr<MixedAudioObserver> observer) = 0;

  virtual void AddInputAudioObserver(
      std::string audio_input_id,
      std::shared_ptr<AudioInputFrameObserver> observer) = 0;

  virtual void RemoveInputAudioObserver(
      std::string audio_input_id,
      std::shared_ptr<AudioInputFrameObserver> observer) = 0;

  virtual MediaSDKString EnumAudioInputsInTrack(uint32_t track_id) = 0;

  virtual std::string GetRenderTargetDeviceID() = 0;

  virtual void SetRenderTargetDeviceID(const std::string& id) = 0;

  virtual bool SetAudioInputReferenceId(
      const std::string& audio_input_id,
      const std::string& audio_ref_input_id) = 0;

  virtual bool SetAudioInputAECOption(const std::string& audio_input_id,
                                      const bool enable) = 0;

  virtual bool SetAudioInputANSOption(const std::string& audio_input_id,
                                      const int32_t level) = 0;

  virtual bool SetAudioInputRawDataOption(const std::string& audio_input_id,
                                          const int32_t mode) = 0;

  virtual bool EnableAudioInputEchoDetection(const std::string& audio_input_id,
                                          const int32_t interval) = 0;
                                          
  virtual std::shared_ptr<MediaSDKString> GetAudioInputListInfo() = 0;

  virtual void SetAudioTrackDelayMs(uint32_t track_id, const int64_t ms) = 0;

  virtual void CreateCustomAudioInput(
      const std::string& id,
      uint32_t track_id,
      hook_api::CustomAudioInputDelegate* delegate,
      MSCallbackBool callback) = 0;
};

}  // namespace mediasdk
