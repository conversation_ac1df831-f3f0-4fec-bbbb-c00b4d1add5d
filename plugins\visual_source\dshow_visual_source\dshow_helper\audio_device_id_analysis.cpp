#include "dshow_helper.h"
#include "base/logging.h"
#include "base/strings/utf_string_conversions.h"
#include <iostream>

namespace mediasdk {

// 分析音频设备ID的实际格式
void AnalyzeAudioDeviceIDs() {
  LOG(INFO) << "=== 分析音频设备ID的实际格式 ===";
  
  // 枚举所有音频设备
  std::vector<DShowDeviceName> audio_devices;
  if (!DShowEnumDevice(audio_devices, CLSID_AudioInputDeviceCategory)) {
    LOG(ERROR) << "Failed to enumerate audio devices";
    return;
  }
  
  LOG(INFO) << "找到 " << audio_devices.size() << " 个音频设备:";
  
  for (size_t i = 0; i < audio_devices.size(); ++i) {
    const auto& device = audio_devices[i];
    LOG(INFO) << "\n--- 音频设备 " << (i + 1) << " ---";
    LOG(INFO) << "Name: " << base::WideToUTF8(device.name);
    LOG(INFO) << "ID: " << base::WideToUTF8(device.id);
    
    // 分析ID格式
    std::wstring usb_path = ExtractUSBPath(device.id);
    if (!usb_path.empty()) {
      LOG(INFO) << "USB路径: " << base::WideToUTF8(usb_path);
      LOG(INFO) << "格式类型: USB设备路径";
    } else {
      LOG(INFO) << "USB路径: (无)";
      LOG(INFO) << "格式类型: 友好名称";
      
      // 尝试从名称中提取设备信息
      std::wstring extracted_info = ExtractUSBInfoFromName(device.name);
      if (extracted_info != device.name) {
        LOG(INFO) << "提取的设备信息: " << base::WideToUTF8(extracted_info);
      }
    }
    
    // 检查是否包含常见的设备型号
    std::wstring core_name = ExtractCoreDeviceName(device.name);
    LOG(INFO) << "核心设备名称: " << base::WideToUTF8(core_name);
  }
}

// 测试音频设备与视频设备的匹配
void TestAudioVideoMatching() {
  LOG(INFO) << "\n=== 测试音频设备与视频设备的匹配 ===";
  
  // 模拟真实的设备信息
  std::vector<std::pair<std::string, std::pair<DShowDeviceName, DShowDeviceName>>> test_cases = {
    {
      "C922一体化设备",
      {
        {L"C922 Pro Stream Webcam", L"\\\\?\\usb#vid_046d&pid_085c&mi_00#6&348515e9&0&0000#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\\global"},
        {L"麦克风 (6- C922 Pro Stream Webcam)", L"麦克风 (6- C922 Pro Stream Webcam)"}
      }
    },
    {
      "Logitech C920",
      {
        {L"Logitech HD Pro Webcam C920", L"\\\\?\\usb#vid_046d&pid_082d&mi_00#7&1234567&0&0000#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\\global"},
        {L"Microphone (HD Pro Webcam C920)", L"Microphone (HD Pro Webcam C920)"}
      }
    },
    {
      "Elgato采集卡",
      {
        {L"Elgato Game Capture HD60 S", L"\\\\?\\usb#vid_0fd9&pid_0063&mi_00#8&abcdefg&0&0000#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\\global"},
        {L"Elgato Audio Capture", L"Elgato Audio Capture"}
      }
    },
    {
      "独立音频设备",
      {
        {L"USB Camera", L"\\\\?\\usb#vid_1234&pid_5678&mi_00#9&xyz123&0&0000#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\\global"},
        {L"USB Audio Device", L"USB Audio Device"}
      }
    }
  };
  
  for (const auto& test_case : test_cases) {
    LOG(INFO) << "\n--- 测试案例: " << test_case.first << " ---";
    
    const auto& video_device = test_case.second.first;
    const auto& audio_device = test_case.second.second;
    
    LOG(INFO) << "视频设备:";
    LOG(INFO) << "  Name: " << base::WideToUTF8(video_device.name);
    LOG(INFO) << "  ID: " << base::WideToUTF8(video_device.id);
    
    LOG(INFO) << "音频设备:";
    LOG(INFO) << "  Name: " << base::WideToUTF8(audio_device.name);
    LOG(INFO) << "  ID: " << base::WideToUTF8(audio_device.id);
    
    // 测试各种匹配策略
    std::vector<DeviceMatchStrategy> strategies = {
      DeviceMatchStrategy::EXACT_ID_MATCH,
      DeviceMatchStrategy::USB_PATH_MATCH,
      DeviceMatchStrategy::FUZZY_NAME_MATCH,
      DeviceMatchStrategy::NAME_PATTERN_MATCH
    };
    
    LOG(INFO) << "匹配结果:";
    float best_confidence = 0.0f;
    DeviceMatchStrategy best_strategy = DeviceMatchStrategy::EXACT_ID_MATCH;
    
    for (const auto& strategy : strategies) {
      float confidence = CalculateDeviceMatchConfidence(video_device, audio_device, strategy);
      
      std::string strategy_name;
      switch (strategy) {
        case DeviceMatchStrategy::EXACT_ID_MATCH: strategy_name = "EXACT_ID_MATCH"; break;
        case DeviceMatchStrategy::USB_PATH_MATCH: strategy_name = "USB_PATH_MATCH"; break;
        case DeviceMatchStrategy::FUZZY_NAME_MATCH: strategy_name = "FUZZY_NAME_MATCH"; break;
        case DeviceMatchStrategy::NAME_PATTERN_MATCH: strategy_name = "NAME_PATTERN_MATCH"; break;
      }
      
      LOG(INFO) << "  " << strategy_name << ": " << confidence;
      
      if (confidence > best_confidence) {
        best_confidence = confidence;
        best_strategy = strategy;
      }
    }
    
    if (best_confidence > 0.5f) {
      LOG(INFO) << "✓ 推荐匹配 (最佳置信度: " << best_confidence << ")";
    } else {
      LOG(INFO) << "✗ 不推荐匹配 (最佳置信度: " << best_confidence << ")";
    }
  }
}

// 测试从音频设备名称提取信息的功能
void TestAudioNameExtraction() {
  LOG(INFO) << "\n=== 测试从音频设备名称提取信息 ===";
  
  std::vector<std::wstring> test_names = {
    L"麦克风 (6- C922 Pro Stream Webcam)",
    L"Microphone (HD Pro Webcam C920)",
    L"Microphone (2- Logitech BRIO)",
    L"Elgato Audio Capture",
    L"USB Audio Device",
    L"Realtek High Definition Audio",
    L"麦克风 (USB Audio Device)",
    L"Line In (Elgato Game Capture HD60 S)"
  };
  
  for (const auto& name : test_names) {
    LOG(INFO) << "\n原始名称: " << base::WideToUTF8(name);
    
    std::wstring extracted_info = ExtractUSBInfoFromName(name);
    LOG(INFO) << "提取信息: " << base::WideToUTF8(extracted_info);
    
    std::wstring core_name = ExtractCoreDeviceName(extracted_info);
    LOG(INFO) << "核心名称: " << base::WideToUTF8(core_name);
  }
}

// 测试完整的音频格式枚举流程
void TestCompleteAudioEnumeration() {
  LOG(INFO) << "\n=== 测试完整的音频格式枚举流程 ===";
  
  DShowDeviceName test_video_device;
  test_video_device.id = L"\\\\?\\usb#vid_046d&pid_085c&mi_00#6&348515e9&0&0000#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\\global";
  test_video_device.name = L"C922 Pro Stream Webcam";
  
  LOG(INFO) << "测试视频设备: " << base::WideToUTF8(test_video_device.name);
  
  ExtendedAudioFormatResult result;
  if (GetAudioFormatsForVideoDevice(test_video_device, result)) {
    LOG(INFO) << "✓ 成功找到音频格式信息";
    
    if (result.has_integrated_audio) {
      LOG(INFO) << "设备类型: 传统一体化设备";
    } else {
      LOG(INFO) << "设备类型: 关联设备";
      LOG(INFO) << "找到 " << result.audio_results.size() << " 个关联音频设备";
      
      for (const auto& audio_result : result.audio_results) {
        LOG(INFO) << "\n音频设备: " << base::WideToUTF8(audio_result.audio_device.name);
        LOG(INFO) << "设备ID: " << base::WideToUTF8(audio_result.audio_device.id);
        LOG(INFO) << "置信度: " << audio_result.confidence_score;
        LOG(INFO) << "匹配策略: " << static_cast<int>(audio_result.match_strategy);
        LOG(INFO) << "匹配详情: " << audio_result.match_details;
        LOG(INFO) << "音频格式数量: " << audio_result.formats.size();
      }
    }
  } else {
    LOG(INFO) << "✗ 未找到音频格式信息";
    LOG(INFO) << "错误: " << result.error_message;
    
    if (!result.suggestions.empty()) {
      LOG(INFO) << "建议:";
      for (const auto& suggestion : result.suggestions) {
        LOG(INFO) << "  - " << suggestion;
      }
    }
  }
}

}  // namespace mediasdk

// 主函数
int main() {
  // 初始化COM
  ::CoInitialize(NULL);
  
  try {
    mediasdk::AnalyzeAudioDeviceIDs();
    mediasdk::TestAudioVideoMatching();
    mediasdk::TestAudioNameExtraction();
    mediasdk::TestCompleteAudioEnumeration();
  } catch (const std::exception& e) {
    LOG(ERROR) << "Exception: " << e.what();
  }
  
  // 清理COM
  ::CoUninitialize();
  
  return 0;
}
