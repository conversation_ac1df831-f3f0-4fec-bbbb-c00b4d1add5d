#pragma once
#include <vector>

#include <base/check.h>
#include <base/logging.h>
#include "texture.h"

#include "media/video/r_g_b_a.h"

#include <media/video/media_video.h>

// for auto test yuv convert logic
// it's too dangerous to modify yuv_2_BGRA BGRA_2_yuv convert logic

class BGRATextureFrame {
 private:
  int width;
  int height;
  std::vector<r_g_b_a> data_;

 public:
  r_g_b_a ReadData(int32_t cx, int32_t cy) const {
    r_g_b_a bgra = data_[cy * width + cx];
    r_g_b_a rgba;
    rgba.r = bgra.b;
    rgba.g = bgra.g;
    rgba.b = bgra.r;
    rgba.a = bgra.a;
    return rgba;
  }

  r_g_b_a ReadData(const XMFLOAT2& pos) const {
    return ReadData((int32_t)pos.x, (int32_t)pos.y);
  }

  void ReverseData(int32_t cx, int32_t cy) {
    width = cx;
    height = cy;
    data_.resize(cy * cx);
  }

  void* data() { return data_.data(); }

  static BGRATextureFrame GetTextureFrameFromDXTexture(
      graphics::Texture& texture) {
    auto size = texture.GetSize();
    BGRATextureFrame ret;
    ret.ReverseData(size.x, size.y);

    auto suc = texture.GetReadTexture()->CopyTo((uint8_t*)ret.data(),
                                                size.x * 4, size.y);
    DCHECK(suc);
    return ret;
  }
};

inline bool check_value(BGRATextureFrame& texture, pos_and_r_g_b_a_value& prefer) {
  auto color = texture.ReadData(prefer.pos);
  if (color != prefer.color) {
    LOG(ERROR) << "prefer error[" << prefer.pos.x << "," << prefer.pos.y
               << "] prefer [" << prefer.color.ToString() << "] get ["
               << color.ToString() << "]";
    DCHECK(false);
    return false;
  } else {
    LOG(ERROR) << "prefer suc[" << prefer.pos.x << "," << prefer.pos.y << "] ["
               << prefer.color.ToString() << "] [" << color.ToString() << "]";
    return true;
  }
}

using check_list = std::vector<pos_and_r_g_b_a_value>;

inline bool check_values(BGRATextureFrame& texture, check_list& list) {
  for (auto& check : list) {
    if (!check_value(texture, check)) {
      return false;
    }
  }
  return true;
}

