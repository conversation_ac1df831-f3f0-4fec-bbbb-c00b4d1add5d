#pragma once

#include <stdint.h>

#include "mediasdk/public/mediasdk_binary_data.hpp"
#include "mediasdk/public/mediasdk_export.h"
#include "mediasdk/public/mediasdk_string.hpp"

namespace mediasdk {

#pragma pack(push, 1)

struct StreamServiceSourceBandwidthInfo {
  int bw = 0;
  int rtt = 0;
  float loss_rate = 0.0f;
};

#pragma pack(pop)

class StreamServiceProxy {
 public:
  virtual ~StreamServiceProxy() {}

  virtual MediaSDKString GetAudioEncodeConfig() = 0;

  virtual MediaSDKString GetVideoEncodeConfig() = 0;

  virtual MediaSDKBinaryData GetAudioEncodeExtraData() = 0;

  virtual MediaSDKBinaryData GetVideoEncodeExtraData() = 0;

  virtual MediaSDKString GetCdnList() = 0;

  virtual void OnBandwidthInfo(
      const StreamServiceSourceBandwidthInfo& info) = 0;
};

}  // namespace mediasdk
