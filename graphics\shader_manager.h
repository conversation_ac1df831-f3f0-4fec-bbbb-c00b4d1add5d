#pragma once
#include <memory>
#include "shader.h"
#include "shader_center.h"

namespace graphics {
class Device;

class ShaderManager {
 public:
  ShaderManager();

  template <typename T>
  T* GetOrCreateShader(const std::shared_ptr<Device>& ins) {
    auto shader =
        shader_factory_->GetOrCreateShader<T>(T::SHADER_ID_STRING);
    if (!shader) {
      return nullptr;
    }
    if (!shader->Init(ins)) {
      return nullptr;
    }
    return shader;
  }

  Shader* GetOrCreateShader(std::shared_ptr<Device> ins,
                                             const char* type) {
    auto shader = shader_factory_->GetOrCreateShader<Shader>(type);
    if (!shader) {
      return nullptr;
    }

    if (!shader->Init(ins)) {
      return nullptr;
    }

    return shader;
  }

  void ClearShaders() {}

 private:
  std::unique_ptr<ShaderFactory> shader_factory_;
};

}  // namespace graphics

