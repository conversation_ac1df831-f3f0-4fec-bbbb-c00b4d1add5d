#pragma once

#include <cstdint>
#include <optional>
#include <string_view>
#include "base/logging.h"
#include "mediasdk/public/mediasdk_defines.h"
#include "mediasdk/public/mediasdk_defines_video.h"
#include "mediasdk_json.hpp"
#include "nlohmann/json.hpp"

namespace mediasdk {

struct VideoEncoderCalcStorage {
  uint32_t bitrate_limit = 0;
  float k = 1.0;
  float b = 0.0;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_WITH_DEFAULT(VideoEncoderCalcStorage,
                                                bitrate_limit,
                                                k,
                                                b);

struct VideoEncoderConfigStorage {
  std::string id;
  std::string name;
  std::string preset;
  std::string profile;
  std::string usage;
  std::string rate_control;
  std::optional<std::vector<VideoEncoderCalcStorage>> max_bitrate_strategy_list;
  std::optional<std::vector<VideoEncoderCalcStorage>> buffer_size_strategy_list;

  uint32_t output_width = 0;
  uint32_t output_height = 0;
  uint32_t bitrate = 0;
  uint32_t key_sec = 2;
  uint32_t pixel_format = 0;
  uint32_t b_frames = 0;
  uint32_t color_space = 0;
  uint32_t timebase = 0;
  uint32_t video_range = 0;
  uint32_t stream_type = 0;
  uint32_t lookahead = 0;
  uint32_t max_cu_size = 0;
  uint32_t min_cu_size = 0;
  uint32_t num_ref0 = 0;
  uint32_t num_ref1 = 0;
  std::optional<uint32_t> adaptive_output_width;
  std::optional<uint32_t> adaptive_output_height;
  std::optional<uint32_t> adaptive_timebase;

  long adapter_luid_high = 0;
  unsigned long adapter_luid_low = 0;

  bool temporal_aq = true;
  bool is_hardware = false;
  bool enable_bref = false;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_WITH_DEFAULT(VideoEncoderConfigStorage,
                                                id,
                                                name,
                                                preset,
                                                profile,
                                                usage,
                                                rate_control,
                                                output_width,
                                                output_height,
                                                bitrate,
                                                key_sec,
                                                pixel_format,
                                                b_frames,
                                                color_space,
                                                timebase,
                                                video_range,
                                                stream_type,
                                                max_cu_size,
                                                min_cu_size,
                                                num_ref0,
                                                num_ref1,
                                                adaptive_output_width,
                                                adaptive_output_height,
                                                adaptive_timebase,
                                                adapter_luid_high,
                                                adapter_luid_low,
                                                lookahead,
                                                temporal_aq,
                                                is_hardware,
                                                enable_bref,
                                                max_bitrate_strategy_list,
                                                buffer_size_strategy_list);

class VideoEncoderConfig {
 public:
  bool FromJsonString(std::string_view json_str) {
    try {
      storage_ = nlohmann::json::parse(json_str);
      return true;
    } catch (const std::exception& e) {
      return false;
    }
  }

  explicit VideoEncoderConfig() = default;

  explicit VideoEncoderConfig(VideoEncoderConfigStorage storage)
      : storage_(std::move(storage)) {}

  ~VideoEncoderConfig() = default;

  std::string GetVideoEncoderId() const { return storage_.id; }

  std::string GetEncoderName() const { return storage_.name; }

  uint32_t GetOutputWidth() const { return storage_.output_width; }

  void SetOutputWidth(const uint32_t value) { storage_.output_width = value; }

  uint32_t GetOutputHeight() const { return storage_.output_height; }

  void SetOutputHeight(const uint32_t value) { storage_.output_height = value; }

  uint32_t GetBitRateKbps() const { return storage_.bitrate; }

  void SetBitRateKbps(const uint32_t value) { storage_.bitrate = value; }

  uint32_t GetMaxBitRateKbps() const {
    return GenerateMaxBitrateKbps(GetBitRateKbps());
  }

  uint32_t GetKeySec() const { return storage_.key_sec; }

  void SetKeySec(const uint32_t value) { storage_.key_sec = value; }

  uint32_t GetGOP() const { return storage_.key_sec * storage_.timebase; }

  PixelFormat GetPixelFormat() const {
    return static_cast<PixelFormat>(storage_.pixel_format);
  }

  void SetPixelFormat(PixelFormat pixel_format) {
    storage_.pixel_format = pixel_format;
  }

  uint32_t GetBFramesInGOP() const { return storage_.b_frames; }

  void SetBFramesInGOP(const uint32_t value) { storage_.b_frames = value; }

  std::string GetPreset() const { return storage_.preset; }

  uint32_t GetLookahead() const { return storage_.lookahead; }

  bool GetTemporalAQ() const { return storage_.temporal_aq; }

  bool GetEnableBref() const { return storage_.enable_bref; }

  uint32_t GetMaxCuSize() { return storage_.max_cu_size; }

  void SetMaxCuSize(const uint32_t value) { storage_.max_cu_size = value; }

  uint32_t GetMinCuSize() { return storage_.min_cu_size; }

  void SetMinCuSize(const uint32_t value) { storage_.min_cu_size = value; }

  uint32_t GetNumRef0() { return storage_.num_ref0; }

  void SetNumRef0(const uint32_t value) { storage_.num_ref0 = value; }

  uint32_t GetNumRef1() { return storage_.num_ref1; }

  void SetMinNumRef1(const uint32_t value) { storage_.num_ref1 = value; }

  uint32_t GetVBVBufferSize() {
    return GenerateVBVBufferSizeKbps(GetBitRateKbps());
  }

  std::string GetProfile() const { return storage_.profile; }

  void SetProfile(const std::string& value) { storage_.profile = value; }

  std::string GetUsage() const { return storage_.usage; }

  void SetUsage(const std::string& value) { storage_.usage = value; }

  std::string GetRateControl() const { return storage_.rate_control; }

  void SetRateControl(const std::string& value) {
    storage_.rate_control = value;
  }

  MSLUID GetAdapterLUID() const {
    return {storage_.adapter_luid_low, storage_.adapter_luid_high};
  }

  void SetAdapterLUID(const MSLUID& luid) {
    storage_.adapter_luid_high = luid.high;
    storage_.adapter_luid_low = luid.low;
  }

  ColorSpace GetColorSpace() const {
    return static_cast<ColorSpace>(storage_.color_space);
  }

  void SetColorSpace(const ColorSpace value) { storage_.color_space = value; }

  uint32_t GetTimeBase() const { return storage_.timebase; }

  void SetTimeBase(const uint32_t value) { storage_.timebase = value; }

  std::optional<uint32_t> GetAdaptiveOutputWidth() const {
    return storage_.adaptive_output_width;
  }

  void SetAdaptiveOutputWidth(std::optional<uint32_t> adaptive_output_width) {
    storage_.adaptive_output_width = adaptive_output_width;
  }

  std::optional<uint32_t> GetAdaptiveOutputHeight() const {
    return storage_.adaptive_output_height;
  }

  void SetAdaptiveOutputHeight(std::optional<uint32_t> adaptive_output_height) {
    storage_.adaptive_output_height = adaptive_output_height;
  }

  std::optional<uint32_t> GetAdaptiveTimeBase() const {
    return storage_.adaptive_timebase;
  }

  void SetAdaptiveTimeBase(std::optional<uint32_t> adaptive_timebase) {
    storage_.adaptive_timebase = adaptive_timebase;
  }

  VideoRange GetVideoRange() const {
    return static_cast<VideoRange>(storage_.video_range);
  }

  void SetVideoRange(const VideoRange value) { storage_.video_range = value; }

  bool GetIsHardWare() const { return storage_.is_hardware; }

  void SetIsHardWare(const bool value) { storage_.is_hardware = value; }

  StreamType GetStreamType() const {
    return static_cast<StreamType>(storage_.stream_type);
  }

  void SetStreamType(const StreamType value) { storage_.stream_type = value; }

  std::string ToString(const int32_t indent = -1) const {
    std::string result;
    try {
      const nlohmann::json json = storage_;
      result = json.dump(indent);
    } catch (const std::exception& e) {
      LOG(ERROR) << "Catch exception: " << e.what();
    }
    return result;
  }

  static uint32_t GenerateDeaultMaxBitrateKbps(const uint32_t bitrate_kbps) {
    return bitrate_kbps <= 4000 ? bitrate_kbps * 1.2f : bitrate_kbps + 1000;
  }

  uint32_t GenerateMaxBitrateKbps(const uint32_t bitrate_kbps) const {
    if (!storage_.max_bitrate_strategy_list ||
        storage_.max_bitrate_strategy_list->empty()) {
      return GenerateDeaultMaxBitrateKbps(bitrate_kbps);
    }
    for (const auto& it : *storage_.max_bitrate_strategy_list) {
      if (bitrate_kbps < it.bitrate_limit) {
        return bitrate_kbps * it.k + it.b;
      }
    }
    return GenerateDeaultMaxBitrateKbps(bitrate_kbps);
  }

  static uint32_t GenerateDefaultVBVBufferSizeKbps(
      const uint32_t bitrate_kbps) {
    return bitrate_kbps <= 4000 ? bitrate_kbps * 1.2f : bitrate_kbps + 1000;
  }

  uint32_t GenerateVBVBufferSizeKbps(const uint32_t bitrate_kbps) const {
    if (!storage_.buffer_size_strategy_list ||
        storage_.buffer_size_strategy_list->empty()) {
      return GenerateDefaultVBVBufferSizeKbps(bitrate_kbps);
    }
    for (const auto& it : *storage_.buffer_size_strategy_list) {
      if (bitrate_kbps < it.bitrate_limit) {
        return bitrate_kbps * it.k + it.b;
      }
    }
    return GenerateDefaultVBVBufferSizeKbps(bitrate_kbps);
  }

  void SetStorage(const VideoEncoderConfigStorage& storage) {
    storage_ = storage;
  }

  VideoEncoderConfigStorage GetStorage() const { return storage_; }

 private:
  VideoEncoderConfigStorage storage_;
};

}  // namespace mediasdk
