#include "fav_player.h"
#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <mediasdk/utils/time_helper.h>
#include "ffmpeg_common.h"

// #define DEBUG_FAV_PLAYER

namespace {
constexpr int64_t kMsToSeconds = 1000ll;

#define ADD_TASK_NUM(a, b)      \
  {                             \
    DCHECK(a >= 0 && a <= 100); \
    a++;                        \
    b();                        \
  }

#define DEL_TASK_NUM(a) \
  { a--; }

}  // namespace

void FAVPlayer::OnOpen(const FFDemuxer*) {}

FAVPlayer::FAVPlayer(int32_t device_index, bool disable_post_driver_task)
    : device_index_(device_index),
      disable_post_driver_task_(disable_post_driver_task) {
  video_decoder_.SetDisablePostDriverTask(disable_post_driver_task);
  audio_decoder_.SetDisablePostDriverTask(disable_post_driver_task);
}

FAVPlayer::~FAVPlayer() {
  Close();
}

void FAVPlayer::AddObserver(PlayerObserver* observer) {
  observer_list_.push_back(observer);
}

void FAVPlayer::OnAudioStream(const FFDemuxer* demuxer,
                              AVFormatContext* context,
                              int audio_stream_index) {
  // only process video, return
  if (mode_ == kModeViewVideo)
    return;
  if (audio_stream_index < 0)
    return;
  stream_a_ = context->streams[audio_stream_index];
  audio_decoder_.AddObserver(this);

  decode_audio_.Start();

  audio_decoder_.Open(context, audio_stream_index);
}

void FAVPlayer::OnNoAudioStream(const FFDemuxer* demuxer) {
  syn_.SetNoAudio();
}

void FAVPlayer::OnOtherStreamPacket(const FFDemuxer*) {
  ADD_TASK_NUM(task_num_, PostDriverWork)
}

void FAVPlayer::OnNoVideoStream(const FFDemuxer* demuxer) {
  syn_.SetNoVideo();
}

void FAVPlayer::OnVideoStream(const FFDemuxer* demuxer,
                              AVFormatContext* context,
                              int video_stream_index) {
  stream_v_ = context->streams[video_stream_index];
  video_decoder_.AddObserver(this);

  decode_video_.Start();
  std::string device_index_str = std::to_string(device_index_);
  video_decoder_.Open(context, video_stream_index, IsHWDecode(),
                      use_hw_surface_, device_index_str.c_str());
}

void FAVPlayer::OnPacket(const FFDemuxer*, AVPacket& packet, AVMediaType type) {
  if (stop_ || IsSeeking())
    return;

  for (auto* observer : observer_list_) {
    observer->OnPacket(packet);
  }

  switch (type) {
    case AVMEDIA_TYPE_UNKNOWN:
      break;
    case AVMEDIA_TYPE_VIDEO:
      video_decoder_.AsnDecode(decode_video_, packet);
      break;
    case AVMEDIA_TYPE_AUDIO:
      if (HasAudio()) {
        audio_decoder_.AsnDecode(decode_audio_, packet);
      }
      break;
    case AVMEDIA_TYPE_DATA:
      break;
    case AVMEDIA_TYPE_SUBTITLE:
      break;
    case AVMEDIA_TYPE_ATTACHMENT:
      break;
    case AVMEDIA_TYPE_NB:
      break;
    default:
      break;
  }
  ADD_TASK_NUM(task_num_, PostDriverWork)
}

void FAVPlayer::OnStop(const FFDemuxer*) {}

void FAVPlayer::CheckLoopPlayTask() {
  if (demuxer_thread_.task_runner()->RunsTasksInCurrentSequence()) {
    DoCheckLoopPlayTask();
  } else {
    demuxer_thread_.task_runner()->PostTask(
        FROM_HERE, base::BindOnce(&FAVPlayer::DoCheckLoopPlayTask,
                                  base::Unretained(this)));
  }
}

void FAVPlayer::DoCheckLoopPlayTask() {
  if (stop_)
    return;
  if (IsSeeking())
    return;
  if (IsPause())
    return;

  if (CheckLastRenderFrame()) {
    SignalPlayEOF();
    syn_.Pause();
    if (enable_loop_) {
#ifdef DEBUG_FAV_PLAYER
      LOG(INFO) << "enable loop, loop by seek";
      LOG(INFO) << "a ts [" << syn_.GetCurrentClock() << "] v ts ["
                << syn_.GetVideoClock() << "] a-v ["
                << syn_.GetCurrentClock() - syn_.GetVideoClock() << "]";
#endif

      if (CanSeek()) {
        Seek(0, true);
      }
    }
  }
}

// for last n video frame render
bool FAVPlayer::CheckLastRenderFrame() {
  TraceBufferedWork();
  if (demuxer_.IsEOF()) {
    if (stream_v_) {
      if (video_decoder_.GetPendingReceivePacket() ||
          video_decoder_.GetPendingSendPacket()) {
        return false;
      }
    }
    if (HasAudio()) {
      if (audio_decoder_.GetPendingReceivePacket() ||
          audio_decoder_.GetPendingSendPacket()) {
        return false;
      }
    }
    if (v_frame_list_.GetLeftFrameCnt()) {
      PostPlayVideoTask();
      return false;
    }
    if (a_frame_list_.GetLeftFrameCnt()) {
      PostPlayAudioTask();
      return false;
    }

    return true;
  } else {
    return false;
  }
}

void FAVPlayer::OnEOF(const FFDemuxer*) {
#ifdef DEBUG_FAV_PLAYER
  LOG(INFO) << "demuxer eof";
#endif
  PostVideoDecodeWork(true);
  PostAudioDecodeWork(true);
}

void FAVPlayer::OnSeekResult(bool val) {
  ResetStatusToStartUP();
#ifdef DEBUG_FAV_PLAYER
  LOG(INFO) << "Seeking stopped";
#endif
  ADD_TASK_NUM(task_num_, PostDriverWork)
  for (auto* observer : observer_list_) {
    observer->OnSeekEnd(this);
  }
}

void FAVPlayer::OnFirstAudioPacket(const FFDemuxer*) {}

void FAVPlayer::OnFirstVideoPacket(const FFDemuxer*) {}

void FAVPlayer::StartPlay() {
  LOG(INFO) << "StartPlay";
  if (HasAudio() && !play_audio_.IsRunning()) {
    play_audio_.Start();
  }

  if (stream_v_ && !play_video_.IsRunning()) {
    play_video_.Start();
  }

  ADD_TASK_NUM(task_num_, PostDriverWork)
}

void FAVPlayer::StepOneFrame() {
  pre_mode_ = mode_;
  mode_ = PlayMode::kModeViewSingleFrame;
  syn_.SetStep(true);
  step_ = true;
  if (IsPause()) {
    Resume();
  } else {
    PostPlayVideoTask();
  }
}

void FAVPlayer::QuitStep() {
  mode_ = pre_mode_;
  syn_.SetStep(false);
  Resume();
}

void FAVPlayer::Tick() {
  if (disable_post_driver_task_) {
    if (task_num_ < 10) {
      ADD_TASK_NUM(task_num_, PostDriverWork)
    }
  }
}

FFDecoder& FAVPlayer::GetAudioDecoder() {
  return audio_decoder_;
}

FFDecoder& FAVPlayer::GetVideoDecoder() {
  return video_decoder_;
}

FFDemuxer& FAVPlayer::GetDemuxer() {
  return demuxer_;
}

void FAVPlayer::SetVideoRender(VideoRender* render) {
  video_render_ = render;
}

void FAVPlayer::SetAudioRender(AudioRender* render) {
  audio_render_ = render;
}

void FAVPlayer::ResetStatusToStartUP() {
#ifdef DEBUG_FAV_PLAYER
  LOG(INFO) << "ResetStatusToStartUP";
  LOG(INFO) << "serial to [" << ++serial_ << "]";
#endif

  syn_.Reset(serial_);
  audio_flushed_ = false;
  video_flushed_ = false;
  a_frame_list_.Clear();
  v_frame_list_.Clear();
  video_decoder_eof_ = false;
  video_decoder_need_more_data_ = false;

  audio_decoder_eof_ = false;
  audio_decoder_need_more_data_ = false;

  seeking_ = false;
}

void FAVPlayer::DoSeekByDemuxer(int64_t ms_ts) {
#ifdef DEBUG_FAV_PLAYER
  LOG(INFO) << "DoSeekByDemuxer[" << ms_ts << "]";
#endif
  for (auto* observer : observer_list_) {
    observer->OnSeekBegin(this);
  }
  demuxer_.Seek(demuxer_thread_, ms_ts);
}

void FAVPlayer::PostDemuxerSeekTask(int64_t ms_ts, bool absolute) {
  int64_t main_base_ms_ = 0;
  if (absolute) {
  } else {
    if (syn_.AudioNotReady() || syn_.VideoNotReady()) {
    } else {
      main_base_ms_ = syn_.GetCurrentClock();
    }
  }
  auto main_ms = main_base_ms_;
  DoSeekByDemuxer(ms_ts + main_ms);
}

bool FAVPlayer::CouldPlay() {
  return !stop_ && !IsPause() && !IsSeeking();
}

void FAVPlayer::DoPostReadWork() {
  demuxer_.AsyReadWork(demuxer_thread_);
}

void FAVPlayer::PostAudioDecodeWork(bool eof) {
  if (IsNoAudio())
    return;
  if (audio_decoder_.GetPendingSendPacket()) {
    audio_decoder_.AsnDecode(decode_audio_);
  } else {
    if (eof) {
      FlushAudioDecoderBySendEmptyPacket();
    } else {
      ADD_TASK_NUM(task_num_, PostDriverWork)
    }
  }
}

void FAVPlayer::PostVideoDecodeWork(bool eof) {
  if (!stream_v_)
    return;

  if (video_decoder_.GetPendingSendPacket()) {
#ifdef DEBUG_FAV_PLAYER
    LOG(INFO) << "send video packet to video decoder";
#endif
    if (IsSeeking()) {
      LOG(WARNING) << "seeking, do not send video packet to video decoder";
      return;
    }
    video_decoder_.AsnDecode(decode_video_);
  } else {
    if (eof) {
      FlushVideoDecoderBySendEmptyPacket();
    } else {
      ADD_TASK_NUM(task_num_, PostDriverWork)
    }
  }
}

void FAVPlayer::FlushAudioDecoderBySendEmptyPacket() {
  audio_decoder_.AsnFlushForEnd(decode_audio_);
}

void FAVPlayer::FlushVideoDecoderBySendEmptyPacket() {
  video_decoder_.AsnFlushForEnd(decode_video_);
}

void FAVPlayer::PostDriverWork() {
  if (!demuxer_thread_.IsRunning()) {
    return;
  }
  if (demuxer_thread_.task_runner()->RunsTasksInCurrentSequence()) {
    DCHECK(task_num_ >= 0 && task_num_ <= 100);
    DEL_TASK_NUM(task_num_)
    if (stop_ || IsSeeking())
      return;
    if (NeedReadMorePacket()) {
      DoPostReadWork();
    }
  } else {
    demuxer_thread_.task_runner()->PostTask(
        FROM_HERE,
        base::BindOnce(&FAVPlayer::PostDriverWork, base::Unretained(this)));
  }
}

void FAVPlayer::TraceBufferedWork() {
  PlayerObserver::BufferSize a;
  PlayerObserver::BufferSize v;
  a.pending_decode = audio_decoder_.GetPendingSendPacket();
  a.pending_receive = audio_decoder_.GetPendingReceivePacket();
  a.pending_play = a_frame_list_.GetLeftFrameCnt();

  v.pending_decode = video_decoder_.GetPendingSendPacket();
  v.pending_receive = video_decoder_.GetPendingReceivePacket();
  v.pending_play = v_frame_list_.GetLeftFrameCnt();
#ifdef DEBUG_FAV_PLAYER
  LOG(INFO) << base::StringPrintf(
      "\r\na decode[%d] receive[%d] play[%d]\r\nv decode[%d] receive[%d] "
      "play[%d]",
      a.pending_decode, a.pending_receive, a.pending_play, v.pending_decode,
      v.pending_receive, v.pending_play);
#endif
  for (auto* observer : observer_list_) {
    observer->OnBufferSize(this, a, v);
  }
}

bool FAVPlayer::NeedReadMorePacket() {
  TraceBufferedWork();
  auto video_need_data = [this]() {
    if (!stream_v_) {
      return true;
    }
    if (!video_decoder_.GetPendingSendPacket()) {
      // no more decode packet, try get decoded frame

      if (video_decoder_need_more_data_) {
        return true;
      }
      // limit pending receive frame count
      if (video_decoder_.GetPendingReceivePacket() > 3) {
#ifdef DEBUG_FAV_PLAYER
        LOG(WARNING) << "video pending too much receive work, skip";
#endif

        return false;
      } else {
        return true;
      }
    } else {
      // check both pending decode && pending receive frame count
      PostVideoDecodeWork();
      if (video_decoder_.GetPendingReceivePacket() > 3) {
#ifdef DEBUG_FAV_PLAYER
        LOG(WARNING) << "video pending too much receive work, skip";
#endif
        return false;
      }
      return true;
    }
  };
  auto video_data_too_mush = [this]() {
    if (video_decoder_.GetPendingReceivePacket() > 3) {
      return true;
    }
    return false;
  };
  if (video_need_data()) {
    if (audio_decoder_.GetPendingReceivePacket() > 100 ||
        audio_decoder_.GetPendingSendPacket() > 100 ||
        a_frame_list_.GetLeftFrameCnt() > 100) {
      return false;  // audio buffered too data
    }
    if (HasVideo()) {
      if (video_decoder_.GetPendingSendPacket() > 100) {
        return false;
      }
      return true;
    }
  }

  if (HasAudio()) {
    if (audio_decoder_.GetPendingSendPacket()) {
      PostAudioDecodeWork();
      if (audio_decoder_.GetPendingReceivePacket() > 6 ||
          audio_decoder_.GetPendingSendPacket() > 6) {
        return false;
      }
    }
    if (video_data_too_mush()) {
      return false;
    }
  }

  return true;
}

void FAVPlayer::PlayVideoTask() {
  if (!CouldPlay())
    return;
  /* while (CouldPlay())*/ {
    if (mode_ == kModeViewSingleFrame) {
      if (!step_)
        return;
    }
    const int64_t first_ms =
        v_frame_list_.GetFirstFrameTS(stream_v_->time_base);
#ifdef DEBUG_FAV_PLAYER
    LOG(INFO) << "video ms[" << first_ms << "]";
#endif
    auto frame_with_serial = v_frame_list_.PopFrame();

    AVFrame* frame = frame_with_serial.frame;
    if (!frame) {
      if (mode_ == kModeViewSingleFrame && syn_.IsStep()) {
        ADD_TASK_NUM(task_num_, PostDriverWork)
        return;
      } else {
        return;
      }
    }
    const int64_t next_ms = v_frame_list_.GetFirstFrameTS(stream_v_->time_base);

    AVFrameRAIIReference v(frame);
    if (frame_with_serial.serial != GetSerial())
      return;
    bool scaled = false;

    bool no_pos_play = (frame->pts == AV_NOPTS_VALUE);  // pts is not valid
    // when this happened, we just play video real time, and audio do not need
    // to check video play status

    if (!CouldPlay()) {
      return;
    }
    auto do_scale = [this, &scaled](AVFrame*& frame) {
      if (use_hw_surface_)
        return;
      if (scaled)
        return;
      scaled = true;
      if (IsHWDecode() && frame->hw_frames_ctx) {
        if (!ff_scale_.HWFrameToFrame(frame)) {
          return;
        }
      }
      auto best = MS_AV_BESTMATCH_FORMAT((AVPixelFormat)frame->format);

      ff_scale_.FrameScaleByFFmpeg(frame, best);
    };
    int64_t cur_diff_ms = 0;
    int64_t next_diff = 0;
    int64_t render_diff = 0;
    int64_t last_render_pts_diff = 0;

    if (!syn_.GetVideoDiff(first_ms, next_ms, cur_diff_ms, &next_diff,
                           &last_render_pts_diff, &render_diff)) {
      // audio not ready
      mediasdk::SleepByUS(10000);
    }
    int64_t render_diff_sleep_ms = 0;
    int64_t check_begin = ms_now_internal();
    if (last_render_pts_diff > 0 && render_diff < last_render_pts_diff) {
      render_diff_sleep_ms = last_render_pts_diff - render_diff;
    }

    if (NeedCheckDrop() && !no_pos_play && syn_.IsVideoTooSlow(cur_diff_ms)) {
#ifdef DEBUG_FAV_PLAYER
      LOG(WARNING) << "Video Too Slow, Just Skip This [" << cur_diff_ms << "] ["
                   << frame << "]";
#endif
      TraceBufferedWork();
      for (auto* observer : observer_list_) {
        observer->OnVideoTooSlow(this, frame, cur_diff_ms);
      }
      for (auto* observer : observer_list_) {
        observer->OnPlayDropVideo(this, frame);
      }
      syn_.UpdateVideoPTS(frame_with_serial.serial, first_ms);

      return;
    }
    int64_t last_diff = 0;
    while (NeedAVSyn() && !no_pos_play && syn_.IsVideoTooQuick(cur_diff_ms) &&
           cur_diff_ms) {
      if (last_diff) {
        if (last_diff < cur_diff_ms) {
          return;  // not possible
        }
      }
      last_diff = cur_diff_ms;
      if (IsSeeking() || stop_ || IsPause())
        return;

      for (auto* observer : observer_list_) {
        observer->OnVideoTooQuick(this, frame, cur_diff_ms);
      }
#ifdef DEBUG_FAV_PLAYER
      LOG(INFO) << "video too quick[" << first_ms << "] diff [" << cur_diff_ms
                << "] [" << frame << "]";
#endif
      if (!scaled && !use_hw_surface_) {
        do_scale(frame);
        syn_.GetVideoDiff(first_ms, next_ms, cur_diff_ms, &next_diff);

        continue;
      }
      if (IsSeeking()) {
        return;
      }
      // limit single sleep time
      if (cur_diff_ms > 20) {
        mediasdk::SleepByUS(20 * 1000);
        syn_.GetVideoDiff(first_ms, next_ms, cur_diff_ms, &next_diff);
      } else {
        mediasdk::SleepByUS(cur_diff_ms * 1000);

        break;
      }
    }

    auto check_render_too_quick = [render_diff_sleep_ms, check_begin,
                                   this](AVFrame* frame) {
      auto final_sleep_ms =
          render_diff_sleep_ms - (ms_now_internal() - check_begin) - 1;

      if (final_sleep_ms > 0) {
#ifdef DEBUG_FAV_PLAYER
        LOG(INFO) << "final sleep[" << final_sleep_ms << "] [" << frame << "]";
#endif
        mediasdk::SleepByUS(std::min(final_sleep_ms, 20ll) * syn_.GetSpeed() *
                            1000);
      }
    };

    if (CouldPlay() && video_render_) {
      do_scale(frame);

      check_render_too_quick(frame);

      if (!no_pos_play) {
        syn_.UpdateVideoPTS(frame_with_serial.serial, first_ms);
      } else {
        syn_.SetVideoPtsNotValid();
      }

      video_render_->PlayVideo(frame);
      step_ = false;
    }
  }
}

void FAVPlayer::PostPlayAudioTask() {
  if (IsNoAudio())
    return;
  auto runner = play_audio_.task_runner();
  if (runner) {
    runner->PostTask(FROM_HERE, base::BindOnce(
                                    [](FAVPlayer* pThis) {
                                      pThis->PlayAudioTask();
                                      pThis->CheckLoopPlayTask();
                                    },
                                    base::Unretained(this)));
  }
}

void FAVPlayer::PostPlayVideoTask() {
  if (!stream_v_)
    return;
  auto runner = play_video_.task_runner();
  if (runner) {
    runner->PostTask(FROM_HERE, base::BindOnce(
                                    [](FAVPlayer* pThis) {
                                      pThis->PlayVideoTask();
                                      if (pThis->IsSeeking()) {
#ifdef DEBUG_FAV_PLAYER
                                        LOG(INFO) << "Seeking, clear "
                                                     "ClearPendingVideoFrames";
#endif
                                        pThis->ClearPendingVideoFrames();
                                      }
                                      pThis->CheckLoopPlayTask();
                                    },
                                    base::Unretained(this)));
  }
}

void FAVPlayer::PlayAudioTask() {
  if (stop_ || syn_.IsPause())
    return;
  {
    auto frame_with_serial = a_frame_list_.PopFrame();
    AVFrame* frame = frame_with_serial.frame;
    if (!frame)
      return;

    AVFrameRAIIReference v(frame);
    const double ms_base = av_q2d(stream_a_->time_base) * 1000ll;
    const int64_t ms = ms_base * frame->pts;
#ifdef DEBUG_FAV_PLAYER
    LOG(INFO) << "audio ms[" << ms << "]";
#endif
    int64_t diff = 0;
    constexpr int32_t max_sleep_cnt = 3;
    int32_t cur_cnt = max_sleep_cnt;
    while (NeedAVSyn() && cur_cnt-- && !stop_ &&
           syn_.IsAudioTookQuick(ms, &diff)) {
      if (IsSeeking())
        return;
#ifdef DEBUG_FAV_PLAYER
      LOG(WARNING) << "audio too quick[" << diff << "]";
#endif
      for (auto* observer : observer_list_) {
        observer->OnAudioTooQuick(this, frame, diff);
      }
      if (disable_post_driver_task_) {
        if (diff > 5) {
          mediasdk::SleepByUS(5000);
        } else {
          mediasdk::SleepByUS(1000);
        }
      } else {
        if (diff > 10) {
          mediasdk::SleepByUS(10000);
        } else {
          mediasdk::SleepByUS(1000);
        }
      }
    }
    if (stop_)
      return;
    syn_.UpdateAudioPTS(frame_with_serial.serial, ms);
    if (CouldPlay() && audio_render_) {
      audio_render_->PlayAudio(frame);
    } else if (!stop_) {
    }

    if (!syn_.IsPause()) {
      ADD_TASK_NUM(task_num_, PostDriverWork)
    }
  }
}

void FAVPlayer::Close() {
  if (stop_)
    return;
  stop_ = true;

  audio_decoder_.Close(&decode_audio_);
  decode_audio_.Stop();

  video_decoder_.Close(&decode_video_);
  decode_video_.Stop();

  play_audio_.Stop();
  play_video_.Stop();

  demuxer_.Close(demuxer_thread_);
  demuxer_thread_.Stop();

  a_frame_list_.Clear();
  v_frame_list_.Clear();
}

void FAVPlayer::Pause() {
  syn_.Pause();
}

void FAVPlayer::Resume() {
  syn_.Resume();
  ADD_TASK_NUM(task_num_, PostDriverWork)
  PostPlayAudioTask();
  PostPlayVideoTask();
}

bool FAVPlayer::IsPause() const {
  return syn_.IsPause();
}

int64_t FAVPlayer::GetSecondsPos() const {
  if (mode_ == kModeViewSingleFrame) {
    return syn_.GetVideoClock() / kMsToSeconds;
  } else {
    return syn_.GetCurrentClock() / kMsToSeconds;
  }
}

int64_t FAVPlayer::GetSecondsDuration() const {
  if (const auto* context = demuxer_.GetContext();
      context && context->duration) {
    return context->duration / AV_TIME_BASE;
  }
  return -1;
}

bool FAVPlayer::CanSeek() const {
  return demuxer_.CanSeek();
}

bool FAVPlayer::IsHWDecode() const {
  return hardware_decode_;
}

bool FAVPlayer::IsSeeking() const {
  return seeking_;
}

void FAVPlayer::OnDecoderEOF(FFDecoder* decoder) {
  if (&audio_decoder_ == decoder) {
    LOG(INFO) << "audio decoder eof";
    audio_decoder_eof_ = true;
    PostVideoDecodeWork();
    CheckLoopPlayTask();
  } else if (&video_decoder_ == decoder) {
    LOG(INFO) << "video decoder eof";
    video_decoder_eof_ = true;
    PostAudioDecodeWork();
    CheckLoopPlayTask();
  }
}

void FAVPlayer::OnDecodeOpened(FFDecoder* decoder, bool hw) {
  if (decoder == &video_decoder_) {
    if (hw != IsHWDecode()) {
      if (IsHWDecode()) {
        LOG(WARNING)
            << "target hw decode, but not support, fall back to soft ware";
        for (auto* observer : observer_list_) {
          observer->OnDecoderFallBackToSoftware(this);
        }
        hardware_decode_ = false;
      }
    }
  }
}

void FAVPlayer::OnNeedMoreData(FFDecoder* decoder) {
  if (IsSeeking())
    return;
  if (stop_)
    return;
  if (demuxer_.IsEOF()) {
    if (video_decoder_.GetPendingSendPacket() == 0) {
      FlushVideoDecoderBySendEmptyPacket();
    }
    if (audio_decoder_.GetPendingSendPacket() == 0) {
      FlushAudioDecoderBySendEmptyPacket();
    }
    return;
  }

  TraceBufferedWork();

  if (decoder == &audio_decoder_) {
    if (audio_decoder_need_more_data_) {
#ifdef DEBUG_FAV_PLAYER
      LOG(WARNING) << "audio still need more packet";
#endif
    }
    audio_decoder_need_more_data_ = true;
#ifdef DEBUG_FAV_PLAYER
    LOG(INFO) << "audio need more data, post read && decode work";
#endif

    if (!audio_decoder_.GetPendingSendPacket()) {
      ADD_TASK_NUM(task_num_, PostDriverWork)
    } else {
      PostAudioDecodeWork();
    }
  } else if (decoder == &video_decoder_) {
    if (video_decoder_need_more_data_) {
#ifdef DEBUG_FAV_PLAYER
      LOG(WARNING) << "video still need more packet";
#endif
    }
    video_decoder_need_more_data_ = true;
#ifdef DEBUG_FAV_PLAYER
    LOG(INFO) << "video need more data, post read && decode work";
#endif
    if (!video_decoder_.GetPendingSendPacket()) {
      ADD_TASK_NUM(task_num_, PostDriverWork)
    } else {
      PostVideoDecodeWork();
    }
  } else {
    DCHECK(false && "un known decoder");
  }
}

void FAVPlayer::OnFlushSuccess(FFDecoder* decoder) {
  if (decoder == &audio_decoder_) {
    audio_flushed_ = true;
  } else if (decoder == &video_decoder_) {
    video_flushed_ = true;
  }
  auto seek_end = [this]() {
    if (HasAudio()) {
      if (!audio_flushed_)
        return false;
    }
    if (stream_v_) {
      if (!video_flushed_)
        return false;
    }
    return true;
  };

  if (seek_end()) {
    if (IsSeeking()) {
      if (seek_request_ != -1) {
        PostDemuxerSeekTask(seek_request_, seek_absolute_);
      }
      seek_request_ = -1;
    }
  }
}

bool FAVPlayer::PreDropped(AVFrame& frame) {
  if (!NeedCheckDrop())
    return false;

  if (IsNoAudio())
    return false;

  int64_t diff = 0;
  const double ms_base = av_q2d(stream_v_->time_base) * 1000ll;
  const int64_t ms = ms_base * frame.pts;
  syn_.GetVideoDiff(ms, 0, diff);
  if (syn_.IsVideoTooSlow(diff) && diff) {
    for (auto* observer : observer_list_) {
      observer->OnPreDropVideo(this, &frame);
    }
    TraceBufferedWork();
#ifdef DEBUG_FAV_PLAYER
    LOG(WARNING) << "video too slow, skip do not send to play[" << diff << "]";
#endif
    return true;
  }
  return false;
}

int32_t FAVPlayer::GetSerial() {
  return serial_;
}

void FAVPlayer::SignalPlayEOF() {
  for (auto* observer : observer_list_) {
    observer->OnPlayEOF(this);
  }
}

void FAVPlayer::HandleVideoFrame(AVFrame& frame, int32_t serial) {
  if (video_decoder_need_more_data_) {
#ifdef DEBUG_FAV_PLAYER
    LOG(INFO) << "video frame received ,do not need more data";
#endif
    video_decoder_need_more_data_ = false;
  }

  bool audio_playing = false;
  if (stream_a_) {
    if (a_frame_list_.GetLeftFrameCnt() && syn_.AudioNotReady()) {
    } else {
      audio_playing = true;
    }
  } else {
    audio_playing = true;
  }

  if (audio_playing) {
    if (PreDropped(frame)) {
      return;
    }
  }
  v_frame_list_.PushFrame(frame, serial);

  PostPlayVideoTask();

  if (!audio_playing) {
    PostPlayAudioTask();
  }
  PostVideoDecodeWork();
  if (audio_decoder_eof_) {
    PostPlayAudioTask();
  }
}

void FAVPlayer::HandleAudioFrame(AVFrame& frame, int32_t serial) {
  if (audio_decoder_need_more_data_) {
#ifdef DEBUG_FAV_PLAYER
    LOG(INFO) << "audio frame received ,do not need more data";
#endif
    audio_decoder_need_more_data_ = false;
  }
  a_frame_list_.PushFrame(frame, serial);
  PostAudioDecodeWork();
  PostPlayAudioTask();
  if (video_decoder_eof_) {
    PostPlayVideoTask();
  }
}

void FAVPlayer::OnFrame(FFDecoder* decoder, AVFrame& frame) {
  if (stop_ || IsSeeking())
    return;
  while (!(stop_ || IsSeeking())) {
    if (decoder == &audio_decoder_) {
      if (!(a_frame_list_.GetLeftFrameCnt() > 6)) {
        break;
      }
    } else if (decoder == &video_decoder_) {
      if (!(v_frame_list_.GetLeftFrameCnt() > 3)) {
        break;
      }
    }

    mediasdk::SleepByUS(10000);
  }
  if (decoder == &audio_decoder_) {
    HandleAudioFrame(frame, serial_);
  } else if (decoder == &video_decoder_) {
    HandleVideoFrame(frame, serial_);
  } else {
    DCHECK(false && "unknown decoder");
  }
}

void FAVPlayer::EnableLoop(bool val) {
  enable_loop_ = val;
}

void FAVPlayer::EnableNoBuffer(bool buffer) {
  demuxer_.EnableNoBuffer(buffer);
}

void FAVPlayer::ResetHardwareDecode(bool val) {
  LOG(INFO) << "hardware decode[" << val << "]";
  hardware_decode_ = val;
}

bool FAVPlayer::Seek(int64_t ms_ts, bool absolute) {
  if (!CanSeek()) {
    LOG(ERROR) << "can not seek,return";
    return false;
  }
  if (IsPause()) {
    Resume();
  }
  // stop current decode and render task
  seeking_ = true;
  if (decode_audio_.IsRunning()) {
#ifdef DEBUG_FAV_PLAYER
    LOG(INFO) << "audio_decoder ClearAndFlushPendingPacket";
#endif
    audio_decoder_.ClearAndFlushPendingPacket(decode_audio_);
  }
  if (decode_video_.IsRunning()) {
#ifdef DEBUG_FAV_PLAYER
    LOG(INFO) << "video_decoder ClearAndFlushPendingPacket";
#endif
    video_decoder_.ClearAndFlushPendingPacket(decode_video_);
  }
  seek_request_ = ms_ts;
  seek_absolute_ = absolute;
  return true;
}

void FAVPlayer::StartUpSeek(const int64_t ms_ts) {
  if (!CanSeek())
    return;
  demuxer_.SimpleSeek(ms_ts);
}

bool FAVPlayer::OpenURL(const std::string& str,
                        const PlayMode mode,
                        const bool use_hw_surface) {
  mode_ = mode;
  use_hw_surface_ = use_hw_surface;
  demuxer_.AddObserver(this);
  if (!demuxer_thread_.IsRunning()) {
    demuxer_thread_.Start();
  }
  const auto ret = demuxer_.OpenURL(str);
  if (mode_ == kModeViewVideo) {
    OnNoAudioStream(&demuxer_);
  }
  return ret;
}

bool FAVPlayer::NeedCheckDrop() const {
  return mode_ != kModeNoDrop;
}
