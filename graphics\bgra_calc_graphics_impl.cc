#include "bgra_calc_graphics_impl.h"

#include "base./logging.h"
#include "base/check.h"
#include "base/strings/stringprintf.h"
#include "bgra_calc_shader.h"
#include "shader_manager.h"

using namespace Microsoft::WRL;

namespace graphics {

BGRACalcGraphicsImpl::BGRACalcGraphicsImpl(Device& ins) : device_(ins) {}

bool BGRACalcGraphicsImpl::AlphaMask(std::shared_ptr<Texture> texture,
                                     std::shared_ptr<Texture> mask,
                                     CalcType type,
                                     const XMFLOAT2 texture_move,
                                     const XMFLOAT2 mask_move) {
  if (!texture) {
    DCHECK(false);
    return false;
  }
  if (!TryCreateResource(*texture, mask == nullptr))
    return false;
  if (!TryUpdateParam(*texture, texture_move, mask_move)) {
    return false;
  }
  return DoAlphaMaskCalc(*texture, mask ? *mask : *texture);
}

bool BGRACalcGraphicsImpl::TryUpdateParam(const Texture& texutre,
                                          const XMFLOAT2& texture_move,
                                          const XMFLOAT2& mask_move) {
  bool update = false;
  if (const_buffer_p_.texture_move != texture_move ||
      const_buffer_p_.mask_move != mask_move) {
    update = true;
    const_buffer_p_.texture_move = texture_move;
    const_buffer_p_.mask_move = mask_move;
  }

  if (!update) {
    return true;
  }

  D3D11_MAPPED_SUBRESOURCE ms = {};
  HRESULT res = S_OK;
  res = GetContext()->Map(ps_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &ms);

  if (FAILED(res)) {
    LOG(ERROR) << "Failed to map[" << res << "]";
    return false;
  }
  memcpy(ms.pData, &const_buffer_p_, sizeof(const_buffer_p_));

  GetContext()->Unmap(ps_buffer_.Get(), 0);
  return true;
}

// prepare GPU resource
bool BGRACalcGraphicsImpl::TryCreateResource(const Texture& texture,
                                             bool half) {
  if (graphics_ && !graphics_->IsEmpty())
    return true;
  HRESULT res = S_OK;
  XMFLOAT2 size = texture.GetSize();
  if (half) {
    size.x /= 2;
  }
  Destroy();
  if (!graphics_) {
    graphics_ = CreateGraphics2D(device_);
  }
  if (graphics_->IsEmpty()) {
    if (!graphics_->CreateNewBGRAGraphics(size.x, size.y)) {
      return false;
    }
  }

  if (graphics_->IsEmpty()) {
    return false;
  }
  if (!ps_buffer_) {
    D3D11_BUFFER_DESC desc = {};
    desc.Usage = D3D11_USAGE_DYNAMIC;
    desc.ByteWidth = sizeof(BGRACalcShader::PS_BUFFER);
    desc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
    desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
    res = GetDevice()->CreateBuffer(&desc, NULL, &ps_buffer_);
    if (FAILED(res)) {
      LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                       GetErrorString(res).c_str());
      return false;
    }
  }

  return true;
}

bool BGRACalcGraphicsImpl::DoAlphaMaskCalc(const Texture& texture,
                                           const Texture& mask) {
  auto shader =
      device_.GetShaderManager()->GetOrCreateShader<BGRACalcShader>(
          graphics_->GetDevice().shared_from_this());
  if (!shader)
    return false;
  ScopedBlendHelper restore(graphics_->GetDevice());
  graphics_->GetDevice().AllowBlend(true);
  if (!graphics_->BeginDraw(false)) {
    return false;
  }
  graphics::ScopedEndDraw end_draw(*graphics_);
  shader->Render(ps_buffer_, texture.GetSRV(0), mask.GetSRV(0));

  return true;
}

std::shared_ptr<Texture> BGRACalcGraphicsImpl::GetOutputTexture() {
  if (graphics_) {
    return graphics_->GetOutputTexture();
  }
  return nullptr;
}

void BGRACalcGraphicsImpl::Destroy() {
  if (graphics_) {
    graphics_->Destroy();
    graphics_ = nullptr;
  }

  if (ps_buffer_) {
    ps_buffer_.Reset();
  }
}

BGRACalcGraphicsImpl::~BGRACalcGraphicsImpl() {
  BGRACalcGraphicsImpl::Destroy();
}

ComPtr<ID3D11Device> BGRACalcGraphicsImpl::GetDevice() {
  return graphics_->GetDevice().GetDevice();
}

ComPtr<ID3D11DeviceContext> BGRACalcGraphicsImpl::GetContext() {
  return graphics_->GetDevice().GetContext();
}
}  // namespace graphics