#pragma once

#include "graphics.h"

#include "color_revert_graphics.h"

namespace graphics {

class ColorRevertGraphicsImpl : public ColorRevertGraphics {
 public:
  ColorRevertGraphicsImpl(Device& ins);

 public:
  std::shared_ptr<Texture> Draw(Texture&, const RevertParam&) override;
  void Destroy() override;

  ~ColorRevertGraphicsImpl() override;

 private:
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice_();
  ID3D11DeviceContext* GetContext_();

 private:
  ShaderManager* GetShaderManager_();

 private:
  std::shared_ptr<Graphics> graphics_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> ps_buffer_;
  Device& device_;
};

}  // namespace graphics
