#pragma once
#include <DirectXMath.h>
#include <cmath>
#include <limits>
#include "DXSimpleMath.h"
#include "graphics_export.h"
#include "graphics_utils.h"
#include "transform.h"

namespace graphics {

class GRAPHICS_EXPORT BorderBuffer {
 public:
  static BorderBuffer CreateBorder(const DirectX::XMFLOAT4& border_color,
                                   const DirectX::XMFLOAT2& border_width,
                                   const graphics::Transform& transform) {
    BorderBuffer border;
    border.SetColor(border_color);
    border.SetScale(transform.GetScale());
    border.SetWidth(border_width);
    return border;
  }

  BorderBuffer(){};

  bool operator==(const BorderBuffer& other) const {
    if (!IsNearEqual(GetScale().x, other.GetScale().x) ||
        !IsNearEqual(GetScale().y, other.GetScale().y)) {
      return false;
    }
    if (!IsNearEqual(GetColor().x, other.GetColor().x) ||
        !IsNearEqual(GetColor().y, other.GetColor().y) ||
        !IsNearEqual(GetColor().z, other.GetColor().z) ||
        !IsNearEqual(GetColor().w, other.GetColor().w)) {
      return false;
    }
    if (!IsNearEqual(GetWidth().x, other.GetWidth().x) ||
        !IsNearEqual(GetWidth().y, other.GetWidth().y)) {
      return false;
    }
    return true;
  }

  const DirectX::XMFLOAT4 GetColor() const { return border_color_; }

  void SetColor(const DirectX::XMFLOAT4& border_color) {
    border_color_ = border_color;
  }

  const DirectX::XMFLOAT2 GetWidth() const { return border_width_; }

  void SetWidth(const DirectX::XMFLOAT2& border_width) {
    border_width_ = border_width;
  }

  const DirectX::XMFLOAT2 GetScale() const { return border_scale_; }

  void SetScale(const DirectX::XMFLOAT2& border_scale) {
    border_scale_ = border_scale;
  }

 private:
  DirectX::XMFLOAT4 border_color_ = XMFLOAT4_EMPTY;
  DirectX::XMFLOAT2 border_width_ = XMFLOAT2_EMPTY;
  DirectX::XMFLOAT2 border_scale_ = XMFLOAT2_EMPTY;
};

}  // namespace graphics
