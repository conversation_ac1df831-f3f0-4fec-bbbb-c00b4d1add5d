#include "gradual_rectangle_impl.h"
#include <DirectXMath.h>

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <comdef.h>
#include <dxgi.h>
#include <dxgitype.h>
#include <stdio.h>
#include <windows.h>
#include <wrl/client.h>
#include "DXSimpleMath.h"
#include "base/check.h"
#include "color_shader.h"
#include "transform_calc.h"

using namespace DirectX;
using namespace Microsoft::WRL;

namespace graphics {

namespace {
const int kRectangleVertexCount = 4;
const int kRectangleIndexCount = 6;
const int kRectangleLineIndexCount = 8;
}  // namespace

int GradualRectangleImpl::GetIndexCnt() {   
  SetupIAS();
  return cur_index_cnt_; }

bool IsEqual(const GradualRectangle::GradualRectangleConfig& left,
    const GradualRectangle::GradualRectangleConfig& right) {
return std::memcmp(&left, &right, sizeof(left)) == 0;
}

std::shared_ptr<GradualRectangleImpl> GradualRectangleImpl::CreateGradualRectangle(
    Device& inst) {
  auto ptr = std::make_shared<GradualRectangleImpl>(inst);
  if (!ptr || !ptr->SetupIAS()) {
    return nullptr;
  }
  return ptr;
}

GradualRectangleImpl::GradualRectangleImpl(Device& ins)
    : instance_(ins) {
}

GradualRectangleImpl::~GradualRectangleImpl() { Destroy(); }

void GradualRectangleImpl::Destroy() {
  if (vertex_buffer_) {
    vertex_buffer_.Reset();
  }
  if (index_buffer_lines_) {
    index_buffer_lines_.Reset();
  }
  if (index_buffer_fill_) {
    index_buffer_fill_.Reset();
  }
  {
    if (config_) {
      config_ = nullptr;
    }
  }
  if (matrix_) {
    matrix_.Reset();
  }
}

ID3D11DeviceContext* GradualRectangleImpl::GetContext() {
  return instance_.GetContext().Get();
}

ID3D11Device* GradualRectangleImpl::GetDevice() {
  return instance_.GetDevice().Get();
}

bool GradualRectangleImpl::UpdateBufferWithVPSize(
    const DirectX::XMFLOAT2& vp,
    const GradualRectangleConfigInternal& conf) {
      auto picSize = XMFLOAT2{conf.bottom_right.x - conf.top_left.x,
        conf.bottom_right.y - conf.top_left.y};
int vertexCnt = 0;
vertex_mem_buffer_[0].position = XMFLOAT3(0.f, 0.f, 0.f);

vertex_mem_buffer_[1].position = XMFLOAT3(0.f, 1.f, 0.f);

vertex_mem_buffer_[2].position = XMFLOAT3(1.f, 1.f, 0.f);

vertex_mem_buffer_[3].position = XMFLOAT3(1.f, 0.f, 0.f);

if (conf.fill) {
fill_ = true;
vertexCnt = 4;
cur_index_cnt_ = 6;
for (int i = 0; i < 4; i++) {
  vertex_mem_buffer_[i].color = conf.color;
}

switch (conf.direction) {
  case GradientDirection::TOP_TO_BOTTOM:
    vertex_mem_buffer_[0].color.w = 1.0f; // top-left
    vertex_mem_buffer_[1].color.w = 0.0f; // bottom-left
    vertex_mem_buffer_[2].color.w = 0.0f; // bottom-right
    vertex_mem_buffer_[3].color.w = 1.0f; // top-right
    break;
  case GradientDirection::BOTTOM_TO_TOP:
    vertex_mem_buffer_[0].color.w = 0.0f; // top-left
    vertex_mem_buffer_[1].color.w = 1.0f; // bottom-left
    vertex_mem_buffer_[2].color.w = 1.0f; // bottom-right
    vertex_mem_buffer_[3].color.w = 0.0f; // top-right
    break;
  case GradientDirection::LEFT_TO_RIGHT:
    vertex_mem_buffer_[0].color.w = 1.0f; // top-left
    vertex_mem_buffer_[1].color.w = 1.0f; // bottom-left
    vertex_mem_buffer_[2].color.w = 0.0f; // bottom-right
    vertex_mem_buffer_[3].color.w = 0.0f; // top-right
    break;
  case GradientDirection::RIGHT_TO_LEFT:
    vertex_mem_buffer_[0].color.w = 0.0f; // top-left
    vertex_mem_buffer_[1].color.w = 0.0f; // bottom-left
    vertex_mem_buffer_[2].color.w = 1.0f; // bottom-right
    vertex_mem_buffer_[3].color.w = 1.0f; // top-right
    break;
}

} else {
vertexCnt = 8;
fill_ = false;
cur_index_cnt_ =
24;  // every line is a rectangle,every rectangle is two TRIANGLE
for (int i = 0; i < vertex_mem_buffer_.size(); i++) {
vertex_mem_buffer_[i].color = conf.color;
}

vertex_mem_buffer_[4].position = XMFLOAT3(
vertex_mem_buffer_[0].position.x + (conf.thinkness) / picSize.x,
vertex_mem_buffer_[0].position.y + (conf.thinkness) / picSize.y, 0.f);
vertex_mem_buffer_[5].position = XMFLOAT3(
vertex_mem_buffer_[1].position.x + (conf.thinkness) / picSize.x,
vertex_mem_buffer_[1].position.y - (conf.thinkness) / picSize.y, 0.f);
vertex_mem_buffer_[6].position = XMFLOAT3(
vertex_mem_buffer_[2].position.x - (conf.thinkness) / picSize.x,
vertex_mem_buffer_[2].position.y - (conf.thinkness) / picSize.y, 0.f);
vertex_mem_buffer_[7].position = XMFLOAT3(
vertex_mem_buffer_[3].position.x - (conf.thinkness) / picSize.x,
vertex_mem_buffer_[3].position.y + (conf.thinkness) / picSize.y, 0.f);
}

D3D11_MAPPED_SUBRESOURCE mapped_resource;
HRESULT res = GetContext()->Map(vertex_buffer_.Get(), 0,
                D3D11_MAP_WRITE_DISCARD, 0, &mapped_resource);
if (res != S_OK) {
LOG(ERROR) << base::StringPrintf("Failed to Map(%s)",
                   GetErrorString(res).c_str());
return false;
}

std::memcpy(mapped_resource.pData, (void*)vertex_mem_buffer_.data(),
sizeof(ColorShader::VERTEXTYPE) * vertexCnt);
GetContext()->Unmap(vertex_buffer_.Get(), 0);
return true;
}

bool GradualRectangleImpl::SetupIAS() {
  if (!cur_index_cnt_) {
    return false;
  }

  UINT stride = sizeof(ColorShader::VERTEXTYPE);
  UINT offset = 0;
  auto context = GetContext();
  context->IASetVertexBuffers(0, 1, vertex_buffer_.GetAddressOf(), &stride,
                              &offset);
  context->IASetIndexBuffer(
      fill_ ? index_buffer_fill_.Get() : index_buffer_lines_.Get(),
      DXGI_FORMAT_R32_UINT, 0);
  context->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);
  context->VSSetConstantBuffers(0, 1, matrix_.GetAddressOf());

  return true;
}

void GradualRectangleImpl::UpdateRectangleConf(
    const GradualRectangle::GradualRectangleConfig* conf) {
      DCHECK(!conf  || conf->vp_size != XMFLOAT2_EMPTY);
      if (!conf) {
        config_->show = false;
      } else {
        if (config_) {
        } else {
          if (IsEqual(pre_conf_, *conf)) {
            return;
          }
        }
        config_ = std::make_unique<GradualRectangleConfigInternal>();
        memcpy(config_.get(), conf, sizeof(GradualRectangleConfig));
        config_->show = true;
      }
}


// LZYTODO CHECK SHADER
bool GradualRectangleImpl::UpdateVertexBuffer(
    const GradualRectangleConfigInternal& conf) {
      if (vertex_buffer_ && index_buffer_lines_ && index_buffer_fill_ && matrix_)
      return true;
    D3D11_BUFFER_DESC desc = {};
    desc.Usage = D3D11_USAGE_DYNAMIC;
    desc.ByteWidth = sizeof(ColorShader::VERTEXTYPE) * 8;
    desc.BindFlags = D3D11_BIND_VERTEX_BUFFER;
    desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
    desc.MiscFlags = 0;
    desc.StructureByteStride = 0;
    D3D11_SUBRESOURCE_DATA data = {};
    data.pSysMem = nullptr;
    data.SysMemPitch = 0;
    data.SysMemSlicePitch = 0;
    HRESULT hRes = GetDevice()->CreateBuffer(&desc, nullptr, &vertex_buffer_);
    if (hRes != S_OK) {
      LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                       GetErrorString(hRes).c_str());
      return false;
    }
    D3D11SetDebugObjectName(vertex_buffer_.Get(), "rectangle_vertex_buffer");
    ULONG indicesFill[24] = {
        1, 2, 6, 1, 6, 5, 6, 2, 3, 6, 3, 7, 4, 7, 3, 4, 3, 0, 1, 5, 4, 1, 4, 0,
    };
    desc = {};
    desc.Usage = D3D11_USAGE_DEFAULT;
    desc.ByteWidth = sizeof(ULONG) * 24;
    desc.BindFlags = D3D11_BIND_INDEX_BUFFER;
    desc.CPUAccessFlags = 0;
    desc.MiscFlags = 0;
    data = {};
    data.pSysMem = indicesFill;
    data.SysMemPitch = 0;
    data.SysMemSlicePitch = 0;
    hRes = GetDevice()->CreateBuffer(&desc, &data, &index_buffer_lines_);
    if (hRes != S_OK) {
      LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                       GetErrorString(hRes).c_str());
      return false;
    }
    D3D11SetDebugObjectName(index_buffer_lines_.Get(), "rectangle_index_buffer");
    ULONG indices[6] = {
        0, 1, 2, 2, 3, 0,
    };
    desc = {};
    desc.Usage = D3D11_USAGE_DEFAULT;
    desc.ByteWidth = sizeof(ULONG) * 6;
    desc.BindFlags = D3D11_BIND_INDEX_BUFFER;
    desc.CPUAccessFlags = 0;
    desc.MiscFlags = 0;
    data = {};
    data.pSysMem = indices;
    data.SysMemPitch = 0;
    data.SysMemSlicePitch = 0;
    hRes = GetDevice()->CreateBuffer(&desc, &data, &index_buffer_fill_);
    if (hRes != S_OK) {
      LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                       GetErrorString(hRes).c_str());
      return false;
    }
    D3D11SetDebugObjectName(index_buffer_fill_.Get(),
                            "rectangle_index_fill_buffer");
    D3D11_BUFFER_DESC buffer_desc = {};
    buffer_desc.Usage = D3D11_USAGE_DYNAMIC;
    buffer_desc.ByteWidth = sizeof(MATRIXBUFFER);
    buffer_desc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
    buffer_desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
    buffer_desc.MiscFlags = 0;
    buffer_desc.StructureByteStride = 0;
    hRes = GetDevice()->CreateBuffer(&buffer_desc, NULL, &matrix_);
  
    D3D11SetDebugObjectName(matrix_.Get(), "rectangle_matrix_buffer");
  
    if (FAILED(hRes)) {
      LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                       GetErrorString(hRes).c_str());
      return false;
    }
    return true;
}

bool GradualRectangleImpl::DrawTo(const DirectX::XMFLOAT2& vp,
                                const DirectX::XMMATRIX& view,
                                const DirectX::XMMATRIX& projection) {
                                  std::unique_ptr<GradualRectangleConfigInternal> config;

                                  {
                                    if (config_) {
                                      pre_conf_ = *config_;
                                    }
                                    std::swap(config, config_);
                                  }
                                  if (config) {
                                    if (!config->show) {
                                      cur_index_cnt_ = 0;
                                      return false;
                                    }
                                
                                    if (!UpdateVertexBuffer(*config))
                                      return false;
                                
                                    if (!UpdateBufferWithVPSize(vp, *config))
                                      return false;
                                    auto pic_size = XMFLOAT2{config->bottom_right.x - config->top_left.x,
                                                             config->bottom_right.y - config->top_left.y};
                                    XMMATRIX world = BuildMatrixToTextureRender(
                                        pic_size, vp, config->top_left, XMFLOAT2_ONE, XMFLOAT4_EMPTY,
                                        config->rotate, config->shear, config->shear_angle);
                                
                                    DoCopyMatrixBuffer(GetContext(), &world, &view, &projection, matrix_);
                                  }
                                  return true;
}

}  // namespace graphics