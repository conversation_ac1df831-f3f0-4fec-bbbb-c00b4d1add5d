#include "end_to_end_delay_video_model.h"
#include "base/notreached.h"
#include "mediasdk/mediasdk_thread.h"
#include "mediasdk/utils/time_helper.h"

namespace mediasdk {

EndtoEndDelayVideoModel::EndtoEndDelayVideoModel()
    : captures_(EndtoEndDelayCaptures::CreateShared()) {
  LOG(INFO) << "[EndtoEnd] set e2e delay: video_model," << std::hex << this;
}

EndtoEndDelayVideoModel::~EndtoEndDelayVideoModel() {
  LOG(INFO) << "[EndtoEnd] rm e2e delay: video_model, " << std::hex << this;
}

void EndtoEndDelayVideoModel::CollectCaptureTimestmapNS(const char* cap_name,
                                                        int64_t timestamp) {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);

  if (!cap_name) {
    NOTREACHED();
    return;
  }
  int64_t now_ns = nano_now();
  int64_t diff_us = (now_ns - timestamp) / 1'000;
  if (diff_us < 0) {
    diff_us = 0;
  }

  {
    std::lock_guard<std::mutex> lg(mutex_);
    auto it = caputure_cost_map_.find(cap_name);
    if (it == caputure_cost_map_.end()) {
      caputure_cost_map_.insert(
          std::make_pair(cap_name, EndtoEndDelayCostData{diff_us, 1}));
    } else {
      it->second.total_us += diff_us;
      it->second.cnt += 1;
    }
  }

  captures_->SetVisualCaptureTimestmapNS(cap_name, timestamp);
}

std::shared_ptr<EndtoEndDelayCaptureConst>
EndtoEndDelayVideoModel::CompleteCollectCaptures() {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  return captures_->MoveToConst();
}

void EndtoEndDelayVideoModel::OutputMixBegin() {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  mix_begin_ns_ = nano_now();
}

void EndtoEndDelayVideoModel::OutputMixEnd() {
  DCHECK_CURRENTLY_ON(ThreadID::RENDER);
  if (0 == mix_begin_ns_) {
    return;
  }
  int64_t diff_us = (nano_now() - mix_begin_ns_) / 1000;
  if (diff_us < 0) {
    diff_us = 0;
  }

  {
    std::lock_guard<std::mutex> lg(mutex_);
    mix_cost_data_.total_us += diff_us;
    mix_cost_data_.cnt++;
  }
}

// EndtoEndDelay
bool EndtoEndDelayVideoModel::FillCostData(
    std::map<std::string, int64_t>& cost_map) {
  // push stream thread

  std::map<const char*, EndtoEndDelayCostData> caputure_cost_map;
  EndtoEndDelayCostData mix_cost_data;
  {
    std::lock_guard<std::mutex> lg(mutex_);
    caputure_cost_map = std::move(caputure_cost_map_);
    mix_cost_data = mix_cost_data_;
    mix_cost_data_ = EndtoEndDelayCostData();
  }

  // captures
  for (auto& [cap_name, cost] : caputure_cost_map) {
    if (0 == cost.cnt) {
      NOTREACHED();
      return false;
    }
    int64_t cost_ms = (cost.total_us / cost.cnt) / 1000;
    if (0 == strcmp(cap_name, "capture_camera")) {
      cost_map.insert_or_assign("anchor_effect_delay", cost_ms);
    } else {
      cost_map.insert_or_assign(std::string(cap_name) + "_delay", cost_ms);
    }
  }
  // output mix
  if (0 == mix_cost_data.cnt) {
    NOTREACHED();
    return false;
  }
  cost_map.insert_or_assign(
      "anchor_local_mix_delay",
      (mix_cost_data.total_us / mix_cost_data.cnt) / 1000);
  return true;
}

}  // namespace mediasdk
