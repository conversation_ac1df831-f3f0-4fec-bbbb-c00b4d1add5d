﻿#include "device_impl.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>

#include <comdef.h>
#include <d3d11_1.h>
#include <d3dcompiler.h>
#include <dxgi.h>
#include <dxgi1_3.h>
#include <dxgitype.h>
#include <windows.h>
#include <wrl/client.h>
#include "shader_manager.h"

using Microsoft::WRL::ComPtr;

namespace {

constexpr IID dxgiFactory2 = {0x50c83a1c,
                              0xe072,
                              0x4c48,
                              {0x87, 0xb0, 0x36, 0x30, 0xfa, 0x36, 0xa6, 0xd0}};

ComPtr<IDXGIAdapter> FindAdapter(ComPtr<IDXGIFactory1> factory1,
                                 const LUID& adapter_id) {
  if (!factory1) {
    return nullptr;
  }

  int index = 0;
  while (true) {
    ComPtr<IDXGIAdapter1> adapter;
    HRESULT hr = factory1->EnumAdapters1(index++, &adapter);
    if (FAILED(hr)) {
      break;
    }
    DXGI_ADAPTER_DESC desc = {};
    hr = adapter->GetDesc(&desc);
    if (FAILED(hr)) {
      continue;
    }

    if (desc.AdapterLuid.HighPart == adapter_id.HighPart &&
        desc.AdapterLuid.LowPart == adapter_id.LowPart) {
      return adapter;
    }
  }
  return nullptr;
}

}  // namespace

namespace graphics {

DeviceImpl::DeviceImpl() {
  shaders_ = std::make_unique<ShaderManager>();
}

std::shared_ptr<DeviceImpl> DeviceImpl::CreateDevice(
    const CreateDeviceOption& opt) {
  LogGraphicsEnvironment();
  auto ret = std::make_shared<DeviceImpl>();
  if (!ret->InitWithOPT(opt)) {
    LOG(ERROR) << "Failed to Init Device";
    DCHECK(false);
    return nullptr;
  }
  return ret;
}

ComPtr<IDXGIFactory1> DeviceImpl::GetDXGIFactory() {
  return factory_;
}

ComPtr<IDXGIAdapter> DeviceImpl::GetAdapter() {
  return adapter_;
}

LUID DeviceImpl::GetAdapterLUID() {
  DXGI_ADAPTER_DESC desc = {};
  const auto adapter = GetAdapter();
  if (!adapter) {
    return {};
  }
  adapter->GetDesc(&desc);
  return desc.AdapterLuid;
}

ComPtr<ID3D11Device> DeviceImpl::GetDevice() {
  return device_;
}

ComPtr<ID3D11DeviceContext> DeviceImpl::GetContext() {
#ifdef _DEBUG
  return debug_context_;
#else
  return device_context_;
#endif
}

ShaderManager* DeviceImpl::GetShaderManager() {
  return shaders_.get();
}

void DeviceImpl::GetViewport(DirectX::XMFLOAT2& pos, DirectX::XMFLOAT2& size) {
  UINT s = 1;
  D3D11_VIEWPORT vp;
  GetContext()->RSGetViewports(&s, &vp);
  pos.x = vp.TopLeftX;
  pos.y = vp.TopLeftY;
  size.x = vp.Width;
  size.y = vp.Height;
}

void DeviceImpl::SetViewport(const DirectX::XMFLOAT2& pos,
                             const DirectX::XMFLOAT2& size) {
  D3D11_VIEWPORT vp;
  vp.TopLeftX = pos.x;
  vp.TopLeftY = pos.y;
  vp.Width = size.x;
  vp.Height = size.y;
  vp.MinDepth = 0.0F;
  vp.MaxDepth = 1.0F;
  GetContext()->RSSetViewports(1, &vp);
}

void DeviceImpl::SignalDeviceLostEventObserver(
    const graphics::DeviceLostObserver::DevLostEvent& event) {
  {
    if (last_report_ts_) {
      // limit 1s
      if (GetTickCount64() < last_report_ts_ + 4000) {
        return;
      }
    }
    std::lock_guard<std::mutex> lock(lock_dev_lost_observer_);
    for (auto* observer : dev_lost_observer_) {
      observer->OnPreDevLost(this);
    }
    last_report_ts_ = GetTickCount64();
  }

  {
    std::lock_guard<std::mutex> lock(lock_dev_lost_observer_);
    for (auto* observer : dev_lost_observer_) {
      observer->OnDevLost(this, event);
    }
  }
}

void DeviceImpl::AddDeviceLostEventObserver(DeviceLostObserver* observer) {
  std::lock_guard<std::mutex> lock(lock_dev_lost_observer_);
  dev_lost_observer_.push_back(observer);
}

void CheckExtendedResourceSharingFeature(ComPtr<ID3D11Device> device) {
  ComPtr<ID3D11Device1> d3d11_1;
  device->QueryInterface(__uuidof(ID3D11Device1), &d3d11_1);
  if (d3d11_1) {
    D3D11_FEATURE_DATA_D3D11_OPTIONS opts;
    ZeroMemory(&opts, sizeof(opts));
    auto hRes = d3d11_1->CheckFeatureSupport(D3D11_FEATURE_D3D11_OPTIONS, &opts,
                                             sizeof(opts));
    if (FAILED(hRes) || !opts.ExtendedResourceSharing) {
      LOG(INFO) << "ExtendedResourceSharing not supported";
    }
  }
}

void DeviceImpl::RemoveDeviceLostEventObserver(DeviceLostObserver* observer) {
  std::lock_guard<std::mutex> lock(lock_dev_lost_observer_);
  dev_lost_observer_.erase(std::remove(dev_lost_observer_.begin(),
                                       dev_lost_observer_.end(), observer),
                           dev_lost_observer_.end());
}

void CheckThreadingSupport(ComPtr<ID3D11Device> device) {
  D3D11_FEATURE_DATA_THREADING option = {0};
  if (const auto hRes = device->CheckFeatureSupport(
          D3D11_FEATURE_THREADING, reinterpret_cast<void*>(&option),
          sizeof(option));
      FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf(
        "Failed to CheckFeatureSupport(D3D11_FEATURE_THREADING,0x%X)", hRes);
  }
  if (!option.DriverConcurrentCreates) {
    LOG(WARNING) << base::StringPrintf(
        "Direct3D11 driver does not support concurrent creation");
  }
}

bool DeviceImpl::InitWithOPT(const CreateDeviceOption& opt) {
  if (const auto hr = CreateDXGIFactory1(dxgiFactory2, (void**)(&factory_));
      FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateDXGIFactory1 {%s}",
                                     GetErrorString(hr).c_str());
    return false;
  }

  LUID adapter_id;
  adapter_id.LowPart = opt.adapter_id.low;
  adapter_id.HighPart = opt.adapter_id.high;
  adapter_ = FindAdapter(factory_, adapter_id);
  if (!adapter_) {
    LOG(ERROR) << "Failed to FindAdapter";
    return false;
  }

  constexpr D3D_FEATURE_LEVEL features[] = {
      D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_1, D3D_FEATURE_LEVEL_10_0,
      D3D_FEATURE_LEVEL_9_3,  D3D_FEATURE_LEVEL_9_2,  D3D_FEATURE_LEVEL_9_1,
  };
  HRESULT res = D3D11CreateDevice(
      adapter_.Get(), D3D_DRIVER_TYPE::D3D_DRIVER_TYPE_UNKNOWN, nullptr,
#ifdef _DEBUG
      D3D11_CREATE_DEVICE_DEBUG | D3D11_CREATE_DEVICE_BGRA_SUPPORT,
#else
      D3D11_CREATE_DEVICE_BGRA_SUPPORT,
#endif  // _DEBUG
      features, sizeof(features) / sizeof(features[0]), D3D11_SDK_VERSION,
      &device_, &feature_level_, &device_context_);

#ifdef _DEBUG
  debug_context_ = new D3D11ContextDebugLayer(device_context_.Get());
  if (FAILED(res)) {
    LOG(ERROR) << base::StringPrintf("Failed to Create D3D11Device %s",
                                     GetErrorString(res).c_str());
    return false;
  }
  res = DXGIGetDebugInterface1(0, IID_PPV_ARGS(&debug_));
#endif  // _DEBUG
  if (FAILED(res)) {
    LOG(ERROR) << "D3D11CreateDevice Failed[" << GetErrorString(res) << "]";
    return false;
  }
  if (!device_) {
    LOG(ERROR) << "Failed to init Device";
    return false;
  }
  LOG(INFO) << "Success init dx on[" << GetAdapterName(adapter_) << "]";
  LOG(INFO) << "DirectX Device Level [" << feature_level_ << "]";
  {
    DXGI_ADAPTER_DESC adapter_desc = {};
    adapter_->GetDesc(&adapter_desc);
    GetPCIDriverDate(adapter_desc.VendorId, adapter_desc.DeviceId,
                     adapter_desc.SubSysId, driver_date_, driver_ver_);
    LOG(INFO) << "driver date[" << driver_date_ << "] driver version["
              << driver_ver_ << "]";
  }
  CheckExtendedResourceSharingFeature(device_);
  CheckThreadingSupport(device_);

  // same with old
  SetGPUThreadPriority(7);

  D3D11_BLEND_DESC blend_desc = {};
  for (int32_t i = 0; i < 8; i++) {
    blend_desc.RenderTarget[i].BlendEnable = TRUE;
    blend_desc.RenderTarget[i].SrcBlend = D3D11_BLEND_SRC_ALPHA;
    blend_desc.RenderTarget[i].DestBlend = D3D11_BLEND_INV_SRC_ALPHA;
    blend_desc.RenderTarget[i].BlendOp = D3D11_BLEND_OP_ADD;
    blend_desc.RenderTarget[i].SrcBlendAlpha = D3D11_BLEND_ONE;
    blend_desc.RenderTarget[i].DestBlendAlpha = D3D11_BLEND_INV_SRC_ALPHA;
    blend_desc.RenderTarget[i].BlendOpAlpha = D3D11_BLEND_OP_ADD;
    blend_desc.RenderTarget[i].RenderTargetWriteMask =
        D3D11_COLOR_WRITE_ENABLE_ALL;
  }
  res = device_->CreateBlendState(&blend_desc, &enable_bs_);
  if (res != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBlendState(%s)",
                                     GetErrorString(res).c_str());
    return false;
  }

  for (int32_t i = 0; i < 8; i++) {
    blend_desc.RenderTarget[i].BlendEnable = FALSE;
  }
  res = device_->CreateBlendState(&blend_desc, &disable_bs_);
  if (res != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateBlendState(%s)",
                                     GetErrorString(res).c_str());
    return false;
  }

  constexpr FLOAT blend_factor[4] = {1.0F, 1.0F, 1.0F, 1.0F};
  GetContext()->OMSetBlendState(enable_bs_.Get(), blend_factor, 0xFFFFFFFF);

  D3D11_DEPTH_STENCIL_DESC depth_desc;
  depth_desc.DepthEnable = TRUE;
  depth_desc.DepthWriteMask = D3D11_DEPTH_WRITE_MASK_ALL;
  depth_desc.DepthFunc = D3D11_COMPARISON_LESS;
  depth_desc.StencilEnable = TRUE;
  depth_desc.StencilReadMask = 0xFF;
  depth_desc.StencilWriteMask = 0xFF;
  depth_desc.FrontFace.StencilFailOp = D3D11_STENCIL_OP_KEEP;
  depth_desc.FrontFace.StencilDepthFailOp = D3D11_STENCIL_OP_KEEP;
  depth_desc.FrontFace.StencilPassOp = D3D11_STENCIL_OP_KEEP;
  depth_desc.FrontFace.StencilFunc = D3D11_COMPARISON_ALWAYS;
  depth_desc.BackFace.StencilFailOp = D3D11_STENCIL_OP_KEEP;
  depth_desc.BackFace.StencilDepthFailOp = D3D11_STENCIL_OP_KEEP;
  depth_desc.BackFace.StencilPassOp = D3D11_STENCIL_OP_KEEP;
  depth_desc.BackFace.StencilFunc = D3D11_COMPARISON_ALWAYS;

  depth_desc.DepthEnable = FALSE;
  depth_desc.StencilEnable = FALSE;
  res = device_->CreateDepthStencilState(&depth_desc, &disable_ds_);
  if (res != S_OK) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateDepthStencilState(%s)",
                                     GetErrorString(res).c_str());
    return false;
  }

  // Close depth test
  GetContext()->OMSetDepthStencilState(disable_ds_.Get(), 1);

  return true;
}

bool DeviceImpl::D3D11CompileShader(const char* shader,
                                    const char* name,
                                    const char* target,
                                    ComPtr<ID3D10Blob>& vs) {
  ComPtr<ID3D10Blob> error_code;
  if (const auto res = D3DCompile(
          shader, strlen(shader), nullptr, nullptr, nullptr, name, target,
          D3D10_SHADER_OPTIMIZATION_LEVEL1, 0, &vs, &error_code);
      FAILED(res)) {
    if (error_code) {
      const auto* error = static_cast<char*>(error_code->GetBufferPointer());
      LOG(ERROR) << base::StringPrintf("Failed to D3DCompile %s", error);
    } else {
      LOG(ERROR) << base::StringPrintf("Failed to D3DCompile %s",
                                       GetErrorString(res).c_str());
    }
    return false;
  }
  return true;
}

bool DeviceImpl::D3D11CreateVertexShader(const char* shader,
                                         const char* name,
                                         ComPtr<ID3D11VertexShader>& vs,
                                         ComPtr<ID3D10Blob>& vs_blob) {
  if (!D3D11CompileShader(shader, name, "vs_4_0", vs_blob)) {
    return false;
  }

  if (const auto res = GetDevice()->CreateVertexShader(
          vs_blob->GetBufferPointer(), vs_blob->GetBufferSize(), nullptr, &vs);
      FAILED(res)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateVertexShader %s",
                                     GetErrorString(res).c_str());
    return false;
  }
  return true;
}

bool DeviceImpl::D3D11CreatePixelShader(const char* shader,
                                        const char* name,
                                        ComPtr<ID3D11PixelShader>& ps) {
  ComPtr<ID3D10Blob> ps_blob;
  if (!D3D11CompileShader(shader, name, "ps_4_0", ps_blob)) {
    return false;
  }
  if (const auto res = GetDevice()->CreatePixelShader(
          ps_blob->GetBufferPointer(), ps_blob->GetBufferSize(), nullptr, &ps);
      FAILED(res)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreatePixelShader %s",
                                     GetErrorString(res).c_str());
    return false;
  }
  return true;
}

bool DeviceImpl::CompileShader(CompileShaderParam& param) {
  HRESULT res = S_OK;
  ComPtr<ID3D10Blob> vs_blob;

  if (param.vs && param.vs_name) {
    if (!D3D11CreateVertexShader(param.vs, param.vs_name, param.vs_shader_,
                                 vs_blob)) {
      return false;
    }
  }

  if (param.ps) {
    if (!D3D11CreatePixelShader(param.ps, param.ps_name, param.ps_shader_)) {
      return false;
    }
  }

  if (param.layout_descs_ && param.layout_cnt_ > 0) {
    if (param.vs && param.vs_name) {
      res = this->GetDevice()->CreateInputLayout(
          param.layout_descs_, param.layout_cnt_, vs_blob->GetBufferPointer(),
          vs_blob->GetBufferSize(), &param.layout_);
      if (FAILED(res)) {
        LOG(ERROR) << base::StringPrintf("Failed to CreateInputLayout %s",
                                         GetErrorString(res).c_str());
        return false;
      }
    }
  }
  return true;
}

void DeviceImpl::AllowBlend(const bool allow) {
  constexpr float blend_factor[4] = {1.0F, 1.0F, 1.0F, 1.0F};
  constexpr UINT flag = 0xFFFFFFFF;
  GetContext()->OMSetBlendState(allow ? enable_bs_.Get() : disable_bs_.Get(),
                                blend_factor, flag);
}

void DeviceImpl::Destroy() {
  if (disable_bs_) {
    disable_bs_.Reset();
  }
  if (enable_bs_) {
    enable_bs_.Reset();
  }
  if (disable_ds_) {
    disable_ds_.Reset();
  }
#ifdef _DEBUG
  if (debug_context_) {
    delete debug_context_;
    debug_context_ = nullptr;
  }
#endif

  shaders_.reset();

  if (device_context_) {
    device_context_->Flush();

    device_context_.Reset();
  }

  if (device_) {
    device_.Reset();
  }

  if (adapter_) {
    adapter_.Reset();
  }
  if (factory_) {
    factory_.Reset();
  }
  ReportLiveObjects();
#ifdef _DEBUG
  if (debug_) {
    debug_.Reset();
  }
#endif
}

void DeviceImpl::ReportLiveObjects() {
#ifdef _DEBUG
  if (debug_) {
    debug_->ReportLiveObjects(DXGI_DEBUG_ALL, DXGI_DEBUG_RLO_ALL);
  }
#endif
}

bool DeviceImpl::SetGPUThreadPriority(const int32_t priority) {
  ComPtr<IDXGIDevice1> dxgi1;
  device_->QueryInterface(__uuidof(IDXGIDevice1), &dxgi1);
  if (!dxgi1) {
    return false;
  }

  if (const auto hRes = dxgi1->SetGPUThreadPriority(priority); FAILED(hRes)) {
    LOG(WARNING) << base::StringPrintf("Waning to SetGPUThreadPriority(0x%X)",
                                       hRes);
    return false;
  }
  return true;
}

std::string DeviceImpl::GetDriverDate() const {
  return driver_date_;
}

std::string DeviceImpl::GetDriverVersion() const {
  return driver_ver_;
}

void DeviceImpl::CheckDevLost(HRESULT res) {
  bool callback = false;
  bool need_restart = false;

  // the two errors can not be restored
  if (res == DXGI_ERROR_DEVICE_REMOVED) {
    need_restart = true;
  } else if (res == DXGI_ERROR_DEVICE_RESET) {
    need_restart = true;
  }

  if (FAILED(res)) {
    callback = true;  // the current video frame must be error
  }
  if (callback) {
    // device lost
    graphics::DeviceLostObserver::DevLostEvent lost_event = {};
    HRESULT removed_reason = device_->GetDeviceRemovedReason();
    lost_event.error = res;
    lost_event.remove_reason = removed_reason;
    lost_event.need_restart = need_restart;
    lost_event.driver_date = GetDriverDate();
    lost_event.driver_name = GetAdapterName(GetAdapter());
    lost_event.driver_ver = GetDriverVersion();
    LOG(WARNING) << "error [" << base::StringPrintf("%x", res)
                 << "] remove reason ["
                 << base::StringPrintf("%x", removed_reason)
                 << "] need restart [" << need_restart << "]";
    SignalDeviceLostEventObserver(lost_event);
  }
}

DeviceImpl::~DeviceImpl() {
  Destroy();
}
}  // namespace graphics