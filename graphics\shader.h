#pragma once
#include <base/check.h>
#include "device.h"

namespace graphics {

#pragma pack(push)
#pragma pack(16)

struct MATRIXBUFFER {
  DirectX::XMMATRIX world;
  DirectX::XMMATRIX view;
  DirectX::XMMATRIX projection;
};

struct CROPBUFFER {
  DirectX::XMFLOAT2 scale;
  DirectX::XMFLOAT2 translate;
};

struct PS_BUFFER {
  float force_alpha = -1.0;
  float pad0;
  float pad1;
  float pad2;

  float outside_gamma;
  float opacity = 1.0f;
  float tone_map_param;
  float color_adjust_flag;

  DirectX::XMFLOAT3 chroma_key;
  float chroma_key_flags;

  float pixel_size_x;
  float pixel_size_y;
  float similarity;
  float smoothness;

  float spill;
  float extrude;
  float chroma_key_opacity = 1.0f;
  float chroma_key_gamma;

  DirectX::XMMATRIX chroma_key_color_matrix;
  DirectX::XMMATRIX color_matrix;

  DirectX::XMFLOAT4 sharpness_param; // width, height, sharpness, flags

  // is_luminance_wipe, luminance_map_progress, luminance_map_softness, luminance_invert
  DirectX::XMFLOAT4 luminance_map_param;
  float is_use_luminance_map_param;
  DirectX::XMFLOAT3 luminance_map_pending;

  // POINT_NINE_BUFFER
  // just use x and y
  DirectX::XMFLOAT4 point_nine_scale;
  // [top_left, top_right, left_top, left_bottom]
  DirectX::XMFLOAT4 border;

  // CLIP_MASK_BUFFER
  // [left, top, right, bottom], value is in [0.0, 1.0]
  DirectX::XMFLOAT4 clip_mask;

  // CORNER_BUFFER
  DirectX::XMFLOAT4 corner_radius;
  DirectX::XMFLOAT4 corner_clip;
  // [scale.x / scale.y, unused, unused, unused]
  DirectX::XMFLOAT4 corner_scale;

  // BORDER_BUFFER
  DirectX::XMFLOAT4 border_color;
  DirectX::XMFLOAT4 border_width;
};

struct LUT_PS_BUFFER {
    DirectX::XMFLOAT4 domain_min;
    DirectX::XMFLOAT4 domain_max;
    float amount;
    int pass_through_alpha;
};

struct SCALE_PS_BUFFER {
  float stretch_start_point;
  float stretch_ratio;
  int stretch_direction;
};

enum TONEMAP_TYPE { REINHARD = 0, UNCHARTED = 1, ACES = 2 };

enum SAMPLE_TYPE { LINER = 0, ANISOTROPIC = 1 };

struct TONEMAP_INFO {
  TONEMAP_TYPE type_ = REINHARD;
  std::string method_;
  std::string obj_name_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_shader_ = nullptr;
};

#pragma pack(pop)

inline bool DoCopyMatrixBuffer(
    ID3D11DeviceContext* context,
    const DirectX::XMMATRIX* world,
    const DirectX::XMMATRIX* view,
    const DirectX::XMMATRIX* projection,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& matrix_buffer) {
  if (!world || !view || !projection)
    return true;
  DirectX::XMMATRIX world_matrix = XMMatrixTranspose(*world);
  DirectX::XMMATRIX view_matrix = XMMatrixTranspose(*view);
  DirectX::XMMATRIX projection_matrix = XMMatrixTranspose(*projection);
  D3D11_MAPPED_SUBRESOURCE map = {};
  if (SUCCEEDED(context->Map(matrix_buffer.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0,
                             &map))) {
    auto* data_ptr = static_cast<MATRIXBUFFER*>(map.pData);
    data_ptr->world = world_matrix;
    data_ptr->view = view_matrix;
    data_ptr->projection = projection_matrix;
    context->Unmap(matrix_buffer.Get(), 0);
    return true;
  }
  DCHECK(false);

  return false;
}

class Shader;
using CreateShaderFunc = std::shared_ptr<Shader> (*)(const char* type);

struct ShaderItem {
  const char* type = nullptr;
  const char* desc = nullptr;
  CreateShaderFunc factory = nullptr;
};

using EnumPluginCallBack = void (*)(void* param, ShaderItem);

using EnumPluginsFunction = void (*)(void* param, EnumPluginCallBack);

class Shader {
 public:
  virtual bool Init(const std::shared_ptr<Device>&) = 0;
  virtual void Destroy() = 0;
  virtual ~Shader() = default;
};
}  // namespace graphics
