#pragma once

#include <memory>

#include "utils/audio/audio_format.h"
#include "utils/audio/audio_frame.h"

namespace mediasdk {

class AudioInputFrameObserver {
 public:
  virtual ~AudioInputFrameObserver() = default;

  virtual void OnInputAudioFrame(const std::string& input_id,
                                 const AudioFrame& frame,
                                 const AudioFormat& format){};

  virtual void OnProcessedAudioFrame(const std::string& input_id,
                                     const AudioFrame& frame,
                                     const AudioFormat& format){};
};

}  // namespace mediasdk
