#pragma once

#include "mediasdk_callback_defines.h"
#include "mediasdk_effect_platform_event_observer.h"
#include "mediasdk_export.h"
#include "mediasdk_string.hpp"

#ifdef __cplusplus
extern "C" {
#endif  // __cplusplus
namespace mediasdk {

// EffectPlatformInitialize:Initialize the EP module by passing in an
// initialized JSON and a closure function
//   which must be initialized after the initialization of the MediaSDK.
// json_param:
// {
//   "appVersion":string, //version of application
//   "deviceType": string,//"Build.MODEL"
//   "appId": string,     //id of application
//   "accessKey": string, //Access key of application in special effects backend
//   "channel": string,   //Environment "test" or "online"
//   "effectCacheDir": string, //Special effects save path
//   "modelCacheDir": string, //Model save path
//   "builtInModelDir": string, //Built in model path
//   "lokiHost": string, //https://api.tiktokv.com
//   "veCloudHost": string, //https://api.tiktokv.com
//   "modelStatus": string, //Model environment (0: internal testing, online: 1,
//        default is 1),
//        provide a switch for QA to switch to the model internal testing
//        environment (modelStatus=0). Some models that have not reached the
//        online standard and need to be updated will be tested in the internal
//        testing environment first.
//   "region":string, //Some models will be distributed according
//        to regions to obtain as much input as possible.
//   "deviceId": string,
//   "userId": string,
//   "ttlsHardwareLevel":string
// }
// Callback: Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API EffectPlatformInitialize(const char* json_param,
                                                     Closure closure);

// EffectPlatformUninitialize: The cleaning function of the EP module
//   needs to be paired with the Effectiveness PlatformInitialize function.
// Callback: Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API EffectPlatformUninitialize(Closure closure);

// EffectPlatformRegistObserver: Observer used to register general events for EP
//   modules.
// event_oberver: The observer used to call back the function
//   will remove the reference when the Effect Platform Uninitializes.
// Callback: Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
EffectPlatformRegistObserver(MediaSDKEPEventObserver* observer,
                             Closure closure);

// EffectPlatformUnregistObserver: Unregist observer for EP modules.
// event_oberver: The observer registed.
// Callback: Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
EffectPlatformUnregistObserver(MediaSDKEPEventObserver* observer,
                               Closure closure);

// EffectPlatformUpdateConfig: Set the user information and ttls_ hardware level
//   information for downloading Effect resources from the network, which are
//   used as URL parameters for requests.
// user_id: user_id.
// hardware_level: ttls_hardware_level.
// Callback: Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
EffectPlatformUpdateConfig(const char* user_id,
                           const char* hardware_level,
                           Closure closure);

// EffectPlatformLoadModels: Load the model, search locally, download if not
// available,
//   and perform information callback in the observer passed by the
//   Effectiveness Platform Register Observer.
// request_id: request id.
// model_name: Name of model.
// requirments: The resource array on which the model depends.
// Callback: Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
EffectPlatformLoadModels(const char* request_id,
                         const char* model_name,
                         MediaSDKStringArray* requirments,
                         Closure closure);

}  // namespace mediasdk

#ifdef __cplusplus
}
#endif  // __cplusplus