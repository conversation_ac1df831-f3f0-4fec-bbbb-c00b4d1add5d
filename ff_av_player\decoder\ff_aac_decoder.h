#pragma once
#include <memory>
#include "ff_decoder.h"
#include "ffmpeg_common.h"

class FF_AAC_Decoder {
 public:
  std::shared_ptr<AVFrameRAII> Decode(const uint8_t* data, int32_t data_size);

  bool OpenWithExtraData(const uint8_t* data, int32_t data_size);

  ~FF_AAC_Decoder();

 private:
  std::shared_ptr<AVFrameRAII> buffered_frame_;
  FFDecoder decoder_;
  bool have_try_ = false;
  bool init_suc_ = false;
  std::shared_ptr<AVPacketRAIIFree> buffered_packet_ = nullptr;
};