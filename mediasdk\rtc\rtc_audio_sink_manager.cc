#include "rtc_audio_sink_manager.h"

namespace mediasdk {

RTCAudioSinkManager::RTCAudioSinkManager(IRTCManager* manager)
    : rtc_manager_(manager) {}

int32_t RTCAudioSinkManager::SetAudioRender(
    const std::string& user_id,
    int32_t stream_index,
    std::shared_ptr<RTCAudioRender> render) {
  if (render) {
    AddAudioRenderForAudioSink(user_id, stream_index, render);
  } else {
    RemoveAudioRenderForAudioSink(user_id, stream_index);
  }

  return 0;
}

void RTCAudioSinkManager::SetRTCEngineCreated(bool created) {
  LOG(INFO) << "SetRTCEngineCreated , from: " << rtc_engine_created_
            << " to: " << created;
  if (!rtc_engine_created_ && created) {
    rtc_engine_created_ = true;
    for (auto& item : audio_render_list_) {
      if (!audio_sink_) {
        audio_sink_ = std::make_unique<RTCAudioSink>(rtc_manager_);
      }
      audio_sink_->AddAudioRender(std::get<0>(item), std::get<1>(item),
                                  std::get<2>(item));
    }
  } else if (rtc_engine_created_ && !created) {
    for (auto& item : audio_render_list_) {
      if (audio_sink_) {
        audio_sink_->RemoveAudioRender(std::get<0>(item), std::get<1>(item));
      }
    }
    audio_sink_.reset();
    rtc_engine_created_ = false;
  }
}

void RTCAudioSinkManager::AddAudioRenderForAudioSink(
    const std::string& user_id,
    int32_t stream_index,
    std::shared_ptr<RTCAudioRender> render) {
  LOG(INFO) << "AddAudioRenderForAudioSink, rtc_engine_created_: "
            << rtc_engine_created_;

  audio_render_list_.push_back(std::make_tuple(user_id, stream_index, render));

  if (rtc_engine_created_) {
    if (!audio_sink_) {
      audio_sink_ = std::make_unique<RTCAudioSink>(rtc_manager_);
    }
    audio_sink_->AddAudioRender(user_id, stream_index, std::move(render));
  } else {
    LOG(INFO)
        << "RTC Engine not create, wait create and add audio render. user_id: "
        << user_id << " stream_index: " << stream_index;
  }
}

void RTCAudioSinkManager::RemoveAudioRenderForAudioSink(
    const std::string& user_id,
    int32_t stream_index) {
  LOG(INFO) << "RemoveAudioRenderForAudioSink, rtc_engine_created_: "
            << rtc_engine_created_;

  if (audio_sink_) {
    audio_sink_->RemoveAudioRender(user_id, stream_index);
  } else {
    LOG(WARNING) << "[RTC] has no audio sink for remove render, user_id:"
                 << user_id << ",stream_index:" << stream_index;
  }

  for (auto item = audio_render_list_.begin();
       item != audio_render_list_.end();) {
    if (std::get<0>(*item) == user_id && std::get<1>(*item) == stream_index) {
      item = audio_render_list_.erase(item);
    } else {
      item++;
    }
  }
}

}  // namespace mediasdk