#pragma once

#include "base/callback.h"
#include "base/files/file_path.h"
#include "base/memory/weak_ptr.h"
#include "base/native_library.h"
#include "mediasdk/component.h"
#include "mediasdk/public/mediasdk_callback_defines.h"

namespace mediasdk {

class VisualSourceFactory;
class AudioInputSourceFactory;

class AudioInputSource;
class AudioInputProxy;

class VisualSource;
class VisualProxy;

class AudioEncoderSource;
class AudioEncoderProxy;

class StreamServiceSource;
class StreamServiceProxy;

class VideoEncoderSource;
class VideoEncoderProxy;

class VisualFilter;

class AudioFilter;
class AudioFilterProxy;

class AudioFormat;

class PluginService : public Component,
                      public base::SupportsWeakPtr<PluginService> {
 public:
  static constexpr char kComponentName[] = "PluginService";

  // Component:
  ThreadID GetExecThreadId() override { return ThreadID::PLUGIN; }

  PluginService() = default;

  ~PluginService() override = default;

  virtual void ReportLoadEvent() = 0;

  virtual std::shared_ptr<PluginInfoArray> EnumSource(PluginType type) = 0;

  // Visual
  virtual std::shared_ptr<MediaSDKString> EnumVisualInput(
      const std::string& plugin_name,
      const std::string& json_config) = 0;

  virtual std::shared_ptr<MediaSDKString> EnumVisualFormat(
      const std::string& plugin_name,
      const std::string& device_id) = 0;

  virtual std::shared_ptr<MediaSDKString> EnumAudioFormat(
      const std::string& plugin_name,
      const std::string& device_id) = 0;

  virtual std::shared_ptr<VisualSource> CreateVisualSource(
      std::shared_ptr<VisualProxy> proxy,
      const std::string& plugin_name,
      const std::string& json_params) = 0;

  virtual bool ReopenVisual(std::shared_ptr<VisualSource> source,
                            const std::string& json_params) = 0;

  virtual bool PauseVisualCapture(std::shared_ptr<VisualSource> source) = 0;

  virtual ResultBoolBool IsVisualCapturePause(
      std::shared_ptr<VisualSource> source) = 0;

  virtual bool ContinueVisualCapture(std::shared_ptr<VisualSource> source) = 0;

  virtual void DestroyVisualSource(std::shared_ptr<VisualSource> source) = 0;

  virtual void DestroyAllVisualSource() = 0;

  virtual std::shared_ptr<MediaSDKString> GetVisualProperty(
      std::shared_ptr<VisualSource> source,
      const std::string& key) = 0;

  virtual bool SetVisualProperty(std::shared_ptr<VisualSource> source,
                                 const std::string& key,
                                 const std::string& json) = 0;
  virtual bool DoVisualAction(std::shared_ptr<VisualSource> source,
                              const std::string& json) = 0;

  virtual std::shared_ptr<MediaSDKString> GetVisualSourceProperty(
      const std::string& plugin_name,
      const std::string& json) = 0;

  virtual bool SetVisualSourceProperty(const std::string& plugin_name,
                                       const std::string& key,
                                       const std::string& json) = 0;

  virtual bool RegisterExternalVisualSourceFactory(
      const PluginInfo& info,
      std::shared_ptr<VisualSourceFactory> factory) = 0;

  virtual bool RegisterExternalAudioInputSourceFactory(
      const PluginInfo& info,
      std::shared_ptr<AudioInputSourceFactory> factory) = 0;

  // Visual Filter
  virtual std::shared_ptr<VisualFilter> CreateVisualFilter(
      const std::string& plugin_name,
      const std::string& json_params) = 0;

  virtual std::shared_ptr<MediaSDKString> GetVisualFilterProperty(
      std::shared_ptr<VisualFilter> filter,
      const std::string& key) = 0;

  virtual bool SetVisualFilterProperty(std::shared_ptr<VisualFilter> filter,
                                       const std::string& key,
                                       const std::string& json) = 0;

  virtual std::shared_ptr<MediaSDKString> VisualFilterAction(
      std::shared_ptr<VisualFilter> filter,
      const std::string& action,
      const std::string& param) = 0;

  // Audio input
  virtual std::shared_ptr<MediaSDKString> EnumAudioInput(
      const std::string& plugin_name) = 0;

  virtual std::shared_ptr<MediaSDKString> GetDefaultCaptureAudio(
      const std::string& plugin_name) = 0;

  virtual std::shared_ptr<MediaSDKString> GetDefaultRenderAudio(
      const std::string& plugin_name) = 0;

  virtual std::shared_ptr<MediaSDKString> EnumCaptureAudio(
      const std::string& plugin_name) = 0;

  virtual std::shared_ptr<MediaSDKString> EnumRenderAudio(
      const std::string& plugin_name) = 0;

  using CreateAudioSourceCallbackType =
      base::OnceCallback<void(std::shared_ptr<AudioInputSource>)>;

  virtual std::shared_ptr<AudioInputSource> CreateAudioInputSource(
      std::shared_ptr<AudioInputProxy> proxy,
      const std::string& plugin_name,
      const std::string& json_params) = 0;

  virtual void DestroyAudioInputSource(
      std::shared_ptr<AudioInputSource> source) = 0;

  virtual void DestroyAudioFilter(
      std::vector<std::shared_ptr<AudioFilter>> filter) = 0;

  virtual void DestroyAllAudioInputSource() = 0;

  virtual void DestroyAllAudioFilter() = 0;

  // Audio filter
  virtual std::shared_ptr<AudioFilter> CreateAudioFilter(
      const std::string& plugin_name,
      const std::string& json_params) = 0;

  virtual bool InitAudioFilter(std::shared_ptr<AudioFilter> filter,
                               const AudioFormat& audio_format,
                               AudioFilterProxy* proxy) = 0;

  virtual void UninitAudioFilter(std::shared_ptr<AudioFilter> filter) = 0;

  virtual bool SetAudioFilterEnable(std::shared_ptr<AudioFilter> filter,
                                    bool enable) = 0;

  virtual ResultBoolBool IsAudioFilterEnable(
      std::shared_ptr<AudioFilter> filter) = 0;

  virtual ResultBoolString GetAudioFilterProperty(
      std::shared_ptr<AudioFilter> filter,
      const std::string& key) = 0;

  virtual bool SetAudioFilterProperty(std::shared_ptr<AudioFilter> filter,
                                      const std::string& key,
                                      const std::string& json) = 0;

  virtual ResultBoolString AudioFilterAction(
      std::shared_ptr<AudioFilter> filter,
      const std::string& action,
      const std::string& param) = 0;

  // Stream service
  virtual std::shared_ptr<StreamServiceSource> CreateStreamServiceSource(
      std::shared_ptr<StreamServiceProxy> proxy,
      const std::string& plugin_name,
      const std::string& json_params) = 0;

  virtual void DestroyStreamServiceSource(
      std::shared_ptr<StreamServiceSource> source) = 0;

  virtual void DestroyAllStreamServiceSource() = 0;

  // Video Encoder
  virtual std::shared_ptr<VideoEncoderSource> CreateVideoEncoderSource(
      std::shared_ptr<VideoEncoderProxy> proxy,
      const std::string& plugin_name,
      const std::string& json_params) = 0;

  virtual bool TestEncoderSessionCountSupported(const std::string& plugin_name,
                                                uint32_t count) = 0;

  // Audio Encoder
  virtual std::shared_ptr<AudioEncoderSource> CreateAudioEncoderSource(
      std::shared_ptr<AudioEncoderProxy> proxy,
      const std::string& plugin_name,
      const std::string& json_params) = 0;
};

}  // namespace mediasdk
