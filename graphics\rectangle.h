#pragma once
#include "device.h"

namespace graphics {
// current just for draw to graphics
// Enum to define the direction of the gradient
enum class GradientDirection {
  TOP_TO_BOTTOM,
  BOTTOM_TO_TOP,
  LEFT_TO_RIGHT,
  RIGHT_TO_LEFT,
};

class GRAPHICS_EXPORT Rectangle;
class GRAPHICS_EXPORT GradualRectangle; // Forward declaration

class GRAPHICS_EXPORT Rectangle {
 public:
  struct GRAPHICS_EXPORT RectangleConfig {
    DirectX::XMFLOAT2 vp_size = DirectX::XMFLOAT2{0.f, 0.f};
    DirectX::XMFLOAT2 top_left;
    DirectX::XMFLOAT2 bottom_right;
    DirectX::XMFLOAT4 color;
    float rotate = .0f;
    float shear_angle = 0.f;
    DirectX::XMFLOAT2 shear = DirectX::XMFLOAT2{1.f, 1.f};
    bool fill = false;  // fill rectangle
    float thinkness = 1.0f;
  };

 public:
  virtual bool DrawTo(const DirectX::XMFLOAT2& vp,
                      const DirectX::XMMATRIX& view,
                      const DirectX::XMMATRIX& projection) = 0;

  virtual void UpdateRectangleConf(const RectangleConfig* conf) = 0;
  virtual int32_t GetIndexCnt() = 0;
  virtual void Destroy() = 0;

  virtual ~Rectangle() {}
};

GRAPHICS_EXPORT std::shared_ptr<Rectangle> CreateRectangle(Device& inst);

// GradualRectangle class, similar to Rectangle but with gradient capabilities
class GRAPHICS_EXPORT GradualRectangle {
 public:
  struct GRAPHICS_EXPORT GradualRectangleConfig {
    DirectX::XMFLOAT2 vp_size = DirectX::XMFLOAT2{0.f, 0.f};
    DirectX::XMFLOAT2 top_left;
    DirectX::XMFLOAT2 bottom_right;
    DirectX::XMFLOAT4 color; // Start color for gradient
    // DirectX::XMFLOAT4 end_color; // Optional: End color for gradient
    GradientDirection direction = GradientDirection::TOP_TO_BOTTOM;
    float rotate = .0f;
    float shear_angle = 0.f;
    DirectX::XMFLOAT2 shear = DirectX::XMFLOAT2{1.f, 1.f};
    bool fill = false;  // fill rectangle
    float thinkness = 1.0f;
  };

 public:
  virtual bool DrawTo(const DirectX::XMFLOAT2& vp,
                      const DirectX::XMMATRIX& view,
                      const DirectX::XMMATRIX& projection) = 0;

  virtual void UpdateRectangleConf(const GradualRectangleConfig* conf) = 0;
  virtual int32_t GetIndexCnt() = 0;
  virtual void Destroy() = 0;

  virtual ~GradualRectangle() {}
};

GRAPHICS_EXPORT std::shared_ptr<GradualRectangle> CreateGradualRectangle(Device& inst);

}  // namespace graphics

