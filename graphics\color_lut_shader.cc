#include "color_lut_shader.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include "DXSimpleMath.h"
#include "pixel_shader.h"
#include "vertex_shader.h"

using namespace Microsoft::WRL;

namespace graphics {

bool ColorLutShader::Init(const std::shared_ptr<Device>& ins) {
  if (!try_init_) {
    init_suc_ = DoInit(ins);
    try_init_ = true;
  }
  return init_suc_;
}

bool ColorLutShader::DoInit(const std::shared_ptr<Device>& ins) {
  device_ = ins;
  Device::CompileShaderParam param = {};
  param.ps = COLOR_LUT_PIXEL_SHADER();
  param.vs = TEXTURE_VERTEX_SHADER();
  param.ps_name = "COLOR_LUT_3D_PS_MAIN";
  param.vs_name = "VS_MAIN";
  D3D11_INPUT_ELEMENT_DESC layout[2];
  layout[0].SemanticName = "POSITION";
  layout[0].SemanticIndex = 0;
  layout[0].Format = DXGI_FORMAT_R32G32B32_FLOAT;
  layout[0].InputSlot = 0;
  layout[0].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
  layout[0].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
  layout[0].InstanceDataStepRate = 0;
  layout[1].SemanticName = "TEXCOORD";
  layout[1].SemanticIndex = 0;
  layout[1].Format = DXGI_FORMAT_R32G32_FLOAT;
  layout[1].InputSlot = 0;
  layout[1].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
  layout[1].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
  layout[1].InstanceDataStepRate = 0;
  param.layout_descs_ = layout;
  param.layout_cnt_ = 2;
  if (!device_->CompileShader(param)) {
    return false;
  }
  vs_shader_ = param.vs_shader_;

  D3D11SetDebugObjectName(vs_shader_.Get(), "color_lut_shader_vs");
  ps_shader_3d_ = param.ps_shader_;
  layout_ = param.layout_;
  D3D11SetDebugObjectName(layout_.Get(), "color_lut_shader_layout");
  D3D11SetDebugObjectName(ps_shader_3d_.Get(), "color_3d_lut_shader_ps");
  param.ps_name = "COLOR_LUT_1D_PS_MAIN";
  param.vs_name = nullptr;
  if (!device_->CompileShader(param)) {
      return false;
  }
  ps_shader_1d_ = param.ps_shader_;

  return true;
}

ID3D11Device* ColorLutShader::GetDevice_() {
  return device_->GetDevice().Get();
}

ID3D11DeviceContext* ColorLutShader::GetContext_() {
  return device_->GetContext().Get();
}

void ColorLutShader::Color1DLutRender(ID3D11ShaderResourceView* view,
    ID3D11ShaderResourceView* lut_1d_view,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& vertex_buffer,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& index_buffer,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& crop_buffer,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& matrix_buffer,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_buffer)
{
    UINT stride = sizeof(ColorLutShader::VERTEXTYPE);
    UINT offset = 0;
    auto context = GetContext_();
    context->IASetVertexBuffers(0, 1, vertex_buffer.GetAddressOf(), &stride,
        &offset);
    context->IASetIndexBuffer(index_buffer.Get(), DXGI_FORMAT_R32_UINT, 0);
    context->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);
    context->VSSetConstantBuffers(0, 1, matrix_buffer.GetAddressOf());
    context->VSSetConstantBuffers(1, 1, crop_buffer.GetAddressOf());
    context->PSSetConstantBuffers(0, 1, ps_buffer.GetAddressOf());
    context->PSSetShaderResources(0, 1, &view);
    context->PSSetShaderResources(1, 1, &lut_1d_view);
    context->IASetInputLayout(layout_.Get());
    context->VSSetShader(vs_shader_.Get(), NULL, 0);
    context->PSSetShader(ps_shader_1d_.Get(), NULL, 0);
    context->PSSetSamplers(0, 1, sampler_.GetAddressOf());
    context->DrawIndexed(6, 0, 0);
}

void ColorLutShader::Color3DLutRender(ID3D11ShaderResourceView* view,
                                ID3D11ShaderResourceView* lut_3d_view,
                                ComPtr<ID3D11Buffer>& vertex_buffer,
                                ComPtr<ID3D11Buffer>& index_buffer,
                                ComPtr<ID3D11Buffer>& crop_buffer,
                                ComPtr<ID3D11Buffer>& matrix_buffer,
                                ComPtr<ID3D11Buffer>& ps_buffer) {
  UINT stride = sizeof(ColorLutShader::VERTEXTYPE);
  UINT offset = 0;
  auto context = GetContext_();
  context->IASetVertexBuffers(0, 1, vertex_buffer.GetAddressOf(), &stride,
                              &offset);
  context->IASetIndexBuffer(index_buffer.Get(), DXGI_FORMAT_R32_UINT, 0);
  context->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);
  context->VSSetConstantBuffers(0, 1, matrix_buffer.GetAddressOf());
  context->VSSetConstantBuffers(1, 1, crop_buffer.GetAddressOf());
  context->PSSetConstantBuffers(0, 1, ps_buffer.GetAddressOf());
  context->PSSetShaderResources(0, 1, &view);
  context->PSSetShaderResources(2, 1, &lut_3d_view);
  context->IASetInputLayout(layout_.Get());
  context->VSSetShader(vs_shader_.Get(), NULL, 0);
  context->PSSetShader(ps_shader_3d_.Get(), NULL, 0);
  context->PSSetSamplers(0, 1, sampler_.GetAddressOf());
  context->DrawIndexed(6, 0, 0);
}

void ColorLutShader::Destroy() {
    if (sampler_) {
        sampler_.Reset();
    }
  if (vs_shader_) {
    vs_shader_.Reset();
  }
  if (ps_shader_3d_) {
      ps_shader_3d_.Reset();
  }
}

ColorLutShader::~ColorLutShader() {
  ColorLutShader::Destroy();
}

}  // namespace graphics