#include "color_revert_graphics_impl.h"

#include <base/logging.h>
#include "color_revert_shader.h"
#include "shader_manager.h"

namespace graphics {

ColorRevertGraphicsImpl::ColorRevertGraphicsImpl(Device& ins) : device_(ins) {}

void ColorRevertGraphicsImpl::Destroy() {
  if (graphics_) {
    graphics_->Destroy();
    graphics_ = nullptr;
  }

  if (ps_buffer_) {
    ps_buffer_.Reset();
  }
}

std::shared_ptr<Texture> ColorRevertGraphicsImpl::Draw(
    Texture& texture,
    const RevertParam& param) {
  if (texture.GetDesc().Format != DXGI_FORMAT::DXGI_FORMAT_B8G8R8A8_UNORM) {
    LOG(ERROR) << "Only Process B8G8R8A8";
    return nullptr;
  }

  if (!graphics_ || graphics_->GetSize() != texture.GetSize()) {
    graphics_ = nullptr;
    graphics_ = CreateGraphics2D(device_);
    if (!graphics_->CreateNewBGRAGraphics(texture.GetSize().x,
                                          texture.GetSize().y)) {
      LOG(ERROR) << "Failed to Create graphics[" << texture.GetSize().x << ","
                 << texture.GetSize().y << "]";
      return nullptr;
    }
  }

  auto shader = GetShaderManager_()->GetOrCreateShader<ColorRevertShader>(
      device_.shared_from_this());

  if (!shader)
    return nullptr;

  ScopedBlendHelper a(device_);
  device_.AllowBlend(true);
  if (!graphics_->BeginDraw(false))
    return nullptr;
  {
    graphics::ScopedEndDraw end_draw(*graphics_);

    ColorRevertShader::PSParam ps_param;
    ps_param.revert_a = param.a ? 1.f : -1.f;
    ps_param.revert_r = param.r ? 1.f : -1.f;
    ps_param.revert_g = param.g ? 1.f : -1.f;
    ps_param.revert_b = param.b ? 1.f : -1.f;
    if (!ps_buffer_) {
      D3D11_BUFFER_DESC bufferDesc;
      ZeroMemory(&bufferDesc, sizeof(bufferDesc));
      bufferDesc.Usage = D3D11_USAGE_DYNAMIC;
      bufferDesc.ByteWidth = sizeof(MATRIXBUFFER);
      bufferDesc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
      bufferDesc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;
      bufferDesc.MiscFlags = 0;
      bufferDesc.StructureByteStride = 0;
      auto hRes = GetDevice_()->CreateBuffer(&bufferDesc, NULL, &ps_buffer_);
      D3D11SetDebugObjectName(ps_buffer_.Get(),
                              "color_revert_shader_cropbuffer");
    }

    if (ps_buffer_) {
      D3D11_MAPPED_SUBRESOURCE map = {};
      GetContext_()->Map(ps_buffer_.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &map);
      auto* dataPtr = (ColorRevertShader::PSParam*)map.pData;
      memcpy_s(dataPtr, sizeof(ps_param), &ps_param, sizeof(ps_param));
      GetContext_()->Unmap(ps_buffer_.Get(), 0);
    }

    shader->RenderTexture(texture.GetSRV(), ps_buffer_);
  }
  return graphics_->GetOutputTexture();
}

ColorRevertGraphicsImpl::~ColorRevertGraphicsImpl() {
  ColorRevertGraphicsImpl::Destroy();
}

Microsoft::WRL::ComPtr<ID3D11Device> ColorRevertGraphicsImpl::GetDevice_() {
  return device_.GetDevice();
}

ID3D11DeviceContext* ColorRevertGraphicsImpl::GetContext_() {
  return device_.GetContext().Get();
}

ShaderManager* ColorRevertGraphicsImpl::GetShaderManager_() {
  return device_.GetShaderManager();
}

}  // namespace graphics