#include "vqos_data.h"
#include <base/strings/stringprintf.h>
#include <mediasdk_json.hpp>
#include <mutex>
#include <shared_mutex>
#include "mediasdk/component_center.h"
#include "mediasdk/data_center/data_center.h"
#include "mediasdk/notify_center.h"
#include "os_version.h"
#include "uuid_generater.h"

namespace {
const std::string kProjectKey = "tiktok_live_studio";

std::string GetUuid() {
  static std::string kUuid =
      mediasdk::UuidGenerater::GenerateUuidWithOutHyphen();
  if (!kUuid.empty()) {
    return kUuid;
  }
  if (auto dc = mediasdk::com::GetDataCenter(); dc) {
    kUuid = dc->GetUserId();
  } else {
    kUuid = "";
  }
  kUuid = base::StringPrintf("%s%lld", kUuid.c_str(), mediasdk::milli_now());
  return kUuid;
}

int64_t GetAndIncrementTraceIndex() {
  static int64_t trace_index = 1;
  static std::mutex trace_mutex;
  std::lock_guard<std::mutex> lck(trace_mutex);
  return trace_index++;
}
}  // namespace

namespace mediasdk {
namespace event_tracking_data {

void LegacyCpuField::FillField(LegacyCpuField& field) {
  if (auto* dc = com::GetDataCenter()) {
    const auto cpu_info = dc->GetCpuInfo();
    field.cpu_soc = cpu_info.name.ToString();
    field.cpu_usage = cpu_info.process_usage;
    field.cpu = cpu_info.process_usage;
    field.cpu_usage_total = cpu_info.system_usage;
    field.cpu_total = cpu_info.system_usage;
    field.cpu_usage_time = cpu_info.system_time;
    field.cpu_time = cpu_info.system_time;
  }
}

void LegacyGpuField::FillField(LegacyGpuField& field) {
  if (auto* dc = com::GetDataCenter()) {
    if (std::vector<GpuInformation> gpu_infos;
        dc->GetGpuInfo(gpu_infos) && !gpu_infos.empty()) {
      field.gpu_name = gpu_infos[0].gpu_name;
      field.gpu_3D_usage = gpu_infos[0].gpu_3d_usage;
      field.gpu_3D_total_usage = gpu_infos[0].system_gpu_3d_usage;
      field.gpu_memory = gpu_infos[0].dedicate_usage / 1024.0 / 1024.0;
      field.gpu_total_memory = gpu_infos[0].total_mem / 1024.0 / 1024.0;
      field.gpu_dedicate_mem = gpu_infos[0].dedicate_mem / 1024.0 / 1024.0;

      if (gpu_infos.size() > 1) {
        field.integrated_gpu_name = gpu_infos[1].gpu_name;
        field.integrated_gpu_3D_usage = gpu_infos[1].gpu_3d_usage;
        field.integrated_gpu_3D_total_usage = gpu_infos[1].system_gpu_3d_usage;
        field.integrated_gpu_memory =
            gpu_infos[1].dedicate_usage / 1024.0 / 1024.0;
        field.integrated_gpu_total_memory =
            gpu_infos[1].total_mem / 1024.0 / 1024.0;
        field.integrated_gpu_dedicate_mem =
            gpu_infos[1].dedicate_mem / 1024.0 / 1024.0;
      }
    }
  }
}

void LegacyVisualFpsField::FillField(LegacyVisualFpsField& field) {
  if (auto* dc = com::GetDataCenter()) {
    if (const auto source_fps = dc->GetSourceFPS()) {
      if (source_fps->camera_fps) {
        if (const auto it = std::min_element(source_fps->camera_fps->begin(),
                                             source_fps->camera_fps->end());
            it != source_fps->camera_fps->end()) {
          field.in_cap_fps = *it;
        }
      }
      if (source_fps->window_fps) {
        if (const auto it = std::min_element(source_fps->window_fps->begin(),
                                             source_fps->window_fps->end());
            it != source_fps->window_fps->end()) {
          field.in_window_fps = *it;
        }
      }
      if (source_fps->monitor_fps) {
        if (const auto it = std::min_element(source_fps->monitor_fps->begin(),
                                             source_fps->monitor_fps->end());
            it != source_fps->monitor_fps->end()) {
          field.in_monitor_fps = *it;
        }
      }
      if (source_fps->cast_fps) {
        if (const auto it = std::min_element(source_fps->cast_fps->begin(),
                                             source_fps->cast_fps->end());
            it != source_fps->cast_fps->end()) {
          field.in_cast_fps = *it;
        }
      }
      if (source_fps->game_fps) {
        if (const auto it = std::min_element(source_fps->game_fps->begin(),
                                             source_fps->game_fps->end());
            it != source_fps->game_fps->end()) {
          field.in_game_fps = *it;
        }
      }
    }
  }
}

void VisualFpsField::FillField(VisualFpsField& field) {
  if (auto* dc = com::GetDataCenter()) {
    if (const auto source_fps = dc->GetSourceFPS()) {
      if (source_fps->camera_fps) {
        field.cap_fps = *source_fps->camera_fps;
      }
      if (source_fps->window_fps) {
        field.window_fps = *source_fps->window_fps;
      }
      if (source_fps->monitor_fps) {
        field.monitor_fps = *source_fps->monitor_fps;
      }
      if (source_fps->cast_fps) {
        field.cast_fps = *source_fps->cast_fps;
      }
      if (source_fps->game_fps) {
        field.game_fps = *source_fps->game_fps;
      }
    }
  }
}

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(BwProbe,
                                                  bw_type,
                                                  bw_probe_state,
                                                  bw_probe_fail_reason,
                                                  bw_probe_result,
                                                  real_bitrate,
                                                  bw_probe_real_protocol,
                                                  message,
                                                  bw_rtt_avg,
                                                  bw_bwe_avg,
                                                  bw_total_time,
                                                  bw_send_packet_time,
                                                  bw_dns_time,
                                                  bw_socket_connect_time,
                                                  bw_rtmp_connect_time);

#define STREAM_FIELD_DEFINE                                                \
  url, push_url, stream_id, stream_name, room_id, dns_ip, dns_parse_time,  \
      cdn_ip, connection_type, connect_elapse, push_protocol, video_codec, \
      codec_name, hardware

#define SESSION_FIELD_DEFINE push_session_id, connect_session_id

#define TRANSPORT_FIELD_DEFINE                                               \
  transport_pts_send_audio_stall_count, transport_pts_send_audio_stall_time, \
      transport_pts_send_video_stall_count,                                  \
      transport_pts_send_video_stall_time, transport_send_audio_stall_count, \
      transport_send_audio_stall_time, transport_send_video_stall_count,     \
      transport_send_video_stall_time, transport_reconnect_count,            \
      transport_reconnect_time
#define NET_STATUS_FIELD_DEFINE \
  bwe, quic_bw, rtt, quic_rtt, loss_rate, quic_loss
#define LEGACY_CPU_FIELD_DEFINE \
  cpu_soc, cpu_usage, cpu_usage_total, cpu_usage_time, cpu, cpu_total, cpu_time
#define LEGACY_GPU_FIELD_DEFINE                                             \
  gpu_name, gpu_3D_usage, gpu_3D_total_usage, gpu_memory, gpu_total_memory, \
      gpu_dedicate_mem, integrated_gpu_name, integrated_gpu_3D_usage,       \
      integrated_gpu_3D_total_usage, integrated_gpu_memory,                 \
      integrated_gpu_total_memory, integrated_gpu_dedicate_mem
#define LEGACY_VISUAL_FPS_FIELD_DEFINE \
  in_cap_fps, in_window_fps, in_monitor_fps, in_cast_fps, in_game_fps
#define VISUAL_FPS_FIELD_DEFINE \
  cap_fps, window_fps, monitor_fps, cast_fps, game_fps
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(StartPush,
                                                  STREAM_FIELD_DEFINE,
                                                  SESSION_FIELD_DEFINE,
                                                  status);
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(StopPush,
                                                  STREAM_FIELD_DEFINE,
                                                  SESSION_FIELD_DEFINE,
                                                  push_duration,
                                                  disconnect_count,
                                                  is_connecting,
                                                  disconnect_elapse,
                                                  error_code,
                                                  abr_strategy,
                                                  abr_switch_info,
                                                  abr_switch_count);
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(FirstFramePreEncode,
                                                  STREAM_FIELD_DEFINE,
                                                  SESSION_FIELD_DEFINE,
                                                  media_type);
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(FirstFrameEncode,
                                                  STREAM_FIELD_DEFINE,
                                                  SESSION_FIELD_DEFINE,
                                                  media_type,
                                                  result);
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(FirstFrameSend,
                                                  STREAM_FIELD_DEFINE,
                                                  SESSION_FIELD_DEFINE,
                                                  media_type,
                                                  result);
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(ConnectStart,
                                                  STREAM_FIELD_DEFINE,
                                                  error_code,
                                                  default_bitrate,
                                                  min_bitrate,
                                                  max_bitrate,
                                                  min_video_bitrate,
                                                  max_video_bitrate,
                                                  gop,
                                                  first_connect,
                                                  reconnect_count,
                                                  error_count,
                                                  connect_status);
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(ConnectStartConnection,
                                                  STREAM_FIELD_DEFINE,
                                                  SESSION_FIELD_DEFINE,
                                                  error_code,
                                                  default_bitrate,
                                                  min_bitrate,
                                                  max_bitrate,
                                                  min_video_bitrate,
                                                  max_video_bitrate,
                                                  gop,
                                                  first_connect,
                                                  reconnect_count,
                                                  error_count,
                                                  elapse,
                                                  status,
                                                  reason);
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(ConnectEnd,
                                                  STREAM_FIELD_DEFINE,
                                                  TRANSPORT_FIELD_DEFINE,
                                                  NET_STATUS_FIELD_DEFINE,
                                                  error_code,

                                                  reconnect_count,
                                                  rate_adjust_times,
                                                  send_package_slow_times);
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(ConnectEndConnection,
                                                  STREAM_FIELD_DEFINE,
                                                  SESSION_FIELD_DEFINE,
                                                  error_code,

                                                  next_connect_session_id,
                                                  push_duration,
                                                  status,
                                                  reason);
NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(PushStreamFail,
                                                  STREAM_FIELD_DEFINE,
                                                  NET_STATUS_FIELD_DEFINE,
                                                  error_code,
                                                  error_msg);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(PushStream,
                                               STREAM_FIELD_DEFINE,
                                               TRANSPORT_FIELD_DEFINE,
                                               NET_STATUS_FIELD_DEFINE,
                                               LEGACY_CPU_FIELD_DEFINE,
                                               LEGACY_GPU_FIELD_DEFINE,
                                               LEGACY_VISUAL_FPS_FIELD_DEFINE,
                                               SESSION_FIELD_DEFINE,
                                               duration,
                                               rtmp_buffer_time,
                                               audio_bitrate,
                                               audio_enc_bitrate,
                                               meta_audio_bitrate,

                                               default_bitrate,
                                               min_bitrate,
                                               max_bitrate,
                                               min_video_bitrate,
                                               max_video_bitrate,
                                               meta_video_bitrate,
                                               i_key_frame_max,

                                               encode_fps,
                                               encode_error_count,
                                               encoder_process_time,

                                               video_profile,
                                               width,
                                               height,

                                               audio_profile,
                                               audio_channel,
                                               audio_sample_rate,
                                               is_mute,
                                               is_link_mic,

                                               preview_fps,
                                               out_cap_fps,

                                               real_video_framerate,
                                               real_bitrate,
                                               drop_count,

                                               meta_video_framerate,

                                               package_delay,

                                               physmem_size,
                                               physmem_used,
                                               physmem_used_total,
                                               physmem_usage_total,

                                               model0_map,
                                               model0_present_cost,
                                               model1_map,
                                               model1_present_cost,
                                               anchor_video_delay,
                                               video_enc_bitrate,
                                               sei_birate,
                                               frame_rate_input,
                                               frame_rate_sent,
                                               abr_switch_info,
                                               abr_switch_is_peak_bitrate);

LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(MediaSdkInitialize,
                                               LEGACY_CPU_FIELD_DEFINE,
                                               LEGACY_GPU_FIELD_DEFINE,
                                               init_result,
                                               init_cost,
                                               event_type,
                                               os_version);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(MediaSdkUnInitialize, duration);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(SourceEvent,
                                               source_category,
                                               source_type,
                                               event_type,
                                               extra_code,
                                               extra_msg);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(FatalError,
                                               error_type,
                                               error_msg,
                                               error_code,
                                               event_type);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(Performance,
                                               LEGACY_GPU_FIELD_DEFINE,
                                               VISUAL_FPS_FIELD_DEFINE,
                                               preview_fps,
                                               render_fps_achieving_rate,
                                               no_ready_fps,
                                               present_ready_fps,
                                               present_fps_achieving_rate,

                                               memory,
                                               total_mem,
                                               page_fault,

                                               cpu_name,
                                               cpu_usage,
                                               cpu_total,

                                               disk_space_left,
                                               disk_space);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(StuckEvent, duration, extra_msg);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(PluginLoadDuration,
                                               duration,
                                               extra_msg);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(PluginInitDuration,
                                               duration,
                                               extra_msg);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(PluginLoaderDuration,
                                               duration,
                                               extra_msg);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(CaeInit,
                                               error_type,
                                               default_bitrate,
                                               encode_fps);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(CaeBitrate,
                                               default_bitrate,
                                               meta_video_bitrate);

LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(CustomFlvMetadata,
                                               extra_code,
                                               extra_msg);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(AbrSwitch,
                                               stream_id,
                                               width,
                                               height,
                                               meta_video_framerate,
                                               abr_switch_fail,
                                               duration);
LARGE_DEFINE_TYPE_NON_INTRUSIVE_ONLY_SERIALIZE(CodecSwitchStall,
                                               stream_id,
                                               width,
                                               height,
                                               meta_video_framerate,
                                               duration,
                                               stall_time,
                                               codec_name);

}  // namespace event_tracking_data

void VqosData::SetDefaultParam() {
  auto dc = com::GetDataCenter();
  if (!dc) {
    return;
  }
  json_["mode"] = "push";
  json_["event_name"] = "mediasdk_v2";
  json_["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
                           std::chrono::system_clock::now().time_since_epoch())
                           .count();
  json_["version"] = dc->GetVersion();
  json_["live_sdk_version"] = dc->GetSdkVersion();
  json_["project_key"] = kProjectKey;
  json_["audio_codec"] = "aac";
  json_["push_type"] = "avo";
  json_["sdk_params"] = "x64";
  json_["livecore_app_alive_uuid"] = GetUuid();
  json_["livecore_event_index"] = GetAndIncrementTraceIndex();

  // #ifdef _DEBUG
  LOG(INFO) << "[VqosData] report trace, event_key: " << json_["event_key"]
            << " livecore_app_alive_uuid: " << json_["livecore_app_alive_uuid"]
            << " livecore_event_index: " << json_["livecore_event_index"];
  // #endif
}

void VqosData::Report() {
  SetDefaultParam();
  auto nc = com::GetNotifyCenter();
  if (nc) {
    nc->GlobalEvent()->Notify(FROM_HERE,
                              &MediaSDKGlobalEventObserver::OnVqosDataReport,
                              json_.dump(0));
  }
}

void VqosData::ReportStartPush(const event_tracking_data::StartPush& data) {
  try {
    json_ = data;
    json_["event_key"] = "start_push";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportStartPush Failed";
  }
}

void VqosData::ReportStopPush(const event_tracking_data::StopPush& data) {
  try {
    json_ = data;
    json_["event_key"] = "stop_push";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportStopPush Failed";
  }
}

void VqosData::ReportSourceEvent(const event_tracking_data::SourceEvent& data) {
  try {
    json_ = data;
    json_["event_key"] = "source_event";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportSourceEvent Failed";
  }
}

void VqosData::ReportFatalError(const event_tracking_data::FatalError& data) {
  try {
    json_ = data;
    json_["event_key"] = "fatal_error";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportFatalError Failed";
  }
}

void VqosData::ReportConnectStart(
    const event_tracking_data::ConnectStart& data) {
  try {
    json_ = data;
    json_["event_key"] = "connect_start";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportConnectStart Failed";
  }
}

void VqosData::ReportConnectStartConnection(
    const event_tracking_data::ConnectStartConnection& data) {
  try {
    json_ = data;
    json_["event_key"] = "connect_start";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportConnectStart Failed";
  }
}

void VqosData::ReportConnectEnd(const event_tracking_data::ConnectEnd& data) {
  try {
    json_ = data;
    json_["event_key"] = "connect_end";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportConnectEnd Failed";
  }
}

void VqosData::ReportConnectEndConnection(
    const event_tracking_data::ConnectEndConnection& data) {
  try {
    json_ = data;
    json_["event_key"] = "connect_end";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportConnectEnd Failed";
  }
}

void VqosData::ReportPushStreamFail(
    const event_tracking_data::PushStreamFail& data) {
  try {
    json_ = data;
    json_["event_key"] = "push_stream_fail";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportPushStreamFail Failed";
  }
}

void VqosData::ReportPushStream(const event_tracking_data::PushStream& data) {
  try {
    json_ = data;
    json_["event_key"] = "push_stream";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportPushStream Failed";
  }
}

void VqosData::ReportFirstFramePreEncode(
    const event_tracking_data::FirstFramePreEncode& data) {
  try {
    json_ = data;
    json_["event_key"] = "first_frame_pre_encode";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportFirstFramePreEncode Failed";
  }
}

void VqosData::ReportFirstFrameEncode(
    const event_tracking_data::FirstFrameEncode& data) {
  try {
    json_ = data;
    json_["event_key"] = "first_frame_encode";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportFirstFrameEncode Failed";
  }
}

void VqosData::ReportFirstFrameSend(
    const event_tracking_data::FirstFrameSend& data) {
  try {
    json_ = data;
    json_["event_key"] = "first_frame_send";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportFirstFrameSend Failed";
  }
}

void VqosData::ReportBwProbe(const event_tracking_data::BwProbe& data) {
  try {
    json_ = data;
    json_["event_key"] = "bw_probe";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportBwProbe Failed";
  }
}

void VqosData::ReportInitialize(
    const event_tracking_data::MediaSdkInitialize& data) {
  try {
    json_ = data;
    json_["event_key"] = "mediasdk_initialize";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportInitialize Failed";
  }
}

void VqosData::ReportUnInitialize(
    const event_tracking_data::MediaSdkUnInitialize& data) {
  try {
    json_ = data;
    json_["event_key"] = "sdk_uninitialize";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportUnInitialize Failed";
  }
}

void VqosData::ReportPerformance(const event_tracking_data::Performance& data,
                                 const nlohmann::json& tea_buffer) {
  try {
    json_ = data;
    json_.merge_patch(tea_buffer);
    json_["event_key"] = "Performance";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportPerformance Failed";
  }
}

void VqosData::ReportStuckEvent(const event_tracking_data::StuckEvent& data) {
  try {
    json_ = data;
    json_["event_key"] = "stuck_event";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportStuckEvent Failed";
  }
}

void VqosData::ReportPluginLoadDuration(
    const event_tracking_data::PluginLoadDuration& data) {
  try {
    json_ = data;
    json_["event_key"] = "plugin_load_duration";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportPluginLoadDuration Failed";
  }
}

void VqosData::ReportPluginInitDuration(
    const event_tracking_data::PluginInitDuration& data) {
  try {
    json_ = data;
    json_["event_key"] = "plugin_init_duration";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportPluginInitDuration Failed";
  }
}

void VqosData::ReportPluginLoaderDuration(
    const event_tracking_data::PluginLoaderDuration& data) {
  try {
    json_ = data;
    json_["event_key"] = "plugin_loader_duration";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportPluginLoaderDuration Failed";
  }
}

void VqosData::ReportCaeInit(const event_tracking_data::CaeInit& data) {
  try {
    json_ = data;
    json_["event_key"] = "cae_init";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportCaeInit Failed";
  }
}

void VqosData::ReportCaeStuck(const event_tracking_data::CaeStuck& data) {
  try {
    json_["event_key"] = "cae_stuck";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportCaeStuck Failed";
  }
}

void VqosData::ReportCaeBitrate(const event_tracking_data::CaeBitrate& data) {
  try {
    json_ = data;
    json_["event_key"] = "cae_bitrate";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportCaeBitrate Failed";
  }
}

void VqosData::ReportCustomFlvMetadata(
    const event_tracking_data::CustomFlvMetadata& data) {
  try {
    json_ = data;
    json_["event_key"] = "custom_flv_metadata";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportCustomFlvMetadata Failed";
  }
}

void VqosData::ReportAbrSwitch(const event_tracking_data::AbrSwitch& data) {
  try {
    json_ = data;
    json_["event_key"] = "abr_switch";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportAbrSwitch Failed";
  }
}

void VqosData::ReportCodecSwitchStall(
    const event_tracking_data::CodecSwitchStall& data) {
  try {
    json_ = data;
    json_["event_key"] = "codec_switch_stall";
    Report();
  } catch (...) {
    LOG(ERROR) << "[VqosData] ReportCodecSwitchStall Failed";
  }
}
}  // namespace mediasdk
