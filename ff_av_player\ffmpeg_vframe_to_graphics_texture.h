#pragma once

#include "ffmpeg_vframe_to_texture.h"
#include "ffmpeg_vframe_to_texture_d3d11.h"
#include "graphics/device.h"
#include "graphics/texture.h"
#include "graphics/yuv_to_bgra_graphics.h"

class FFVFrameToGraphicsTexture {
 public:
  explicit FFVFrameToGraphicsTexture(graphics::Device& dev) : device(dev) {}

  ~FFVFrameToGraphicsTexture() {
    if (shared_texture_) {
      shared_texture_ = nullptr;
    }
    if (shared_texture_from_decode_context_) {
      shared_texture_from_decode_context_ = nullptr;
    }
  }

  void ConvertPrepare(AVFrame* v_frame) {
    AVFrameRAIIReference v(v_frame);

    if (!convert_) {
      convert_ = CreateYUVToBGRAGraphics(device);
    }
    auto handle_memory_frame = [this](AVFrame* v_frame) {
      graphics::TextureFrame frame = {};
      if (!FFFrameToTextureFrame(*v_frame, frame)) {
        DCHECK(false && "invalid format");
      }
      if (convert_) {
        convert_->ConvertMemoryToBGRAPrepare(frame);
      }
    };
    auto handle_texture_frame = [this](AVFrame* v_frame) {
      if (!hw_decode_context_) {
        hw_decode_context_ =
            std::make_unique<FFD3D11VideoFrameToVideoTexture>();
      }
      if (hw_decode_context_) {
        hw_decode_context_->AVFrameCopyToD3D11Texture(
            device, v_frame, shared_texture_,
            shared_texture_from_decode_context_);
      }
      color_range_ = v_frame->color_range;
      colorspace_ = v_frame->colorspace;
    };
    if (v_frame->format == AVPixelFormat::AV_PIX_FMT_D3D11) {
      hw_texture_ = true;
      handle_texture_frame(v_frame);
    } else {
      hw_texture_ = false;
      handle_memory_frame(v_frame);
    }
  }

  void Convert() {
    if (convert_ && !hw_texture_) {
      convert_->ConvertMemoryToBGRADraw();
    } else if (convert_ && hw_texture_) {
      if (shared_texture_ && convert_) {
        if (shared_texture_->IsKeyedMutex()) {
          shared_texture_->AcquireKeyedAccess(0, INFINITE);
        }
        convert_->ConvertTextureToBGRA(
            shared_texture_,
            fromFFColorSpaceWithDefault(colorspace_,
                                        mediasdk::ColorSpace::kColorSpaceBT709),
            fromFFVideoFormatWithDefault(
                color_range_, mediasdk::VideoRange::kVideoRangePartial));
        if (shared_texture_->IsKeyedMutex()) {
          shared_texture_->ReleaseKeyedAccess(0);
        }
      }
    }
  }

  std::shared_ptr<graphics::Texture> GetOutputTexture() {
    if (!convert_) {
      return nullptr;
    }
    return convert_->GetOutputTexture();
  }

 private:
  std::shared_ptr<graphics::YUVToBGRAGraphics> convert_;
  std::unique_ptr<FFD3D11VideoFrameToVideoTexture> hw_decode_context_;
  std::shared_ptr<graphics::Texture> shared_texture_;
  Microsoft::WRL::ComPtr<ID3D11Texture2D> shared_texture_from_decode_context_;
  graphics::Device& device;
  bool hw_texture_ = false;
  AVColorRange color_range_ = AVCOL_RANGE_UNSPECIFIED;
  AVColorSpace colorspace_ = AVCOL_SPC_UNSPECIFIED;
};