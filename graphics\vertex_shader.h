#pragma once

namespace graphics {

static const char* TEXTURE_VERTEX_SHADER() {
  return R"(
cbuffer MatrixBuffer
{
	matrix world;
	matrix view;
	matrix project;
};

struct VS_INPUT
{
    float4 position : POSITION;
    float2 tex : TEXCOORD0;
};

struct PS_INPUT
{
    float4 position : SV_POSITION;
    float2 tex : TEXCOORD0;
};

cbuffer CROP_INPUT
{
	float2 scale;
	float2 translate;
};

PS_INPUT VS_MAIN(VS_INPUT input)
{
    PS_INPUT output;  

    input.position.w = 1.0f;

    output.position = mul(input.position, world);

    output.position = mul(output.position, view);

    output.position = mul(output.position, project);

	output.tex = input.tex * scale + translate;

    return output;
}
)";
}
}  // namespace graphics