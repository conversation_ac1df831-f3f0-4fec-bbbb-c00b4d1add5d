#pragma once

#include "mediasdk_callback_defines.h"
#include "mediasdk_defines.h"

#pragma pack(push, 1)

namespace mediasdk {

struct CreateVisualParams {
  MediaSDKString plugin_name;
  MediaSDKString json_params;
  uint32_t audio_track_id;
  bool destroy_when_all_ref_removed;
};

struct CreateDuplicateParams {
  bool is_visible;
  MSTransform trans;
  MediaSDKString target_visual_id;
};

struct ReopenParam {
  MediaSDKString canvas_item_id;
  MSTransform target_transform;
};

enum HittestCursorPos {
  kHittestCursorPosNothing = 0,
  kHittestCursorPosLeftTop,
  kHittestCursorPosRightTop,
  kHittestCursorPosRightBottom,
  kHittestCursorPosLeftBottom,
  kHittestCursorPosTop,
  kHittestCursorPosRight,
  kHittestCursorPosBottom,
  kHittestCursorPosLeft,
  kHittestCursorPosMiddle,
};

enum VisualClipStatus {
  kVisualClipStatusNone = 0,
  kVisualClipStatusLeftTop,
  kVisualClipStatusRightTop,
  kVisualClipStatusRightBottom,
  kVisualClipStatusLeftBottom,
  kVisualClipStatusTop,
  kVisualClipStatusRight,
  kVisualClipStatusBottom,
  kVisualClipStatusLeft,
};

enum ShortcutAction {
  kShortcutActionNothing = 0,
  kShortcutActionKeyUp,
  kShortcutActionKeyDown,
  kShortcutActionKeyLeft,
  kShortcutActionKeyRight,
  kShortcutActionClip,
  kShortcutActionStretch,
};

enum VirtualCameraObjectFitMode {
  kVirtualCameraObjectFitModeContain = 0,
  kVirtualCameraObjectFitModeCover,
};

enum GameCaptureEventType {
  kCaptureEventNil = -1,
  kCaptureSuccessBuffer = 0,
  kCaptureSuccessTexture = 1,
  kCaptureErrorTimeOut = 2,
  kCaptureErrorTexture = 3,
  kCaptureErrorInitialize = 4,
  kCaptureErrorInternal = 5,
  kCaptureErrorHook = 6,
  kCaptureErrorDevice = 7,
  kCaptureEventCommon = 8,
  kCaptureEventInject = 9,
  kCaptureEventFirstFrame = 10,
  kCaptureEventDx9CPUHook = 11,
  kCaptureEventDx9GPUHook = 12,
  kCaptureEventDx10CPUHook = 13,
  kCaptureEventDx10GPUHook = 14,
  kCaptureEventDx101CPUHook = 15,
  kCaptureEventDx101GPUHook = 16,
  kCaptureEventDx11CPUHook = 17,
  kCaptureEventDx11GPUHook = 18,
  kCaptureEventDx12CPUHook = 19,
  kCaptureEventDx12GPUHook = 20,
  kCaptureEventGlCPUHook = 21,
  kCaptureEventGlGPUHook = 22,
  kCaptureEventVulkanCPUHook = 23,
  kCaptureEventVulkanGPUHook = 24,
  kCaptureEventStop = 25,
  kCaptureErrorMemCreateError = 26,
  kCaptureEventVulkanCPUHookFail = 50,
  kCaptureEventVulkanGPUHookFail = 51,
  kCaptureEventVulkanAllHookFail = 52,
  kCaptureEventErrorException = 60,
};

typedef MediaSDKArray<PluginInfo> PluginInfoArray;

}  // namespace mediasdk

#pragma pack(pop)
