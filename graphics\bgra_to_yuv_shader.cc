#include "bgra_to_yuv_shader.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>

using namespace Microsoft::WRL;

namespace graphics {

static const char* CONST_VERTEX_SHADER() {
  return R"(
cbuffer VS_CONSTANT_BUFFER : register(b0)
{
   float	cx;
   float	cy;
};
struct PS_INPUT_POSITION
{
	float4 pos : SV_POSITION;
};

struct PS_SizePos
{
	float3 tex : TEXCOORD0;
	float4 pos : SV_POSITION;
};

struct PS_INPUT_X0X1Z
{
	float3 tex : TEXCOORD0;
};
float2 VIDToPos(uint vid)
{
	bool right = vid == 2;
	bool top = vid == 1;
	float x = - 1.0f + right * 4.0f;
	float y = - 1.0f + top * 4.0f;
	return float2(x,y);
}

PS_INPUT_POSITION Y_VS_MAIN(uint vid : SV_VERTEXID)
{
	float2 vs_pos = VIDToPos(vid);
	PS_INPUT_POSITION output;
	output.pos = float4(vs_pos.x, vs_pos.y, 0.0, 1.0);
	return output;
}
PS_SizePos UV_VS_MAIN(uint vid : SV_VERTEXID)
{
	bool right = vid == 2;

	bool top = vid == 1;

	float2 vs_pos = VIDToPos(vid);

	float sample_diff_x = 1.0 / cx;

	float sample_base_x = right * 2.f;

	float sample_left_x = sample_base_x - sample_diff_x;

	float sample_y =  1.f - top * 2.f;

	PS_SizePos output;

	output.tex = float3(sample_left_x, sample_base_x, sample_y);

	output.pos = float4(vs_pos.x, vs_pos.y, 0.0, 1.0);

	return output;
}
)";
}

static const char* CONST_PIXEL_SHADER() {
  return R"(
Texture2D input_texture : register(t0);
SamplerState sample : register(s0);
struct PS_INPUT_POSITION
{
	float4 pos : SV_POSITION;
};

struct PS_SizePos
{
	float3 tex : TEXCOORD0;
	float4 pos : SV_POSITION;
};

struct PS_INPUT_X0X1Z
{
	float3 tex : TEXCOORD0;
};

cbuffer BGRAToYUV : register(b0)
{
   float4   Y;
   float4	U;
   float4	V;
};

float Y_PS_MAIN(PS_INPUT_POSITION input) : SV_TARGET
{
	float3 rgb = input_texture.Load(int3(input.pos.xy, 0)).rgb;
	float y = dot(Y.xyz, rgb) + Y.w;// y use raw, so we use load to sample
	return y;
};

float2 UV_PS_MAIN(PS_INPUT_X0X1Z input) : SV_TARGET
{
	float3 rgb_x_0 = input_texture.Sample(sample, input.tex.xz).rgb;// sample two line
	float3 rgb_x_1 = input_texture.Sample(sample, input.tex.yz).rgb;// sample two line

	float3 rgb = (rgb_x_0 + rgb_x_1) * 0.5f;// half x sample

	float u = dot(U.xyz, rgb) + U.w;
	float v = dot(V.xyz, rgb) + V.w;
	return float2(u, v);
};
float U_PS_MAIN(PS_INPUT_X0X1Z input) : SV_TARGET
{
	float3 rgb_x_0 = input_texture.Sample(sample, input.tex.xz).rgb;// sample two line
	float3 rgb_x_1 = input_texture.Sample(sample, input.tex.yz).rgb;// sample two line

	float3 rgb = (rgb_x_0 + rgb_x_1) * 0.5f;// half x sample

	float u = dot(U.xyz, rgb) + U.w;
	return u;
};
float V_PS_MAIN(PS_INPUT_X0X1Z input) : SV_TARGET
{
	float3 rgb_x_0 = input_texture.Sample(sample, input.tex.xz).rgb;// sample two line
	float3 rgb_x_1 = input_texture.Sample(sample, input.tex.yz).rgb;// sample two line

	float3 rgb = (rgb_x_0 + rgb_x_1) * 0.5f;// half x sample

	float v = dot(V.xyz, rgb) + V.w;
	return v;
};
)";
}

bool BGRAToYUVShader::Init(const std::shared_ptr<Device>& ins) {
  instance_ = ins;
  if (!try_init_) {
    init_suc_ = DoInit();
    try_init_ = true;
  }

  return init_suc_;
}

bool BGRAToYUVShader::DoInit() {
  context_ = instance_->GetContext().Get();
  Device::CompileShaderParam param = {};
  param.ps = CONST_PIXEL_SHADER();
  param.vs = CONST_VERTEX_SHADER();
  param.ps_name = "Y_PS_MAIN";
  param.vs_name = "Y_VS_MAIN";

  if (!instance_->CompileShader(param)) {
    LOG(ERROR) << "Failed to Compile Shader";
    return false;
  }

  ps_shader_y_ = param.ps_shader_;
  D3D11SetDebugObjectName(ps_shader_y_.Get(), "bgra to yuv y ps ");
  vs_shader_y_ = param.vs_shader_;
  D3D11SetDebugObjectName(vs_shader_y_.Get(), "bgra to yuv y vs");
  param.ps_name = "UV_PS_MAIN";
  param.vs_name = "UV_VS_MAIN";

  if (!instance_->CompileShader(param)) {
    LOG(ERROR) << "Failed to Compile Shader";
    return false;
  }

  ps_shader_uv_ = param.ps_shader_;
  D3D11SetDebugObjectName(ps_shader_uv_.Get(), "bgra to yuv uv ps");
  vs_shader_uv_ = param.vs_shader_;
  D3D11SetDebugObjectName(vs_shader_uv_.Get(), "bgra to yuv uv vs");

  param.ps_name = "V_PS_MAIN";
  param.vs_name = nullptr;

  if (!instance_->CompileShader(param)) {
    LOG(ERROR) << "Failed to Compile Shader";
    return false;
  }

  ps_shader_v_ = param.ps_shader_;
  D3D11SetDebugObjectName(ps_shader_v_.Get(), "bgra to yuv v ps");
  param.ps_name = "U_PS_MAIN";
  param.vs_name = nullptr;

  if (!instance_->CompileShader(param)) {
    LOG(ERROR) << "Failed to Compile Shader";
    return false;
  }

  ps_shader_u_ = param.ps_shader_;
  D3D11SetDebugObjectName(ps_shader_u_.Get(), "bgra to yuv u ps");
  if (!CreateIAS_())
    return false;

  return true;
}

bool BGRAToYUVShader::CreateIAS_() {
  D3D11_SAMPLER_DESC desc;
  desc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
  desc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.MipLODBias = 0.0F;
  desc.MaxAnisotropy = 1;
  desc.ComparisonFunc = D3D11_COMPARISON_ALWAYS;
  desc.BorderColor[0] = 0;
  desc.BorderColor[1] = 0;
  desc.BorderColor[2] = 0;
  desc.BorderColor[3] = 0;
  desc.MinLOD = 0;
  desc.MaxLOD = D3D11_FLOAT32_MAX;
  HRESULT hRes = GetDevice_()->CreateSamplerState(&desc, &sampler_);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateSamplerState(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  return true;
}

void BGRAToYUVShader::RenderY(ComPtr<ID3D11Buffer>& vs_buffer,
                              ComPtr<ID3D11Buffer>& ps_buffer,
                              ID3D11ShaderResourceView* views) {
  return Render(vs_buffer, ps_buffer, views, ps_shader_y_, vs_shader_y_);
}

void BGRAToYUVShader::RenderUV(ComPtr<ID3D11Buffer>& vs_buffer,
                               ComPtr<ID3D11Buffer>& ps_buffer,
                               ID3D11ShaderResourceView* views) {
  return Render(vs_buffer, ps_buffer, views, ps_shader_uv_, vs_shader_uv_);
}

void BGRAToYUVShader::RenderU(ComPtr<ID3D11Buffer>& vs,
                              ComPtr<ID3D11Buffer>& ps,
                              ID3D11ShaderResourceView* views) {
  return Render(vs, ps, views, ps_shader_u_, vs_shader_uv_);
}

void BGRAToYUVShader::RenderV(ComPtr<ID3D11Buffer>& vs,
                              ComPtr<ID3D11Buffer>& ps,
                              ID3D11ShaderResourceView* views) {
  return Render(vs, ps, views, ps_shader_v_, vs_shader_uv_);
}

void BGRAToYUVShader::Render(ComPtr<ID3D11Buffer>& vs_buffer,
                             ComPtr<ID3D11Buffer>& ps_buffer,
                             ID3D11ShaderResourceView* views,
                             ComPtr<ID3D11PixelShader>& ps_shader,
                             ComPtr<ID3D11VertexShader>& vs_shader) {
  auto context = GetContext_();
  context->VSSetConstantBuffers(0, 1, vs_buffer.GetAddressOf());

  context->PSSetConstantBuffers(0, 1, ps_buffer.GetAddressOf());

  context->PSSetShaderResources(0, 1, &views);

  context->PSSetShader(ps_shader.Get(), NULL, 0);

  context->VSSetShader(vs_shader.Get(), NULL, 0);

  context->PSSetSamplers(0, 1, sampler_.GetAddressOf());

  context->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);

  context->Draw(3, 0);
}

ComPtr<ID3D11Device> BGRAToYUVShader::GetDevice_() {
  return instance_->GetDevice();
}

ID3D11DeviceContext* BGRAToYUVShader::GetContext_() {
  return context_;
}

void BGRAToYUVShader::Destroy() {}

BGRAToYUVShader::~BGRAToYUVShader() {
  BGRAToYUVShader::Destroy();
}
}  // namespace graphics