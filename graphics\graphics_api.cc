#include "graphics_api.h"

#include <dxgi.h>
#include <wrl/client.h>
#include "base/logging.h"
#include "base/strings/stringprintf.h"
#include "device.h"
#include "third_party/WICTextureLoader/WICTextureLoader11.h"

namespace {

void LogAdapterInfo(const graphics::GpuAdapterInfo& info) {
  LOG(INFO) << base::StringPrintf("Adapter(%d) Name: %s", info.index_,
                                  info.model_.c_str());
  LOG(INFO) << base::StringPrintf("\t PCI ID:         %x:%x", info.vendor_id_,
                                  info.device_id_);
  LOG(INFO) << base::StringPrintf("\t Adapter LUID:  0x%08x_0x%08x",
                                  info.luid_high_, info.luid_low_);
  LOG(INFO) << base::StringPrintf("\t Dedicated Video Memory: %u",
                                  info.dedicated_memory_);
  LOG(INFO) << base::StringPrintf("\t Shared System Memory:   %u",
                                  info.shared_memory_);
  LOG(INFO) << base::StringPrintf("\t Driver Version:  %s",
                                  info.driver_version_.c_str());
}
}  // namespace

namespace graphics {

GRAPHICS_EXPORT int32_t IsWICCanDecodeMetaFile(const wchar_t* fileName,
                                               uint32_t* puiWidth,
                                               uint32_t* puiHeight) {
  return DirectX::IsWICCanDecodeMetaFile(fileName, puiWidth, puiHeight);
}

GRAPHICS_EXPORT bool EnumAdapterInfo(std::vector<GpuAdapterInfo>& infos) {
  infos.clear();
  Microsoft::WRL::ComPtr<IDXGIFactory1> factory1;
  if (const auto hr =
          CreateDXGIFactory1(__uuidof(IDXGIFactory1), (void**)(&factory1));
      FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateDXGIFactory1 {%s}",
                                     GetErrorString(hr).c_str());
    return false;
  }
  int index = 0;
  Microsoft::WRL::ComPtr<IDXGIAdapter1> adapter;
  while (true) {
    if (const auto hr = factory1->EnumAdapters1(index++, &adapter);
        FAILED(hr)) {
      break;
    }
    DXGI_ADAPTER_DESC desc = {};
    if (const auto hr = adapter->GetDesc(&desc); FAILED(hr)) {
      continue;
    }
    GpuAdapterInfo info;
    info.model_ = base::StringPrintf("%ls", desc.Description);
    info.index_ = index;
    info.luid_high_ = desc.AdapterLuid.HighPart;
    info.luid_low_ = desc.AdapterLuid.LowPart;
    info.vendor_id_ = desc.VendorId;
    info.device_id_ = desc.DeviceId;
    info.dedicated_memory_ = desc.DedicatedVideoMemory;
    info.shared_memory_ = desc.SharedSystemMemory;
    LARGE_INTEGER umd;

    if (const auto hr =
            adapter->CheckInterfaceSupport(__uuidof(IDXGIDevice), &umd);
        SUCCEEDED(hr)) {
      const UINT64 version = umd.QuadPart;
      const UINT16 aa = (version >> 48) & 0xffff;
      const UINT16 bb = (version >> 32) & 0xffff;
      const UINT16 ccccc = (version >> 16) & 0xffff;
      const UINT16 ddddd = version & 0xffff;

      info.driver_version_ =
          base::StringPrintf("%hu.%hu.%hu.%hu", aa, bb, ccccc, ddddd);
    } else {
      info.driver_version_ = "Unknown";
    }
    info.driver_name_ = desc.Description;
    LogAdapterInfo(info);
    infos.emplace_back(info);
  }
  return true;
}

}  // namespace graphics