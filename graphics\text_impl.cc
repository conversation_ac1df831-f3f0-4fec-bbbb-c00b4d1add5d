﻿#include "text_impl.h"

#include "gdi_font_render.h"

namespace graphics {

std::shared_ptr<Text> TextImpl::CreateText(Device& dev) {
  return std::make_shared<TextImpl>(dev);
}

std::shared_ptr<Texture> TextImpl::UpdateTextToTexture(const TextOPT& opt) {
  if (!render_) {
    render_ = std::make_unique<GDIFontRender>();
  }
  render_->UpdateSetting(opt);
  if (!render_->GetBufferSize())
    return nullptr;
  auto new_size = render_->GetSize();
  if (!texture_ || !XMFLOAT2IntEqual(texture_->GetSize(), new_size)) {
    texture_ =
        CreateTexture2D(device_, (int32_t)new_size.x, (int32_t)new_size.y,
                        mediasdk::PixelFormat::kPixelFormatBGRA,
                        Texture::TEXTURE_USAGE::TEXTURE_USAGE_CPU_MAP_WRITE |
                            Texture::TEXTURE_USAGE_SHADER_RESOURCE,
                        "text texture");
  }
  if (!texture_)
    return nullptr;
  texture_->CopyFrom(render_->GetBuffer(), ((int32_t)new_size.x) * 4,
                     (int32_t)new_size.y);
  return texture_;
}

TextImpl::~TextImpl() {}

}  // namespace graphics