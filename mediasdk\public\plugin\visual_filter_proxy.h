#pragma once

#include <stdint.h>

#include "graphics/texture.h"
#include "graphics/transform.h"
#include "mediasdk/public/mediasdk_string.hpp"
#include "mediasdk/component_center.h"
#include "mediasdk/data_center/data_center.h"

namespace mediasdk {
class VisualFilter;

struct VideoFilterSourceEvent {
  const char* source_name;
  int event_type;
  int extra_code;
  MediaSDKString extra_msg;
};

class VisualFilterProxy {
 public:
  virtual ~VisualFilterProxy() = default;

  virtual MSTransform GetCanvasItemTransform() const = 0;

  virtual graphics::Device* GetDevice() const = 0;

  virtual MediaSDKString GetABConfig(const MediaSDKString& key) const = 0;

  virtual void SendNotify(VisualFilter* filter,
                          const MediaSDKString& notify,
                          const MediaSDKString& data) = 0;

  virtual void ReportFilterSourceEvent(const VideoFilterSourceEvent& event) = 0;

  // Backward compatibility, no expansion
  virtual void ReportProfilterData(const char* data_name,
                                   int64_t period_ns) = 0;

  virtual void ReportBeginCostProfilterData(const char* data_name,
                                            int64_t begin_ns) = 0;

  virtual void ReportCostProfilterData(const char* data_name,
                                       int64_t period_ns) = 0;

  virtual int GetModelSettingfps() = 0;
};

}  // namespace mediasdk
