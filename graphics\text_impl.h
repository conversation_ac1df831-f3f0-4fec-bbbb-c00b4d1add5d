#pragma once

#include <vector>
#include "device.h"
#include "gdi_font_render.h"
#include "text.h"

namespace graphics {

class GDIFontRender;

class GRAPHICS_EXPORT TextImpl : public Text {
 public:
  static std::shared_ptr<Text> CreateText(Device& inst);

  TextImpl(Device& dev) : device_(dev) {}

  std::shared_ptr<Texture> UpdateTextToTexture(const TextOPT& opt) override;

  ~TextImpl() override;

 private:
  std::unique_ptr<GDIFontRender> render_;
  std::shared_ptr<Texture> texture_;
  Device& device_;
};

}  // namespace graphics
