import pathlib
import time
import os
import shutil
import zipfile


class CalcTime:
    def __init__(self, str):
        self.begin_time = time.time()
        self.str = str
        print("begin exec:[" + self.str + "]")

    def count(self):
        print(
            "exec:["
            + self.str
            + "] success cost [%s] seconds]" % (time.time() - self.begin_time)
        )


def exec_cmd(cmd, success_rtn=0):
    cnt = CalcTime(cmd)
    status_code = os.system(cmd)
    if status_code != success_rtn:
        print("-------------exec: [" + cmd + "] fail with[" + str(status_code) + "]!--------------")
        return False
    else:
        cnt.count()
        return True


def remove_path(path):
    if not os.path.exists(path):
        return

    if os.path.isdir(path):
        shutil.rmtree(path)
    else:
        os.remove(path)


def replace_file_str_line(encode, full_path, finder, replace_line):
    result = True
    tmp_file = full_path + ".tmp"
    tmp_file1 = full_path + ".tmp1"
    remove_path(tmp_file)
    remove_path(tmp_file1)
    try:
        file_tmp = open(tmp_file, "w+", encoding=encode)
        file = open(full_path, "r", encoding=encode)
        while True:
            line = file.readline()
            if line:
                if line.find(finder) != -1:
                    file_tmp.writelines(replace_line)
                else:
                    file_tmp.writelines(line)
            else:
                break
        file_tmp.close()
        file.close()
        os.rename(full_path, tmp_file1)
        os.rename(tmp_file, full_path)
    except Exception as e:
        result = False
        print("----------Error:" + str(e) + "----------")
        print("----------replace file:[" + full_path + "] failed!!----------")
    finally:
        remove_path(tmp_file)
        remove_path(tmp_file1)
    return result


def publish_pdb_to_local(local_path, symstore_dir):
    retry_cnt = 3
    while retry_cnt:
        if not exec_cmd(
            R"symstore add /r /f {} /s {} /t mediasdk".format(local_path, symstore_dir)
        ):
            retry_cnt = retry_cnt - 1
            time.sleep(5)
            continue
        return True
    return False


def zip_folder(folder_to_zip, zip_path):
    f_list = list(pathlib.Path(folder_to_zip).glob("**/*"))
    z = zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED)
    for f in f_list:
        z.write(f, str(f)[len(folder_to_zip) :])
    z.close()


def git_push_force(root_dir):
    os.chdir(root_dir)
    if not exec_cmd("git push -f origin HEAD"):
        return False
    return True

def safe_copy(source, destination):
    try:
        if os.path.isdir(destination):
            destination = os.path.join(destination, os.path.basename(source))
        shutil.copy(source, destination)
        return destination
    except Exception as e:
        print(f"Error occurred while copying file. {e}")
        return None