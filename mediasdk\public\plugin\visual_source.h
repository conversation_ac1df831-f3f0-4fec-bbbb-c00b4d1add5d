#pragma once

#include "graphics/texture.h"
#include "mediasdk/public/mediasdk_string.hpp"
#include "mediasdk/public/plugin/plugin_export.h"
#include "visual_proxy.h"

namespace mediasdk {

class VisualSource {
 public:
  virtual ~VisualSource() = default;

  virtual const char* GetName() const = 0;

  virtual bool Action(const char* json_params) = 0;

  virtual bool Prepare(int64_t timestamp_ns) = 0;

  virtual void Convert() = 0;

  virtual graphics::Texture* GetTexture() = 0;

  virtual int64_t GetTextureTimestampNS() = 0;

  virtual bool Pause() = 0;

  virtual bool Continue() = 0;

  virtual bool IsPaused() = 0;

  virtual MediaSDKString GetProperty(const char* key) = 0;

  virtual bool SetProperty(const char* key, const char* json) = 0;

  virtual void OnMouseEvent(const char* json_params) = 0;

  virtual void OnKeyboardEvent(const char* json_params) = 0;

  virtual bool HasAudio() = 0;

  virtual void InitGraphicsResource() = 0;

  virtual void ReleaseGraphicsResource() = 0;

  virtual bool Reopen(const char* json_params) = 0;

  virtual bool EnableAlpha() = 0;

  virtual float GetFps() = 0;

  virtual void DoRenderTask(int64_t timestamp_ns) {}

  // Indicates to the MediaSDK whether the image frame data has been updated.
  // If there is no update, some subsequent processing logic will not be
  // executed, and the last cached frame will be displayed.
  virtual bool IsUpdate() { return true; }

  // Provide an extra transform setting that will be combined with the
  // transform of Visual itself, and applied when the texture of VisualSource is
  // displayed on the screen
  // !!! Warning:
  //  Scale 0 mines the texture is disappeared, not allowed yet
  virtual MSTransform GetExtraTransform() const { return EMPTY_MSTRANSFORM; }

  virtual const char* GetSubTypeName() const { return nullptr; }

  virtual VisualFrameQueueConverter* GetVisualFrameQueueConverter() {
    return nullptr;
  }
  virtual bool EnableMultipleReferences() const { return true; }
};

enum EnumFormatType {
  kEnumFormatTypeVideo,
  kEnumFormatTypeAudio,
};

extern "C" PLUGIN_EXPORT VisualSource* CreateVisualSource(
    VisualProxy* proxy,
    const char* json_params);

extern "C" PLUGIN_EXPORT void DestroyVisualSource(VisualSource* source);

// Optional, Enumerate input devices for source
extern "C" PLUGIN_EXPORT MediaSDKStringData EnumVideoInput(const char* json);

// Optional, Enumerate format of input devices for source type = EnumFormatType
extern "C" PLUGIN_EXPORT MediaSDKStringData EnumFormat(const char* device_id,
                                                       int32_t type);

// Optional, Get plugin visual source property
extern "C" PLUGIN_EXPORT MediaSDKStringData
GetVisualPluginProperty(const char* json);

// Optional, Set plugin visual source property
extern "C" PLUGIN_EXPORT bool SetVisualPluginProperty(const char* key,
                                                      const char* json);

}  // namespace mediasdk
