#pragma once

#include <stdint.h>
#include <vector>
#include "mediasdk_api_alloc.h"
#include "mediasdk_string.hpp"

#pragma pack(push, 1)

namespace mediasdk {

template <typename T>
class MediaSDKArray {
 public:
  MediaSDKArray() {}

  MediaSDKArray(const std::vector<T>& value_vec) {
    size_ = value_vec.size();
    data_ = static_cast<T*>(AllocBuffer(size_ * sizeof(T)));

    for (size_t i = 0; i < size_; ++i) {
      // Use placement new to copy-construct objects in the new memory space.
      new (&data_[i]) T(value_vec[i]);
    }
  }

  ~MediaSDKArray() { Free(); }

  MediaSDKArray(const MediaSDKArray& other) {
    size_ = other.size_;
    data_ = static_cast<T*>(AllocBuffer(size_ * sizeof(T)));

    for (size_t i = 0; i < size_; ++i) {
      new (&data_[i]) T(other.data_[i]);
    }
  }

  MediaSDKArray& operator=(const MediaSDKArray& other) {
    if (this != &other) {
      Free();

      size_ = other.size_;
      data_ = static_cast<T*>(AllocBuffer(size_ * sizeof(T)));

      for (size_t i = 0; i < size_; ++i) {
        new (&data_[i]) T(other.data_[i]);
      }
    }
    return *this;
  }

  MediaSDKArray(MediaSDKArray&& other) noexcept {
    data_ = other.data_;
    size_ = other.size_;

    other.data_ = nullptr;
    other.size_ = 0;
  }

  MediaSDKArray& operator=(MediaSDKArray&& other) noexcept {
    if (this != &other) {
      Free();

      data_ = other.data_;
      size_ = other.size_;
      other.data_ = nullptr;
      other.size_ = 0;
    }
    return *this;
  }

  std::vector<T> ToVector() const {
    return std::vector<T>(data_, data_ + size_);
  }

  inline size_t Size() const { return size_; }

  inline T& At(size_t loc) const { return data_[loc]; }

 private:
  void Free() {
    if (data_) {
      for (size_t i = 0; i < size_; ++i) {
        data_[i].~T();
      }
      FreeBuffer((void*)data_);
      data_ = nullptr;
      size_ = 0;
    }
  }

  void Build() {}

 private:
  size_t size_ = 0;
  T* data_ = nullptr;
};

typedef MediaSDKArray<MediaSDKString> MediaSDKStringArray;
typedef MediaSDKArray<uint32_t> MediaSDKU32Array;

}  // namespace mediasdk

#pragma pack(pop)
