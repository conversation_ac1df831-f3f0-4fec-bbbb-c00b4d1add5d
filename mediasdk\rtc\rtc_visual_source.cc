#include "rtc_visual_source.h"

#include "base/mediasdk/thread_safe_deleter.h"
#include "nlohmann/json.hpp"

namespace {

const char kRtcVisualSourceName[] = "RtcVisualSource";
const char kMaxFrameQueueCountSectionName[] = "rtc_visual_source";

}  // namespace

namespace mediasdk {

// static
std::shared_ptr<RTCVisualSource> RTCVisualSource::Create(
    VisualProxy* proxy,
    const std::string& json_params) {
  auto rtc_audio_sourc = std::shared_ptr<RTCVisualSource>(
      new RTCVisualSource(proxy, json_params),
      base::ThreadSafeDeleter<RTCVisualSource>());
  return rtc_audio_sourc;
}

RTCVisualSource::RTCVisualSource(VisualProxy* proxy,
                                 const std::string& json_params)
    : proxy_(proxy), json_params_(json_params) {
  render_ = std::make_shared<RTCVideoRenderImpl>(this);
}

RTCVisualSource::~RTCVisualSource() {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (render_) {
    render_->Detach();
    render_.reset();
  }
}

bool RTCVisualSource::Action(const char* json_params) {
  return false;
}

bool RTCVisualSource::Prepare(int64_t timestamp_ns) {
  int64_t cost_us = (micro_now() - video_frame_timestmap_ns_ / 1000);
  if (cost_us < 0) {
    NOTREACHED();
    cost_us = 0;
  }
  last_elapse_.store((cost_us / 1000));

  return true;
}

void RTCVisualSource::Convert() {}

graphics::Texture* RTCVisualSource::GetTexture() {
  if (proxy_) {
    return proxy_->GetLatestTextureFromQueue();
  }
  return nullptr;
}

int64_t RTCVisualSource::GetTextureTimestampNS() {
  return video_frame_timestmap_ns_;
}

bool RTCVisualSource::Pause() {
  return false;
}

bool RTCVisualSource::Continue() {
  return false;
}

bool RTCVisualSource::IsPaused() {
  return false;
}

MediaSDKString RTCVisualSource::GetProperty(const char* key) {
  if (!key) {
    return {};
  }
  if (strcmp("rtc_user_info", key) == 0) {
    return {json_params_};
  }
  return {};
}

bool RTCVisualSource::SetProperty(const char* key, const char* json) {
  return false;
}

void RTCVisualSource::OnMouseEvent(const char* json_params) {}

void RTCVisualSource::OnKeyboardEvent(const char* json_params) {}

bool RTCVisualSource::HasAudio() {
  return false;
}

void RTCVisualSource::InitGraphicsResource() {
  if (proxy_) {
    auto max_frame_queue_count =
        proxy_->GetConfiguredMaxFrameQueueCount(kMaxFrameQueueCountSectionName);
    proxy_->InitVisualFrameQueue(kVisualFrameQueueConverterTypeYuv,
                                 max_frame_queue_count);
    LOG(INFO) << "RTCVisualSource"
              << ": Visual frame queue max count: " << max_frame_queue_count;
  }
}

void RTCVisualSource::ReleaseGraphicsResource() {}

const char* RTCVisualSource::GetName() const {
  return kRtcVisualSourceName;
}

bool RTCVisualSource::Reopen(const char* json_params) {
  return false;
}

bool RTCVisualSource::OnFrame(const VisualSourceFrame& video_frame) {
  rate_calc_.AddFrame();

  if (proxy_) {
    video_frame_timestmap_ns_ = video_frame.timestamp;
    proxy_->EnqueueVideoFrame(video_frame);
  }

  return true;
}

int RTCVisualSource::GetRenderElapseMS() {
  return last_elapse_.load();
}

bool RTCVisualSource::EnableAlpha() {
  return true;
}

float RTCVisualSource::GetFps() {
  return rate_calc_.CalculateFPS();
}

}  // namespace mediasdk