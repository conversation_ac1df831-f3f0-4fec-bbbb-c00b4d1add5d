#pragma once

#include <memory>
#include <string>
#include "base/native_library.h"
#include "base/synchronization/waitable_event.h"
#include "mediasdk/public/mediasdk_defines.h"
#include "public/plugin/plugin_global_proxy.h"

namespace mediasdk {

class SourceFactory {
 public:
  SourceFactory(base::NativeLibrary lib,
                const std::shared_ptr<PluginInfo>& info);

  virtual ~SourceFactory() = default;

  std::string GetName() const { return name_; }

  int64_t GetInitTs() { return init_ts_; }

  virtual PluginType GetType() const = 0;

  virtual base::NativeLibrary GetLibrary() const { return library_; }

  virtual std::shared_ptr<PluginInfo> GetPluginInfo() const { return info_; }

  virtual bool IsExternal() { return false; }

  bool NeedAsyncGlobalInit(PluginGlobalProxy* proxy);

  bool PluginGlobalInit(PluginGlobalProxy* proxy);

  void PluginGlobalUnInit();

  void SetInitialized(bool init_success);

  bool WaitInitializedSuccess();

  bool IsInitSuccess() { return init_success_; }

 protected:
  std::string name_;
  int64_t init_ts_ = 0;
  base::NativeLibrary library_ = nullptr;
  std::shared_ptr<PluginInfo> info_;
  // The source factory is initialized in an unloaded state, and when the
  // source's `PluginGlobalInit` function is successfully called, it is set to a
  // loaded state. Accessing source in an unloaded state requires waiting for
  // their loading event to complete
  bool init_success_ = false;
  base::WaitableEvent init_event_;
};

}  // namespace mediasdk
