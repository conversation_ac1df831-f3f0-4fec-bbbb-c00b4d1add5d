#include "rtc_visual_source_factory.h"

#include "mediasdk/component_proxy.h"
#include "mediasdk/rtc/rtc_controller.h"
#include "rtc_visual_source.h"

namespace mediasdk {

RTCVisualSourceFactory::RTCVisualSourceFactory(std::shared_ptr<PluginInfo> info)
    : VisualSourceFactory(nullptr, info) {
  SetInitialized(true);
}

RTCVisualSourceFactory::~RTCVisualSourceFactory() {}

std::shared_ptr<VisualSource> RTCVisualSourceFactory::CreateSource(
    VisualProxy* proxy,
    const std::string& json_params) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);
  LOG(INFO) << "[RTC] Create Rtc source";
  auto source = RTCVisualSource::Create(proxy, json_params);
  if (source) {
    sources_.insert(source);
    nlohmann::json json_root;
    try {
      json_root = nlohmann::json::parse(json_params);
      if (!json_root.empty()) {
        std::string user_id = json_root["user_id"];
        int stream_index = json_root["stream_index"];
        RTCControllerProxy::Call(FROM_HERE, MSCallback<int>(),
                                 &RTCController::SetRemoteProxyVideoRender,
                                 user_id, stream_index, source->GetRender());
      }
    } catch (const std::exception& e) {
      LOG(ERROR) << "Catch exception: " << e.what()
                 << " json string:" << json_params;
    }
    return source;
  } else {
    return nullptr;
  }
}

void RTCVisualSourceFactory::Destroy(std::shared_ptr<VisualSource> source) {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  if (!source) {
    return;
  }

  auto it = sources_.find(source);
  if (it != sources_.end()) {
    const auto* source_name = source->GetName();
    LOG(INFO) << "Destroy [" << (source_name ? source_name : "") << "]";

    MediaSDKString user_info = source->GetProperty("rtc_user_info");
    nlohmann::json json_root;
    try {
      json_root = nlohmann::json::parse(user_info.ToString());
      if (!json_root.empty()) {
        std::string user_id = json_root["user_id"];
        int stream_index = json_root["stream_index"];
        RTCControllerProxy::Call(FROM_HERE, MSCallback<int>(),
                                 &RTCController::SetRemoteProxyVideoRender,
                                 user_id, stream_index, nullptr);
      }
    } catch (const std::exception& e) {
      LOG(ERROR) << "Catch exception: " << e.what()
                 << " json string:" << user_info.ToString();
    }

    sources_.erase(it);
  }
}

void RTCVisualSourceFactory::DestroyAll() {
  sources_.clear();
}

}  // namespace mediasdk