#pragma once

#include <wasapi_device.h>

#include <base/threading/thread.h>

#include "audio_monitor.h"
#include "audio_wasapi_monitor.h"

namespace mediasdk {

class AudioMonitorImpl : public AudioMonitor {
 public:
  explicit AudioMonitorImpl();

  bool Open(AudioFormat format, const WASAPIDevice::DeviceName& dev) override;

  void Close() override;

  bool Play(const AudioFrame& data, bool mute) override;

  void SignalStop() override;

  std::wstring GetCurrentDevice() override;

  ~AudioMonitorImpl() override;

 private:
  void OpenDevInThread(mediasdk::AudioFormat format,
                       const WASAPIDevice::DeviceName& dev);

  static void CloseDevInThread(std::shared_ptr<WASAPIAudioMonitor> monitor);

 private:
  std::shared_ptr<std::thread> play_thread_;
  std::shared_ptr<WASAPIAudioMonitor> monitor_;
  bool failed_ = false;
  base::WaitableEvent init_event_;
  mediasdk::AudioFormat format_;
};

}  // namespace mediasdk
