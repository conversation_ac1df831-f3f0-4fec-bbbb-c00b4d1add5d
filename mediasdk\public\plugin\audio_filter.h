#pragma once

#include <string>

#include "audio/audio_frame.h"
#include "audio/audio_format.h"
#include "mediasdk/public/mediasdk_string.hpp"
#include "mediasdk/public/plugin/audio_filter_proxy.h"
#include "mediasdk/public/plugin/plugin_export.h"

namespace mediasdk {

class AudioFilter {
 public:
  virtual ~AudioFilter() = default;

  virtual bool Init(const AudioFormat& audio_format,
                    AudioFilterProxy* proxy) = 0;

  virtual void Uninit() = 0;

  virtual AudioFrame* Process(AudioFrame* audio_frame) = 0;

  virtual bool IsEnable() const = 0;

  virtual void SetEnable(bool enable) = 0;

  virtual MediaSDKString GetProperty(const char* key) { return {}; }

  virtual bool SetProperty(const char* key, const char* value) { return false; }

  virtual MediaSDKString Action(const char* action, const char* param) {
    return {};
  }
};

extern "C" PLUGIN_EXPORT AudioFilter* CreateAudioFilter(
    const char* json_params);

extern "C" PLUGIN_EXPORT void DestroyAudioFilter(AudioFilter* audio_filter);

}  // namespace mediasdk
