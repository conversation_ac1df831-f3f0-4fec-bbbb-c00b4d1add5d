#pragma once

#include "public/plugin/plugin_global_proxy.h"

namespace mediasdk {

class PluginGlobalProxyImpl : public PluginGlobalProxy {
 public:
  MSAdapterInfo GetCurrentAdapterInfo() override;

  int32_t GetAllAdapterInfoCnt() override;

  MSAdapterInfo GetAdapterInfo(int32_t index) override;

  virtual void NotifyGlobalEvent(PluginInfo info,
                                 MediaSDKString notify_event) override;
};

}  // namespace mediasdk
