#pragma once
#include <array>
#include "color_shader.h"
#include "device.h"
#include "rectangle.h"
#include "transform.h"

namespace graphics {
// current just for draw to graphics
class RectangleImpl : public Rectangle {
 public:
  static std::shared_ptr<RectangleImpl> CreateRectangle(Device& inst);
  RectangleImpl(Device& ins);

 public:
  bool DrawTo(const XMFLOAT2& vp,
              const XMMATRIX& view,
              const XMMATRIX& projection) override;

  void UpdateRectangleConf(const RectangleConfig* conf) override;
  int32_t GetIndexCnt() override;
  void Destroy() override;
  ~RectangleImpl() override;

 private:
  struct RectangleConfigInternal : public Rectangle::RectangleConfig {
    bool show = false;
  };

  bool SetupIAS();

  ID3D11DeviceContext* GetContext();
  ID3D11Device* GetDevice();
  bool UpdateBufferWithVPSize(const XMFLOAT2& vp,
                              const RectangleConfigInternal& conf);
  bool UpdateVertexBuffer(const RectangleConfigInternal& conf);

 private:
  Microsoft::WRL::ComPtr<ID3D11Buffer> vertex_buffer_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> index_buffer_fill_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> index_buffer_lines_;
  std::array<ColorShader::VERTEXTYPE, 8> vertex_mem_buffer_;
  int32_t cur_index_cnt_ = 0;
  bool fill_ = false;
  Device& instance_;

  // for dynamic update line config
  std::unique_ptr<RectangleConfigInternal> config_;
  XMMATRIX view_;

  Microsoft::WRL::ComPtr<ID3D11Buffer> matrix_;
  RectangleConfig pre_conf_ = {};
};

}  // namespace graphics
