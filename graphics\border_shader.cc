#include "border_shader.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include "DXSimpleMath.h"
#include "pixel_shader.h"
#include "vertex_shader.h"

using namespace Microsoft::WRL;

namespace graphics {

bool BorderShader::Init(const std::shared_ptr<Device>& ins) {
  if (!try_init_) {
    init_suc_ = DoInit(ins);
    try_init_ = true;
  }

  return init_suc_;
}

bool BorderShader::DoInit(const std::shared_ptr<Device>& ins) {
  device_ = ins;
  Device::CompileShaderParam param = {};
  param.ps = TEXTURE_PIXEL_SHADER();
  param.vs = TEXTURE_VERTEX_SHADER();
  param.ps_name = "BORDER_PS_MAIN";
  param.vs_name = "VS_MAIN";
  D3D11_INPUT_ELEMENT_DESC layout[2];
  layout[0].SemanticName = "POSITION";
  layout[0].SemanticIndex = 0;
  layout[0].Format = DXGI_FORMAT_R32G32B32_FLOAT;
  layout[0].InputSlot = 0;
  layout[0].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
  layout[0].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
  layout[0].InstanceDataStepRate = 0;
  layout[1].SemanticName = "TEXCOORD";
  layout[1].SemanticIndex = 0;
  layout[1].Format = DXGI_FORMAT_R32G32_FLOAT;
  layout[1].InputSlot = 0;
  layout[1].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
  layout[1].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
  layout[1].InstanceDataStepRate = 0;
  param.layout_descs_ = layout;
  param.layout_cnt_ = 2;
  if (!device_->CompileShader(param)) {
    return false;
  }
  vs_shader_ = param.vs_shader_;
  ps_shader_ = param.ps_shader_;

  layout_ = param.layout_;
  D3D11SetDebugObjectName(vs_shader_.Get(), "border_shader_vs");
  D3D11SetDebugObjectName(ps_shader_.Get(), "border_shader_ps");
  D3D11SetDebugObjectName(layout_.Get(), "border_shader_layout");
  D3D11_SAMPLER_DESC desc;
  desc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
  desc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.MipLODBias = 0.0F;
  desc.MaxAnisotropy = 1;
  desc.ComparisonFunc = D3D11_COMPARISON_ALWAYS;
  desc.BorderColor[0] = 0;
  desc.BorderColor[1] = 0;
  desc.BorderColor[2] = 0;
  desc.BorderColor[3] = 0;
  desc.MinLOD = 0;
  desc.MaxLOD = D3D11_FLOAT32_MAX;
  HRESULT hRes = GetDevice_()->CreateSamplerState(&desc, &sampler_);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to CreateSamplerState(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }
  D3D11SetDebugObjectName(sampler_.Get(), "border_shader_sampler");
  return true;
}

ID3D11Device* BorderShader::GetDevice_() {
  return device_->GetDevice().Get();
}

ID3D11DeviceContext* BorderShader::GetContext_() {
  return device_->GetContext().Get();
}

void BorderShader::RenderBorder(ID3D11ShaderResourceView* view,
                                ComPtr<ID3D11Buffer>& vertex_buffer,
                                ComPtr<ID3D11Buffer>& index_buffer,
                                ComPtr<ID3D11Buffer>& crop_buffer,
                                ComPtr<ID3D11Buffer>& matrix_buffer,
                                ComPtr<ID3D11Buffer>& ps_buffer) {
  UINT stride = sizeof(BorderShader::VERTEXTYPE);
  UINT offset = 0;
  auto context = GetContext_();
  context->IASetVertexBuffers(0, 1, vertex_buffer.GetAddressOf(), &stride,
                              &offset);
  context->IASetIndexBuffer(index_buffer.Get(), DXGI_FORMAT_R32_UINT, 0);
  context->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);
  context->VSSetConstantBuffers(0, 1, matrix_buffer.GetAddressOf());
  context->VSSetConstantBuffers(1, 1, crop_buffer.GetAddressOf());
  context->PSSetConstantBuffers(0, 1, ps_buffer.GetAddressOf());
  context->PSSetShaderResources(0, 1, &view);
  context->IASetInputLayout(layout_.Get());
  context->VSSetShader(vs_shader_.Get(), NULL, 0);
  context->PSSetShader(ps_shader_.Get(), NULL, 0);
  context->PSSetSamplers(0, 1, sampler_.GetAddressOf());
  context->DrawIndexed(6, 0, 0);
}

void BorderShader::Destroy() {
  if (sampler_) {
    sampler_.Reset();
  }

  if (vs_shader_) {
    vs_shader_.Reset();
  }
  if (ps_shader_) {
    ps_shader_.Reset();
  }
  if (layout_) {
    layout_.Reset();
  }
}

BorderShader::~BorderShader() {
  BorderShader::Destroy();
}

}  // namespace graphics