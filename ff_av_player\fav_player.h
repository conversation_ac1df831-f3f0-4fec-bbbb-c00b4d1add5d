#pragma once
#include <base/logging.h>
#include "av_frame_list.h"
#include "avsyn/av_syn_v_to_a.h"
#include "decoder/ff_decoder.h"
#include "demuxer/ff_demuxer.h"
#include "ff_swsscale/ff_swsscale.h"

class FAVPlayer final : public FFDemuxer::DemuxerObserver,
                        public FFDecoder::DecoderObserver {
 public:
  class PlayerObserver {
   public:
    virtual ~PlayerObserver() = default;

    virtual void OnPacket(const AVPacket& packet){};

    virtual void OnPreDropVideo(FAVPlayer*, AVFrame*){};

    virtual void OnPlayDropVideo(FAVPlayer*, AVFrame*){};

    virtual void OnPlayFirstAudioFrame(FAVPlayer*, AVFrame* frame){};

    virtual void OnPlayFirstVideoFrame(FAVPlayer*, AVFrame* frame){};

    virtual void OnAudioTooQuick(FAVPlayer*, AVFrame*, int64_t diff){};

    virtual void OnVideoTooSlow(FAVPlayer*, AVFrame*, int64_t diff) {}

    virtual void OnVideoTooQuick(FAVPlayer*, AVFrame*, int64_t diff){};

    virtual void OnSeekBegin(FAVPlayer*){};

    virtual void OnSeekEnd(FAVPlayer*){};

    // when user want hw decode but actually use software decoder
    virtual void OnDecoderFallBackToSoftware(FAVPlayer*){};

    virtual void OnPlayEOF(FAVPlayer*){};

    struct BufferSize {
      int pending_decode = 0;
      int pending_receive = 0;
      int pending_play = 0;
    };

    virtual void OnBufferSize(FAVPlayer*,
                              const BufferSize& audio,
                              const BufferSize& video){};
  };

  enum PlayMode {
    kModeNormalView = 1,
    kModeViewSingleFrame = 2,
    kModeViewVideo = 3,
    kModeNoAVSynCheck = 4,
    kModeNoDrop = 5,
  };

  class AudioRender {
   public:
    virtual ~AudioRender() = default;
    virtual void PlayAudio(AVFrame* frame) = 0;
  };

  class VideoRender {
   public:
    virtual ~VideoRender() = default;
    virtual void PlayVideo(AVFrame* frame) = 0;
    virtual void OnScaleCost(int64_t ns_ts) = 0;
  };

 public:
  explicit FAVPlayer(int32_t device_index, bool disable_post_driver_task);
  ~FAVPlayer() override;
  void AddObserver(PlayerObserver*);

  // DemuxerObserver
  void OnOpen(const FFDemuxer*) override;
  void OnAudioStream(const FFDemuxer* demuxer,
                     AVFormatContext* context,
                     int audio_stream_index) override;
  void OnVideoStream(const FFDemuxer* demuxer,
                     AVFormatContext* context,
                     int audio_stream_index) override;
  void OnPacket(const FFDemuxer*, AVPacket& packet, AVMediaType type) override;
  void OnStop(const FFDemuxer*) override;
  void OnEOF(const FFDemuxer*) override;
  void OnFirstAudioPacket(const FFDemuxer*) override;
  void OnFirstVideoPacket(const FFDemuxer*) override;
  void OnSeekResult(bool) override;
  void OnNoAudioStream(const FFDemuxer*) override;
  void OnNoVideoStream(const FFDemuxer*) override;
  void OnOtherStreamPacket(const FFDemuxer*) override;

  // DecoderObserver
  void OnFrame(FFDecoder*, AVFrame& frame) override;
  void OnFlushSuccess(FFDecoder*) override;
  void OnNeedMoreData(FFDecoder*) override;
  void OnDecoderEOF(FFDecoder*) override;
  void OnDecodeOpened(FFDecoder*, bool hw) override;

  bool OpenURL(const std::string& str,
               PlayMode mode = kModeNormalView,
               bool use_hw_surface = false);
  void EnableLoop(bool val);
  // add buffer size will add stability but add delay,
  // reduce buffer size will reduce delay
  void EnableNoBuffer(bool no_buffer);
  // if play real time steaming url, we need to call
  // StartPlay quickly after we call OpenURL,otherwise server
  // may close this connect
  void StartPlay();

  void Pause();
  void Resume();

  bool IsPause() const;

  bool CanSeek() const;
  bool IsSeeking() const;

  bool IsHWDecode() const;

  int64_t GetSecondsPos() const;
  int64_t GetSecondsDuration() const;  // seconds,-1 invalid call
  bool Seek(int64_t ms, bool cur = false);
  void StartUpSeek(const int64_t ms);

  void ResetHardwareDecode(bool val);

  void Close();
  void SetVideoRender(VideoRender*);
  void SetAudioRender(AudioRender*);
  FFDemuxer& GetDemuxer();
  FFDecoder& GetAudioDecoder();
  FFDecoder& GetVideoDecoder();
  void StepOneFrame();
  void QuitStep();

  void Tick();

 private:
  int32_t GetSerial();
  void SignalPlayEOF();
  void HandleVideoFrame(AVFrame& frame, int32_t serial);
  void HandleAudioFrame(AVFrame& frame, int32_t serial);
  bool PreDropped(AVFrame& frame);
  void CheckLoopPlayTask();
  void DoCheckLoopPlayTask();
  bool CheckLastRenderFrame();
  void PostPlayAudioTask();
  void PostPlayVideoTask();
  void PlayAudioTask();
  void PlayVideoTask();
  bool NeedReadMorePacket();
  void TraceBufferedWork();
  void PostDriverWork();
  void FlushVideoDecoderBySendEmptyPacket();
  void FlushAudioDecoderBySendEmptyPacket();
  void PostVideoDecodeWork(bool eof = false);
  void PostAudioDecodeWork(bool eof = false);
  void DoPostReadWork();
  void PostDemuxerSeekTask(int64_t ms_ts, bool absolute);
  void DoSeekByDemuxer(int64_t ms_ts);
  bool CouldPlay();
  void ResetStatusToStartUP();
  bool NeedCheckDrop() const;

  bool IsNoAudio() const { return !stream_a_; }

  bool HasAudio() const { return !IsNoAudio(); }

  bool HasVideo() const { return !!stream_v_; }

  bool NeedAVSyn() const {
    bool ret = (mode_ == kModeNormalView || mode_ == kModeNoDrop);
    return ret;
  }

  void ClearPendingVideoFrames() { v_frame_list_.Clear(); }

 private:
  FFDemuxer demuxer_;
  FFDecoder audio_decoder_;
  FFDecoder video_decoder_;
  AVStream* stream_v_ = nullptr;
  AVStream* stream_a_ = nullptr;
  VideoRender* video_render_ = nullptr;
  AudioRender* audio_render_ = nullptr;
  std::atomic_bool enable_loop_ = false;
  std::atomic_bool stop_ = false;
  AVSynClock syn_;

  AVFrameList a_frame_list_;
  AVFrameList v_frame_list_;

  base::Thread play_audio_{"play_audio"};
  base::Thread play_video_{"play_video"};
  base::Thread decode_audio_{"decode_audio"};
  base::Thread decode_video_{"decode_video"};
  base::Thread demuxer_thread_{"demuxer"};

  std::atomic_bool audio_flushed_ = {false};
  std::atomic_bool video_flushed_ = {false};
  int64_t seek_request_ = 0;
  bool seek_absolute_ = false;
  bool hardware_decode_ = {false};
  FFSWSScale ff_scale_;

  std::vector<PlayerObserver*> observer_list_;
  PlayMode pre_mode_ = PlayMode::kModeNormalView;
  PlayMode mode_ = PlayMode::kModeNormalView;
  bool step_ = false;
  bool video_decoder_need_more_data_ = false;
  bool video_decoder_eof_ = false;

  bool audio_decoder_need_more_data_ = false;
  bool audio_decoder_eof_ = false;

  bool seeking_ = false;
  bool use_hw_surface_ = true;  // true for pure surface
  bool disable_post_driver_task_ = false;
  int32_t device_index_ = 0;  // for hw surface shared texture from same device;

  int32_t serial_ = 0;  // for seek operation
  std::atomic_int task_num_ = 0;
};