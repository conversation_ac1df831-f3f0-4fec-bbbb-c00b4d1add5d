#pragma once

#if defined(WIN32)

#if defined(PLUGIN_IMPLEMENTATION)
#define PLUGIN_EXPORT __declspec(dllexport)
#else
#define PLUGIN_EXPORT __declspec(dllimport)
#endif  // defined(PLUGIN_IMPLEMENTATION)

#else  // defined(WIN32)

#if defined(PLUGIN_IMPLEMENTATION)
#define PLUGIN_EXPORT __attribute__((visibility("default")))
#else
#define PLUGIN_EXPORT
#endif  // defined(PLUGIN_IMPLEMENTATION)
#endif


