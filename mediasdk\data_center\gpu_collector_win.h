#pragma once

#include "gpu_collector.h"
#include "pdh_wrapper.h"

namespace gpu_collector {
class GpuCollectorImpl final : public GpuCollector {
 public:
  GpuCollectorImpl() = default;

  ~GpuCollectorImpl() override;

  int GpuNumber(int pid) override;

  bool ProcessUsage(uint64_t pid, std::vector<GpuUsage>& usage) override;

  bool SystemUsage(std::vector<GpuUsage>& usage) override;

  bool IsInitialized() const;

 private:
  PdhWrapper pdh_wrapper_;
  std::vector<GpuCollectItem> collect_items_ = {};
};
}  // namespace gpu_collector