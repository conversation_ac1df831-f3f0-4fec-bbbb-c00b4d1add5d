#pragma once

#include <cstdint>

#include "graphics/public/graphics_texture_frame.h"

namespace mediasdk {

class VisualFrameQueueConverter {
 public:
  virtual ~VisualFrameQueueConverter() = default;

  virtual bool Prepare(const graphics::TextureFrame& frame) = 0;

  virtual bool Convert() = 0;

  virtual void ResetOnNextPrepare() = 0;

  virtual graphics::Texture* GetTexture() = 0;
};

}  // namespace mediasdk
