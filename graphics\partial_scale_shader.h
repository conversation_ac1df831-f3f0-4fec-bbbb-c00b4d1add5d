#pragma once

#include <DirectXMath.h>
#include <wrl/client.h>
#include "shader.h"
#include "transform.h"
#include "partial_scale_params.h"
#include "texture.h"
using namespace DirectX;
namespace graphics {

class Device;

class PartialScaleShader : public Shader {
 public:
 struct VERTEXTYPE {
    XMFLOAT3 position;
    XMFLOAT2 texture;
    XMFLOAT4 color;
  };

  // The structure for the constant buffer that VS will use for scale/offset parameters.
  // This should match the PatialScaleParams struct for easy mapping.
  struct VSScaleParamsBuffer {
    DirectX::XMFLOAT2 scale;         // PatialScaleParams.scale
    DirectX::XMFLOAT2 offset;        // PatialScaleParams.offset
    DirectX::XMFLOAT2 texture_size;  // PatialScaleParams.texture_size
    DirectX::XMFLOAT2 reserved;      // PatialScaleParams.reserved
  };

  static inline const char* SHADER_ID_STRING = "partial_scale_shader";

  static std::shared_ptr<Shader> Create(const char* type) {
    if (strcmp(type, SHADER_ID_STRING) == 0) {
      return std::make_shared<PartialScaleShader>();
    }
    return nullptr;
  }

  static void EnumPluginsFunction(void* param, EnumPluginCallBack fpn) {
    fpn(param, ShaderItem{SHADER_ID_STRING, "Shader for drawing texture with partial scaling", PartialScaleShader::Create});
  }

  PartialScaleShader();
  ~PartialScaleShader() override;

  bool Init(const std::shared_ptr<Device>& device) override;
  void Render(ID3D11ShaderResourceView* view,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& vertex_buffer,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& index_buffer,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& crop_buffer,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& matrix_buffer,
    Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_buffer);
  void Destroy() override;

 private:
  bool DoInit(const std::shared_ptr<Device>& ins);
  bool CreateVertexAndIndexBuffers();

  std::shared_ptr<Device> device_;
  ID3D11DeviceContext* context_;

  Microsoft::WRL::ComPtr<ID3D11VertexShader> vertex_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> pixel_shader_;
  Microsoft::WRL::ComPtr<ID3D11InputLayout> input_layout_;
  Microsoft::WRL::ComPtr<ID3D11SamplerState> sampler_state_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> vs_matrix_buffer_; // For world, view, projection (b0)
  // scale_params_buffer (b1) is managed and passed by GraphicsImpl

  Microsoft::WRL::ComPtr<ID3D11Buffer> vertex_buffer_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> index_buffer_;
  UINT index_count_;

  bool try_init_ = false;
  bool init_suc_ = false;
};

}  // namespace graphics