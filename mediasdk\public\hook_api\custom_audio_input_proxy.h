#pragma once

#include "mediasdk/public/mediasdk_defines.h"

namespace mediasdk {

namespace hook_api {

class CustomAudioInputProxy {
 public:
  virtual ~CustomAudioInputProxy() {}

  // Inject audio data into `CustomAudioInput`.
  // The length of the injected audio data and the timestamp must be strictly
  // aligned with the injection frequency.
  // Note: The injected audio data needs to be aligned with the internal audio
  // processing format. The expected format is as follows:
  // - AUDIO_FORMAT: AUDIO_FORMAT_FLOAT_PLANAR
  // - CHANNEL_LAYOUT: CHANNEL_STEREO
  // - Sample Rate: 48kHz
  virtual void InputCustomAudioData(const AudioSourceFrame& frame) = 0;
};

}  // namespace hook_api

}  // namespace mediasdk