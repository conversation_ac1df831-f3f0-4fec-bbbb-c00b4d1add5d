#include "color_params.h"
#include <DirectXMath.h>
#include "graphics_utils.h"

namespace graphics {

namespace {
DirectX::XMFLOAT3 CalcClearColor(uint32_t bk_color) {
  int r = (bk_color >> 16) & 0xFF;
  int g = (bk_color >> 8) & 0xFF;
  int b = bk_color & 0xFF;
  return {r * 1.0f / 255, g * 1.0f / 255, b * 1.0f / 255};
}

float ColorNonLinearToLinear(float color) {
  return (color <= 0.04045f) ? (color / 12.92f)
                             : powf((color + 0.055f) / 1.055f, 2.4f);
}

DirectX::XMFLOAT3 CalcLinearColor(DirectX::XMFLOAT3 color) {
  float r = ColorNonLinearToLinear(color.x);
  float g = ColorNonLinearToLinear(color.y);
  float b = ColorNonLinearToLinear(color.z);
  return {r, g, b};
}

}  // namespace

ColorParams::ColorParams(const ColorParams& other) {
  Assign(other);
}

ColorParams& ColorParams::operator=(const ColorParams& other) {
  if (this != &other) {
    Assign(other);
  }

  return *this;
}

bool ColorParams::operator==(const ColorParams& other) const {
  return IsNearEqual(brightness_, other.Brightness()) &&
         IsNearEqual(saturation_, other.Saturation()) &&
         IsNearEqual(contrast_, other.Contrast()) &&
         IsNearEqual(hue_shift_, other.HueShift()) &&
         IsNearEqual(opacity_, other.Opacity()) &&
         IsNearEqual(gamma_, other.Gamma()) &&
         add_color_ == other.GetAddColor() && mul_color_ == other.GetMulColor();
}

bool ColorParams::operator!=(const ColorParams& other) const {
  return !(*this == other);
}

bool ColorParams::IsEmpty() const {
  return *this == ColorParams();
}

void ColorParams::Reset() {
  *this = ColorParams();
}

float ColorParams::Brightness() const {
  return brightness_;
}

void ColorParams::SetBrightness(float brightness) {
  brightness_ = brightness;
}

float ColorParams::Saturation() const {
  return saturation_;
}

void ColorParams::SetSaturation(float saturation) {
  saturation_ = saturation;
}

float ColorParams::Contrast() const {
  return contrast_;
}

void ColorParams::SetContrast(float contrast) {
  contrast_ = contrast;
}

float ColorParams::HueShift() const {
  return hue_shift_;
}

void ColorParams::SetHueShift(float hue_shift) {
  hue_shift_ = hue_shift;
}

float ColorParams::Opacity() const {
  return opacity_;
}

void ColorParams::SetOpacity(float opacity) {
  opacity_ = opacity;
}

float ColorParams::Gamma() const {
  return gamma_;
}

void ColorParams::SetGamma(float gamma) {
  gamma_ = gamma;
}

void ColorParams::SetAddColor(uint32_t add_color) {
  add_color_ = add_color;
}

uint32_t ColorParams::GetAddColor() const {
  return add_color_;
}

void ColorParams::SetMulColor(uint32_t mul_color) {
  mul_color_ = mul_color;
}

uint32_t ColorParams::GetMulColor() const {
  return mul_color_;
}

void ColorParams::Assign(const ColorParams& other) {
  brightness_ = other.Brightness();
  saturation_ = other.Saturation();
  contrast_ = other.Contrast();
  hue_shift_ = other.HueShift();
  opacity_ = other.Opacity();
  gamma_ = other.Gamma();
  add_color_ = other.GetAddColor();
  mul_color_ = other.GetMulColor();
}

DirectX::XMMATRIX BuildColorCorrectionMatrix(const ColorParams& color_params) {
  DirectX::XMMATRIX brightness_contrast_mat =
      BuildBrightnessAndContrastMatrix(color_params);
  DirectX::XMMATRIX hue_mat = BuildHueShiftMatrix(color_params);
  DirectX::XMMATRIX sat_mat = BuildSaturationMatrix(color_params);
  DirectX::XMMATRIX color_mat = BuildCorrectionColorMatrix(color_params);
  DirectX::XMMATRIX final_mat =
      brightness_contrast_mat * sat_mat * hue_mat * color_mat;

  return final_mat;
}

DirectX::XMMATRIX BuildBrightnessAndContrastMatrix(
    const ColorParams& color_params) {
  // clang-format off
  // color = (color - (0.5f * (1.0f-brightness))) * contrast + 0.5f * (1.0f+brightness);
  DirectX::XMMATRIX brightness_contrast_mat{};
  float b = color_params.Brightness();
  float c = tan((45 + 44 * color_params.Contrast()) / 180 * DirectX::XM_PI);
  float z = 0.5f * (1.0f + b) + c * (b - 0.5f);
  brightness_contrast_mat = {c,    0.0f, 0.0f, 0.0f,
                                0.0f, c,    0.0f, 0.0f,
                                0.0f, 0.0f, c,    0.0f,
                                z,    z,    z,    1.0f};
  // clang-format on
  return brightness_contrast_mat;
}

DirectX::XMMATRIX BuildHueShiftMatrix(const ColorParams& color_params) {
  // clang-format off
  DirectX::XMMATRIX hue_mat{};
  // ref: https://stackoverflow.com/questions/8507885/shift-hue-of-an-rgb-color
  float cos_a = cos(DirectX::XMConvertToRadians(color_params.HueShift() * 180.0f));
  float sin_a = sin(DirectX::XMConvertToRadians(color_params.HueShift() * 180.0f));
  float mat_00 = cos_a + (1.0f - cos_a) / 3.0f;
  float mat_01 = 1.0f / 3.0f * (1.0f - cos_a) - sqrt(1.0f / 3.0f) * sin_a;
  float mat_02 = 1.0f / 3.0f * (1.0f - cos_a) + sqrt(1.0f / 3.0f) * sin_a;
  float mat_10 = 1.0f / 3.0f * (1.0f - cos_a) + sqrt(1.0f / 3.0f) * sin_a;
  float mat_11 = cos_a + 1.0f / 3.0f * (1.0f - cos_a);
  float mat_12 = 1.0f / 3.0f * (1.0f - cos_a) - sqrt(1.0f / 3.0f) * sin_a;
  float mat_20 = 1.0f / 3.0f * (1.0f - cos_a) - sqrt(1.0f / 3.0f) * sin_a;
  float mat_21 = 1.0f / 3.0f * (1.0f - cos_a) + sqrt(1.0f / 3.0f) * sin_a;
  float mat_22 = cos_a + 1.0f / 3.0f * (1.0f - cos_a);
  hue_mat = {mat_00, mat_01, mat_02, 0.0f,
                mat_10, mat_11, mat_12, 0.0f,
                mat_20, mat_21, mat_22, 0.0f,
                0.0f,   0.0f,   0.0f,   1.0f};
  // clang-format on
  return hue_mat;
}

DirectX::XMMATRIX BuildSaturationMatrix(const ColorParams& color_params) {
  // clang-format off
  DirectX::XMMATRIX sat_mat{};
  // ref: https://www.graficaobscura.com/matrix/index.html
  const float rwgt = 0.3086f;
  const float gwgt = 0.6094f;
  const float bwgt = 0.0820f;
  const float s = IsLessThan(color_params.Saturation(), 0.0f) ? 
     color_params.Saturation() + 1.0f : color_params.Saturation() * 5.0f + 1.0f;
  const float a = (1.0f-s)*rwgt + s;
  const float b = (1.0f-s)*rwgt;
  const float c = (1.0f-s)*rwgt;
  const float d = (1.0f-s)*gwgt;
  const float e = (1.0f-s)*gwgt + s;
  const float f = (1.0f-s)*gwgt;
  const float g = (1.0f-s)*bwgt;
  const float h = (1.0f-s)*bwgt;
  const float i = (1.0f-s)*bwgt + s;

  sat_mat = {
        a,    b,    c,    0.0f,
        d,    e,    f,    0.0f,
        g,    h,    i,    0.0f,
        0.0f, 0.0f, 0.0f, 1.0f,
  };

  // clang-format on
  return sat_mat;
}

DirectX::XMMATRIX BuildCorrectionColorMatrix(const ColorParams& color_params) {
  DirectX::XMMATRIX color_mat{};

  auto add_color = CalcClearColor(color_params.GetAddColor());
  auto linear_add_color = CalcLinearColor(add_color);

  auto mul_color = CalcClearColor(color_params.GetMulColor());
  auto linear_mul_color = CalcLinearColor(mul_color);

  const float a = linear_mul_color.x;
  const float b = linear_mul_color.y;
  const float c = linear_mul_color.z;
  const float d = linear_add_color.x;
  const float e = linear_add_color.y;
  const float f = linear_add_color.z;

  // clang-format off
  color_mat = {
      a,     0.0f,  0.0f,  0.0f,
      0.0f,  b,     0.0f,  0.0f, 
      0.0f,  0.0f,  c,     0.0f, 
      d,     e,     f,     1.0f,
  };
  // clang-format on
  return color_mat;
}

float GammaCorrection(float input_gamma) {
  return (IsLessThan(input_gamma, 0.0f)) ? (-input_gamma + 1.0f)
                                         : (1.0 / (input_gamma + 1.0f));
}
}  // namespace graphics