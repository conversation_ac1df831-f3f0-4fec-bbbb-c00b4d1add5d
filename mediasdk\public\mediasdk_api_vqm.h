#pragma once

#include "mediasdk_callback_defines.h"
#include "mediasdk_export.h"

#ifdef __cplusplus
extern "C" {
#endif  // __cplusplus

namespace mediasdk {

// Initialize the VideoQualityManager
// Synchronous callback result, ResultBoolString, when success in result is
// true, parse the value in result
MEDIASDK_EXPORT void MS_API InitVQM(const char* json_params, Closure closure);

// UnInitialize the VideoQualityManager
// Synchronous callback result, bool
MEDIASDK_EXPORT void MS_API UnInitVQM(Closure closure);

// Recommend go live parameters based on device performance level, network
// speed test, camera maximum acquisition capability and other parameters.
// Synchronous callback result, ResultBoolString, when success in result is
// true, parse the value in result
MEDIASDK_EXPORT void MS_API
QueryGoLiveRecommendedParams(const char* json_params, Closure closure);

// Recommend more suitable camera parameters based on current live encoding
// parameters, device performance level, and current camera parameters.
// Synchronous callback result, ResultBoolString, when success in result is
// true, parse the value in result
MEDIASDK_EXPORT void MS_API
QueryCameraRecommendedParams(const char* json_params, Closure closure);

// Query specific go live parameters based on the user's selected gear id and
// current topic id.
// Synchronous callback result, ResultBoolString, when success in result is
// true, parse the value in result
MEDIASDK_EXPORT void MS_API
QueryGoLiveManuallySelectedParams(const char* json_params, Closure closure);

// Query the best parameters in the camera parameter list based on the target
// parameters.
// Synchronous callback result, ResultBoolString, when success in
// result is true, parse the value in result
MEDIASDK_EXPORT void MS_API
QueryCameraBestParamsForTarget(const char* json_params, Closure closure);
}  // namespace mediasdk

#ifdef __cplusplus
}
#endif  // __cplusplus