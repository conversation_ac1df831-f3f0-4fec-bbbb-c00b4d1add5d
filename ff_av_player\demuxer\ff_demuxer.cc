#include "ff_demuxer.h"

#include <base/bind.h>
#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <base/strings/sys_string_conversions.h>
#include "ffmpeg_common.h"

extern "C" {
#include "libavutil/display.h"
}

namespace {
double GetDisplayRotation(const int32_t* display_matrix) {
  double theta =
      display_matrix ? -round(av_display_rotation_get(display_matrix)) : 0;
  theta -= 360 * floor(theta / 360 + 0.9 / 360);
  return theta;
}
}  // namespace

bool FFDemuxer::GetFrameInfo(const std::string& file_path,
                             double* duration,
                             float* angle,
                             int* frame_width,
                             int* frame_height) {
  AVFormatContext* context_ = nullptr;

  if (const auto r =
          avformat_open_input(&context_, file_path.c_str(), nullptr, nullptr)) {
    LOG(ERROR) << "Failed to open file: " << file_path << " " << r;
    return false;
  }

  if (const auto r = avformat_find_stream_info(context_, nullptr); r < 0) {
    LOG(ERROR) << "Failed to find stream info"
               << " " << r;
    avformat_close_input(&context_);
    return false;
  }
  const AVStream* video_stream = nullptr;
  const int index =
      av_find_best_stream(context_, AVMEDIA_TYPE_VIDEO, -1, -1, nullptr, 0);
  if (index >= 0) {
    video_stream = context_->streams[index];
  } else {
    LOG(ERROR) << "No video stream found in the file!";
    avformat_close_input(&context_);
    return false;
  }

  if (duration)
  {
      
      if (context_->duration != AV_NOPTS_VALUE)
      {
          *duration = static_cast<double>(context_->duration) / AV_TIME_BASE;
      }
      else
      {
          LOG(ERROR) << "No find video_stream duration!";
      }
  }

  if (angle) {
    const auto* display_matrix =
        reinterpret_cast<int32_t*>(av_stream_get_side_data(
            video_stream, AV_PKT_DATA_DISPLAYMATRIX, nullptr));
    *angle = static_cast<int32_t>(GetDisplayRotation(display_matrix));
  }

  if (frame_width) {
    *frame_width = video_stream->codecpar->width;
  }
  if (frame_height) {
    *frame_height = video_stream->codecpar->height;
  }
  avformat_close_input(&context_);
  return true;
}

void FFDemuxer::AddObserver(DemuxerObserver* obs) {
  obs_list_.push_back(obs);
}

void FFDemuxer::RemoveObserver(DemuxerObserver* obs) {
  obs_list_.erase(std::remove(obs_list_.begin(), obs_list_.end(), obs),
                  obs_list_.end());
}

void FFDemuxer::SignalPacket(AVPacket& packet) {
  const int stream_index = packet.stream_index;
  if (stream_index == video_stream_index_) {
    if (!video_packet_cnt_) {
      for (auto* observer : obs_list_) {
        observer->OnFirstVideoPacket(this);
      }
    }
    ++video_packet_cnt_;
    video_packet_byte_ += packet.size;
  } else if (stream_index == audio_stream_index_) {
    if (!audio_packet_cnt_ && !audio_packet_byte_) {
      for (auto* observer : obs_list_) {
        observer->OnFirstAudioPacket(this);
      }
    }
    ++audio_packet_cnt_;
    audio_packet_byte_ += packet.size;
  } else {
    for (auto* observer : obs_list_) {
      observer->OnOtherStreamPacket(this);
    }
    return;
  }
  const auto packet_type = stream_index == video_stream_index_
                               ? AVMEDIA_TYPE_VIDEO
                               : AVMEDIA_TYPE_AUDIO;
  for (auto* observer : obs_list_) {
    observer->OnPacket(this, packet, packet_type);
  }
}

void FFDemuxer::EnableNoBuffer(const bool no_buffer) {
  no_buffer_ = no_buffer;
}

void FFDemuxer::SimpleSeek(const int64_t ms_ts) {
  const int64_t ts = ms_ts * AV_TIME_BASE / 1000;
#ifdef DEBUG_FAV_PLAYER
  LOG(INFO) << "seek target [" << ts << "]";
#endif
  const int ret = avformat_seek_file(format_context_, -1,
                                     std::numeric_limits<int64_t>::min(), ts,
                                     std::numeric_limits<int64_t>::max(), 0);
  if (ret < 0) {
    LOG(ERROR) << "Failed to seek[" << FFErrorToString(ret) << "]";
  }
}

void FFDemuxer::SeekWork(const int64_t ms_ts) {
  eof_ = false;
  const int64_t ts = ms_ts * AV_TIME_BASE / 1000;
#ifdef DEBUG_FAV_PLAYER
  LOG(INFO) << "seek target [" << ts << "]";
#endif
  ResetStatics();
  const int ret = avformat_seek_file(format_context_, -1,
                                     std::numeric_limits<int64_t>::min(), ts,
                                     std::numeric_limits<int64_t>::max(), 0);
  if (ret < 0) {
    LOG(ERROR) << "Failed to seek[" << FFErrorToString(ret) << "]";
  }
  const auto result = ret >= 0;
  ResetStatics();
  for (auto* observer : obs_list_) {
    observer->OnSeekResult(result);
  }
}

bool FFDemuxer::Seek(const base::Thread& thread, int64_t ms_ts) {
  if (!CanSeek()) {
    DCHECK(false && "can not seek, invalid call");
    return false;
  }
  const auto runner = thread.task_runner();
  if (!runner) {
    return false;
  }

  runner->PostTask(FROM_HERE, base::BindOnce(&FFDemuxer::SeekWork,
                                             base::Unretained(this), ms_ts));

  return true;
}

bool FFDemuxer::OpenURL(const std::string& url, int32_t time_out) {
  format_context_ = avformat_alloc_context();

  AVDictionary* opts = nullptr;
  if (no_buffer_) {
    av_dict_set(&opts, "fflags", "nobuffer", 0);
  }
  if (url.find(".flv") != std::string::npos) {
    av_dict_set_int(&opts, "flv_full_metadata", 1, 0);
  }

  if (const auto r =
          avformat_open_input(&format_context_, url.c_str(), nullptr, &opts);
      r < 0) {
    LOG(ERROR) << "Failed to avformat_open_input "
               << " [" << FFErrorToString(r) << "]";
    return false;
  }

  {
    for (const auto* t = av_dict_get(opts, "", nullptr, AV_DICT_IGNORE_SUFFIX);
         t; t = av_dict_get(opts, "", t, AV_DICT_IGNORE_SUFFIX)) {
      LOG(WARNING) << "un handled [" << t->key << "] [" << t->value << "]";
    }
    av_dict_free(&opts);
  }

  if (const auto r = avformat_find_stream_info(format_context_, nullptr);
      r < 0) {
    LOG(ERROR) << "Failed to avformat_find_stream_info"
               << " [" << FFErrorToString(r) << "]";
    DCHECK(false);
    return false;
  }
  av_dump_format(format_context_, 0, nullptr, 0);

  for (int32_t i = 0; i < format_context_->nb_streams; i++) {
    AVStream* stream = format_context_->streams[i];
    if (!stream) {
      continue;
    }

    stream->discard = AVDISCARD_ALL;
  }

  audio_stream_index_ = av_find_best_stream(format_context_, AVMEDIA_TYPE_AUDIO,
                                            -1, -1, nullptr, 0);
  video_stream_index_ = av_find_best_stream(format_context_, AVMEDIA_TYPE_VIDEO,
                                            -1, -1, nullptr, 0);

  if (video_stream_index_ == AVERROR_STREAM_NOT_FOUND) {
    for (auto* observer : obs_list_) {
      observer->OnNoVideoStream(this);
    }
  } else {
    if (const auto* display_matrix =
            reinterpret_cast<int32_t*>(av_stream_get_side_data(
                format_context_->streams[video_stream_index_],
                AV_PKT_DATA_DISPLAYMATRIX, nullptr))) {
      rotate_ = static_cast<int32_t>(GetDisplayRotation(display_matrix));
    }
    for (auto* observer : obs_list_) {
      observer->OnVideoStream(this, format_context_, video_stream_index_);
    }
  }
  if (audio_stream_index_ == AVERROR_STREAM_NOT_FOUND) {
    for (auto* observer : obs_list_) {
      observer->OnNoAudioStream(this);
    }
  } else {
    for (auto* observer : obs_list_) {
      observer->OnAudioStream(this, format_context_, audio_stream_index_);
    }
  }

  return true;
}

void FFDemuxer::AsyReadWork(const base::Thread& thread) {
  if (stop_)
    return;

  if (const auto runner = thread.task_runner()) {
    runner->PostTask(FROM_HERE, base::BindOnce(&FFDemuxer::SingleReadWork,
                                               base::Unretained(this)));
  } else {
    DCHECK(false && "stopping");
  }
}

bool FFDemuxer::IsEOF() const {
  return eof_;
}

bool FFDemuxer::CanSeek() const {
  DCHECK(format_context_ && "call this after open success");
  return (format_context_->pb &&
          format_context_->pb->seekable & AVIO_SEEKABLE_NORMAL);
}

void FFDemuxer::Close(base::Thread& thread) {
  stop_ = true;
  thread.Stop();
  ResetStatics();
  if (format_context_) {
    avformat_close_input(&format_context_);
    format_context_ = nullptr;
  }
}

int64_t FFDemuxer::GetAudioCnt() {
  return audio_packet_cnt_;
}

int64_t FFDemuxer::GetVideoCnt() {
  return video_packet_cnt_;
}

int64_t FFDemuxer::GetAudioByte() {
  return audio_packet_byte_;
}

int64_t FFDemuxer::GetVideoByte() {
  return video_packet_byte_;
}

void FFDemuxer::ResetStatics() {
  audio_packet_byte_ = 0;
  video_packet_byte_ = 0;
  audio_packet_cnt_ = 0;
  video_packet_cnt_ = 0;
}

void FFDemuxer::SingleReadWork() {
  if (stop_ || IsEOF()) {
    return;
  }
  AVPacket packet = {};
  if (!pause_) {
    if (const auto read_result = av_read_frame(format_context_, &packet);
        read_result == 0) {
      SignalPacket(packet);
      av_packet_unref(&packet);
      eof_ = false;
    } else if (read_result == AVERROR_EOF) {
      eof_ = true;

      for (auto* observer : obs_list_) {
        observer->OnEOF(this);
      }
    } else {
      LOG(ERROR) << "Failed to avformat_find_stream_info ["
                 << FFErrorToString(read_result);
    }
  }
}

AVFormatContext* FFDemuxer::GetContext() const {
  return format_context_;
}