﻿#include "display_flip_alpha_impl.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <dxgi1_3.h>
#include "base/check.h"

using namespace Microsoft::WRL;

namespace {
constexpr int kSwapCHainBufferCount = 3;
}

namespace graphics {

std::shared_ptr<DisplayImplFlipModeAlpha>
DisplayImplFlipModeAlpha::CreateDisplay2D(Device& ins,
                                          HWND hwnd,
                                          int cx,
                                          int cy) {
  DCHECK(::IsWindow(hwnd));
  RECT rect = {};
  GetClientRect(hwnd, &rect);
  DCHECK(rect.right - rect.left == cx);
  DCHECK(rect.bottom - rect.top == cy);
  auto ret = std::make_shared<DisplayImplFlipModeAlpha>(ins);
  LOG(INFO) << "create displayAlpha [" << hwnd << "] cx [" << cx << "] cy ["
            << cy << "] top left [" << rect.left << "," << rect.top
            << "] bottom right[" << rect.right << "," << rect.bottom << "]";
  if (!ret->OpenWithHWND(hwnd)) {
    return nullptr;
  }

  return ret;
}

DisplayImplFlipModeAlpha::DisplayImplFlipModeAlpha(Device& ins)
    : device_(ins) {}

bool DisplayImplFlipModeAlpha::OpenWithHWND(HWND hwnd) {
  Destroy();
  graphics_ = CreateGraphics2D(device_);

  RECT rect = {};
  GetClientRect(hwnd, &rect);
  if (rect.right <= rect.left || rect.bottom <= rect.top) {
    LOG(INFO) << base::StringPrintf("Window Size Error %d %d %d %d", rect.left,
                                    rect.top, rect.right, rect.bottom);
    return false;
  }
  auto cx = rect.right - rect.left;
  auto cy = rect.bottom - rect.top;
  // Create Device
  // default running on first adapter
  HRESULT res = S_OK;
  do {
    swap_chain_desc1_.Width = cx;
    swap_chain_desc1_.Height = cy;
    swap_chain_desc1_.Format = DXGI_FORMAT_B8G8R8A8_UNORM;
    swap_chain_desc1_.SampleDesc.Count = 1;
    swap_chain_desc1_.SampleDesc.Quality = 0;
    swap_chain_desc1_.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    swap_chain_desc1_.BufferCount = 2;
    swap_chain_desc1_.Scaling = DXGI_SCALING_STRETCH;
    swap_chain_desc1_.SwapEffect = DXGI_SWAP_EFFECT_FLIP_SEQUENTIAL;
    swap_chain_desc1_.AlphaMode = DXGI_ALPHA_MODE_PREMULTIPLIED;
    swap_chain_desc1_.Flags = 0;
    ComPtr<IDXGIFactory3> factory3;

    device_.GetDXGIFactory()->QueryInterface(__uuidof(IDXGIFactory3),
                                             &factory3);
    if (!factory3)
      break;

    if (factory3->CreateSwapChainForComposition(
            device_.GetDevice().Get(), &swap_chain_desc1_, nullptr,
            swap_chain1_.GetAddressOf()) != S_OK)
      return false;

    DCompositionCreateDevice(
        nullptr, __uuidof(IDCompositionDevice),
        reinterpret_cast<void**>(dcompDevice_.GetAddressOf()));

    dcompDevice_->CreateTargetForHwnd(hwnd, TRUE, &target_);

    dcompDevice_->CreateVisual(visual_.GetAddressOf());
    visual_->SetContent(swap_chain1_.Get());
    target_->SetRoot(visual_.Get());
    dcompDevice_->Commit();

    ComPtr<IDXGISwapChain2> swap2;
    res = swap_chain1_.As(&swap2);
    if (FAILED(res)) {
      LOG(ERROR) << base::StringPrintf("Failed to Convert to swap2 %s",
                                       GetErrorString(res).c_str());
    } else if (swap2) {
      swap2->SetMaximumFrameLatency(1);
      waitable_event_ = swap2->GetFrameLatencyWaitableObject();
      if (waitable_event_ == NULL) {
        LOG(ERROR)
            << "DX11Display2D::CreateV2 GetFrameLatencyWaitableObject failed";
      }
    }

  } while (0);

  if (!swap_chain1_) {
    ZeroMemory(&swap_chain_desc1_, sizeof(swap_chain_desc1_));
    swap_chain_desc1_.Width = cx;
    swap_chain_desc1_.Height = cy;
    swap_chain_desc1_.Format = DXGI_FORMAT_B8G8R8A8_UNORM;
    swap_chain_desc1_.SampleDesc.Count = 1;
    swap_chain_desc1_.SampleDesc.Quality = 0;
    swap_chain_desc1_.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    swap_chain_desc1_.BufferCount = kSwapCHainBufferCount;
    swap_chain_desc1_.Scaling = DXGI_SCALING_STRETCH;
    swap_chain_desc1_.SwapEffect = DXGI_SWAP_EFFECT_FLIP_SEQUENTIAL;
    swap_chain_desc1_.AlphaMode = DXGI_ALPHA_MODE_PREMULTIPLIED;
    swap_chain_desc1_.Flags = 0;
    ComPtr<IDXGIFactory3> factory3;
    device_.GetDXGIFactory()->QueryInterface(__uuidof(IDXGIFactory3),
                                             &factory3);
    if (!factory3)
      return false;

    auto res = factory3->CreateSwapChainForComposition(
        device_.GetDevice().Get(), &swap_chain_desc1_, nullptr,
        swap_chain1_.GetAddressOf());
    if (res != S_OK) {
      LOG(ERROR) << base::StringPrintf(
          "DX11Display2D::CreateV2 CreateSwapChain(BitBlt), %x", res);
      return false;
    }
  }
  res = device_.GetDXGIFactory()->MakeWindowAssociation(hwnd,
                                                        DXGI_MWA_NO_ALT_ENTER);
  if (FAILED(res)) {
    LOG(ERROR) << base::StringPrintf("Failed to MakeWindowAssociation %s",
                                     GetErrorString(res).c_str());
    return false;
  }

  hwnd_ = hwnd;

  LOG(INFO) << base::StringPrintf("Success Create Swap Chain %d.%d", cx, cy);
  if (!CreateTexture()) {
    return false;
  }
  device_.SetViewport(XMFLOAT2_EMPTY, graphics_->GetSize());
  return true;
}

bool DisplayImplFlipModeAlpha::CreateTexture() {
  return graphics_->CreateWithSwapChain(swap_chain1_.Get());
}

bool DisplayImplFlipModeAlpha::IsReady(uint32_t wait_time) {
  bool ret = false;
  do {
    if (waitable_event_ == NULL ||
        WaitForSingleObject(waitable_event_, wait_time) == WAIT_OBJECT_0) {
      ret = true;
      break;
    }

    ++not_ready_cnt_;
    if (not_ready_cnt_ % 10 == 0) {
      HRESULT hr = swap_chain1_->Present(0, DXGI_PRESENT_TEST);
      if (FAILED(hr)) {
        device_.CheckDevLost(hr);
      }
    }
  } while (0);
  return ret;
}

bool DisplayImplFlipModeAlpha::Present() {
  not_ready_cnt_ = 0;
  HRESULT res = S_OK;
  auto do_present = [&res, this]() {
    if (swap_chain1_)
      res = swap_chain1_->Present(waitable_event_ ? 1 : 0, 0u);

    last_present_ts_ = mediasdk::GetCurrentTimeDelta();
  };
  ID3D11RenderTargetView* const rtv[D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT] = {
      NULL};
  device_.GetContext()->OMSetRenderTargets(
      D3D11_SIMULTANEOUS_RENDER_TARGET_COUNT, rtv, NULL);
  do_present();

  if (FAILED(res)) {
    device_.CheckDevLost(res);
    LOG(ERROR) << base::StringPrintf("Failed to Present(0x%s)",
                                     GetErrorString(res).c_str());
    return false;
  }
  return true;
}

Graphics& DisplayImplFlipModeAlpha::GetGraphics() {
  return *graphics_;
}

void DisplayImplFlipModeAlpha::Destroy() {
  if (graphics_) {
    graphics_->Destroy();
    graphics_ = nullptr;
  }
  if (waitable_event_) {
    CloseHandle(waitable_event_);
    waitable_event_ = nullptr;
  }
  if (swap_chain1_) {
    swap_chain1_.Reset();
  }
  if (visual_) {
    visual_.Reset();
  }
  if (target_) {
    target_.Reset();
  }
  if (dcompDevice_) {
    dcompDevice_.Reset();
  }
}

bool DisplayImplFlipModeAlpha::Resize(uint32_t cx, uint32_t cy) {
  if (!graphics_ || !swap_chain1_)
    return false;

  DXGI_SWAP_CHAIN_DESC1 pre = swap_chain_desc1_;
  graphics_->Destroy();
  HRESULT res = swap_chain1_->ResizeBuffers(
      kSwapCHainBufferCount, cx, cy, DXGI_FORMAT::DXGI_FORMAT_B8G8R8A8_UNORM,
      pre.Flags);
  if (FAILED(res)) {
    LOG(ERROR) << "Failed to ResizeBuffers [" << GetErrorString(res) << "]";
    device_.CheckDevLost(res);
    return false;
  }
  if (!graphics_->CreateWithSwapChain(swap_chain1_.Get())) {
    return false;
  }
  swap_chain1_->GetDesc1(&swap_chain_desc1_);

  return true;
}

DisplayImplFlipModeAlpha::~DisplayImplFlipModeAlpha() {
  Destroy();
}
}  // namespace graphics