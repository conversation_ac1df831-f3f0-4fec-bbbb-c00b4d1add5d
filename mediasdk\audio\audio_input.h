#pragma once

#include <memory>
#include <mutex>
#include <string>

#include "base/mediasdk/observer_list_thread_safe_weak.h"

#include "mediasdk/public/mediasdk_defines.h"
#include "mediasdk/public/mediasdk_defines_audio.h"
#include "mediasdk/public/plugin/audio_filter.h"

#include "audio/audio_dump_manager.h"
#include "audio/audio_format.h"
#include "audio/audio_frame.h"
#include "audio/audio_frame_observer.h"
#include "audio/audio_input_performance_calc.h"
#include "audio/audio_monitor_manager.h"
#include "audio/rms_calculator.h"
#include "audio_filter_chain.h"
#include "data_center/audio_performance_store.h"
#include "mediasdk/utils/audio/hook_api_audio_dump_helper.h"
#include "public/hook_api/hook_api.h"

namespace mediasdk {

class AudioVolume;
class AudioResample;
class AudioInputSource;
class AudioMixerInput;

class AudioInput : public AudioPerformanceStoreCalc {
 public:
  virtual ~AudioInput();

  // Source related abstract methods
  virtual void AttachSource(std::shared_ptr<AudioInputSource> source) = 0;
  virtual std::shared_ptr<AudioInputSource> DetachSource() = 0;
  virtual bool IsFromVisual() = 0;
  virtual std::string GetDevName() = 0;
  virtual std::string GetName() = 0;
  virtual std::shared_ptr<AudioInputSource> source() = 0;

  const std::string& GetId() const { return id_; }

  // AudioPerformanceStoreCalc:
  std::string GetAudioInputCalcId() override;
  MSAudioPerformance CalcPerformance() override;

  // for debug reason, return audio config info
  std::string ToString();

  AudioFormat GetOutputFormat() const { return output_format_; }

  // Interface for external input of audio data
  void InputAudioFrame(const AudioFormat& format, const AudioFrame& frame);

  void SetVolume(float);
  float GetVolume();
  void SetSyncOffset(int32_t);
  int32_t GetSyncOffset();
  void SetMonitorType(uint32_t monitor);
  uint32_t GetMonitorType();
  virtual void SetMute(bool);
  bool IsMute();
  void SetBalance(float);
  float GetBalance();
  void SetMono(bool);
  bool IsMono();
  void SetInterval(int32_t interval);

  void SetAudioInputParams(const AudioInputParams& param);
  void GetPerformance(MSAudioPerformance& performance);

  AudioFilterChain& GetAudioFilterChainRef() { return filter_chain_; }

  void AddObserver(std::shared_ptr<AudioInputFrameObserver> observer);
  void RemoveObserver(std::shared_ptr<AudioInputFrameObserver> observer);

  void ChangeRenderTargetTo(const std::string& id);

  void ComputePreAudioVolume(const AudioFormat& format,
                             const AudioFrame& frame);

 protected:
  virtual void OnSetMute(bool mute) {}

  virtual void OnSetVolume(float volume) {}

  void DumpAudioToFile(const std::string& path,
                       const AudioFormat& format,
                       const AudioFrame& frame);

  AudioInput(const std::string& id, const AudioFormat& output_format);

  void NotifyRawFrame(const AudioFormat& format, const AudioFrame& frames);
  void NotifyProcessedFrame(const AudioFormat& format,
                            const AudioFrame& frames);

  void OnApplyMono(AudioFrame& frame);
  void OnApplyVolume(AudioFrame& frame);
  void OnApplyBalance(AudioFrame& frame);
  void OnApplyMute(AudioFrame& frame);
  void OnApplyFilter(AudioFrame& frame);
  void ProcessAudioFrame(const AudioFormat& format, const AudioFrame& frame);

 private:
  void ProcessAudioFrameInternal(const AudioFormat& format,
                                 const AudioFrame& frame);
  void AudioPreProcess(AudioFrame& frame);
  void AudioConsume(AudioFrame& frame);
  bool ResampleFrameIfNeeded(const AudioFormat& input_format,
                             const AudioFrame& input_frame,
                             AudioFrame& output_frame);

 protected:
  const std::string id_;
  bool is_mono_ = false;
  float balance_ = 0.5f;
  bool is_mute_ = false;
  int32_t sync_offset_ = 0;
  float volume_ = 1.f;
  RMSCalculator rms_calculator_;
  AudioMonitorManager monitor_manager_;
  AudioFilterChain filter_chain_;
  AudioInputPerformanceCalc performance_calc_;
  std::shared_ptr<AudioResample> resample_;
  AudioFormat output_format_;
  base::ObserverListThreadSafeWeak<AudioInputFrameObserver> observer_list_;
  bool notify_format_ = false;
  
#ifdef ENABLE_AUDIO_DUMP
  std::unique_ptr<AudioDumpManager> audio_dump_manager_;
#endif  // ENABLE_AUDIO_DUMP

#if ENABLE_HOOK_API_AUDIO_DUMP
  std::unique_ptr<HookApiAudioDumpHelper> raw_audio_dump_helper_;
  std::unique_ptr<HookApiAudioDumpHelper> processed_audio_dump_helper_;
#endif  // ENABLE_HOOK_API_AUDIO_DUMP
};

}  // namespace mediasdk
