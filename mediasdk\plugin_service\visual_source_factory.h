#pragma once

#include <memory>
#include <string>
#include "base/native_library.h"
#include "mediasdk/public/mediasdk_defines.h"
#include "mediasdk/public/mediasdk_string.hpp"
#include "source_factory.h"

namespace mediasdk {

class VisualSource;
class VisualProxy;

class VisualSourceFactory : public SourceFactory {
 public:
  VisualSourceFactory(base::NativeLibrary lib,
                      const std::shared_ptr<PluginInfo>& info)
      : SourceFactory(lib, info) {}

  virtual ~VisualSourceFactory() = default;

  // SourceFactory:
  PluginType GetType() const override { return PluginType::kVisual; }

  virtual std::shared_ptr<VisualSource> CreateSource(
      VisualProxy* proxy,
      const std::string& json_params) = 0;

  virtual void Destroy(std::shared_ptr<VisualSource> source) = 0;

  virtual void DestroyAll() = 0;

  virtual MediaSDKString EnumVideoInput(const std::string& json) { return {}; }

  virtual MediaSDKString EnumFormat(const std::string& device_id,
                                    int32_t type) {
    return {};
  }

  virtual MediaSDKString GetVisualSourceProperty(const std::string& json) {
    return {};
  }

  virtual bool SetVisualSourceProperty(const std::string& key,
                                       const std::string& json) {
    return false;
  }
};

}  // namespace mediasdk
