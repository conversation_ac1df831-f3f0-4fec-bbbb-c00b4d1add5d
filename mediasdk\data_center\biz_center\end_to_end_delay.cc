#include "end_to_end_delay.h"
#include <algorithm>
#include "base/notreached.h"
#include "mediasdk/component_proxy.h"
#include "mediasdk/data_center/data_center.h"

namespace mediasdk {

EndtoEndDelay::EndtoEndDelay() {
  if (auto* dc = com::GetDataCenter(); dc) {
    dc->RegistEndtoEndDelay(this);
  }
}

EndtoEndDelay::~EndtoEndDelay() {
  if (auto* dc = com::GetDataCenter(); dc) {
    dc->UnregistEndtoEndDelay(this);
  }
}

}  // namespace mediasdk