#pragma once

#include <memory>
#include <string>
#include "mediasdk/public/plugin/audio_input_source.h"
#include "platform/win32/wasapi/wasapi_device.h"
#include "nlohmann/json.hpp"

namespace mediasdk {

// 前向声明
class DShowAudioOnlyCapture;

// 音频设备工厂类，根据设备类型创建对应的音频输入源
class AudioDeviceFactory {
 public:
  // 根据设备信息创建音频输入源
  static std::shared_ptr<AudioInputSource> CreateAudioInputSource(
      AudioInputProxy* proxy,
      const std::string& device_id,
      const nlohmann::json& json_params);

  // 检查设备是否为 DirectShow 设备
  static bool IsDirectShowDevice(const std::string& device_id);

  // 从设备 ID 获取设备类型
  static AudioDeviceType GetDeviceType(const std::string& device_id);

 private:
  // 创建 WASAPI 音频输入源
  static std::shared_ptr<AudioInputSource> CreateWASAPIAudioInputSource(
      AudioInputProxy* proxy,
      const nlohmann::json& json_params);

  // 创建 DirectShow 音频输入源
  static std::shared_ptr<AudioInputSource> CreateDirectShowAudioInputSource(
      AudioInputProxy* proxy,
      const std::string& device_id,
      const nlohmann::json& json_params);
};

// DirectShow 音频输入源实现
class DirectShowAudioInputSource : public AudioInputSource {
 public:
  explicit DirectShowAudioInputSource(AudioInputProxy* proxy);
  virtual ~DirectShowAudioInputSource();

  // 创建 DirectShow 音频捕获
  bool Create(const std::string& device_id, const nlohmann::json& json_params);
  void Destroy();

  // AudioInputSource 接口实现
  MediaSDKString GetProperty(const char* key) override;
  const char* GetAudioSourceName() override;
  bool Action(const char* json_params) override;
  void SetDeviceVolume(float volume) override;
  float GetDeviceVolume() override;
  void SetDeviceMute(bool mute) override;
  bool GetDeviceMute() override;
  void SetDeviceUsedQPC(bool used) override;
  bool GetDeviceUsedQPC() override;
  bool NeedSyn() override;

 private:
  AudioInputProxy* proxy_;
  std::string device_id_;
  bool is_created_;
  bool is_muted_;
  float volume_;

  // DirectShow 音频捕获实例
  std::unique_ptr<DShowAudioOnlyCapture> dshow_audio_capture_;
};

}  // namespace mediasdk
