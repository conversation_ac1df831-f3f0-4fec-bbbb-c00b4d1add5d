
#pragma once

#include <memory>
#include "mediasdk/public/plugin/audio_input_proxy.h"
#include "mediasdk/public/plugin/audio_input_source.h"
#include "mediasdk/public/plugin/plugin_defines.h"
#include "source_factory.h"

namespace mediasdk {

class AudioInputSourceFactory : public SourceFactory {
 public:
  AudioInputSourceFactory(base::NativeLibrary lib,
                          const std::shared_ptr<PluginInfo>& info)
      : SourceFactory(lib, info) {}

  virtual ~AudioInputSourceFactory() {}

  // SourceFactory:
  PluginType GetType() const override { return PluginType::kAudio; }

  virtual std::shared_ptr<AudioInputSource> CreateSource(
      std::shared_ptr<AudioInputProxy> proxy,
      const std::string& json_params) = 0;

  virtual void Destroy(std::shared_ptr<AudioInputSource> source) = 0;

  virtual void DestroyAll() = 0;

  virtual MediaSDKStringData EnumAudioInput() = 0;

  virtual MediaSDKStringData GetDefaultAudioInput() = 0;

  virtual MediaSDKStringData GetDefaultAudioOutput() = 0;

  virtual MediaSDKStringData EnumCaptureAudio() = 0;

  virtual MediaSDKStringData EnumRenderAudio() = 0;
};

}  // namespace mediasdk
