#pragma once

#include "graphics/device.h"
#include "graphics/texture.h"

extern "C" {
#include <libavutil/frame.h>
#include <libavutil/hwcontext.h>
#include <libavutil/hwcontext_d3d11va.h>
}

class FFD3D11VideoFrameToVideoTexture {
 public:
  void AVFrameCopyToD3D11Texture(graphics::Device& device,
                                 AVFrame* ff_frame,
                                 std::shared_ptr<graphics::Texture>& shared,
                                 Microsoft::WRL::ComPtr<ID3D11Texture2D>&
                                     shared_texture_from_decode_context) {
    if (!shared) {
      shared = CreateTexture2D(
          device, ff_frame->width, ff_frame->height, mediasdk::kPixelFormatNV12,
          graphics::Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE |
              graphics::Texture::TEXTURE_USAGE::TEXTURE_USAGE_KEYED_MUTEXED);
    }

    if (!ff_frame->hw_frames_ctx)
      return;

    {
      AVHWFramesContext* src_ctx =
          (AVHWFramesContext*)ff_frame->hw_frames_ctx->data;
      AVD3D11VADeviceContext* device_hwctx =
          (AVD3D11VADeviceContext*)(src_ctx->device_ctx->hwctx);

      ID3D11Texture2D* t_frame = nullptr;

      UINT t_index = 0;
      {
        t_frame = (ID3D11Texture2D*)ff_frame->data[0];
        AVPixelFormat format = (AVPixelFormat)ff_frame->format;
        int64_t index = (int64_t)ff_frame->data[1];
        t_index = UINT(index);
        if (!ff_decode_device_)
          t_frame->GetDevice(ff_decode_device_.GetAddressOf());
        if (!ff_decode_context_) {
          ff_decode_device_->GetImmediateContext(&ff_decode_context_);
        }
      }
      {
        Microsoft::WRL::ComPtr<IDXGIKeyedMutex> keyed;

        if (!shared_texture_from_decode_context) {
          HRESULT hr = ff_decode_device_->OpenSharedResource(
              shared->GetSharedHandle(), __uuidof(ID3D11Texture2D),
              &shared_texture_from_decode_context);
          if (FAILED(hr)) {
            return;
          }
        }
        if (shared_texture_from_decode_context) {
          shared_texture_from_decode_context.As(&keyed);
        }
        if (keyed) {
          keyed->AcquireSync(0, INFINITE);
        }
        {
          device_hwctx->lock(device_hwctx->lock_ctx);

          ff_decode_context_->CopySubresourceRegion(
              shared_texture_from_decode_context.Get(), 0, 0, 0, 0, t_frame,
              t_index, 0);
          ff_decode_context_->Flush();
          device_hwctx->unlock(device_hwctx->lock_ctx);
        }
        if (keyed) {
          keyed->ReleaseSync(0);
        }
      }
    }
  }

 private:
  Microsoft::WRL::ComPtr<ID3D11Device> ff_decode_device_;
  Microsoft::WRL::ComPtr<ID3D11DeviceContext> ff_decode_context_;
};