#pragma once

#include <stdint.h>
#include <string>
#include "mediasdk/public/plugin/plugin_export.h"

namespace mediasdk {

class StreamServiceProxy;

enum StreamServiceSourceProtocol {
  kStreamServiceSourceProtocolDefault = 0,
  kStreamServiceSourceProtocolRecord = 1,
  kStreamServiceSourceProtocolRtmpTcp = 2,
  kStreamServiceSourceProtocolRtmpTls = 3,
  kStreamServiceSourceProtocolRtmpQuic = 4
};

class StreamServiceSource {
 public:
  virtual ~StreamServiceSource() {}

  virtual bool NeedFlvPacket() = 0;

  // Obtain the time threshold for packet loss. When the time spent on the Write
  // process exceeds this threshold, the packet loss logic will be executed.
  // There are two packet loss strategies, B and P. Returning 0 means no packet
  // loss is required
  virtual uint32_t GetDropBThresholdMs() = 0;

  virtual uint32_t GetDropPThresholdMs() = 0;

  virtual MediaSDKString GetCurentCdnIp() = 0;

  virtual StreamServiceSourceProtocol GetProtocol() = 0;

  virtual StreamServiceSourceBandwidthInfo GetNetStatus() = 0;

  virtual int GetSocketConnectTime() = 0;

  virtual int GetDnsResolveTime() = 0;

  virtual const char* GetName() = 0;

  virtual bool IsRecord() = 0;
  // Success returns 0, other error codes are defined by specific service source
  virtual int Connect() = 0;

  virtual int Disconnect() = 0;

  virtual int WriteVideo(const uint8_t* data,
                         size_t len,
                         bool is_key_frame,
                         uint32_t dts_ms,
                         uint32_t pts_ms) = 0;

  virtual int WriteAudio(const uint8_t* data, size_t len, uint32_t pts_ms) = 0;

  virtual int WriteScript(const uint8_t* data, size_t len) = 0;
};

extern "C" PLUGIN_EXPORT StreamServiceSource* CreateStreamServiceSource(
    StreamServiceProxy* proxy,
    const char* json_params);

extern "C" PLUGIN_EXPORT void DestroyStreamServiceSource(
    StreamServiceSource* source);

}  // namespace mediasdk
