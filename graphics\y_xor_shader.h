#pragma once

#include <array>
#include <mutex>
#include "public/graphics_defines.h"
#include "shader.h"

namespace graphics {

class YXORShader : public Shader {
 public:
  static inline const char* SHADER_ID_STRING = "y_xor_shader";

  static std::shared_ptr<Shader> CreateYXORShader(const char* type) {
    if (strcmp(type, SHADER_ID_STRING) == 0) {
      return std::make_shared<YXORShader>();
    }
    return nullptr;
  }

  static void EnumPluginsFunction(void* param, EnumPluginCallBack fpn) {
    fpn(param,
        ShaderItem{SHADER_ID_STRING, "shader for draw texture to graphics",
                   YXORShader::CreateYXORShader});
  }

 public:
  bool Init(const std::shared_ptr<Device>&) override;
  void Render(ID3D11ShaderResourceView* views[kMaxVideoPlanes]);
  ~YXORShader() override;
  void Destroy() override;

 private:
  bool DoInit();

 protected:
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice();
  Microsoft::WRL::ComPtr<ID3D11DeviceContext> GetContext();

  bool try_init_ = false;
  bool init_suc_ = false;
  std::shared_ptr<Device> instance_;

  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_;
  Microsoft::WRL::ComPtr<ID3D11VertexShader> vs_;
};
}  // namespace graphics
