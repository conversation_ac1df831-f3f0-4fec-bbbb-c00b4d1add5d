
#include "source_factory.h"

#include "time_helper.h"

namespace mediasdk {

SourceFactory::SourceFactory(base::NativeLibrary lib,
                             const std::shared_ptr<PluginInfo>& info)
    : library_(lib),
      info_(info),
      init_event_(base::WaitableEvent::ResetPolicy::MANUAL,
                  base::WaitableEvent::InitialState::NOT_SIGNALED) {
  DCHECK(info_);

  name_ = info_ ? info_->name.ToString() : std::string();
  init_ts_ = milli_now();
}

bool SourceFactory::NeedAsyncGlobalInit(PluginGlobalProxy* proxy) {
  typedef bool (*PluginNeedAsyncGlobalInitFunc)(PluginGlobalProxy *
                                                global_proxy);

  PluginNeedAsyncGlobalInitFunc func =
      reinterpret_cast<PluginNeedAsyncGlobalInitFunc>(
          base::GetFunctionPointerFromNativeLibrary(
              library_, "PluginNeedAsyncGlobalInit"));

  if (!func) {
    return false;
  }

  return func(proxy);
}

bool SourceFactory::PluginGlobalInit(PluginGlobalProxy* proxy) {
  typedef bool (*PluginGlobalInitFunc)(PluginGlobalProxy*);

  PluginGlobalInitFunc func = reinterpret_cast<PluginGlobalInitFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_, "PluginGlobalInit"));

  if (!func) {
    // If the PluginGlobalInit function is not found, it means that no initial
    // action is required and returns success
    return true;
  }

  return func(proxy);
}

void SourceFactory::PluginGlobalUnInit() {
  typedef bool (*PluginGlobalUnInitFunc)();

  PluginGlobalUnInitFunc func = reinterpret_cast<PluginGlobalUnInitFunc>(
      base::GetFunctionPointerFromNativeLibrary(library_,
                                                "PluginGlobalUnInit"));

  if (func) {
    func();
  }

  // FIXME (lgd): The timing control of DLL exit needs to be more rigorous.
  // In order to reduce the crash rate during exit, it is temporarily closed.
  // We look forward to excellent experts solving the problem
  // base::UnloadNativeLibrary(library_);
}

void SourceFactory::SetInitialized(bool init_success) {
  init_success_ = init_success;
  init_event_.Signal();
}

bool SourceFactory::WaitInitializedSuccess() {
  init_event_.Wait();
  return init_success_;
}

}  // namespace mediasdk
