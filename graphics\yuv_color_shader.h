#pragma once

#include <mutex>
#include "shader.h"
using namespace DirectX;

namespace graphics {
class YUVColorShader : public Shader {
 public:
  struct VERTEXTYPE {
    DirectX::XMFLOAT3 position;
    DirectX::XMFLOAT4 color;  // yuv
  };

  static inline const char* SHADER_ID_STRING = "yuv_color_shader";

  static std::shared_ptr<Shader> CreateColorShader(const char* type) {
    if (strcmp(type, SHADER_ID_STRING) == 0) {
      return std::make_shared<YUVColorShader>();
    }
    return nullptr;
  }

  static void EnumPluginsFunction(void* param, EnumPluginCallBack fpn) {
    fpn(param,
        ShaderItem{SHADER_ID_STRING, "shader for draw texture to graphics",
                   YUVColorShader::CreateColorShader});
  }

 public:
  bool Init(const std::shared_ptr<Device>&) override;

  void Render(int32_t vertex, int32_t index);
  void Destroy() override;
  ~YUVColorShader() override;

 private:
  bool Init_(const std::shared_ptr<Device>& ins);

 protected:
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice_();
  ID3D11DeviceContext* GetContext_();

 private:
  bool try_init_ = false;
  bool init_suc_ = false;
  std::shared_ptr<Device> device_;

  Microsoft::WRL::ComPtr<ID3D11VertexShader> vs_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11InputLayout> layout_;
};
}  // namespace graphics
