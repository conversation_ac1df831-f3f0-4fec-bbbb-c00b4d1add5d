#include "plugin_manager.h"

#include <magic_enum.hpp>
#include "mediasdk/component_center.h"
#include "mediasdk/mediasdk_thread.h"
#include "mediasdk/plugin_service/audio_encoder_source_factory.h"
#include "mediasdk/plugin_service/audio_filter_factory.h"
#include "mediasdk/plugin_service/audio_input_source_factory_impl.h"
#include "mediasdk/plugin_service/stream_service_source_factory.h"
#include "mediasdk/plugin_service/video_encoder_source_factory.h"
#include "mediasdk/plugin_service/visual_filter_factory.h"
#include "mediasdk/plugin_service/visual_source_factory.h"
#include "notify_center.h"

namespace mediasdk {

PluginManager::PluginManager() {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  plugin_global_proxy_ = std::make_unique<PluginGlobalProxyImpl>();
}

PluginManager::~PluginManager() {
  DCHECK_CURRENTLY_ON(ThreadID::PLUGIN);

  UnLoadAll();
  plugin_global_proxy_.reset();
  loader_.reset();
}

void PluginManager::Load() {
  if (IsLoading()) {
    LOG(ERROR) << "Still loading";
    return;
  }

  UnLoadAll();

  const auto load_start_ts = milli_now();
  // `loader_` will be reset on `OnLoadFinished`
  loader_ = PluginLoader::Create(this);
  if (loader_) {
    loader_->Load();
  }

  auto load_cost_ms = milli_now() - load_start_ts;
  LOG(INFO) << "PluginManager::Load cost " << load_cost_ms;
}

void PluginManager::UnLoadAll() {
  for (auto& [type, factory_list] : factories_) {
    for (auto& factory : factory_list) {
      factory->PluginGlobalUnInit();
    }
  }
  factories_.clear();
}

void PluginManager::ReportLoadEvent() {
  if (loader_) {
    loader_->ReportLoadEvent();
  }
}

std::shared_ptr<VisualSourceFactory> PluginManager::GetVisualSourceFactory(
    const std::string& plugin_name) {
  return std::dynamic_pointer_cast<VisualSourceFactory>(
      FindAndWaitFactoryReady(PluginType::kVisual, plugin_name));
}

std::shared_ptr<AudioFilterFactory> PluginManager::GetAudioFilterFactory(
    const std::string& plugin_name) {
  return std::dynamic_pointer_cast<AudioFilterFactory>(
      FindAndWaitFactoryReady(PluginType::kAudioFilter, plugin_name));
}

std::shared_ptr<VisualFilterFactory> PluginManager::GetVisualFilterFactory(
    const std::string& plugin_name) {
  return std::dynamic_pointer_cast<VisualFilterFactory>(
      FindAndWaitFactoryReady(PluginType::kVisualFilter, plugin_name));
}

std::shared_ptr<AudioInputSourceFactory>
PluginManager::GetAudioInputSourceFactory(const std::string& plugin_name) {
  return std::dynamic_pointer_cast<AudioInputSourceFactory>(
      FindAndWaitFactoryReady(PluginType::kAudio, plugin_name));
}

std::shared_ptr<StreamServiceSourceFactory>
PluginManager::GetServiceSourceFactory(const std::string& plugin_name) {
  return std::dynamic_pointer_cast<StreamServiceSourceFactory>(
      FindAndWaitFactoryReady(PluginType::kService, plugin_name));
}

std::shared_ptr<VideoEncoderSourceFactory>
PluginManager::GetVideoEncoderSourceFactory(const std::string& plugin_name) {
  return std::dynamic_pointer_cast<VideoEncoderSourceFactory>(
      FindAndWaitFactoryReady(PluginType::kVideoEncoder, plugin_name));
}

std::shared_ptr<AudioEncoderSourceFactory>
PluginManager::GetAudioEncoderSourceFactory(const std::string& plugin_name) {
  return std::dynamic_pointer_cast<AudioEncoderSourceFactory>(
      FindAndWaitFactoryReady(PluginType::kAudioEncoder, plugin_name));
}

std::shared_ptr<PluginInfoArray> PluginManager::EnumSource(PluginType type) {
  std::vector<PluginInfo> info_list;

  auto it = factories_.find(type);
  if (it != factories_.end()) {
    for (const auto& factory : it->second) {
      if (factory) {
        // External plugin will not be enum
        if (factory->IsExternal()) {
          continue;
        }

        if (!factory->WaitInitializedSuccess()) {
          continue;
        }

        const auto info = factory->GetPluginInfo();
        if (info) {
          info_list.push_back(*(info));
        }
      }
    }
  }

  return std::make_shared<PluginInfoArray>(info_list);
}

void PluginManager::DestroyVisualSource(std::shared_ptr<VisualSource> source) {
  auto it = factories_.find(PluginType::kVisual);
  if (it != factories_.end()) {
    for (const auto& factory : it->second) {
      auto visual_factory =
          std::dynamic_pointer_cast<VisualSourceFactory>(factory);
      if (visual_factory) {
        visual_factory->Destroy(source);
      }
    }
  }
}

void PluginManager::DestroyAllVisualSource() {
  auto it = factories_.find(PluginType::kVisual);
  if (it != factories_.end()) {
    for (const auto& factory : it->second) {
      auto visual_factory =
          std::dynamic_pointer_cast<VisualSourceFactory>(factory);
      if (visual_factory) {
        visual_factory->DestroyAll();
      }
    }
  }
}

bool PluginManager::RegisterExternalVisualSourceFactory(
    const PluginInfo& info,
    std::shared_ptr<VisualSourceFactory> factory) {
  auto& factory_list = factories_[PluginType::kVisual];
  auto it = std::find_if(factory_list.begin(), factory_list.end(),
                         [&info](const std::shared_ptr<SourceFactory>& f) {
                           return f->GetName() == info.name.ToString();
                         });
  if (it != factory_list.end()) {
    if (factory) {
      LOG(ERROR) << "external visual source factory " << info.name.ToString()
                 << " already exists";
      return false;
    } else {
      LOG(INFO) << "Unregister external visual source factory "
                << info.name.ToString();
      factory_list.erase(it);
      return true;
    }
  }
  LOG(INFO) << "Register external visual source factory "
            << info.name.ToString();
  factory_list.push_back(factory);
  return true;
}

bool PluginManager::RegisterExternalAudioInputSourceFactory(
    const PluginInfo& info,
    std::shared_ptr<AudioInputSourceFactory> factory) {
  auto& factory_list = factories_[PluginType::kAudio];
  auto it = std::find_if(factory_list.begin(), factory_list.end(),
                         [&info](const std::shared_ptr<SourceFactory>& f) {
                           return f->GetName() == info.name.ToString();
                         });
  if (it != factory_list.end()) {
    if (factory) {
      LOG(ERROR) << "external audio input source factory "
                 << info.name.ToString() << " already exists";
      return false;
    } else {
      LOG(INFO) << "Unregister external audio input source factory "
                << info.name.ToString();
      factory_list.erase(it);
      return true;
    }
  }
  LOG(INFO) << "Register external audio input source factory "
            << info.name.ToString();
  factory_list.push_back(factory);
  return true;
}

void PluginManager::DestroyAudioFilter(std::shared_ptr<AudioFilter> filter) {
  auto it = factories_.find(PluginType::kAudioFilter);
  if (it != factories_.end()) {
    for (const auto& factory : it->second) {
      auto audio_filter_factory =
          std::dynamic_pointer_cast<AudioFilterFactory>(factory);
      if (audio_filter_factory) {
        audio_filter_factory->Destroy(filter);
      }
    }
  }
}

void PluginManager::DestroyAllAudioFilter() {
  auto it = factories_.find(PluginType::kAudioFilter);
  if (it != factories_.end()) {
    for (const auto& factory : it->second) {
      auto audio_filter_factory =
          std::dynamic_pointer_cast<AudioFilterFactory>(factory);
      if (audio_filter_factory) {
        audio_filter_factory->DestroyAll();
      }
    }
  }
}

void PluginManager::DestroyAudioInputSource(
    std::shared_ptr<AudioInputSource> source) {
  auto it = factories_.find(PluginType::kAudio);
  if (it != factories_.end()) {
    for (const auto& factory : it->second) {
      auto audio_input_factory =
          std::dynamic_pointer_cast<AudioInputSourceFactory>(factory);
      if (audio_input_factory) {
        audio_input_factory->Destroy(source);
      }
    }
  }
}

void PluginManager::DestroyAllAudioInputSource() {
  auto it = factories_.find(PluginType::kAudio);
  if (it != factories_.end()) {
    for (const auto& factory : it->second) {
      auto audio_input_factory =
          std::dynamic_pointer_cast<AudioInputSourceFactory>(factory);
      if (audio_input_factory) {
        audio_input_factory->DestroyAll();
      }
    }
  }
}

void PluginManager::DestroyStreamServiceSource(
    std::shared_ptr<StreamServiceSource> source) {
  auto it = factories_.find(PluginType::kService);
  if (it != factories_.end()) {
    for (const auto& factory : it->second) {
      auto stream_service_factory =
          std::dynamic_pointer_cast<StreamServiceSourceFactory>(factory);
      if (stream_service_factory) {
        stream_service_factory->Destroy(source);
      }
    }
  }
}

void PluginManager::DestroyAllStreamServiceSource() {
  auto it = factories_.find(PluginType::kService);
  if (it != factories_.end()) {
    for (const auto& factory : it->second) {
      auto stream_service_factory =
          std::dynamic_pointer_cast<StreamServiceSourceFactory>(factory);
      if (stream_service_factory) {
        stream_service_factory->DestroyAll();
      }
    }
  }
}

void PluginManager::OnPluginLoaded(std::shared_ptr<SourceFactory> factory) {
  if (!factory) {
    NOTREACHED();
    return;
  }

  factories_[factory->GetType()].push_back(factory);
}

void PluginManager::OnPluginInitializeResult(
    std::shared_ptr<SourceFactory> factory) {
  if (!factory) {
    NOTREACHED();
    return;
  }

  if (!factory->IsInitSuccess()) {
    // If initialization fails, remove this plugin from the list
    auto& factory_list = factories_[factory->GetType()];
    factory_list.erase(
        std::remove_if(factory_list.begin(), factory_list.end(),
                       [&](const std::shared_ptr<SourceFactory>& f) {
                         return f == factory;
                       }),
        factory_list.end());
  } else {
    if (auto info = (factory ? factory->GetPluginInfo() : nullptr)) {
      LOG(INFO) << "Success to init plugin: id(" << info->id.ToString()
                << "), type(" << info->type << "), name("
                << info->name.ToString() << "), desc(" << info->desc.ToString()
                << "), cost(" << milli_now() - factory->GetInitTs() << ")";
    }
  }
}

PluginGlobalProxy* PluginManager::GetPluginGlobalProxy() {
  DCHECK(plugin_global_proxy_);
  return plugin_global_proxy_.get();
}

void PluginManager::OnLoadFinished() {
  LOG(INFO) << "all plugin loaded";
  loader_.reset();
  // NotifyAllPluginLoaded();
}

void PluginManager::OnSubTypePluginsLoadFinished(PluginType type) {
  LOG(INFO) << "plugin of [" << magic_enum::enum_name(type) << "] all loaded";
  // NotifySubTypePluginLoaded(type);
}

std::shared_ptr<SourceFactory> PluginManager::FindAndWaitFactoryReady(
    PluginType type,
    const std::string& plugin_name) {
  auto it = factories_.find(type);
  if (it != factories_.end()) {
    for (const auto& factory : it->second) {
      if (factory && factory->GetName() == plugin_name) {
        // When using a factory, wait for the factory to complete loading
        if (factory->WaitInitializedSuccess()) {
          return factory;
        }
      }
    }
  }
  return nullptr;
}

}  // namespace mediasdk
