#pragma once

#include "audio_input.h"
#include "mediasdk/public/plugin/audio_input_proxy.h"
#include "mediasdk/public/plugin/audio_input_source.h"

namespace mediasdk {

class SourceAudioInput : public AudioInput, public AudioInputProxy {
 public:
  static std::shared_ptr<SourceAudioInput> Create(
      const std::string& id,
      const AudioFormat& output_format);

  virtual ~SourceAudioInput() override;

  // AudioInput:
  void AttachSource(std::shared_ptr<AudioInputSource> source) override;
  std::shared_ptr<AudioInputSource> DetachSource() override;
  bool IsFromVisual() override;
  std::string GetDevName() override;
  std::string GetName() override;
  std::shared_ptr<AudioInputSource> source() override;

 protected:
  // AudioInputProxy:
  void OnAudio(const AudioFormat& format,
               const AudioSourceFrame& input_source_frame) override;
  void SignalSourceEvent(const char* name) override;
  void ReportSourceEvent(const AudioSourceEvent& event) override;
  MediaSDKString GetInitParams() override;

 protected:
  SourceAudioInput(const std::string& id, const AudioFormat& output_format);

  std::shared_ptr<AudioInputSource> source_;
};

}  // namespace mediasdk