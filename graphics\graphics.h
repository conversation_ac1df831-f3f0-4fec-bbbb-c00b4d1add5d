#pragma once
#include <DirectXMath.h>
#include "chroma_key_params.h"
#include "color_params.h"
#include "device.h"
#include "lines.h"
#include "point_nine.h"
#include "rectangle.h"
#include "sharpness_params.h"
#include "texture.h"
#include "transform.h"
#include "yuv_rectangle.h"
#include "color_lut_params.h"
#include "luminance_map_params.h"
#include "partial_scale_params.h"

namespace graphics {

class GRAPHICS_EXPORT Graphics {
 public:
  virtual bool CreateNewY8Graphics(int cx, int cy) = 0;

  virtual bool CreateNewBGRAGraphics(int cx, int cy) = 0;

  virtual bool CreateNewSharedBGRAGraphics(int cx,
                                           int cy,
                                           D3D11_RESOURCE_MISC_FLAG flag) = 0;

  virtual bool CreateNewRGBAGraphics(int cx, int cy) = 0;

  virtual bool CreateGraphics(const D3D11_TEXTURE2D_DESC& desc,
                              const D3D11_RENDER_TARGET_VIEW_DESC* target) = 0;

  virtual bool CreateWithSwapChain(IDXGISwapChain* swapChain) = 0;

  virtual bool CreateGraphics(ID3D11Texture2D* texture,
                              const D3D11_RENDER_TARGET_VIEW_DESC* target) = 0;

  virtual bool CreateGraphics(Texture& texture) = 0;

  virtual bool CreateGraphicsFromTexture(
      Microsoft::WRL::ComPtr<ID3D11Texture2D> texture) = 0;

  virtual bool BeginDraw(bool clear = true,
                         DirectX::XMFLOAT2* size = nullptr,
                         const DirectX::XMFLOAT4& color =
                             DirectX::XMFLOAT4(0.0F, 0.0F, 0.0F, 0.0F)) = 0;

  virtual void DrawBGRATexture(Texture& texture,
                               const Transform& transform,
                               const bool tone_mapping = true) = 0;

  virtual void DrawBGRATextureForceAlpha(Texture& texture,
                                         const Transform& transform,
                                         bool force_tex_alpha) = 0;

  virtual bool DrawBGRATextureSampleFlags(Texture& texture,
                                          const Transform& transform,
                                          bool sample_anisotropic = false) = 0;

  virtual void DrawBGRATextureColorParams(Texture& texture,
                                          const Transform& transform,
                                          const ColorParams& color_params) = 0;

  virtual void DrawBGRATextureChromaKeyParams(
      Texture& texture,
      const Transform& transform,
      const ChromaKeyParams& chroma_key_params) = 0;

  virtual void DrawBGRATextureSharpnessParams(
      Texture& texture,
      const Transform& transform,
      const SharpnessParams& sharpness_params) = 0;

  virtual void DrawRGBATextureLuminanceMapParams(
      Texture& texture,
      const Transform& transform,
      const LuminanceMapParams& luminance_map_params) = 0;

  virtual void DrawBGRAPointNineTexture(Texture& texture,
                                        const Transform& transform,
                                        const PointNine& point_nine) = 0;

  virtual void DrawCornerTexture(Texture& texture,
                                 const XMFLOAT4& corner_radius,
                                 const float& ratio,
                                 const Transform& transform,
                                 bool refer_width,
                                 bool fixed_radius) = 0;

  virtual void DrawBorderTexture(Texture& texture,
                                 const XMFLOAT4& color,
                                 const XMFLOAT2& width,
                                 const float& ratio,
                                 const Transform& transform) = 0;

  virtual void DrawPartialScaleTexture(Texture& texture,
                                       const Transform& transform,
                                       const PartialScaleParams& params) = 0;

  virtual void DrawY8Texture(Texture& texture, const Transform& transform) = 0;

  virtual void DrawBGRAClipMask(const DirectX::XMFLOAT2& texture_size,
                                const Transform& transform,
                                const DirectX::XMFLOAT4& clip_mask) = 0;

  virtual void DrawLines(Lines& lines) = 0;

  virtual bool DrawLinesPrepare(Lines& lines) = 0;

  virtual void DrawLinesDraw(Lines& lines) = 0;

  virtual void DrawVLine(const XMFLOAT4& line,
                         const XMFLOAT4& color,
                         const float thinkness) = 0;

  virtual void DrawVDottedLine(const XMFLOAT4& line,
                               const XMFLOAT4& color,
                               const float thinkness) = 0;

  virtual void DrawHLine(const XMFLOAT4& line,
                         const XMFLOAT4& color,
                         const float thinkness) = 0;

  virtual void DrawHDottedLine(const XMFLOAT4& line,
                               const XMFLOAT4& color,
                               const float thinkness) = 0;

  virtual void DrawRectangle(Rectangle& rectangle) = 0;

  virtual bool DrawRectanglePrepare(Rectangle& rectangle) = 0;

  virtual bool DrawGradualRectanglePrepare(GradualRectangle& rectangle) = 0;

  virtual void DrawRectangleDraw(Rectangle& rectangle) = 0;

  virtual void DrawGradualRectangleDraw(GradualRectangle& rectangle) = 0;

  virtual bool DrawRectanglesPrepare(Rectangle** rectangle, int cnt) = 0;

  virtual void DrawRectanglesDraw(Rectangle** rectangle, int cnt) = 0;

  virtual void DrawRectangles(Rectangle** rectangle, int cnt) = 0;

  // Added for drawing rectangles with gradient
  virtual void DrawGradualRectangle(GradualRectangle& conf) = 0;

  virtual void DrawYUVColorRectangle(YUVRectangle& rectangle) = 0;

  virtual void DrawLutTexture(Texture& texture,
      Texture* texture_1dlut,
      Texture* texture_3dlut,
      const XMFLOAT4& domain_min,
      const XMFLOAT4& domain_max,
      const Transform& transform,
      float amount,
      bool pass_through_alpha) = 0;

  virtual void EndDraw() = 0;

  virtual DirectX::XMFLOAT2 GetSize() = 0;

  virtual bool IsEmpty() = 0;

  virtual std::shared_ptr<Texture> GetOutputTexture() = 0;

  virtual std::shared_ptr<Texture> MoveOutputTexture() = 0;

  virtual Device& GetDevice() = 0;

  virtual ~Graphics() = default;

  virtual void Destroy() = 0;
};

class GRAPHICS_EXPORT ScopedEndDraw {
 public:
  explicit ScopedEndDraw(Graphics& g) : g_(g) {}

  ~ScopedEndDraw() { g_.EndDraw(); }

  Graphics& g_;
};

GRAPHICS_EXPORT std::shared_ptr<Graphics> CreateGraphics2D(Device& ins);

}  // namespace graphics
