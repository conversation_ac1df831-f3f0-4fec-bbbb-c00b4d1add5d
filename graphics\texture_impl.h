#pragma once

#include <base/check.h>
#include <dxgi.h>
#include "device.h"
#include "texture.h"

namespace graphics {

class TextureImpl : public Texture {
 public:
  static std::shared_ptr<TextureImpl> CreateTexture2D(Device& inst,
                                                      HANDLE handle,
                                                      uint64_t usage);
  static std::shared_ptr<TextureImpl> CreateTexture2D(
      Device& inst,
      ID3D11Texture2D* texture,
      ID3D11ShaderResourceView* view,
      uint64_t usage);
  static std::shared_ptr<TextureImpl> CreateTexture2D(Device& inst,
                                                      ID3D11Texture2D* texture,
                                                      uint64_t usage);

  static std::shared_ptr<TextureImpl> CreateTexture2D(
      Device& inst,
      int cx,
      int cy,
      DXGI_FORMAT format,
      uint64_t usage);  // for GPU Only

  // create texture and initialize from CPU frame buffer
  static std::shared_ptr<TextureImpl> CreateTexture2D(Devi<PERSON>& inst,
                                                      const TextureFrame& frame,
                                                      uint64_t usage);

  // create texture from file by WIC
  static std::shared_ptr<TextureImpl> CreateTexture2D(Device& inst,
                                                      const wchar_t* file,
                                                      uint64_t usage);

  // create texture from file memory by WIC
  static std::shared_ptr<TextureImpl> CreateTexture2D(
      Device& inst,
      const uint8_t* file_mem_data,
      int32_t file_mem_data_size,
      uint64_t usage);

  static std::shared_ptr<TextureImpl> AdapterWICResourceToTexture(
      Device& inst,
      ID3D11Resource* texture,
      uint64_t usage);

  // create 3Dtexture from buffer
  static std::shared_ptr<graphics::TextureImpl> CreateTexture2D(
      Device& inst,
      const uint8_t* buffer,
      int width,
      int height,
      DXGI_FORMAT format,
      uint64_t usage);

  // create 3Dtexture from buffer
  static std::shared_ptr<graphics::TextureImpl> CreateTexture3D(
      Device& inst,
      const uint8_t* buffer,
      int width,
      int height,
      int depth,
      DXGI_FORMAT format,
      uint64_t usage);

 public:
  TextureImpl(Device& ins);

  Device& GetDevice() const override { return device_; }

  D3D11_TEXTURE2D_DESC GetDesc() const override;

  ID3D11ShaderResourceView* GetSRV(int plane = 0) const override;
  ID3D11Texture2D* GetTexture() const override;
  bool SetupVertex() override;
  HRESULT BuildFromTextureView(ID3D11Texture2D* texture,
                               ID3D11ShaderResourceView* view,
                               uint64_t usage) override;
  HRESULT BuildFromTexture(ID3D11Texture2D* texture, uint64_t usage) override;
  DirectX::XMFLOAT2 GetSize() const override;

  bool IsEmpty() const override;

  bool Map(D3D11_MAPPED_SUBRESOURCE& desc,
           bool readOnly,
           unsigned int flag = 0) override;

  bool NeedUnMap() const override;

  void UnMap() override;

  bool CopyFrom(uint8_t* data, int32_t line_size, int cy) override;
  bool CopyTo(uint8_t* data, int32_t line_size, int cy) override;
  bool CopyTo(ID3D11Texture2D* texture) const override;
  bool CopyTextureFrom(ID3D11Texture2D* texture) override;
  bool CopySubFrom(ID3D11Texture2D* texture,
                   const DirectX::XMFLOAT2& start,
                   const DirectX::XMFLOAT2& end) override;
  bool CopySubTo(ID3D11Texture2D* texture,
                 const DirectX::XMFLOAT2& start,
                 DirectX::XMFLOAT2& end) override;

  bool IsKeyedMutex() const override;

  void ReleaseKeyedAccess(uint64_t key = 0) override;

  bool AcquireKeyedAccess(uint64_t key = 0, uint32_t ms = 0) override;
  bool GetDC(bool discard, void** hdc) override;
  bool ReleaseDC() override;
  // utf8 path
  bool SaveAS(const char* path) const override;
  std::shared_ptr<Texture> GenerateReadTexture() const override;
  std::shared_ptr<Texture> CopyTexture() override;

  HANDLE GetSharedHandle() const override;
  void Destroy() override;
  uint64_t GetUsage() const override;
  void SetDebugName(const char* name) override;

  TONEMAP_TYPE GetToneMapType() const override { return tonemap_type_; }

  float GetToneMapParam() const override { return tonemap_param_; }

  void SetToneMapType(const TONEMAP_TYPE& type) override {
    tonemap_type_ = type;
  }

  void SetToneMapParam(const float param) override { tonemap_param_ = param; }

  ~TextureImpl() override;

 private:
  Microsoft::WRL::ComPtr<ID3D11DeviceContext> GetContext() const;

  HRESULT BuildFromSharedHandle(HANDLE handle, uint64_t usage);

  HRESULT BuildFromDesc(int cx, int cy, DXGI_FORMAT format, uint64_t usage);

  HRESULT BuildFromFrameBuffer(const TextureFrame& frame, uint64_t usage);
  HRESULT BuildResourceView();

  HRESULT BuildTextureFromBuffer(const uint8_t* buffer,
                                 int width,
                                 int height,
                                 DXGI_FORMAT format,
                                 uint64_t usage);

  HRESULT Build3DTextureFromBuffer(const uint8_t* buffer,
                                   int width,
                                   int height,
                                   int depth,
                                   DXGI_FORMAT format,
                                   uint64_t usage);

  HRESULT Build3DResourceView();

 private:
  D3D11_TEXTURE2D_DESC desc_ = {};
  D3D11_TEXTURE3D_DESC desc3d_ = {};

  HANDLE shared_handle_ = NULL;
  Microsoft::WRL::ComPtr<ID3D11Texture2D> texture_;
  Microsoft::WRL::ComPtr<ID3D11Texture3D> texture3d_;

  Microsoft::WRL::ComPtr<IDXGISurface1> texture_for_HDC_;
  Microsoft::WRL::ComPtr<ID3D11ShaderResourceView>
      resource_views_[kMaxVideoPlanes] = {};
  Device& device_;
  uint64_t usage_;
  std::string name_;
  TONEMAP_TYPE tonemap_type_ = ACES;
  float tonemap_param_ = 0.25;

  bool need_un_map_ = false;
};

inline DXGI_FORMAT PixelFormatToDXGIFormat(mediasdk::PixelFormat format) {
  switch (format) {
    case mediasdk::kPixelFormatUnspecified:
      break;
    case mediasdk::kPixelFormatBGRA:
      return DXGI_FORMAT::DXGI_FORMAT_B8G8R8A8_UNORM;
      break;
    case mediasdk::kPixelFormatNV12:
      return DXGI_FORMAT::DXGI_FORMAT_NV12;
      break;
    case mediasdk::kPixelFormatY8:
      return DXGI_FORMAT::DXGI_FORMAT_R8_UNORM;
      break;
    case mediasdk::kPixelFormatU8V8:
      return DXGI_FORMAT::DXGI_FORMAT_R8G8_UNORM;
      break;
    case mediasdk::kPixelFormatRGBA:
      return DXGI_FORMAT::DXGI_FORMAT_R8G8B8A8_UNORM;
      break;
    case mediasdk::kPixelFormatRGBA16:
      return DXGI_FORMAT::DXGI_FORMAT_R16G16B16A16_FLOAT;
      break;
    default:
      break;
  }
  DCHECK(false);
  return DXGI_FORMAT::DXGI_FORMAT_UNKNOWN;
}

inline mediasdk::PixelFormat DXGIFormatToPixelFormat(DXGI_FORMAT format) {
  switch (format) {
    case DXGI_FORMAT_R8G8B8A8_UNORM:
      return mediasdk::PixelFormat::kPixelFormatRGBA;
    case DXGI_FORMAT_B8G8R8A8_UNORM:
      return mediasdk::PixelFormat::kPixelFormatBGRA;
      break;
    case DXGI_FORMAT_NV12:
      return mediasdk::PixelFormat::kPixelFormatNV12;
      break;
    case DXGI_FORMAT_R8_UNORM:
      return mediasdk::PixelFormat::kPixelFormatY8;
    default:
      break;
  }
  DCHECK(false);
  return mediasdk::PixelFormat::kPixelFormatUnspecified;
}

}  // namespace graphics
