#pragma once

#include "mediasdk_defines.h"

namespace mediasdk {

class MediaSDKRTCEventObserver {
 public:
  virtual void OnEngineStart(int error_code) = 0;

  virtual void OnEngineStop() = 0;

  virtual void OnJoinChannel(MediaSDKString room_id,
                             MediaSDKString user_id,
                             int state,
                             MediaSDKString extra_info) = 0;

  virtual void OnLeaveChannel() = 0;

  virtual void OnUserJoined(MediaSDKString user_id, int elapsed) = 0;

  virtual void OnUserLeave(MediaSDKString user_id, int reason) = 0;

  virtual void OnLocalStreamStats(MediaSDKString stats) = 0;

  virtual void OnRemoteStreamStats(MediaSDKString user_id,
                                   MediaSDKString stats) = 0;

  virtual void OnUserPublishStream(MediaSDKString user_id,
                                   int stream_index,
                                   bool is_screen,
                                   int media_stream_type) = 0;

  virtual void OnUserUnpublishStream(MediaSDKString user_id,
                                     int stream_index,
                                     int media_stream_type,
                                     int reason) = 0;

  virtual void OnNetworkQuality(MediaSDKString quality) = 0;

  virtual void OnConnectionStateChanged(int state) = 0;

  virtual void OnWarning(int warn) = 0;

  virtual void OnError(int error) = 0;

  virtual void OnForwardStreamStateChanged(
      MediaSDKString stream_state_infos) = 0;

  virtual void OnLocalAudioPropertiesReport(
      MediaSDKString audio_properties_infos) = 0;

  virtual void OnRemoteAudioPropertiesReport(
      MediaSDKString audio_properties_infos) = 0;

  virtual void OnActiveSpeaker(MediaSDKString room_id, MediaSDKString uid) = 0;

  virtual void OnAudioDeviceStateChanged(MediaSDKString state_info) = 0;

  virtual void OnFirstRemoteAudioFrame(MediaSDKString user_id,
                                       int stream_index) = 0;

  virtual void OnFirstRemoteVideoFrameDecoded(MediaSDKString user_id,
                                              int stream_index,
                                              MediaSDKString frame_info) = 0;

  virtual void OnFirstRemoteVideoFrameRendered(MediaSDKString user_id,
                                               int stream_index,
                                               MediaSDKString frame_info) = 0;

  virtual void OnRemoteVideoSizeChanged(MediaSDKString user_id,
                                        int stream_index,
                                        MediaSDKString frame_info) = 0;

  virtual void OnSEIMessageReceived(MediaSDKString room_id,
                                    MediaSDKString user_id,
                                    int stream_index,
                                    MediaSDKString message) = 0;

  virtual void OnRoomMessageReceived(MediaSDKString user_id,
                                     MediaSDKString message) = 0;

  virtual void OnUserMessageReceived(MediaSDKString user_id,
                                     MediaSDKString message) = 0;

  virtual void OnStreamMixingEvent(MediaSDKString task_id,
                                   int event_type,
                                   int error,
                                   int mixed_stream_type) = 0;
};

}  // namespace mediasdk
