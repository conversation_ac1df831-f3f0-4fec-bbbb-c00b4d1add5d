#pragma once
#include "media/video/r_g_b_a.h"
#include "texture_color_convert_matrix_builder.h"

#include <base/check.h>
#include "media/video/video_frame.h"
#include "media/video/ycbcr.h"

namespace mediasdk {

inline y_Cb_Cr_alpha readYCbCrFromVideoFrame(const mediasdk::VideoFrame& in,
                                             int cx,
                                             int cy) {
  y_Cb_Cr_alpha ret = {};
  const auto& data = in.GetData();

  switch (in.GetFormat()) {
    case mediasdk::kPixelFormatNV12: {
      // yyyyyyyy
      // uvuv
      int line_size = in.GetWidth();
      ret.y = data[0][cy * line_size + cx];
      ret.Cb = data[1][cy / 2 * line_size + cx / 2 * 2];
      ret.Cr = data[1][cy / 2 * line_size + cx / 2 * 2 + 1];
      ret.alpha = 255;
      break;
    }
    case mediasdk::kPixelFormatI420: {
      int line_size = in.GetWidth();
      ret.y = data[0][cy * line_size + cx];
      ret.Cb = data[1][cy / 4 * line_size + cx / 2];
      ret.Cr = data[2][cy / 4 * line_size + cx / 2];
      ret.alpha = 255;
      break;
    }
    case mediasdk::kPixelFormatI444: {
      int line_size = in.GetWidth();
      ret.y = data[0][cy * line_size + cx];
      ret.Cb = data[1][cy * line_size + cx];
      ret.Cr = data[2][cy * line_size + cx];
      ret.alpha = 255;
      break;
    }
    case mediasdk::kPixelFormatI422: {
      int line_size = in.GetWidth();
      ret.y = data[0][cy * line_size + cx];
      ret.Cb = data[1][cy / 2 * line_size + cx / 2];
      ret.Cr = data[2][cy / 2 * line_size + cx / 2];
      ret.alpha = 255;
      break;
    }
    case mediasdk::kPixelFormatI440: {
      break;
    }
    case mediasdk::kPixelFormatI410: {
      break;
    }
    case mediasdk::kPixelFormatI411: {
      int line_size = (in.GetWidth() + 4) / 4 * 4 * cy + cx / 4 * 4;
      int left = cx % 4;
      switch (left) {
        case 0:
          ret.y = data[0][line_size + 0];

          ret.Cr = data[2][cy / 4 * line_size + cx / 2];
          break;
        case 1:
          ret.y = data[0][line_size + 2];
          break;
        case 2:
          ret.y = data[0][line_size + 3];
          break;
        case 3:
          ret.y = data[0][line_size + 5];
          break;
        default:
          break;
      }
      ret.Cb = data[0][line_size + 1];
      ret.Cr = data[0][line_size + 4];
      ret.alpha = 255;
      break;
    }
    case mediasdk::kPixelFormatI400: {
      break;
    }
    case mediasdk::kPixelFormatYV12: {
      int line_size = in.GetWidth();
      ret.y = data[0][cy * line_size + cx];
      ret.Cr = data[1][cy / 4 * line_size + cx / 2];
      ret.Cb = data[2][cy / 4 * line_size + cx / 2];
      ret.alpha = 255;
      break;
    }
    case mediasdk::kPixelFormatNV21: {
      int line_size = in.GetWidth();
      ret.y = data[0][cy * line_size + cx];
      ret.Cr = data[1][cy / 2 * line_size + cx / 2 * 2];
      ret.Cb = data[1][cy / 2 * line_size + cx / 2 * 2 + 1];
      ret.alpha = 255;
      break;
    }
    case mediasdk::kPixelFormatUYVY: {
      int line_size = in.GetWidth();
      int begin = cy * line_size * 2 + cx * 2 + 1;

      ret.y = data[0][begin];
      ret.Cb = data[0][cy * line_size * 2 + cx / 2 * 4 + 0];
      ret.Cr = data[0][cy * line_size * 2 + cx / 2 * 4 + 2];
      ret.alpha = 255;
      break;
    }
    case mediasdk::kPixelFormatYUY2: {
      int line_size = in.GetWidth();
      int begin = cy * line_size * 2 + cx * 2;

      ret.y = data[0][begin];
      ret.Cb = data[0][cy * line_size * 2 + cx / 2 * 4 + 1];
      ret.Cr = data[0][cy * line_size * 2 + cx / 2 * 4 + 3];
      ret.alpha = 255;
      break;
    }
    case mediasdk::kPixelFormatARGB: {
      break;
    }
    case mediasdk::kPixelFormatXRGB: {
      break;
    }
    case mediasdk::kPixelFormatRGB24: {
      break;
    }
    case mediasdk::kPixelFormatRGBA: {
      break;
    }
    case mediasdk::kPixelFormatBGR24: {
      break;
    }
    case mediasdk::kPixelFormatBGRA: {
      break;
    }
    case mediasdk::kPixelFormatMJPEG: {
      DCHECK(false);
      return ret;
    }
    case mediasdk::kPixelFormatI444A: {
      break;
    }
    case mediasdk::kPixelFormatI420A: {
      break;
    }
    case mediasdk::kPixelFormatI422A: {
      break;
    }
    case mediasdk::kPixelFormatYVYU: {
      int line_size = in.GetWidth();
      int begin = cy * line_size * 2 + cx * 2;

      ret.y = data[0][begin];
      ret.Cr = data[0][cy * line_size * 2 + cx / 2 * 4 + 1];
      ret.Cb = data[0][cy * line_size * 2 + cx / 2 * 4 + 3];
      ret.alpha = 255;
      break;
    }
    case mediasdk::kPixelFormatY8: {
      break;
    }
    default:
      break;
  }
  return ret;
};

inline r_g_b_a ConvertYCbCrToRGBA(y_Cb_Cr_alpha yuva,
                                  const XMFLOAT4& Y,
                                  const XMFLOAT4& U,
                                  const XMFLOAT4& V,
                                  const XMFLOAT4& min_v,
                                  const XMFLOAT4& max_v) {
  // limit value range
  float y = yuva.y / 255.f;
  float Cb = yuva.Cb / 255.f;
  float Cr = yuva.Cr / 255.f;

  y = y < min_v.x ? min_v.x : y;
  y = y > max_v.x ? max_v.x : y;

  Cb = Cb < min_v.y ? min_v.y : Cb;
  Cb = Cb > max_v.y ? max_v.y : Cb;

  Cr = Cr < min_v.z ? min_v.z : Cr;
  Cr = Cr > max_v.z ? max_v.z : Cr;

  auto float4_to_float3 = [](const XMFLOAT4& in) {
    FXMVECTOR ret = {in.x, in.y, in.z};
    return ret;
  };

  float b = y * Y.x + Cb * Y.y + Cr * Y.z + Y.w;
  float g = y * U.x + Cb * U.y + Cr * U.z + U.w;
  float r = y * V.x + Cb * V.y + Cr * V.z + V.w;

  auto clamp_to_0_255 = [](float v) {
    int32_t ret = v * 255.f;
    if (ret < 0) {
      return 0;
    } else if (ret > 255) {
      return 255;
    }
    return ret;
  };
  r_g_b_a ret = {};

  ret.r = clamp_to_0_255(r);
  ret.g = clamp_to_0_255(g);
  ret.b = clamp_to_0_255(b);
  ret.a = 255;
  return ret;
}

inline void YUVToBGRAConvert(const mediasdk::VideoFrame& in,
                             mediasdk::VideoFrame& out) {
  DCHECK(in.GetCS() != mediasdk::ColorSpace::kColorSpaceUnspecified &&
         in.GetVR() != mediasdk::VideoRange::kVideoRangeUnspecified && in.GetWidth() > 0 &&
         in.GetHeight() > 0);
  // 1. build matrix
  // 2. get y Cb Cr alpha
  // 3. get rgb from y CbCr with(cx,cy)

  XMFLOAT4 Y;
  XMFLOAT4 U;
  XMFLOAT4 V;
  XMFLOAT4 min_v;
  XMFLOAT4 max_v;
  BuildPlanesYUVToBGRA(in.GetCS(), in.GetVR(), Y, U, V, min_v, max_v,
                       in.GetFormat());
  uint8_t* data = out.GetPlaneData(0);
  for (int cy = 0; cy < in.GetHeight(); cy++) {
    for (int cx = 0; cx < in.GetWidth(); cx++) {
      auto yCbCrAlpha = readYCbCrFromVideoFrame(in, cx, cy);

      int32_t begin = cy * in.GetWidth() * 4 + cx * 4;
      r_g_b_a rgba = ConvertYCbCrToRGBA(yCbCrAlpha, Y, U, V, min_v, max_v);
      data[begin] = rgba.r;
      data[++begin] = rgba.g;
      data[++begin] = rgba.b;
      data[++begin] = rgba.a;
    }
  }
}

}  // namespace media
