#include "dshow_helper.h"
#include "base/logging.h"
#include "base/strings/utf_string_conversions.h"
#include <iostream>

namespace mediasdk {

// 测试DirectShow设备匹配的实际行为
void TestDirectShowDeviceMatching() {
  LOG(INFO) << "=== 测试DirectShow设备匹配实际行为 ===";
  
  // 模拟真实的C922 Pro Stream Webcam设备信息
  DShowDeviceName c922_video;
  c922_video.id = L"\\\\?\\usb#vid_046d&pid_085c&mi_00#6&348515e9&0&0000#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\\global";
  c922_video.name = L"C922 Pro Stream Webcam";
  
  DShowDeviceName c922_audio;
  c922_audio.id = L"麦克风 (6- C922 Pro Stream Webcam)";
  c922_audio.name = L"麦克风 (6- C922 Pro Stream Webcam)";
  
  LOG(INFO) << "视频设备:";
  LOG(INFO) << "  ID: " << base::WideToUTF8(c922_video.id);
  LOG(INFO) << "  Name: " << base::WideToUTF8(c922_video.name);
  
  LOG(INFO) << "音频设备:";
  LOG(INFO) << "  ID: " << base::WideToUTF8(c922_audio.id);
  LOG(INFO) << "  Name: " << base::WideToUTF8(c922_audio.name);
  
  // 测试各种匹配策略
  std::vector<DeviceMatchStrategy> strategies = {
    DeviceMatchStrategy::EXACT_ID_MATCH,
    DeviceMatchStrategy::USB_PATH_MATCH,
    DeviceMatchStrategy::FUZZY_NAME_MATCH,
    DeviceMatchStrategy::NAME_PATTERN_MATCH
  };
  
  for (const auto& strategy : strategies) {
    float confidence = CalculateDeviceMatchConfidence(c922_video, c922_audio, strategy);
    
    std::string strategy_name;
    switch (strategy) {
      case DeviceMatchStrategy::EXACT_ID_MATCH:
        strategy_name = "EXACT_ID_MATCH (重新定义)";
        break;
      case DeviceMatchStrategy::USB_PATH_MATCH:
        strategy_name = "USB_PATH_MATCH";
        break;
      case DeviceMatchStrategy::FUZZY_NAME_MATCH:
        strategy_name = "FUZZY_NAME_MATCH";
        break;
      case DeviceMatchStrategy::NAME_PATTERN_MATCH:
        strategy_name = "NAME_PATTERN_MATCH";
        break;
    }
    
    LOG(INFO) << strategy_name << ": 置信度 = " << confidence;
    
    if (confidence > 0.5f) {
      LOG(INFO) << "  ✓ 匹配成功";
    } else {
      LOG(INFO) << "  ✗ 匹配失败";
    }
  }
}

// 测试USB接口检测
void TestUSBInterfaceDetection() {
  LOG(INFO) << "\n=== 测试USB接口检测 ===";
  
  // 测试连续接口的情况
  std::wstring video_id = L"\\\\?\\usb#vid_046d&pid_085c&mi_00#6&348515e9&0&0000#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\\global";
  std::wstring audio_id = L"\\\\?\\usb#vid_046d&pid_085c&mi_01#6&348515e9&0&0001#{6994ad04-93ef-11d0-a3cc-00a0c9223196}\\global";
  
  LOG(INFO) << "测试连续USB接口:";
  LOG(INFO) << "  Video ID: " << base::WideToUTF8(video_id);
  LOG(INFO) << "  Audio ID: " << base::WideToUTF8(audio_id);
  
  std::wstring video_usb = ExtractUSBPath(video_id);
  std::wstring audio_usb = ExtractUSBPath(audio_id);
  
  LOG(INFO) << "  Video USB Path: " << base::WideToUTF8(video_usb);
  LOG(INFO) << "  Audio USB Path: " << base::WideToUTF8(audio_usb);
  
  bool same_device = IsSameUSBDevice(video_usb, audio_usb);
  bool consecutive = IsConsecutiveUSBInterface(video_id, audio_id);
  
  LOG(INFO) << "  Same USB Device: " << (same_device ? "是" : "否");
  LOG(INFO) << "  Consecutive Interfaces: " << (consecutive ? "是" : "否");
  
  // 测试非连续接口的情况
  std::wstring audio_id2 = L"\\\\?\\usb#vid_046d&pid_085c&mi_03#6&348515e9&0&0003#{6994ad04-93ef-11d0-a3cc-00a0c9223196}\\global";
  
  LOG(INFO) << "\n测试非连续USB接口:";
  bool consecutive2 = IsConsecutiveUSBInterface(video_id, audio_id2);
  LOG(INFO) << "  mi_00 vs mi_03 Consecutive: " << (consecutive2 ? "是" : "否");
}

// 测试完整的音频格式枚举流程
void TestCompleteAudioFormatEnumeration() {
  LOG(INFO) << "\n=== 测试完整的音频格式枚举流程 ===";
  
  // 模拟不同类型的设备
  std::vector<std::pair<std::string, DShowDeviceName>> test_cases = {
    {
      "C922一体化设备",
      {L"C922 Pro Stream Webcam", L"\\\\?\\usb#vid_046d&pid_085c&mi_00#6&348515e9&0&0000#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\\global"}
    },
    {
      "Elgato采集卡",
      {L"Elgato Game Capture HD60 S", L"\\\\?\\usb#vid_0fd9&pid_0063&mi_00#7&1234567&0&0000#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\\global"}
    },
    {
      "AVerMedia采集卡",
      {L"AVerMedia Live Gamer Portable 2 Plus", L"\\\\?\\usb#vid_07ca&pid_0337&mi_00#8&abcdefg&0&0000#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\\global"}
    }
  };
  
  for (const auto& test_case : test_cases) {
    LOG(INFO) << "\n--- 测试设备: " << test_case.first << " ---";
    
    ExtendedAudioFormatResult result;
    if (GetAudioFormatsForVideoDevice(test_case.second, result)) {
      LOG(INFO) << "✓ 找到音频格式信息";
      
      if (result.has_integrated_audio) {
        LOG(INFO) << "  设备类型: 传统一体化设备（相同设备ID）";
      } else {
        LOG(INFO) << "  设备类型: 关联设备";
        LOG(INFO) << "  找到 " << result.audio_results.size() << " 个关联音频设备";
        
        for (const auto& audio_result : result.audio_results) {
          LOG(INFO) << "  音频设备: " << base::WideToUTF8(audio_result.audio_device.name);
          LOG(INFO) << "  置信度: " << audio_result.confidence_score;
          LOG(INFO) << "  匹配策略: " << static_cast<int>(audio_result.match_strategy);
          LOG(INFO) << "  匹配详情: " << audio_result.match_details;
        }
      }
    } else {
      LOG(INFO) << "✗ 未找到音频格式信息";
      LOG(INFO) << "  错误: " << result.error_message;
      
      if (!result.suggestions.empty()) {
        LOG(INFO) << "  建议:";
        for (const auto& suggestion : result.suggestions) {
          LOG(INFO) << "    - " << suggestion;
        }
      }
    }
  }
}

// 测试策略优先级
void TestStrategyPriority() {
  LOG(INFO) << "\n=== 测试策略优先级 ===";
  
  DShowDeviceName video_device;
  video_device.id = L"\\\\?\\usb#vid_046d&pid_085c&mi_00#6&348515e9&0&0000#{65e8773d-8f56-11d0-a3b9-00a0c9223196}\\global";
  video_device.name = L"C922 Pro Stream Webcam";
  
  // 模拟多个可能的音频设备
  std::vector<DShowDeviceName> candidate_audio_devices = {
    {L"麦克风 (6- C922 Pro Stream Webcam)", L"麦克风 (6- C922 Pro Stream Webcam)"},
    {L"C922 Audio Device", L"\\\\?\\usb#vid_046d&pid_085c&mi_01#6&348515e9&0&0001#{6994ad04-93ef-11d0-a3cc-00a0c9223196}\\global"},
    {L"USB Audio Device", L"\\\\?\\usb#vid_1234&pid_5678&mi_01#9&abcdefg&0&0001#{6994ad04-93ef-11d0-a3cc-00a0c9223196}\\global"},
    {L"Realtek Audio", L"HDAUDIO\\FUNC_01&VEN_10EC&DEV_0295&SUBSYS_17AA3136&REV_1000\\4&1234567&0&0001"}
  };
  
  LOG(INFO) << "视频设备: " << base::WideToUTF8(video_device.name);
  LOG(INFO) << "候选音频设备匹配结果:";
  
  std::vector<DeviceMatchStrategy> strategies = {
    DeviceMatchStrategy::EXACT_ID_MATCH,
    DeviceMatchStrategy::FUZZY_NAME_MATCH,
    DeviceMatchStrategy::USB_PATH_MATCH,
    DeviceMatchStrategy::NAME_PATTERN_MATCH
  };
  
  for (size_t i = 0; i < candidate_audio_devices.size(); ++i) {
    const auto& audio_device = candidate_audio_devices[i];
    LOG(INFO) << "\n  候选设备 " << (i + 1) << ": " << base::WideToUTF8(audio_device.name);
    
    float best_confidence = 0.0f;
    DeviceMatchStrategy best_strategy = DeviceMatchStrategy::EXACT_ID_MATCH;
    
    for (const auto& strategy : strategies) {
      float confidence = CalculateDeviceMatchConfidence(video_device, audio_device, strategy);
      
      if (confidence > best_confidence) {
        best_confidence = confidence;
        best_strategy = strategy;
      }
      
      if (confidence > 0.0f) {
        std::string strategy_name;
        switch (strategy) {
          case DeviceMatchStrategy::EXACT_ID_MATCH: strategy_name = "EXACT_ID"; break;
          case DeviceMatchStrategy::FUZZY_NAME_MATCH: strategy_name = "FUZZY_NAME"; break;
          case DeviceMatchStrategy::USB_PATH_MATCH: strategy_name = "USB_PATH"; break;
          case DeviceMatchStrategy::NAME_PATTERN_MATCH: strategy_name = "NAME_PATTERN"; break;
        }
        LOG(INFO) << "    " << strategy_name << ": " << confidence;
      }
    }
    
    if (best_confidence > 0.5f) {
      LOG(INFO) << "    ✓ 推荐匹配 (置信度: " << best_confidence << ")";
    } else {
      LOG(INFO) << "    ✗ 不推荐匹配";
    }
  }
}

}  // namespace mediasdk

// 主函数
int main() {
  // 初始化COM
  ::CoInitialize(NULL);
  
  try {
    mediasdk::TestDirectShowDeviceMatching();
    mediasdk::TestUSBInterfaceDetection();
    mediasdk::TestCompleteAudioFormatEnumeration();
    mediasdk::TestStrategyPriority();
  } catch (const std::exception& e) {
    LOG(ERROR) << "Exception: " << e.what();
  }
  
  // 清理COM
  ::CoUninitialize();
  
  return 0;
}
