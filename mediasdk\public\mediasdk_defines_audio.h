#pragma once

#include "mediasdk_string.hpp"

#pragma pack(push, 1)

namespace mediasdk {

struct AudioInputParams {
  float volume = 1.0f;   // audio volume after process
  float balance = 0.5f;  // audio balance factor, 0.0~0.1, default 0.5
  uint32_t sync_offset =
      0;  // audio timestamp offset to sync with video, in seconds
  uint32_t interval = 120;  // audio interval time
  bool mono = false;        // whether to mix all audio channels to mono
  bool mute = false;        // whether to mute
  uint32_t monitor_type = 0;
};

struct CreateAudioParams {
  uint32_t track_id;
  MediaSDKString plugin_name;
  MediaSDKString json_params;
};

}  // namespace mediasdk

#pragma pack(pop)
