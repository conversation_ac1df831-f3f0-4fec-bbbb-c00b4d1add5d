#pragma once

#include <d3d11.h>
#include <stdint.h>

#include "mediasdk/public/mediasdk_defines.h"

namespace mediasdk {

namespace hook_api {

class AudioInputInterceptorTarget {
 public:
  virtual void OnInterceptoredAudio(const AudioSourceFrame& frame) = 0;
};

class AudioInputInterceptor {
 public:
  virtual void OnAudio(const AudioSourceFrame& frame) = 0;
};

class AudioHookPoint {
 public:
  virtual void OnAudioInputRawFrame(const char* audio_input_id,
                                    bool is_mute,
                                    const AudioSourceFrame& frame) = 0;

  virtual void OnAudioInputFilteredFrame(const char* audio_input_id,
                                         bool is_mute,
                                         const AudioSourceFrame& frame) = 0;

  virtual void OnMixedAudioFrame(uint32_t track_id,
                                 const AudioSourceFrame& frame) = 0;

  // Ensure that no data is sent back to AudioInputInterceptorTarget after
  // AudioInputInterceptor is unloaded.
  virtual AudioInputInterceptor* GetAudioInputInterceptor(
      const char* audio_input_id,
      AudioInputInterceptorTarget* delegate) = 0;

  // Note: The `OnAudioInputInterceptorTargetDestroyed` and
  // `GetAudioInputInterceptor` and `AudioInputInterceptor::OnAudio` methods may
  // be called from different threads. Implementers should ensure thread safety.
  virtual void OnAudioInputInterceptorTargetDestroyed(
      AudioInputInterceptorTarget* target) = 0;
};

}  // namespace hook_api

}  // namespace mediasdk