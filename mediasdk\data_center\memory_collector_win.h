#pragma once
#include <atomic>
#include <mutex>
#include "memory_collector.h"

namespace memory_collector {

class MemoryCollectorImpl final : public MemoryCollector {
 public:
  MemoryCollectorImpl() = default;

  ~MemoryCollectorImpl() override = default;

  bool TotalMemInMB(int& mem) override;

  bool TotalMemUsedInMB(int& mem) override;

  bool ProcessMemInMB(int& mem) override;

  bool ProcessPageFaultNum(int& num) override;

 private:
  bool ProcessQuery();

 private:
  int64_t last_total_mem_time_ = 0;
  int last_total_mem_ = 0;

  int64_t last_total_mem_used_time_ = 0;
  int last_total_used_mem_ = 0;

  int64_t last_process_mem_time_ = 0;
  int last_process_mem_ = 0;
  int64_t last_page_fault_num_ = 0;
  int64_t history_page_fault_num_ = 0;

  std::mutex collect_mutex_;
};
}  // namespace memory_collector