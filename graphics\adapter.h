#pragma once

#include <stdint.h>
#include <string>
#include "graphics_export.h"
#include "mediasdk/public/mediasdk_defines.h"

namespace graphics {

#pragma pack(push, 1)

struct GRAPHICS_EXPORT AdapterInfo {
  mediasdk::MSLUID adapter_id;
  uint32_t vendor_id;
  uint32_t device_id;
  wchar_t adapter_name[128];
};

struct GRAPHICS_EXPORT GpuAdapterInfo {
  std::string model_;
  uint32_t vendor_id_ = 0;
  uint32_t device_id_ = 0;
  uint32_t luid_high_ = 0;
  uint32_t luid_low_ = 0;
  int64_t dedicated_memory_ = 0;
  int64_t shared_memory_ = 0;
  std::string driver_version_;
  std::wstring driver_name_;
  int index_ = 0;
};

#pragma pack(pop)

GRAPHICS_EXPORT bool GetFirstAdapterInfo(AdapterInfo* info);
GRAPHICS_EXPORT bool GetFirstNoBasicRenderAdapterInfo(AdapterInfo* info);

}  // namespace graphics
