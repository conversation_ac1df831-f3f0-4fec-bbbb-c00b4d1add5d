#include "yuv_graphics_shader.h"

#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <array>
#include "DXSimpleMath.h"

using namespace Microsoft::WRL;

namespace graphics {

VIDEO_CONVERT_FORMAT VIDEO_CONVERT_FORMATS[] = {
    {mediasdk::kPixelFormatUnspecified},

    {mediasdk::kPixelFormatNV12, 2, "HALF_XY_VS_MAIN", "NV12_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 2, DXGI_FORMAT_R8G8_UNORM},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatI420, 3, "HALF_XY_VS_MAIN", "I420_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 2, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 2, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatI444, 3, "RAWCXCY_VS_Main", "I444_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatI422, 3, "XX_FULL_Y_VS_MAIN", "I422_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{}}},
    {mediasdk::kPixelFormatI440},
    {mediasdk::kPixelFormatI410},
    {mediasdk::kPixelFormatI411},
    {mediasdk::kPixelFormatI400},
    {mediasdk::kPixelFormatYV12, 3, "HALF_XY_VS_MAIN", "YV12_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 2, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 2, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatNV21, 2, "HALF_XY_VS_MAIN", "NV21_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 2, DXGI_FORMAT_R8G8_UNORM},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatUYVY, 1, "HALF_X_FULL_Y_VS_MAIN", "UYVY_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{2, 1, DXGI_FORMAT_B8G8R8A8_UNORM},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatYUY2, 1, "HALF_X_FULL_Y_VS_MAIN", "YUY2_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{2, 1, DXGI_FORMAT_B8G8R8A8_UNORM},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatARGB, 1, "RAWCXCY_VS_Main", "ARGB_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8G8B8A8_UNORM},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatXRGB, 1, "RAWCXCY_VS_Main", "XRGB_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8G8B8A8_UNORM},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatRGB24, 1, "RAWCXCY_VS_Main", "RGB_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM, 3},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatRGBA, 1, "RAWCXCY_VS_Main", "RGBA_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8G8B8A8_UNORM},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatBGR24, 1, "RAWCXCY_VS_Main", "BGR_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM, 3},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatBGRA, 1, "RAWCXCY_VS_Main", "BGRA_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8G8B8A8_UNORM},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},
    {mediasdk::kPixelFormatMJPEG},

    {mediasdk::kPixelFormatI444A, 4, "RAWCXCY_VS_Main", "I444A_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM}}},

    {mediasdk::kPixelFormatI420A, 4, "HALF_XY_VS_MAIN", "I420A_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 2, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 2, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM}}},

    {mediasdk::kPixelFormatI422A, 4, "XX_FULL_Y_VS_MAIN", "I422A_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM}}},

    {mediasdk::kPixelFormatYVYU, 1, "HALF_X_FULL_Y_VS_MAIN", "YVYU_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{2, 1, DXGI_FORMAT_B8G8R8A8_UNORM},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatNV12TEXTURE, 2, "DXGI_SIMPLE_VS_MAIN",
     "NV12_TEXTURE_PS_MAIN",  // this is different with others, but use same yuv
                              // matrix
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM},
         YUV_PLANE_DATA_DESC{2, 2, DXGI_FORMAT_R8G8_UNORM},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatY8, 1, "RAWCXCY_VS_Main", "Y8_PS_MAIN",
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM}, YUV_PLANE_DATA_DESC{},
         YUV_PLANE_DATA_DESC{}, YUV_PLANE_DATA_DESC{}}},

    {mediasdk::kPixelFormatY8TEXTURE, 2, "DXGI_SIMPLE_VS_MAIN",
     "Y8_TEXTURE_PS_MAIN",  // this is different with others, but use same yuv
                            // matrix
     std::array<YUV_PLANE_DATA_DESC, kMaxVideoPlanes>{
         YUV_PLANE_DATA_DESC{1, 1, DXGI_FORMAT_R8_UNORM}, YUV_PLANE_DATA_DESC{},
         YUV_PLANE_DATA_DESC{}}},
    {mediasdk::kPixelFormatU8V8},
    {mediasdk::kPixelFormatRGBA16},
    {mediasdk::kPixelFormatMax}

};  // namespace graphics

VIDEO_CONVERT_FORMAT GetStaticInfo(mediasdk::PixelFormat format) {
  return VIDEO_CONVERT_FORMATS[format];
}

// https://www.reddit.com/r/gamedev/comments/2j17wk/a_slightly_faster_bufferless_vertex_shader_trick/
static const char* CONST_VERTEX_SHADER() {
  return R"(
cbuffer TEXTURE_SIZE : register(b0)
{
   float	cx;
   float	cy;
};

struct PS_Pos
{
	float4 pos : SV_POSITION;
};

struct PS_pos_tex_xyZW
{
	float2 tex : TEXCOORD0;
    float4 pos : POSITION;
};

struct PS_pos_tex_xy
{
	float2 tex : TEXCOORD0;
	float4 pos : SV_POSITION;
};

struct PS_pos_tex_xyZ
{
	float3 tex : TEXCOORD0;
	float4 pos : SV_POSITION;
};

struct PS_TexXY
{
	float2 tex : TEXCOORD0;
};

struct PS_TexXYZ
{
	float3 tex : TEXCOORD0;
};

// common get pos from vid logic

float4 VS_GET_POS_TEX_XY(uint vid)
{
	bool right = vid == 2;
	bool top = vid == 1;
	float x = - 1.0f + right * 4.f;
	float y = - 1.0f + top * 4.f;
	float u = right * 2.f;
	float v =  1.0f - top * 2.f;
	return float4(x,y,u,v);
}

PS_Pos RAWCXCY_VS_Main(uint vid : SV_VERTEXID)
{
	float4 pos_tex_xy = VS_GET_POS_TEX_XY(vid);

	PS_Pos output;

	output.pos = float4(pos_tex_xy.x, pos_tex_xy.y, 0.0, 1.0);

	return output;
}

PS_pos_tex_xy HALF_X_FULL_Y_VS_MAIN(uint vid : SV_VERTEXID)
{
	float4 pos_tex_xy = VS_GET_POS_TEX_XY(vid);

	float texture_resource_x = cx * pos_tex_xy.z * 0.5;
	float texture_resource_y = cy * pos_tex_xy.w;

	PS_pos_tex_xy output;
	output.tex = float2(texture_resource_x, texture_resource_y);
	output.pos = float4(pos_tex_xy.x, pos_tex_xy.y, 0.0, 1.0);
	return output;
}

PS_pos_tex_xy HALF_XY_VS_MAIN(uint vid : SV_VERTEXID)
{
	float4 pos_tex_xy = VS_GET_POS_TEX_XY(vid);

	float texture_resource_x = cx * pos_tex_xy.z * 0.5f;

	float texture_resource_y = cy * pos_tex_xy.w * 0.5f;

	PS_pos_tex_xy output;

	output.tex = float2(texture_resource_x, texture_resource_y);

	output.pos = float4(pos_tex_xy.x, pos_tex_xy.y, 0.0f, 1.0f);

	return output;
}

PS_pos_tex_xyZ XX_FULL_Y_VS_MAIN(uint vid : SV_VERTEXID)
{
	float4 pos_tex_xy = VS_GET_POS_TEX_XY(vid);

	float texture_resource_x_0 = cx * pos_tex_xy.z;
    float texture_resource_x_1 = cx * pos_tex_xy.z * 0.5;

	PS_pos_tex_xyZ output;

	output.tex = float3(texture_resource_x_0, texture_resource_x_1, cy * pos_tex_xy.w);

	output.pos = float4(pos_tex_xy.x, pos_tex_xy.y, 0.0, 1.0);

	return output;
}

PS_pos_tex_xy DXGI_SIMPLE_VS_MAIN(PS_pos_tex_xyZW input)
{
	return input;
}
)";
}

static const char* CONST_PIXEL_SHADER() {
  return R"(
Texture2D texture0 : register(t0);
Texture2D texture1 : register(t1);
Texture2D texture2 : register(t2);
Texture2D texture3 : register(t3);
SamplerState tex_sampler : register(s0);

struct PS_Pos
{
	float4 pos : SV_POSITION;
};
struct PS_pos_tex_xy
{
	float2 tex  : TEXCOORD0;
	float4 pos  : SV_POSITION;
};
struct PS_pos_tex_xyZ
{
	float3 tex : TEXCOORD0;
	float4 pos : SV_POSITION;
};
struct PS_TexXY
{
	float2 tex : TEXCOORD0;
};
struct PS_TexXYZ
{
	float3 tex : TEXCOORD0;
};

cbuffer PS_BUFFER : register(b0)
{
   float4   Y;
   float4	U;
   float4	V;
   float4	min_v;
   float4	max_v;
};

inline float3 YUV_2_RGB(float3 yuv)
{
	yuv = clamp(yuv, min_v.xyz, max_v.xyz);// limit range

    // yuv to rgb
	float r = dot(Y.xyz, yuv) + Y.w;
	float g = dot(U.xyz, yuv) + U.w;
	float b = dot(V.xyz, yuv) + V.w;

	return float3(r, g, b);
}

float4 I420_PS_MAIN(PS_pos_tex_xy input) : SV_TARGET
{
	float y = texture0.Load(int3(input.pos.xy, 0)).x;

	int3 chroma_pos = int3(input.tex, 0);

	float Cb = texture1.Load(chroma_pos).x;

	float Cr = texture2.Load(chroma_pos).x;

	float3 rgb = YUV_2_RGB(float3(y, Cb, Cr));

	return float4(rgb, 1.f);
}

float4 Y8_PS_MAIN(PS_Pos input) : SV_TARGET
{
	float y = texture0.Load(int3(input.pos.xy, 0)).x;

	float Cb = 0.5f;

	float Cr = 0.5f;//todo add color param

	float3 rgb = YUV_2_RGB(float3(y, Cb, Cr));

	return float4(rgb, 1.f);
}

float4 I420A_PS_MAIN(PS_pos_tex_xy input) : SV_TARGET
{
	int3 raw_pos = int3(input.pos.xy, 0);

	float y = texture0.Load(raw_pos).x;
	float alpha = texture3.Load(raw_pos).x;

	int3 chroma_pos = int3(input.tex, 0);

	float Cb = texture1.Load(chroma_pos).x;

	float Cr = texture2.Load(chroma_pos).x;

	return float4(YUV_2_RGB(float3(y, Cb, Cr)), alpha);
}

float4 YV12_PS_MAIN(PS_pos_tex_xy input) : SV_TARGET
{
	float y = texture0.Load(int3(input.pos.xy, 0)).x;

	int3 chroma_pos = int3(input.tex, 0);

	float Cr = texture1.Load(chroma_pos).x;

	float Cb = texture2.Load(chroma_pos).x;

	float3 rgb = YUV_2_RGB(float3(y, Cb, Cr));

	return float4(rgb, 1.f);
}

float4 NV12_PS_MAIN(PS_pos_tex_xy input) : SV_TARGET
{
	float y = texture0.Load(int3(input.pos.xy, 0)).x;

	float2 CbCr = texture1.Load(int3(input.tex, 0)).xy;

    return float4(YUV_2_RGB(float3(y, CbCr)), 1.f);
}

float4 NV21_PS_MAIN(PS_pos_tex_xy input) : SV_TARGET
{
	float y = texture0.Load(int3(input.pos.xy, 0)).x;

	float2 CbCr = texture1.Load(int3(input.tex, 0)).xy;

    return float4(YUV_2_RGB(float3(y, CbCr.y, CbCr.x)), 1.f);
}

float4 I444_PS_MAIN(PS_Pos input) : SV_TARGET
{
	int3 xy = int3(input.pos.xy, 0);

	float y = texture0.Load(xy).x;

	float Cb = texture1.Load(xy).x;
	float Cr = texture2.Load(xy).x;

    return float4(YUV_2_RGB(float3(y, Cb, Cr)), 1.f);
}

float4 I444A_PS_MAIN(PS_Pos input) : SV_TARGET
{
	int3 xy = int3(input.pos.xy, 0);
	float y = texture0.Load(xy).x;
	float Cb = texture1.Load(xy).x;
	float Cr = texture2.Load(xy).x;

    float a = texture3.Load(xy).x;

    return float4(YUV_2_RGB(float3(y, Cb, Cr)), a);
}

float4 I422_PS_MAIN(PS_TexXYZ input) : SV_TARGET
{
	float y = texture0.Load(int3(input.tex.xz, 0)).x;

	int3 chroma_pos = int3(input.tex.yz, 0);

	float Cb = texture1.Load(chroma_pos).x;

	float Cr = texture2.Load(chroma_pos).x;

    return float4(YUV_2_RGB(float3(y, Cb, Cr)), 1.f);
}

float4 I422A_PS_MAIN(PS_TexXYZ input) : SV_TARGET
{
	int3 raw_pos = int3(input.tex.xz, 0);
	float y = texture0.Load(raw_pos).x;

	int3 chroma = int3(input.tex.yz, 0);

	float Cb = texture1.Load(chroma).x;

	float Cr = texture2.Load(chroma).x;

	float alpha = texture3.Load(raw_pos).x;

    return float4(YUV_2_RGB(float3(y, Cb, Cr)), alpha);
}

float4 YUY2_PS_MAIN(PS_pos_tex_xy input) : SV_TARGET
{
	float4 pixel = texture0.Load(int3(input.tex.xy, 0));
	float2 y2y1 = pixel.zx;
	float2 uv = pixel.yw;
	float value = frac(input.tex.x);
	float y = (value < 0.5) ? y2y1.x : y2y1.y;

    return float4(YUV_2_RGB(float3(y, uv)), 1.f);
}
float4 I411_PS_MAIN(PS_pos_tex_xy input) : SV_TARGET
{
	float4 pixel = texture0.Load(int3(input.tex.xy, 0));
	float2 y2y1 = pixel.zx;
	float2 uv = pixel.yw;
	float value = frac(input.tex.x);
	float y = (value < 0.5) ? y2y1.x : y2y1.y;

    return float4(YUV_2_RGB(float3(y, uv)), 1.f);
}
float4 YVYU_PS_MAIN(PS_TexXY input) : SV_TARGET
{
    // y0v0y1u0
    // x  y  z  w

	float4 tex = texture0.Load(int3(input.tex.xy, 0));
	float2 y0y1 = tex.zx;
	float2 CbCr = tex.wy;
	float value = frac(input.tex.x);
	float y = (value < 0.5) ? y0y1.x : y0y1.y;

    return float4(YUV_2_RGB(float3(y, CbCr)), 1.f);
}

float4 UYVY_PS_MAIN(PS_TexXY input) : SV_TARGET
{
    //u0y0v0y1
   // x   y  z  w
	float4 tex = texture0.Load(int3(input.tex.xy, 0));

	float2 y0y1 = tex.yw;

	float2 CbCr = tex.zx;

	float value = frac(input.tex.x);

	float y = (value < 0.5) ? y0y1.x : y0y1.y;

	return float4(YUV_2_RGB(float3(y, CbCr)), 1.f);
}

float4 BGR_PS_MAIN(PS_Pos input) : SV_TARGET
{
	float x = input.pos.x * 3.0;
	float y = input.pos.y;
	float b = texture0.Load(int3(x - 1.0, y, 0)).x;
	float g = texture0.Load(int3(x, y, 0)).x;
	float r = texture0.Load(int3(x + 1.0, y, 0)).x;
	float3 rgb = float3(r, g, b);
	rgb = rgb * max_v.x  - min_v.x;

	return float4(rgb, 1.f);
}

float4 RGB_PS_MAIN(PS_Pos input) : SV_TARGET
{
	float x = input.pos.x * 3.0;
	float y = input.pos.y;
	float r = texture0.Load(int3(x - 1.0, y, 0)).x;
	float g = texture0.Load(int3(x, y, 0)).x;
	float b = texture0.Load(int3(x + 1.0, y, 0)).x;
	float3 rgb = float3(r, g, b);
	rgb = rgb * max_v.x  - min_v.x;

	return float4(rgb, 1.f);
}

float4 BGRA_PS_MAIN(PS_Pos input) : SV_TARGET
{
	float4 rgba = texture0.Load(int3(input.pos.xy, 0));
	rgba.rgb = rgba.rgb * max_v.x  - min_v.x;
    return float4(float3(rgba.b,rgba.g,rgba.r), rgba.a);
}

float4 RGBA_PS_MAIN(PS_Pos input) : SV_TARGET
{
	float4 rgba = texture0.Load(int3(input.pos.xy, 0));
	rgba.rgb = rgba.rgb * max_v.x  - min_v.x;
    return float4(rgba.rgb, rgba.a);
}

float4 ARGB_PS_MAIN(PS_Pos input) : SV_TARGET
{
// rgba
// argb
// bgra
	float4 value = texture0.Load(int3(input.pos.xy, 0));
	float3 rgb = float3(value.g,value.b,value.a);

	rgb = rgb * max_v.x  - min_v.x;

    return float4(rgb, value.r);
}

float4 XRGB_PS_MAIN(PS_Pos input) : SV_TARGET
{
// rgba
// xrgb
// bgrx
	float4 value = texture0.Load(int3(input.pos.xy, 0));
	float3 rgb = float3(value.g,value.b,value.a);
	rgb = rgb * max_v.x  - min_v.x;

	return float4(rgb, 1.f);
}

float4 NV12_TEXTURE_PS_MAIN(PS_pos_tex_xy input) : SV_TARGET
{
	float y = texture0.Sample(tex_sampler, input.tex);

	float2 uv = texture1.Sample(tex_sampler, input.tex);
    
    return float4(YUV_2_RGB(float3(y, uv.x, uv.y)), 1.f);
}

float4 Y8_TEXTURE_PS_MAIN(PS_pos_tex_xy input) : SV_TARGET
{
	float y = texture0.Sample(tex_sampler, input.tex);

	float2 uv = float2(0.5f,0.5f);
    
    return float4(YUV_2_RGB(float3(y, uv.x, uv.y)), 1.f);
}

)";
}

void SHADER_RESOURCE::Destroy() {
  if (ps) {
    ps.Reset();
  }
  if (vs) {
    vs.Reset();
  }
  if (input_layout) {
    input_layout.Reset();
  }
  if (vertex) {
    vertex.Reset();
  }
  if (index) {
    index.Reset();
  }
}

bool YUVGraphicsShader::Init(const std::shared_ptr<Device>& ins) {
  instance_ = ins;

  return true;
}

YUVGraphicsShader::~YUVGraphicsShader() {
  YUVGraphicsShader::Destroy();
}

void YUVGraphicsShader::Destroy() {
  if (sampler_) {
    sampler_.Reset();
  }
  for (auto& convert : convert_shader_) {
    if (convert) {
      convert->Destroy();
    }
  }
}

std::shared_ptr<SHADER_RESOURCE> YUVGraphicsShader::Init(
    mediasdk::PixelFormat format) {
  if (convert_shader_[format])
    return convert_shader_[format];
  auto info = GetStaticInfo(format);
  if (info.format != format)
    return nullptr;

  Device::CompileShaderParam param = {};
  param.ps = CONST_PIXEL_SHADER();
  param.vs = CONST_VERTEX_SHADER();
  param.ps_name = info.ps_name;
  param.vs_name = info.vs_name;

  D3D11_INPUT_ELEMENT_DESC layout[2];
  layout[0].SemanticName = "POSITION";
  layout[0].SemanticIndex = 0;
  layout[0].Format = DXGI_FORMAT_R32G32B32_FLOAT;
  layout[0].InputSlot = 0;
  layout[0].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
  layout[0].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
  layout[0].InstanceDataStepRate = 0;
  layout[1].SemanticName = "TEXCOORD";
  layout[1].SemanticIndex = 0;
  layout[1].Format = DXGI_FORMAT_R32G32_FLOAT;
  layout[1].InputSlot = 0;
  layout[1].AlignedByteOffset = D3D11_APPEND_ALIGNED_ELEMENT;
  layout[1].InputSlotClass = D3D11_INPUT_PER_VERTEX_DATA;
  layout[1].InstanceDataStepRate = 0;
  if (info.format == mediasdk::kPixelFormatNV12TEXTURE ||
      info.format == mediasdk::kPixelFormatY8TEXTURE) {
    param.layout_descs_ = layout;
    param.layout_cnt_ = 2;
  }
  if (!instance_->CompileShader(param)) {
    return nullptr;
  }
  auto ret = std::make_shared<SHADER_RESOURCE>();
  ret->format_info = info;
  ret->input_layout = param.layout_;
  ret->ps = param.ps_shader_;
  ret->vs = param.vs_shader_;
  if (format == mediasdk::PixelFormat::kPixelFormatNV12TEXTURE ||
      info.format == mediasdk::PixelFormat::kPixelFormatY8TEXTURE) {
    // texture type input should use index for draw
    YUVGraphicsShader::VERTEXTYPE vertex[4];
    vertex[0].position = XMFLOAT3(-1.0F, 1.0F, 0.0F);
    vertex[1].position = XMFLOAT3(1.0F, 1.0F, 0.0F);
    vertex[2].position = XMFLOAT3(-1.0F, -1.0F, 0.0F);
    vertex[3].position = XMFLOAT3(1.0F, -1.0F, 0.0F);
    vertex[0].texture = XMFLOAT2(0.0F, 0.0F);
    vertex[1].texture = XMFLOAT2(1.0F, 0.0F);
    vertex[2].texture = XMFLOAT2(0.0F, 1.0F);
    vertex[3].texture = XMFLOAT2(1.0F, 1.0F);
    D3D11_BUFFER_DESC desc;
    ZeroMemory(&desc, sizeof(desc));
    desc.Usage = D3D11_USAGE_DEFAULT;
    desc.ByteWidth = sizeof(VERTEXTYPE) * 4;
    desc.BindFlags = D3D11_BIND_VERTEX_BUFFER;
    D3D11_SUBRESOURCE_DATA data;
    ZeroMemory(&data, sizeof(data));
    data.pSysMem = vertex;
    data.SysMemPitch = 0;
    data.SysMemSlicePitch = 0;
    HRESULT hRes = GetDevice()->CreateBuffer(&desc, &data, &ret->vertex);
    if (hRes != S_OK) {
      LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                       GetErrorString(hRes).c_str());
      return nullptr;
    }

    ZeroMemory(&data, sizeof(data));
    data.pSysMem = vertex;
    data.SysMemPitch = 0;
    data.SysMemSlicePitch = 0;

    uint32_t indices[6] = {
        0, 1, 2, 2, 1, 3,
    };
    ZeroMemory(&desc, sizeof(desc));
    desc.Usage = D3D11_USAGE_DEFAULT;
    desc.ByteWidth = sizeof(uint32_t) * 6;
    desc.BindFlags = D3D11_BIND_INDEX_BUFFER;
    ZeroMemory(&data, sizeof(data));
    data.pSysMem = indices;
    hRes = GetDevice()->CreateBuffer(&desc, &data, &ret->index);
    if (hRes != S_OK) {
      LOG(ERROR) << base::StringPrintf("Failed to CreateBuffer(%s)",
                                       GetErrorString(hRes).c_str());
      return nullptr;
    }
  }
  if (!CreateIAS())
    return nullptr;

  convert_shader_[format] = ret;

  return ret;
}

bool YUVGraphicsShader::CreateIAS() {
  if (sampler_)
    return true;

  D3D11_SAMPLER_DESC desc = {};
  desc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
  desc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
  desc.MipLODBias = 0.0F;
  desc.MaxAnisotropy = 0;
  desc.ComparisonFunc = D3D11_COMPARISON_ALWAYS;
  desc.BorderColor[0] = 0;
  desc.BorderColor[1] = 0;
  desc.BorderColor[2] = 0;
  desc.BorderColor[3] = 0;
  desc.MinLOD = 0;
  desc.MaxLOD = D3D11_FLOAT32_MAX;
  HRESULT hRes = GetDevice()->CreateSamplerState(&desc, &sampler_);
  if (FAILED(hRes)) {
    LOG(ERROR) << base::StringPrintf("Failed to v(%s)",
                                     GetErrorString(hRes).c_str());
    return false;
  }

  return true;
}

void YUVGraphicsShader::Render(
    mediasdk::PixelFormat format,
    ComPtr<ID3D11Buffer>& vs_buffer,
    ComPtr<ID3D11Buffer>& ps_buffer,
    ID3D11ShaderResourceView* views[kMaxVideoPlanes]) {
  auto converter = Init(format);
  if (!converter) {
    LOG(ERROR) << "Failed to init with format[" << format << "]";
    return;
  }

  if (converter->vertex) {
    UINT offset = 0;
    UINT stride = sizeof(VERTEXTYPE);
    GetContext()->IASetVertexBuffers(0, 1, converter->vertex.GetAddressOf(),
                                     &stride, &offset);
    GetContext()->IASetIndexBuffer(converter->index.Get(), DXGI_FORMAT_R32_UINT,
                                   0);
  } else {
    GetContext()->IASetVertexBuffers(0, 0, nullptr, nullptr, nullptr);
    GetContext()->IASetIndexBuffer(nullptr, DXGI_FORMAT::DXGI_FORMAT_UNKNOWN,
                                   0);
  }

  GetContext()->VSSetConstantBuffers(0, 1, vs_buffer.GetAddressOf());

  GetContext()->PSSetConstantBuffers(0, 1, ps_buffer.GetAddressOf());
  int32_t planes = converter->format_info.planes;
  if (planes > 0) {
    for (int32_t i = 0; i < planes; i++) {
      GetContext()->PSSetShaderResources(i, 1, &views[i]);
    }
  }
  if (converter->input_layout != NULL) {
    GetContext()->IASetInputLayout(converter->input_layout.Get());
  } else {
    GetContext()->IASetInputLayout(nullptr);
  }

  GetContext()->PSSetShader(converter->ps.Get(), NULL, 0);

  GetContext()->VSSetShader(converter->vs.Get(), NULL, 0);

  GetContext()->PSSetSamplers(0, 1, sampler_.GetAddressOf());

  GetContext()->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);

  if (format == mediasdk::kPixelFormatNV12TEXTURE ||
      format == mediasdk::kPixelFormatY8TEXTURE) {
    GetContext()->DrawIndexed(6, 0, 0);  // input full index && index buffer
  } else {
    GetContext()->Draw(3, 0);  // input only VID
  }
}

ComPtr<ID3D11Device> YUVGraphicsShader::GetDevice() {
  return instance_->GetDevice();
}

ComPtr<ID3D11DeviceContext> YUVGraphicsShader::GetContext() {
  return instance_->GetContext();
}
}  // namespace graphics