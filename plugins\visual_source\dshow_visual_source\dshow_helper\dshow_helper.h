#pragma once

// clang-format off
#include <baseclasses/streams.h>
#include <dshow.h>
#include <ks.h>
#include <ksmedia.h>
#include <ksproxy.h>
#include <wrl/client.h>
#include <cassert>
#include <string>
#include <vector>
#include "video_capture_format.h"
#include "audio_capture_format.h"
// clang-format on

namespace mediasdk {

struct DShowDeviceName {
  std::wstring name;
  std::wstring id;
};

// 设备匹配策略
enum class DeviceMatchStrategy {
  EXACT_ID_MATCH = 0,      // 精确ID匹配（一体化设备）
  NAME_PATTERN_MATCH = 1,  // 名称模式匹配
  USB_PATH_MATCH = 2,      // USB路径匹配
  FUZZY_NAME_MATCH = 3     // 模糊名称匹配
};

// 音频设备匹配结果
struct AudioDeviceMatchResult {
  DShowDeviceName audio_device;
  std::vector<AudioCaptureFormat> formats;
  float confidence_score;
  DeviceMatchStrategy match_strategy;
  std::string match_details;
};

// 扩展的音频格式枚举结果
struct ExtendedAudioFormatResult {
  DShowDeviceName source_video_device;
  std::vector<AudioDeviceMatchResult> audio_results;
  bool has_integrated_audio;
  std::string error_message;
  std::vector<std::string> suggestions;
};

struct VideoProcAmpStruct {
  long default_value;
  long value;
  long min_value;
  long max_value;
  long step;
  long default_flag;
  long flag;
  VideoProcAmpProperty property_index;
};

struct CameraControlStruct {
  long default_value;
  long value;
  long min_value;
  long max_value;
  long step;
  long default_flag;
  long flag;
  CameraControlProperty property_index;
};

BOOL ValidVideoProcAmpStruct(const VideoProcAmpStruct& st);

BOOL ValidCameraControlStruct(const CameraControlStruct& st);

BOOL DShowEnumDevice(std::vector<DShowDeviceName>& names, const CLSID& clsid);

BOOL IsPinCategory(IPin* pin, REFGUID category);

BOOL IsPinStreamMajorType(IPin* pin, REFGUID majortype);

BOOL IsPinMajorType(IPin* pin, REFGUID majortype);

BOOL TranslateMediatypeToVideoCaptureFormat(
    VideoCaptureFormat& format,
    const GUID& sub_type,
    const VIDEOINFOHEADER* video_info_header,
    const VIDEO_STREAM_CONFIG_CAPS* video_stream_caps);

mediasdk::PixelFormat TranslateMediaSubtypeToPixelFormat(const GUID& sub_type);

Microsoft::WRL::ComPtr<IPin> FindPinByName(IBaseFilter* filter,
                                           PIN_DIRECTION dest,
                                           LPCWSTR name);

Microsoft::WRL::ComPtr<IPin> FindPinByMedium(IBaseFilter* filter,
                                             REGPINMEDIUM& medium);

Microsoft::WRL::ComPtr<IPin> FindPinByCategoryAndStreamMajorType(
    IBaseFilter* filter,
    PIN_DIRECTION dest,
    REFGUID category,
    REFGUID majortype);

Microsoft::WRL::ComPtr<IPin> FindPinByMajortype(IBaseFilter* filter,
                                                PIN_DIRECTION dest,
                                                REFGUID majortype);

Microsoft::WRL::ComPtr<IBaseFilter> GetDeviceBaseFilter(
    const DShowDeviceName& name,
    const CLSID& id);

Microsoft::WRL::ComPtr<IBaseFilter> GetDeviceBaseFilter(const CLSID& id,
                                                        REGPINMEDIUM& medium);

BOOL GetPinMedium(IPin* pin, REGPINMEDIUM& reg_medium);

BOOL GetDeviceFormats(const DShowDeviceName& name,
                      const CLSID& id,
                      std::vector<VideoCaptureFormat>& formats);

BOOL GetDeviceFormats(const DShowDeviceName& name,
                      std::vector<AudioCaptureFormat>& formats);

BOOL GetPinMediaType(IPin* pin, REFGUID sub_type, AM_MEDIA_TYPE** pp_type);

// 扩展的音频格式枚举函数
BOOL GetAudioFormatsForVideoDevice(
    const DShowDeviceName& video_device,
    ExtendedAudioFormatResult& result);

// 设备关联查找函数
std::vector<DShowDeviceName> FindAssociatedAudioDevices(
    const DShowDeviceName& video_device,
    DeviceMatchStrategy strategy);

// 计算设备匹配置信度
float CalculateDeviceMatchConfidence(
    const DShowDeviceName& video_device,
    const DShowDeviceName& audio_device,
    DeviceMatchStrategy strategy);

// 提取设备核心名称
std::wstring ExtractCoreDeviceName(const std::wstring& device_name);

// 提取USB路径信息
std::wstring ExtractUSBPath(const std::wstring& device_id);

// 检查是否是连续的USB接口（一体化设备的特征）
bool IsConsecutiveUSBInterface(const std::wstring& device_id1, const std::wstring& device_id2);

}  // namespace mediasdk
