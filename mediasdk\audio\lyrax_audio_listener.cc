#include "lyrax_audio_listener.h"

#include "mediasdk/component_center.h"
#include "mediasdk/notify_center.h"

namespace mediasdk {

LyraxAudioListener::LyraxAudioListener() {}

void LyraxAudioListener::onApiCall(const std::string& api_name,
                                   lyrax::LyraxErrorCode error_code, const std::string& msg) {
  // do nothing now
}

void LyraxAudioListener::onEngineInfo(const std::string& msg) {
  // do nothing now
}

void LyraxAudioListener::onDeviceEventInfo(const lyrax::LyraxAudioDeviceEventInfo& info) {
  // do nothing now
}

void LyraxAudioListener::onEchoDetectionResult(float probability) {
  auto nc = com::GetNotifyCenter();
  nc->AudioEvent()->Notify(FROM_HERE,
                           &MediaSDKAudioStatusObserver::OnEchoDetectionResult,
                           probability);
}
}  // namespace mediasdk