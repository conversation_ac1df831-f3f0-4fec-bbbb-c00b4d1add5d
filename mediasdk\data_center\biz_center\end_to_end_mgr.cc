#include "end_to_end_mgr.h"
#include "mediasdk/component_center.h"
#include "mediasdk/data_center/data_center.h"
#include "mediasdk/utils/time_helper.h"

namespace mediasdk {

namespace {
std::string CreateEndtoEndDelaySEI(
    std::shared_ptr<EndtoEndDelayCaptureConst> captures,
    int64_t encode_end_ns) {
  auto* dc = com::GetDataCenter();
  if (!dc) {
    NOTREACHED();
    return "";
  }

  if (!captures) {
    NOTREACHED();
    return "";
  }

  // fill sei data
  nlohmann::json j_data;

  // fill capture timestamps
  bool has_caps = false;
  for (auto& [k, v] : captures->GetAllTimestmaps()) {
    if (0 == v) {
      continue;
    }
    if (int64_t ntp_ms = dc->ChangeToTTNtpMS(v); 0 == ntp_ms) {
      NOTREACHED();
      continue;
    } else {
      j_data[k] = ntp_ms;
      has_caps = true;
    }
  }

  if (!has_caps) {
    // NOTREACHED();
    return "";
  }

  // fill encode timestamp
  if (int64_t ntp_ms = dc->ChangeToTTNtpMS(encode_end_ns); 0 == ntp_ms) {
    return "";
  } else {
    j_data["encode"] = ntp_ms;
  }
  nlohmann::json sei;
  sei["video_e2e_delay"] = j_data;
  return std::string("JSON") + sei.dump();
}

EndtoEndSEIforRTC CreateEndtoEndSEIforRTC(
    std::shared_ptr<EndtoEndDelayCaptureConst> captures,
    int64_t effect_ns) {
  auto* dc = com::GetDataCenter();
  if (!dc) {
    NOTREACHED();
    return EndtoEndSEIforRTC();
  }

  if (!captures) {
    NOTREACHED();
    return EndtoEndSEIforRTC();
  }

  // fill sei data
  EndtoEndSEIforRTC sei;
  nlohmann::json other_data;
  bool has_caps = false;
  // fill capture timestamps
  for (auto& [k, v] : captures->GetAllTimestmaps()) {
    if (int64_t ntp_ms = dc->ChangeToLocalMS(v); 0 == ntp_ms) {
      NOTREACHED();
      continue;
    } else {
      if ("capture_camera" == k) {
        sei.camera_ntp_ms = ntp_ms;
      } else {
        other_data[k] = ntp_ms;
      }
      has_caps = true;
    }
  }

  if (!has_caps) {
    NOTREACHED();
    return EndtoEndSEIforRTC();
  }

  // fill effect timestamp
  if (0 != sei.camera_ntp_ms && 0 != effect_ns) {
    if (int64_t ntp_ms = dc->ChangeToTTNtpMS(effect_ns); 0 != ntp_ms) {
      sei.effect_ntp_ms = ntp_ms;
    }
  }

  sei.other_sei_json = other_data.dump();
  return sei;
}

}  // namespace

EndtoEndMgr::EndtoEndMgr(const EndtoEndMgr& r) {
  this->end_to_end_delay_captures_ = r.end_to_end_delay_captures_;
  this->before_mix_ns_ = r.before_mix_ns_;
  this->encode_end_ns_ = r.encode_end_ns_;
}

EndtoEndMgr::EndtoEndMgr(std::shared_ptr<EndtoEndDelayCaptureConst> captures,
                         int64_t before_mix_ns)
    : end_to_end_delay_captures_(captures), before_mix_ns_(before_mix_ns) {}

bool EndtoEndMgr::IsValid() const {
  return !!end_to_end_delay_captures_;
}

void EndtoEndMgr::SetEncodeEndNS(int64_t encode_end_ns) const {
  DCHECK(encode_end_ns);
  encode_end_ns_ = encode_end_ns;
}

int64_t EndtoEndMgr::GetPushCostUS(int64_t push_end_ns) const {
  if (0 == encode_end_ns_) {
    NOTREACHED();
    return 0;
  }
  int64_t diff_ns = (push_end_ns - encode_end_ns_);
  if (diff_ns < 0) {
    diff_ns = 0;
  }
  DCHECK(diff_ns);
  return diff_ns / 1000;
}

int64_t EndtoEndMgr::GetTotalCostUS(int64_t push_end_ns) const {
  if (!end_to_end_delay_captures_) {
    return 0;
  }
  int64_t diff_ns =
      push_end_ns - end_to_end_delay_captures_->GetMinCaptureTimestampNS();
  if (diff_ns < 0) {
    diff_ns = 0;
  }
  DCHECK(diff_ns);
  return diff_ns / 1000;
}

std::string EndtoEndMgr::GetEndtoEndDelaySEI() const {
  if (!end_to_end_delay_captures_) {
    return "";
  }
  if (0 == encode_end_ns_) {
    // NOTREACHED();
    return "";
  }
  return CreateEndtoEndDelaySEI(end_to_end_delay_captures_, encode_end_ns_);
}

EndtoEndSEIforRTC EndtoEndMgr::GetEndtoEndDelaySEIforRTC() const {
  if (!end_to_end_delay_captures_) {
    return EndtoEndSEIforRTC();
  }
  return CreateEndtoEndSEIforRTC(end_to_end_delay_captures_, before_mix_ns_);
}

}  // namespace mediasdk
