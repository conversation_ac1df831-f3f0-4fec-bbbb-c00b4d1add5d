#include "video_encode_intput_adaptive.h"

#include "base/logging.h"

namespace mediasdk {

// VideoEncodeInputAdaptiveLock

VideoEncodeInputAdaptiveLock::~VideoEncodeInputAdaptiveLock() {
  ptr_->mutex_.unlock();
}

VideoEncodeInputAdaptiveLock::VideoEncodeInputAdaptiveLock(
    VideoEncodeInputAdaptive* ptr)
    : ptr_(ptr) {
  ptr_->mutex_.lock();
}

// VideoEncodeInputAdaptive

VideoEncodeInputAdaptive::VideoEncodeInputAdaptive(
    VideoEncodeAdaptiveConfig config,
    VideoEncodeInputAdaptiveDelegate* delegate) {
  DCHECK(delegate);
  config_ = config;
  delegate_ = delegate;
  init_fps_ = config_.fps;
}

VideoEncodeInputAdaptive::~VideoEncodeInputAdaptive() {}

void VideoEncodeInputAdaptive::InputTexture(const VideoTexture& texture) {
  if (!mutex_.try_lock()) {
    cache_queue_.Enqueue(texture);
    return;
  }

  std::lock_guard<std::recursive_mutex> lock(mutex_, std::adopt_lock);

  while (!cache_queue_.IsEmpty()) {
    auto tex = cache_queue_.DeQueue();
    if (tex) {
      Input(*tex);
    }
  }

  Input(texture);
}

std::unique_ptr<VideoEncodeInputAdaptiveLock> VideoEncodeInputAdaptive::Lock() {
  return std::make_unique<VideoEncodeInputAdaptiveLock>(this);
}

void VideoEncodeInputAdaptive::UpdateConfig(
    const VideoEncodeAdaptiveConfig& config) {
  DCHECK(config.fps > 0 && config.width > 0 && config.height > 0);
  config_ = config;
}

VideoEncodeAdaptiveConfig VideoEncodeInputAdaptive::GetConfig() {
  return config_;
}

bool VideoEncodeInputAdaptive::SmoothFrameDropping(const int64_t timestamp_ns) {
  auto current_fps = config_.fps;
  if (current_fps >= init_fps_) {
    return false;
  }
  return output_dropper_.Droppable(timestamp_ns, current_fps);
}

bool VideoEncodeInputAdaptive::NeedScale(graphics::Texture* texture) {
  if (!texture || config_.width == 0 || config_.height == 0) {
    return false;
  }

  if (texture->GetSize().x == config_.width &&
      texture->GetSize().y == config_.height) {
    return false;
  }
  return true;
}

std::shared_ptr<graphics::Texture> VideoEncodeInputAdaptive::AdaptiveTexture(
    graphics::Texture* texture) {
  if (!texture || config_.width == 0 || config_.height == 0) {
    return nullptr;
  }

  DCHECK(texture->GetUsage() &
         graphics::Texture::TEXTURE_USAGE::TEXTURE_USAGE_SHADER_RESOURCE);

  const auto texture_size = texture->GetSize();

  graphics::Transform transform;
  float scale_x = static_cast<float>(config_.width) / texture_size.x;
  float scale_y = static_cast<float>(config_.height) / texture_size.y;
  float scale = std::min(scale_x, scale_y);
  transform.SetScale({scale, scale});

  const int32_t target_width = config_.width;
  const int32_t target_height = config_.height;

  // Create or recreate graphics object if needed
  if (!scale_graphics_ || (scale_graphics_->GetSize().x != target_width ||
                           scale_graphics_->GetSize().y != target_height)) {
    scale_graphics_ = graphics::CreateGraphics2D(texture->GetDevice());
    if (scale_graphics_) {
      scale_graphics_->CreateNewBGRAGraphics(target_width, target_height);
    }
  }

  if (!scale_graphics_) {
    LOG(ERROR) << "Create scale graphics failed";
    return nullptr;
  }

  graphics::ScopedEndDraw end(*scale_graphics_);
  scale_graphics_->BeginDraw();

  if (texture) {
    scale_graphics_->DrawBGRATexture(*texture, transform);
    return scale_graphics_->GetOutputTexture();
  }

  return nullptr;
}

void VideoEncodeInputAdaptive::Input(const VideoTexture& texture) {
  if (SmoothFrameDropping(texture.GetTimestampNS())) {
    return;
  }

  if (NeedScale(texture.GetTexture())) {
    auto tex = AdaptiveTexture(texture.GetTexture());
    if (tex != nullptr) {
      VideoTexture adaptive_texture = texture;
      adaptive_texture.SetTexture(tex);
      if (delegate_) {
        delegate_->OnAdaptiveTexture(adaptive_texture);
      }
    }
  } else {
    if (delegate_) {
      delegate_->OnAdaptiveTexture(texture);
    }
  }
}

}  // namespace mediasdk
