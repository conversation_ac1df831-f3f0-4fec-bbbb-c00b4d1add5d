#include "av_packet_list.h"
#include <base/check.h>

int32_t AVPacketItemList::GetPendingSendPacket() const {
  std::shared_lock lock(lock_packet_list_);
  return packet_list_.size();
}

int32_t AVPacketItemList::GetPendingReceivePacket() const {
  std::shared_lock lock(lock_packet_list_);
  return pending_packet_list_.size();
}

void AVPacketItemList::PushPacket(const AVPacket& packet) {
  AVPacket* ref = av_packet_clone(&packet);

  std::unique_lock lock(lock_packet_list_);
  packet_list_.push_back(ref);
}

void AVPacketItemList::PushFlushPacket() {
  AVPacket* empty = av_packet_alloc();
  empty->data = nullptr;
  empty->size = 0;

  std::unique_lock lock(lock_packet_list_);

  packet_list_.push_back(empty);
}

AVPacket* AVPacketItemList::FrontPacket() {
  std::unique_lock lock(lock_packet_list_);
  if (packet_list_.empty()) {
    return nullptr;
  }
  AVPacket* ref = packet_list_.front();
  DCHECK(ref && "not impl");
  if (ref->data) {
    pending_packet_list_.push_back(ref);
  }
  packet_list_.pop_front();
  return ref;
}

void AVPacketItemList::PopPendingReceive() {
  std::unique_lock lock(lock_packet_list_);
  pending_packet_list_.pop_front();
}

AVPacketItemList::~AVPacketItemList() {
  Clear();
}

void AVPacketItemList::Clear() {
  std::list<AVPacket*> list;
  {
    std::unique_lock lock(lock_packet_list_);
    std::swap(list, packet_list_);
    pending_packet_list_.clear();
  }

  for (auto* packet : list) {
    av_packet_free(&packet);
  }
  list.clear();
}