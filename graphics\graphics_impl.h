#pragma once

#include <DirectXMath.h>
#include <unordered_map>
#include "graphics.h"

#include "border.h"
#include "buffered_texture_buffer.h"
#include "corner.h"

using namespace DirectX;

namespace graphics {

class GraphicsImpl : public Graphics {
 public:
  GraphicsImpl(Device& ins);
  static std::shared_ptr<GraphicsImpl> CreateGraphics2D(Device& ins);
  bool CreateNewY8Graphics(int cx, int cy) override;
  bool CreateNewBGRAGraphics(int cx, int cy) override;
  bool CreateNewSharedBGRAGraphics(int cx,
                                   int cy,
                                   D3D11_RESOURCE_MISC_FLAG flag) override;
  bool CreateNewRGBAGraphics(int cx, int cy) override;

  bool CreateGraphics(const D3D11_TEXTURE2D_DESC& desc,
                      const D3D11_RENDER_TARGET_VIEW_DESC* target) override;

  bool CreateWithSwapChain(IDXGISwap<PERSON>hain* swap_chain) override;

  bool CreateGraphics(ID3D11Texture2D* texture,
                      const D3D11_RENDER_TARGET_VIEW_DESC* target) override;

  bool CreateGraphics(Texture& texutre) override;

  bool CreateGraphicsFromTexture(
      Microsoft::WRL::ComPtr<ID3D11Texture2D> texture) override;

  bool BeginDraw(bool clear = true,
                 XMFLOAT2* size = nullptr,
                 const DirectX::XMFLOAT4& color =
                     DirectX::XMFLOAT4(0.0F, 0.0F, 0.0F, 0.0F)) override;

  void DrawBGRATexture(Texture& texture,
                       const Transform& transform,
                       const bool tone_mapping = true) override;

  void DrawBGRATextureChromaKeyParams(
      Texture& texture,
      const Transform& transform,
      const ChromaKeyParams& chroma_key_params) override;

  void DrawPartialScaleTexture(Texture& texture,
        const Transform& transform,
        const PartialScaleParams& params) override;

  void DrawBGRATextureForceAlpha(Texture& texture,
                                 const Transform& transform,
                                 bool force_tex_alpha) override;

  bool DrawBGRATextureSampleFlags(Texture& texture,
                                  const Transform& transform,
                                  bool sample_anisotropic = false) override;

  void DrawBGRATextureColorParams(Texture& texture,
                                  const Transform& transform,
                                  const ColorParams& color_params) override;

  void DrawRGBATextureLuminanceMapParams(Texture& texture,
                                         const Transform& transform,
                                         const LuminanceMapParams& luminance_map_params) override;

  void DrawBGRATextureSharpnessParams(
      Texture& texture,
      const Transform& transform,
      const SharpnessParams& sharpness_params) override;

  void DrawBGRAPointNineTexture(Texture& texture,
                                const Transform& transform,
                                const PointNine& point_nine) override;

  void DrawCornerTexture(Texture& texture,
                         const XMFLOAT4& corner_radius,
                         const float& ratio,
                         const Transform& transform,
                         bool refer_width,
                         bool fixed_radius) override;

  void DrawBorderTexture(Texture& texture,
                         const XMFLOAT4& color,
                         const XMFLOAT2& width,
                         const float& ratio,
                         const Transform& transform) override;

  void DrawBGRAClipMask(const DirectX::XMFLOAT2& texture_size,
                        const Transform& transform,
                        const DirectX::XMFLOAT4& clip_mask) override;

  void DrawY8Texture(Texture& texture, const Transform& transform) override;

  void DrawLines(Lines& lines) override;
  bool DrawLinesPrepare(Lines& lines) override;
  void DrawLinesDraw(Lines& lines) override;
  void DrawVLine(const XMFLOAT4& line,
                 const XMFLOAT4& color,
                 const float thinkness) override;
  void DrawVDottedLine(const XMFLOAT4& line,
                       const XMFLOAT4& color,
                       const float thinkness) override;
  void DrawHLine(const XMFLOAT4& line,
                 const XMFLOAT4& color,
                 const float thinkness) override;
  void DrawHDottedLine(const XMFLOAT4& line,
                       const XMFLOAT4& color,
                       const float thinkness) override;
  void DrawRectangle(Rectangle& rectangle) override;
  bool DrawRectanglePrepare(Rectangle& rectangle) override;
  void DrawGradualRectangle(GradualRectangle& conf) override; // Added for GradualRectangle
  void DrawRectangleDraw(Rectangle& rectangle) override;
  void DrawRectangles(Rectangle** rectangle, int cnt) override;
  void DrawYUVColorRectangle(YUVRectangle& rectangle) override;
  bool DrawGradualRectanglePrepare(GradualRectangle& rectangle) override;
  void DrawGradualRectangleDraw(GradualRectangle& rectangle) override;


  bool DrawRectanglesPrepare(Rectangle** rectangle, int cnt) override;
  void DrawRectanglesDraw(Rectangle** rectangle, int cnt) override;

  void DrawLutTexture(Texture& texture,
      Texture* texture_1dlut,
      Texture* texture_3dlut,
      const XMFLOAT4& domain_min,
      const XMFLOAT4& domain_max,
      const Transform& transform,
      float amount,
      bool pass_through_alpha) override;

  void EndDraw() override;

  XMFLOAT2 GetSize() override;
  bool IsEmpty() override;
  std::shared_ptr<Texture> GetOutputTexture() override;
  std::shared_ptr<Texture> MoveOutputTexture() override;

  Device& GetDevice() override;
  ~GraphicsImpl() override;
  void Destroy() override;

 protected:
  bool CreateNewGraphics(DXGI_FORMAT format, int cx, int cy);
  bool CreateSharedGraphics(DXGI_FORMAT format,
                            int cx,
                            int cy,
                            D3D11_RESOURCE_MISC_FLAG flag);

  ID3D11Device* GetDevice_();
  ID3D11DeviceContext* GetContext_();
  ShaderManager* GetShaderManager_();

 private:
  void DrawBGRATextureInternal(Texture& texture,
                               DXGI_FORMAT format,
                               const Transform& transform,
                               bool force_tex_alpha = false,
                               bool tone_mapping = true,
                               bool sample_anisotropic = false);

  void DrawBGRATextureLuminanceMapParamsInternal(Texture& texture,
                                               DXGI_FORMAT format,
                                               const Transform& transform,
                                               const LuminanceMapParams& luminance_map_params);

  void DrawBGRAChromaKeyParamsTextureInternal(
      Texture& texture,
      DXGI_FORMAT format,
      const Transform& transform,
      const ChromaKeyParams& chroma_key_params);

      void DrawPartialScaleTextureInternal(
       Texture& texture,
       const Transform& transform,
       const PartialScaleParams& params
      );

  void DrawBGRAColorParamsTextureInternal(Texture& texture,
                                          DXGI_FORMAT format,
                                          const Transform& transform,
                                          const ColorParams& color_params);

  void DrawBGRASharpnessParamsTextureInternal(
      Texture& texture,
      DXGI_FORMAT format,
      const Transform& transform,
      const SharpnessParams& sharpness_params);

  void DrawBGRAPointNineTextureInternal(Texture& texture,
                                        DXGI_FORMAT format,
                                        const Transform& transform,
                                        const PointNine& point_nine);

  void DrawCornerTextureInternal(Texture& texture,
                                 const XMFLOAT4& corner_radius,
                                 const float& ratio,
                                 const Transform& transform,
                                 bool refer_width,
                                 bool fixed_radius);

  void DrawBorderTextureInternal(Texture& texture,
                                 const XMFLOAT4& color,
                                 const XMFLOAT2& width,
                                 const float& ratio,
                                 const Transform& transform);

  void DrawBGRAClipMaskInternal(const void* handle,
                                const DirectX::XMFLOAT2& texture_size,
                                const Transform& transform,
                                const DirectX::XMFLOAT4& clip_mask);

  void DrawLutTextureInternal(Texture& texture,
      Texture* texture_1dlut,
      Texture* texture_3dlut,
      const XMFLOAT4& domain_min,
      const XMFLOAT4& domain_max,
      const Transform& transform,
      float amount,
      bool pass_through_alpha);

  XMMATRIX GetLineView();

  const XMMATRIX& GetViewMatrix();

  const XMMATRIX& GetProjectionMatrix();

  void ClearBufferedBuffer();

  void DestroyBuffer();

  BufferedTextureBuffer* GetBufferedBuffer(
      Texture* texture,
      const Transform* trans,
      const PointNine& point_nine,
      const CornerBuffer& corner_buffer,
      const BorderBuffer& border_buffer,
      const DirectX::XMFLOAT4& clip_mask_param,
      bool force_tex_alpha = false,
      const ColorParams& color_params = {},
      const ChromaKeyParams& chroma_key_params = {},
      const SharpnessParams& sharpness_params = {},
      const ColorLutParams& color_lut_params = {},
      const LuminanceMapParams& luminance_map_params = {},
      const PartialScaleParams& params = {});

  BufferedTextureBuffer* GetNoTextureBufferedBuffer(
      const void* handle,
      const DirectX::XMFLOAT2& texture_size,
      const Transform* trans,
      const PointNine& point_nine,
      const CornerBuffer& corner_buffer,
      const BorderBuffer& border_buffer,
      const DirectX::XMFLOAT4& clip_mask_param);

  BufferedTextureBuffer* GetLutBufferedBuffer(
      Texture* texture,
      const Transform* trans,
      const ColorLutParams& color_lut_params = {});

      BufferedTextureBuffer* GetScaleBufferedBuffer(
        Texture* texture,
        const Transform* trans,
        const PartialScaleParams& scale_params = {});

  void ReleaseBufferedBuffer(Texture*);

  std::unique_ptr<BufferedTextureBuffer> CreateBufferedBuffer();
  void DestroyExcludeOutputTexture();

  std::vector<XMFLOAT4> GetDottedLines(const XMFLOAT4& line);
  bool DottedLineFinished(const XMFLOAT2& start,
                          const XMFLOAT2& end,
                          const XMFLOAT2& cur);

 protected:
  Device& device_;
  ID3D11DeviceContext* context_;
  std::shared_ptr<Texture> output_texture_;  // for output

  Microsoft::WRL::ComPtr<ID3D11Texture2D> render_2d_;           // for draw
  Microsoft::WRL::ComPtr<ID3D11Texture2D> depth_2d_;            // for draw
  Microsoft::WRL::ComPtr<ID3D11DepthStencilView> depth_view_;   // for draw
  Microsoft::WRL::ComPtr<ID3D11RenderTargetView> render_view_;  // for draw

  Microsoft::WRL::ComPtr<ID3D11DepthStencilView>
      previous_dsv_;  // for resume last draw target
  Microsoft::WRL::ComPtr<ID3D11RenderTargetView>
      previous_rtv_;                  // for resume last draw target
  XMFLOAT2 previous_view_port_[2]{};  // for restore previous viewport

  XMMATRIX up_view_matrix_ = XMMatrixIdentity();
  XMMATRIX view_matrix_ = XMMatrixIdentity();
  XMMATRIX projection_matrix_ = XMMatrixIdentity();

  XMMATRIX graphics_world_matrix_ = XMMatrixIdentity();

  std::unordered_map<const void*, std::unique_ptr<BufferedTextureBuffer>>
      buffered_texture_buffers_;
};

}  // namespace graphics
