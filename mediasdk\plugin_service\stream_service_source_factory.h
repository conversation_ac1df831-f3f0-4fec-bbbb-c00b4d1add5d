
#pragma once

#include <memory>
#include <set>
#include "mediasdk/public/mediasdk_defines.h"
#include "source_factory.h"

namespace mediasdk {
struct PluginInfo;

class StreamServiceSource;

class StreamServiceProxy;

class StreamServiceSourceFactory : public SourceFactory {
 public:
  static std::shared_ptr<StreamServiceSourceFactory> Create(
      base::NativeLibrary library,
      std::shared_ptr<PluginInfo> info);

  StreamServiceSourceFactory(base::NativeLibrary library,
                             std::shared_ptr<PluginInfo> info);

  // SourceFactory:
  PluginType GetType() const override { return PluginType::kService; }

  std::shared_ptr<StreamServiceSource> CreateSource(
      std::shared_ptr<StreamServiceProxy> proxy,
      const std::string& json_params);

  void Destroy(std::shared_ptr<StreamServiceSource> source);

  void DestroyAll();

  std::shared_ptr<PluginInfo> GetInfo() { return info_; }

 private:
  bool Load();

 private:
  typedef StreamServiceSource* (
      *CreateStreamServiceSourceFunc)(StreamServiceProxy* proxy, const char*);
  typedef void (*DestroyStreamServiceSource)(StreamServiceSource*);

  CreateStreamServiceSourceFunc create_func_ = nullptr;
  DestroyStreamServiceSource destroy_func_ = nullptr;

  std::set<std::shared_ptr<StreamServiceSource>> sources_;
};

}  // namespace mediasdk
