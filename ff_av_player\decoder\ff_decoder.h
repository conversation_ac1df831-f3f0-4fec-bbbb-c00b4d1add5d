#pragma once
#include <base/threading/thread.h>
#include <atomic>
#include <memory>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavcodec/packet.h>
#include <libavformat/avformat.h>
#include <libavutil/frame.h>
}

#include <ff_av_player/av_packet_list.h>
#include "ffmpeg_common.h"
#include "task_filter.h"

class FFDecoder {
 public:
  static AVPacket* BuildPacketFromMemory(
      std::shared_ptr<AVPacketRAIIFree>& buffered_packet,
      const uint8_t* data,
      int32_t data_size,
      bool fake_buffer = false);

  class DecoderObserver {
   public:
    virtual ~DecoderObserver() = default;
    virtual void OnFrame(FFDecoder*, AVFrame& frame) = 0;
    virtual void OnFlushSuccess(FFDecoder*) = 0;
    virtual void OnNeedMoreData(FFDecoder*) = 0;
    virtual void OnDecoderEOF(FFDecoder*) = 0;
    virtual void OnDecodeOpened(FFDecoder*, bool) = 0;
  };

  ~FFDecoder();
  bool Open(AVCodecID id, const std::vector<uint8_t>& extra = {});
  bool Open(AVFormatContext* context,
            int index,
            bool hardware = false,
            bool pure_hw = false,
            const char* device_id = nullptr);

  void AsnDecode(const base::Thread& thread, const AVPacket& packet);
  void AsnFlushForEnd(const base::Thread& thread);
  void AsnDecode(const base::Thread& thread);
  void AsnGetFrame(const base::Thread& thread);

  void Close(base::Thread*);

  void AddObserver(DecoderObserver*);
  void RemoveObserver(DecoderObserver*);

  int32_t GetPendingSendPacket() const;
  int32_t GetPendingReceivePacket() const;

  void ClearAndFlushPendingPacket(const base::Thread&);

  AVPixelFormat GetHWPixelFormat() const;

  bool Receive(AVFrame& frame);

  bool SynDecode(const AVPacket& packet, bool& retry);

  void SetDisablePostDriverTask(bool disable) {
    disable_post_driver_task_ = disable;
  }

 protected:
  bool ReceiveInternal(AVFrame& frame);
  bool SynDecodeInternal(const AVPacket& packet, bool& retry);
  void SynGetFrame();
  // flush last input packet, we can call this before close or after seek
  void Flush(const base::Thread& thread);
  void DecodeTask();

 protected:
  AVCodecContext* codec_context_ = nullptr;
  const AVCodec* codec_ = nullptr;
  std::vector<DecoderObserver*> obs_list_;
  std::atomic_bool stop_ = false;
  AVPacketItemList packet_list_;
  std::atomic_bool flushing_ = {false};
  AVBufferRef* hw_context_ = {nullptr};
  AVPixelFormat hw_pixel_format_ = AVPixelFormat::AV_PIX_FMT_NONE;

  int64_t pre_log_ms_ = 0;
  mediasdk::TaskFilter task_filter_;
  bool disable_post_driver_task_ = false;
};