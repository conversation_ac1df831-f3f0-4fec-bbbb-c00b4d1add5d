﻿#include "adapter.h"

#include <dxgi.h>
#include <wrl/client.h>
#include "base/logging.h"
#include "base/strings/utf_string_conversions.h"

using namespace Microsoft::WRL;

namespace graphics {

bool GetFirstAdapterInfo(AdapterInfo* info) {
  if (!info) {
    LOG(ERROR) << "Invalid input params info.";
    return false;
  }

  ComPtr<IDXGIFactory1> factory1;
  HRESULT hr = CreateDXGIFactory1(__uuidof(IDXGIFactory1), (void**)(&factory1));
  if (FAILED(hr)) {
    LOG(ERROR) << "Failed to call CreateDXGIFactory1, ret: " << std::hex << hr;
    return false;
  }

  int index = 0;

  ComPtr<IDXGIAdapter1> adapter;
  hr = factory1->EnumAdapters1(index, &adapter);
  if (FAILED(hr)) {
    LOG(ERROR) << "Failed to call EnumAdapters1, ret: " << std::hex << hr;
    return false;
  }

  DXGI_ADAPTER_DESC1 desc;
  hr = adapter->GetDesc1(&desc);
  if (FAILED(hr)) {
    LOG(ERROR) << "Failed to call GetDesc1, ret: " << std::hex << hr;
    return false;
  }

  info->adapter_id.low = desc.AdapterLuid.LowPart;
  info->adapter_id.high = desc.AdapterLuid.HighPart;
  info->vendor_id = desc.VendorId;
  info->device_id = desc.DeviceId;
  wcscpy_s(info->adapter_name, 127, desc.Description);
  LOG(INFO) << "Success to get first adapter info, desc: "
            << base::WideToUTF8(desc.Description);
  return true;
}

bool GetFirstNoBasicRenderAdapterInfo(AdapterInfo* info) {
  if (!info) {
    LOG(ERROR) << "Invalid input params info.";
    return false;
  }

  ComPtr<IDXGIFactory1> factory1;
  HRESULT hr = CreateDXGIFactory1(__uuidof(IDXGIFactory1), (void**)(&factory1));
  if (FAILED(hr)) {
    LOG(ERROR) << "Failed to call CreateDXGIFactory1, ret: " << std::hex << hr;
    return false;
  }

  int index = 0;

  while (1) {
    ComPtr<IDXGIAdapter1> adapter;
    hr = factory1->EnumAdapters1(index++, &adapter);
    if (FAILED(hr)) {
      LOG(ERROR) << "Failed to call EnumAdapters1, ret: " << std::hex << hr;
      return false;
    }

    DXGI_ADAPTER_DESC1 desc;
    hr = adapter->GetDesc1(&desc);
    if (FAILED(hr)) {
      LOG(ERROR) << "Failed to call GetDesc1, ret: " << std::hex << hr;
      return false;
    }
    if (desc.VendorId == 0x1414 && desc.DeviceId == 0x8c) {
      continue;
    }
    info->adapter_id.low = desc.AdapterLuid.LowPart;
    info->adapter_id.high = desc.AdapterLuid.HighPart;
    info->vendor_id = desc.VendorId;
    info->device_id = desc.DeviceId;
    wcscpy_s(info->adapter_name, 127, desc.Description);
    LOG(INFO) << "Success to get first no basic render adapter info, desc: "
              << base::WideToUTF8(desc.Description);
    return true;
  }
}

}  // namespace graphics