#pragma once

#include <mutex>
#include "frame_dropper.h"
#include "graphics/graphics.h"
#include "video/video_texture.h"
#include "video_encode_intput_adaptive_cache_queue.h"
#include "video_encoder_adaptive_director.h"

namespace mediasdk {

class VideoEncodeInputAdaptive;

class VideoEncodeInputAdaptiveLock {
 public:
  VideoEncodeInputAdaptiveLock(VideoEncodeInputAdaptive* ptr);

  ~VideoEncodeInputAdaptiveLock();

 private:
  VideoEncodeInputAdaptive* ptr_ = nullptr;
};

class VideoEncodeInputAdaptiveDelegate {
 public:
  virtual void OnAdaptiveTexture(const VideoTexture& texture) = 0;
};

class VideoEncodeInputAdaptive {
 public:
  VideoEncodeInputAdaptive(VideoEncodeAdaptiveConfig config,
                           VideoEncodeInputAdaptiveDelegate* delegate);

  ~VideoEncodeInputAdaptive();

  void InputTexture(const VideoTexture& texture);

  std::unique_ptr<VideoEncodeInputAdaptiveLock> Lock();

  void UpdateConfig(const VideoEncodeAdaptiveConfig& config);

  VideoEncodeAdaptiveConfig GetConfig();

 private:
  bool SmoothFrameDropping(const int64_t timestamp_ns);

  bool NeedScale(graphics::Texture* texture);

  std::shared_ptr<graphics::Texture> AdaptiveTexture(
      graphics::Texture* texture);

  void Input(const VideoTexture& texture);

 private:
  friend class VideoEncodeInputAdaptiveLock;
  VideoEncodeAdaptiveConfig config_ = {};
  VideoEncodeInputAdaptiveDelegate* delegate_ = nullptr;
  std::recursive_mutex mutex_;
  std::shared_ptr<graphics::Graphics> scale_graphics_;
  FrameDropper output_dropper_{};
  uint32_t init_fps_ = 0;
  VideoEncodeInputAdaptiveCacheQueue cache_queue_;
};

}  // namespace mediasdk
