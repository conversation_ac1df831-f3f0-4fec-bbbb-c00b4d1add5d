
#pragma once

#include <base/native_library.h>
#include <memory>
#include "base/memory/weak_ptr.h"
#include "mediasdk/public/plugin/plugin_defines.h"
#include "source_factory.h"

namespace mediasdk {

class VideoEncoderSource;

class VideoEncoderProxy;

class VideoEncoderSourceFactory
    : public SourceFactory,
      public base::SupportsWeakPtr<VideoEncoderSourceFactory> {
 public:
  static std::shared_ptr<VideoEncoderSourceFactory> Create(
      base::NativeLibrary library,
      std::shared_ptr<PluginInfo> info,
      PluginGlobalProxy* plugin_global_proxy);

  VideoEncoderSourceFactory(base::NativeLibrary library,
                            std::shared_ptr<PluginInfo> info,
                            PluginGlobalProxy* plugin_global_proxy);

  ~VideoEncoderSourceFactory();

  // SourceFactory:
  PluginType GetType() const override { return PluginType::kVideoEncoder; }

  std::shared_ptr<VideoEncoderSource> CreateSource(
      VideoEncoderProxy* proxy,
      const std::string& json_params);

  bool TestEncoderSessionCountSupported(uint32_t count);

 private:
  bool Load();

  void Destroy(VideoEncoderSource* source);

 private:
  typedef VideoEncoderSource* (
      *CreateVideoEncoderSourceFunc)(VideoEncoderProxy*, const char*);
  typedef void (*DestroyVideoEncoderSourceFunc)(VideoEncoderSource*);
  typedef bool (*TestEncoderSessionCountSupportedFunc)(uint32_t,
                                                       PluginGlobalProxy*);

 private:
  CreateVideoEncoderSourceFunc create_func_ = nullptr;
  DestroyVideoEncoderSourceFunc destroy_func_ = nullptr;
  TestEncoderSessionCountSupportedFunc
      test_encoder_session_count_support_func_ = nullptr;

  PluginGlobalProxy* plugin_global_proxy_ = nullptr;
};

}  // namespace mediasdk
