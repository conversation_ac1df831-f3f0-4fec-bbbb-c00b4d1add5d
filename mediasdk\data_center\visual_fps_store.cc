#include "visual_fps_store.h"

#include "component_center.h"
#include "data_center/data_center.h"

namespace mediasdk {

VisualFpsStoreCalc::VisualFpsStoreCalc(const std::string& visual_id)
    : visual_id_(visual_id) {
  auto* dc = com::GetDataCenter();
  if (dc) {
    dc->GetVisualFpsStore().RegisterVisualFpsCalc(this);
  }
}

VisualFpsStoreCalc::~VisualFpsStoreCalc() {
  auto* dc = com::GetDataCenter();
  if (dc) {
    dc->GetVisualFpsStore().UnregisterVisualFpsCalc(this);
  }
}

VisualFpsStore::VisualFpsStore() {}

VisualFpsStore::~VisualFpsStore() {}

void VisualFpsStore::RegisterVisualFpsCalc(VisualFpsStoreCalc* calc) {
  std::lock_guard<std::mutex> lock(calc_map_mtx_);
  if (calc) {
    calc_map_[calc->GetVisualId()] = calc;
  }
}

void VisualFpsStore::UnregisterVisualFpsCalc(VisualFpsStoreCalc* calc) {
  std::lock_guard<std::mutex> lock(calc_map_mtx_);
  if (calc) {
    calc_map_.erase(calc->GetVisualId());
  }
}

std::pair<bool, float> VisualFpsStore::GetVisualFps(
    const std::string& visual_id) {
  std::lock_guard<std::mutex> lock(calc_map_mtx_);
  auto it = calc_map_.find(visual_id);
  if (it != calc_map_.end() && it->second) {
    return {true, it->second->CalcFps()};
  }
  // Return false if the visual ID is not found
  return {false, 0.0f};
}

}  // namespace mediasdk