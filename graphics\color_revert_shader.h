#pragma once

#include <mutex>
#include "shader.h"

namespace graphics {

class ColorRevertShader : public Shader {
 public:
  struct PSParam {
    float revert_r;
    float revert_g;
    float revert_b;
    float revert_a;
  };

  static inline const char* SHADER_ID_STRING = "color_revert_shader";

  static std::shared_ptr<Shader> CreateTextureShader(const char* type) {
    if (strcmp(type, SHADER_ID_STRING) == 0) {
      return std::make_shared<ColorRevertShader>();
    }
    return nullptr;
  }

  static void EnumPluginsFunction(void* param, EnumPluginCallBack fpn) {
    fpn(param,
        ShaderItem{SHADER_ID_STRING, "shader for draw texture to graphics",
                   ColorRevertShader::CreateTextureShader});
  }

 public:
  bool Init(const std::shared_ptr<Device>&) override;

  void RenderTexture(ID3D11ShaderResourceView* pSRView,
                     Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_param);
  void Destroy() override;
  ~ColorRevertShader() override;

 private:
  bool _Init(const std::shared_ptr<Device>& ins);
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice_();
  ID3D11DeviceContext* GetContext_();

 private:
  bool try_init_ = false;
  bool init_suc_ = false;
  std::shared_ptr<Device> device_;

  Microsoft::WRL::ComPtr<ID3D11VertexShader> vs_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11SamplerState> sampler_;
};

}  // namespace graphics
