#include "ff_swsscale.h"
#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include "ffmpeg_common.h"
extern "C" {
#include <libavutil/hwcontext.h>
}

FFSWSScale::~FFSWSScale() {
  if (swscontext_) {
    sws_freeContext(swscontext_);
    swscontext_ = nullptr;
  }
}

bool FFSWSScale::HWFrameToFrame(
    AVFrame*& frame,
    AVPixelFormat target_format /*= AVPixelFormat::AV_PIX_FMT_NONE*/) {
  if (target_format != AVPixelFormat::AV_PIX_FMT_NONE) {
    return HWFrameToFrameToTarget(frame, target_format);
  } else {
    return HWFrameToFrameDefault(frame);
  }
}

bool FFSWSScale::FrameScaleByFFmpeg(AVFrame*& frame, AVPixelFormat format) {
  if ((AVPixelFormat)frame->format == format) {
    return true;
  }
  // scale by sws scale
  if (!swscontext_) {
    int32_t space = MS_AV_CS_TO_SWS_CS(frame->colorspace);
    int32_t range = frame->color_range == AVCOL_RANGE_JPEG ? 1 : 0;
    const int* coeff = sws_getCoefficients(space);
    swscontext_ = sws_getCachedContext(
        NULL, frame->width, frame->height, (AVPixelFormat)frame->format,
        frame->width, frame->height, format, SWS_POINT, NULL, NULL, NULL);
    if (!swscontext_) {
      LOG(ERROR) << base::StringPrintf("Failed to sws_getCachedContext");
      return false;
    }

#define FIXED_1_0 (1 << 16)
    int ret = sws_setColorspaceDetails(swscontext_, coeff, range, coeff, range,
                                       0, FIXED_1_0, FIXED_1_0);
    if (ret < 0) {
      LOG(ERROR) << base::StringPrintf("Failed to sws_setColorspaceDetails[")
                 << FFErrorToString(ret);
      return false;
    }
  }

  if (!swscontext_) {
    return false;
  }
  AVFrame* new_frame = av_frame_alloc();

  if (!new_frame) {
    LOG(ERROR) << base::StringPrintf("Failed to av_frame_alloc");
    return false;
  }
  new_frame->format = format;
  new_frame->width = frame->width;
  new_frame->height = frame->height;
  new_frame->pts = frame->pts;
  new_frame->pkt_dts = frame->pkt_dts;

  int ret = av_frame_get_buffer(new_frame, 32);
  if (ret < 0) {
    LOG(ERROR) << base::StringPrintf("Failed to av_frame_get_buffer[")
               << FFErrorToString(ret);
    av_frame_free(&new_frame);
    return false;
  }

  ret = sws_scale(swscontext_, (const uint8_t* const*)frame->data,
                  frame->linesize, 0, frame->height, new_frame->data,
                  new_frame->linesize);
  if (ret < 0) {
    LOG(ERROR) << base::StringPrintf("Failed to sws_scale");
    return false;
  }
  av_frame_free(&frame);
  frame = new_frame;

  return true;
}

bool FFSWSScale::HWFrameToFrameToTarget(AVFrame*& frame, AVPixelFormat format) {
  if (!HWFrameToFrameDefault(frame))
    return false;
  return FrameScaleByFFmpeg(frame, format);
}

bool FFSWSScale::HWFrameToFrameDefault(AVFrame*& frame) {
  AVFrame* output = av_frame_alloc();
  if (!output) {
    return false;
  }
  AVFrameRAIIReference free_outout(output);

  int err = av_hwframe_transfer_data(output, frame, 0);
  if (err < 0) {
    LOG(ERROR) << "Failed to av_hwframe_transfer_data [" << FFErrorToString(err)
               << "]";
    return false;
  }

  err = av_frame_copy_props(output, frame);
  if (err < 0) {
    LOG(ERROR) << "Failed to av_frame_copy_props [" << FFErrorToString(err)
               << "]";
    return false;
  }

  av_frame_unref(frame);
  av_frame_move_ref(frame, output);
  return true;
}
