#pragma once

#include "graphics_export.h"
#include "mediasdk/public/mediasdk_defines_visual.h"

namespace graphics {

class GRAPHICS_EXPORT ColorLutParams {
 public:
  struct PixelSize {
    float x;
    float y;
  };

  ColorLutParams() = default;

  ColorLutParams(const ColorLutParams& other);

  ColorLutParams& operator=(const ColorLutParams& other);

  bool operator==(const ColorLutParams& other) const;

  bool operator!=(const ColorLutParams& other) const;

  bool IsEmpty() const;

  void Reset();

  void SetAmount(float amount);
    
const float GetAmount() const;

void SetDomainMax(const std::vector<float>& domain_max);
std::vector<float>  GetDomainMax() const;

void SetDomainMin(const std::vector<float>& domain_min);
std::vector<float> GetDomainMin() const;

void SetPassThroughAlpha(bool pass_through_alpha);

bool GetPassThroughAlpha() const;

 private:
  void Assign(const ColorLutParams& other);

  float amount_ = 0.0f;
  std::vector<float> domain_min_;
  std::vector<float> domain_max_;

  bool pass_through_alpha_ = false;

};

}  // namespace graphics