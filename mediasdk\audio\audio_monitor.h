#pragma once

#include <audio/audio_frame.h>
#include <wasapi_device.h>
#include "audio/audio_format.h"

namespace mediasdk {

class AudioMonitor {
 public:
  virtual bool Open(mediasdk::AudioFormat format,
                    const WASAPIDevice::DeviceName& dev) = 0;

  virtual void Close() = 0;

  virtual bool Play(const mediasdk::AudioFrame& data, bool mute = false) = 0;

  virtual void SignalStop() = 0;

  virtual std::wstring GetCurrentDevice() = 0;

  virtual ~AudioMonitor() = default;
};

std::shared_ptr<AudioMonitor> CreateAudioMonitor();

}  // namespace mediasdk
