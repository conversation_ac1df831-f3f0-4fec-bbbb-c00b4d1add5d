#pragma once
#include "mediasdk/public/mediasdk_defines.h"

namespace mediasdk {

// Global plugin proxy of MediaSDK. Plugin can use this proxy to access some
// capabilities and resources of MediaSDK.
class PluginGlobalProxy {
 public:
  virtual ~PluginGlobalProxy() = default;

  // Retrieve the specified graphics card information used by the MediaSDK video
  // system
  virtual MSAdapterInfo GetCurrentAdapterInfo() = 0;

  virtual int32_t GetAllAdapterInfoCnt() = 0;

  virtual MSAdapterInfo GetAdapterInfo(int32_t index) = 0;

  // Used for global notification. Events that are actively generated within the
  // plugin and are not related to specific plugin object can be notified to the
  // upper layer through this interface. MediaSDK will pass it to
  // MediaSDKGlobalEventObserver::OnPluginGlobalEvent
  virtual void NotifyGlobalEvent(PluginInfo info,
                                 MediaSDKString notify_event) = 0;
};

}  // namespace mediasdk
