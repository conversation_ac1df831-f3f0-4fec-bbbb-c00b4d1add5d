﻿#include "display.h"
#include "display_impl.h"

#include "display_flip_alpha_impl.h"
#include "display_flip_impl.h"

namespace graphics {

std::shared_ptr<Display> CreateDisplay2D(Device& ins,
                                         HWND hwnd,
                                         int cx,
                                         int cy,
                                         DisplayModeOpt mode) {
  switch (mode.mode) {
    case DISPLAY_MODE::DISPLAY_MODE_BITBLT: {
      return DisplayImpl::CreateDisplay2D(ins, hwnd, cx, cy);
      break;
    }
    case DISPLAY_MODE::DISPLAY_MODE_FLIP: {
      return DisplayImplFlipMode::CreateDisplay2D(ins, hwnd, cx, cy);
      break;
    }
    case DISPLAY_MODE::DISPLAY_MODE_FLIP_WAITABLE: {
      break;
    }
    case DISPLAY_MODE::DISPLAY_MODE_FLIP_ALPHA: {
      return DisplayImplFlipModeAlpha::CreateDisplay2D(ins, hwnd, cx, cy);
      break;
    }

    default:
      break;
  }
  return nullptr;
}

}  // namespace graphics