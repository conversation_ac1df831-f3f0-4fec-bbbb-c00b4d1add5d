#pragma once

#include <nlohmann/json.hpp>
#include <optional>

namespace mediasdk {

enum class SDKInitResult {
  kSuccess = 0,
  kAdapterError = 1,
  kComponentError = 2,
};

enum class SDKInitType {
  kAllComponent = 0,
  kDataCenter = 1,
  kNotifyCenter = 2,
  kEffectPlatform = 3,
  kPluginService = 4,
  kAudioController = 5,
  kVideoController = 6,
  kStreamController = 7,
  kRtcController = 8,
  kPluginLoad = 100,
};

enum class AdaptiveStatus {
  kNormal = 0,
  kUpNormal = 1,
  kDownNormal = 2,
  kUpAbnormal = 3,
  kDownAbnormal = 4
};

/**
 * @brief data struct for event tracking
 * @note make sure all field names are consistent with the backend
 */
namespace event_tracking_data {
struct BwProbe {
  std::string bw_type = "result";
  int bw_probe_state = 0;
  int bw_probe_fail_reason = 0;
  int bw_probe_result = 0;
  int real_bitrate = 0;
  int bw_probe_real_protocol = 0;
  std::string message;
  int bw_rtt_avg = 0;
  int bw_bwe_avg = 0;
  int bw_total_time = 0;
  int bw_send_packet_time = 0;
  int bw_dns_time = 0;
  int bw_socket_connect_time = 0;
  int bw_rtmp_connect_time = 0;
};

/**
 * @brief common field for stream event tracking
 * @note not a real event tracking.
 */
struct StreamField {
  std::string url;
  std::string push_url;

  std::string stream_id;
  /**
   * @note should be "stream_%{stream_id}"
   */
  std::string stream_name;

  std::string room_id;

  std::string dns_ip;
  int dns_parse_time = 0;
  std::string cdn_ip;
  std::string connection_type;
  int connect_elapse = 0;

  std::string push_protocol;

  /**
   * @note valid value: h264 / h265
   */
  std::string video_codec;
  std::string codec_name;
  /**
   * @note true if codec_name is
   * ByteVC0VideoEncoderSource/ByteVC1VideoEncoderSource
   */
  int hardware = 0;
};

/**
 * @brief session field for stream event tracking
 * @note not a real event tracking.
 */
struct StreamSessionField {
  std::string push_session_id;
  std::string connect_session_id;
};

/**
 * @brief transport field for stream event tracking
 * @note not a real event tracking.
 */
struct TransportField {
  int transport_pts_send_audio_stall_count = 0;
  int transport_pts_send_audio_stall_time = 0;
  int transport_pts_send_video_stall_count = 0;
  int transport_pts_send_video_stall_time = 0;

  int transport_send_audio_stall_count = 0;
  int transport_send_audio_stall_time = 0;
  int transport_send_video_stall_count = 0;
  int transport_send_video_stall_time = 0;

  int transport_reconnect_count = 0;
  int transport_reconnect_time = 0;
};

/**
 * @brief net status field for stream event tracking
 * @note not a real event tracking.
 */
struct NetStatusField {
  int bwe = 0;
  int quic_bw = 0;
  int rtt = 0;
  int quic_rtt = 0;
  float loss_rate = 0.0f;
  float quic_loss = 0.0f;
};

/**
 * @brief cpu info field for stream event tracking
 * @note not a real event tracking.
 */
struct LegacyCpuField {
  std::string cpu_soc;
  double cpu_usage = 0.0;
  double cpu = 0.0;
  double cpu_usage_total = 0.0;
  double cpu_total = 0.0;
  double cpu_usage_time = 0.0;
  double cpu_time = 0.0;
  static void FillField(LegacyCpuField& field);
};

/**
 * @brief gpu info field for stream event tracking
 * @note not a real event tracking.
 */
struct LegacyGpuField {
  std::optional<std::string> gpu_name;
  std::optional<double> gpu_3D_usage = 0.0;
  std::optional<double> gpu_3D_total_usage = 0.0;
  std::optional<double> gpu_memory = 0.0;
  std::optional<double> gpu_total_memory = 0.0;
  std::optional<double> gpu_dedicate_mem = 0.0;

  std::optional<std::string> integrated_gpu_name;
  std::optional<double> integrated_gpu_3D_usage = std::nullopt;
  std::optional<double> integrated_gpu_3D_total_usage = std::nullopt;
  std::optional<double> integrated_gpu_memory = std::nullopt;
  std::optional<double> integrated_gpu_total_memory = std::nullopt;
  std::optional<double> integrated_gpu_dedicate_mem = std::nullopt;
  static void FillField(LegacyGpuField& field);
};

struct LegacyVisualFpsField {
  std::optional<float> in_cap_fps = std::nullopt;
  std::optional<float> in_window_fps = std::nullopt;
  std::optional<float> in_monitor_fps = std::nullopt;
  std::optional<float> in_cast_fps = std::nullopt;
  std::optional<float> in_game_fps = std::nullopt;
  static void FillField(LegacyVisualFpsField& field);
};

struct VisualFpsField {
  std::vector<float> cap_fps;
  std::vector<float> window_fps;
  std::vector<float> monitor_fps;
  std::vector<float> cast_fps;
  std::vector<float> game_fps;
  static void FillField(VisualFpsField& field);
};

struct StartPush : StreamField, StreamSessionField {
  std::string status = "push";
};

struct StopPush : StreamField, StreamSessionField {
  int64_t push_duration;
  int disconnect_count;
  int is_connecting;
  int64_t disconnect_elapse;
  int error_code;
  std::optional<std::string> abr_strategy;
  std::optional<std::string> abr_switch_info;
  std::optional<int> abr_switch_count;
};

struct FirstFramePreEncode : StreamField, StreamSessionField {
  std::string media_type;
};

struct FirstFrameEncode : StreamField, StreamSessionField {
  std::string media_type;
  bool result;
};

struct FirstFrameSend : StreamField, StreamSessionField {
  std::string media_type;
  bool result;
};

struct ConnectStart : StreamField {
  int error_code = 200;

  int default_bitrate = 0;
  int min_bitrate = 0;
  int max_bitrate = 0;
  int min_video_bitrate = 0;
  int max_video_bitrate = 0;
  int gop = 0;

  bool first_connect = true;
  int reconnect_count = 0;
  int error_count = 0;

  bool connect_status = true;
};

struct ConnectStartConnection : StreamField, StreamSessionField {
  int error_code = 200;

  int default_bitrate = 0;
  int min_bitrate = 0;
  int max_bitrate = 0;
  int min_video_bitrate = 0;
  int max_video_bitrate = 0;
  int gop = 0;

  bool first_connect = true;
  int reconnect_count = 0;
  int error_count = 0;

  int64_t elapse = 0;
  std::string status = "push";
  std::string reason = "0";
};

struct ConnectEnd : StreamField, NetStatusField, TransportField {
  int error_code = 200;

  int reconnect_count = 0;
  int rate_adjust_times = 0;
  int send_package_slow_times = 0;
};

struct ConnectEndConnection : StreamField, StreamSessionField {
  int error_code = 200;

  std::string next_connect_session_id;
  int64_t push_duration = 0;  // TODO connect_start to connect_end
  std::string status = "push";
  std::string reason = "0";
};

struct PushStreamFail : StreamField, NetStatusField {
  int error_code = 200;
  std::string error_msg;
};

struct PushStream : StreamField,
                    NetStatusField,
                    TransportField,
                    LegacyCpuField,
                    LegacyGpuField,
                    LegacyVisualFpsField,
                    StreamSessionField {
  float duration = 0.0f;
  int rtmp_buffer_time = 0;

  int audio_bitrate = 0;
  int audio_enc_bitrate = 0;
  int meta_audio_bitrate = 0;

  int default_bitrate = 0;
  int min_bitrate = 0;
  int max_bitrate = 0;
  int min_video_bitrate = 0;
  int max_video_bitrate = 0;
  int meta_video_bitrate = 0;
  int i_key_frame_max = 0;

  double encode_fps = 0.0;
  double encode_error_count = 0.0;
  int encoder_process_time = 0;

  std::string video_profile;
  int width = 0;
  int height = 0;

  std::string audio_profile = "LC";
  int audio_channel = 0;
  int audio_sample_rate = 0;
  bool is_mute = false;
  int is_link_mic = 0;

  int preview_fps = 0;
  float out_cap_fps = 0.0f;
  int meta_video_framerate = 0;

  double real_video_framerate = 0.0;
  double real_bitrate = 0.0;
  int64_t drop_count = 0;
  int package_delay = 0;

  int physmem_size = 0;
  int physmem_used = 0;
  int physmem_used_total = 0;
  int physmem_usage_total = 0;

  int64_t model0_map = 0;
  int64_t model0_present_cost = 0;
  int64_t model1_map = 0;
  int64_t model1_present_cost = 0;

  std::map<std::string, int64_t> anchor_video_delay;

  double video_enc_bitrate = 0;
  double sei_birate = 0;

  // Encoder input frame rate, unit: fps
  int frame_rate_input = 0;
  // Encoder output frame rate, unit: fps
  int frame_rate_sent = 0;

  std::optional<std::string> abr_switch_info;
  std::optional<int> abr_switch_is_peak_bitrate;
};

struct MediaSdkInitialize : LegacyCpuField, LegacyGpuField {
  int init_result = 0;
  int init_cost = 0;
  int event_type = 0;  // used as component id currently
  std::string os_version;
};

struct MediaSdkUnInitialize {
  int duration = 0;
};

struct SourceEvent {
  /**
   * @note SOURCE_CATEGORY::kVideo / SOURCE_CATEGORY::kAudio
   */
  int source_category = 0;
  std::string source_type;
  int event_type = 0;
  int extra_code = 0;
  std::string extra_msg;
};

struct FatalError {
  int error_type = 0;
  std::string error_msg;
  int error_code = 0;
  int event_type = 0;
};

struct Performance : LegacyGpuField, VisualFpsField {
  float preview_fps = 0.0f;
  float render_fps_achieving_rate = 0.0f;
  float no_ready_fps = 0.0f;
  float present_ready_fps = 0.0f;
  float present_fps_achieving_rate = 0.0f;

  int memory = 0;
  int total_mem = 0;
  int page_fault = 0;

  std::string cpu_name;
  double cpu_usage = 0.0;
  double cpu_total = 0.0;

  int disk_space_left = 0;
  int disk_space = 0;
};

struct StuckEvent {
  int duration = 0;
  std::string extra_msg;
};

struct PluginLoadDuration {
  int duration = 0;
  std::string extra_msg;
};

struct PluginInitDuration {
  int duration = 0;
  std::string extra_msg;
};

struct PluginLoaderDuration {
  int duration = 0;
  std::string extra_msg;
};

struct CaeInit {
  int error_type = 0;
  int default_bitrate = 0;
  double encode_fps = 0.0;
};

struct CaeStuck {};

struct CaeBitrate {
  int default_bitrate = 0;
  int meta_video_bitrate = 0;
};

enum CustomFlvMetadataEventType : int {
  kCustomFlvMetadataByRTMP = 0,
  kCustomFlvMetadataByRTC = 1,
};

struct CustomFlvMetadata {
  int event_type = 0;
  int extra_code = 0;
  std::string extra_msg;
};

struct AbrSwitch {
  std::string stream_id = "";
  int width = 0;
  int height = 0;
  int meta_video_framerate = 0;
  int abr_switch_fail = 0;
  float duration = 0.0f;
};

struct CodecSwitchStall {
  std::string stream_id = "";
  int width = 0;
  int height = 0;
  int meta_video_framerate = 0;
  float duration = 0.0f;
  float stall_time = 0.0f;
  std::string codec_name = "";
};

}  // namespace event_tracking_data

enum class SOURCE_CATEGORY : int {
  kVideo = 0,
  kAudio = 1,
  kVideoFilter = 2,
  kAudioFilter = 3
};
enum class SOURCE_EVENT_TYPE : int { kCreate = 0, kDestroy = 1 };
enum class SOURCE_EXTRA_CODE : int { kSuccess = 0, kFailed = 1 };
enum class FATAL_ERROR_TYPE : int {
  kDefault = 0,
  kDeviceLost = 1,
  kEncoderFreeze = 2,
  kLoadLibraryError = 3,
  kAudioTimeOut = 4,
  kPtsOverFlow = 5,
  kVideoEncodeGopOverflow = 6,
  kVideoEncodeFailed = 7,
  kStartPushStreamFailed = 8,
  kAudioTrackWaitTimeout = 9,
};

class VqosData {
 public:
  VqosData() = default;

  ~VqosData() = default;

  void ReportStartPush(const event_tracking_data::StartPush& data);

  void ReportStopPush(const event_tracking_data::StopPush& data);

  void ReportConnectStart(const event_tracking_data::ConnectStart& data);

  void ReportConnectStartConnection(
      const event_tracking_data::ConnectStartConnection& data);

  void ReportConnectEnd(const event_tracking_data::ConnectEnd& data);

  void ReportConnectEndConnection(
      const event_tracking_data::ConnectEndConnection& data);

  void ReportPushStreamFail(const event_tracking_data::PushStreamFail& data);

  void ReportPushStream(const event_tracking_data::PushStream& data);

  void ReportFirstFramePreEncode(
      const event_tracking_data::FirstFramePreEncode& data);

  void ReportFirstFrameEncode(
      const event_tracking_data::FirstFrameEncode& data);

  void ReportFirstFrameSend(const event_tracking_data::FirstFrameSend& data);

  void ReportBwProbe(const event_tracking_data::BwProbe& data);

  void ReportInitialize(const event_tracking_data::MediaSdkInitialize& data);

  void ReportUnInitialize(
      const event_tracking_data::MediaSdkUnInitialize& data);

  void ReportSourceEvent(const event_tracking_data::SourceEvent& data);

  void ReportFatalError(const event_tracking_data::FatalError& data);

  void ReportPerformance(const event_tracking_data::Performance& data,
                         const nlohmann::json& tea_buffer);

  void ReportStuckEvent(const event_tracking_data::StuckEvent& data);

  void ReportPluginLoadDuration(
      const event_tracking_data::PluginLoadDuration& data);

  void ReportPluginInitDuration(
      const event_tracking_data::PluginInitDuration& data);

  void ReportPluginLoaderDuration(
      const event_tracking_data::PluginLoaderDuration& data);

  void ReportCaeInit(const event_tracking_data::CaeInit& data);

  void ReportCaeStuck(const event_tracking_data::CaeStuck& data);

  void ReportCaeBitrate(const event_tracking_data::CaeBitrate& data);

  void ReportCustomFlvMetadata(
      const event_tracking_data::CustomFlvMetadata& data);

  void ReportAbrSwitch(const event_tracking_data::AbrSwitch& data);

  void ReportCodecSwitchStall(
      const event_tracking_data::CodecSwitchStall& data);

 private:
  void Report();

  void SetDefaultParam();

  nlohmann::json json_;
};

}  // namespace mediasdk
