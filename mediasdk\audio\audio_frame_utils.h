#pragma once
#include <softintrin.h>
#include <xmmintrin.h>
#include <base/check.h>

namespace mediasdk {

inline void mme_add(float* data_dst,
                    const float* data_src,
                    const uint32_t cnt) {
  int32_t end = cnt - cnt % 4;
  for (int i = 0; i < end; i += 4) {
    __m128 mix =
        _mm_add_ps(_mm_load_ps(data_dst + i), _mm_load_ps(data_src + i));
    _mm_store_ps(data_dst + i, mix);
  }
  for (int i = end; i < cnt; i++) {
    data_dst[i] += data_src[i];
  }
}

inline void mme_mul(float* data_dst, const float vol, const uint32_t cnt) {
  int32_t end = cnt - cnt % 4;
  __m128 scale = _mm_set_ps1(vol);
  for (int i = 0; i < end; i += 4) {
    _mm_store_ps(data_dst + i, _mm_mul_ps(_mm_load_ps(data_dst + i), scale));
  }
  for (int i = end; i < cnt; i++) {
    data_dst[i] *= vol;
  }
}

inline void LimitAudioMinMax(float* data, int32_t size) {
  if (!data)
    return;

  DCHECK((size & 0xF) == 0);

  __m128 max_sse = _mm_set_ps1(1.0F);
  __m128 min_sse = _mm_set_ps1(-1.0F);

  const int32_t speed_up_cnt = (size - size % 4);

  for (int32_t i = 0; i < speed_up_cnt; i += 4) {
    __m128 mix = _mm_load_ps(data + i);
    mix = _mm_max_ps(mix, min_sse);
    mix = _mm_min_ps(mix, max_sse);
    _mm_store_ps(data + i, mix);
  }
  for (int32_t i = speed_up_cnt; i < size; ++i) {
    data[i] = (std::min)((std::max)(data[i], -1.0F), 1.0F);
  }
}

inline void AudioAdd(float* dst, const float* src, uint32_t size) {
  mme_add(dst, src, size);
}

inline void AudioMul(float* dst, const float vol, uint32_t size) {
  mme_mul(dst, vol, size);
}

inline void AudioZero(float* data, uint32_t count) {
  memset(data, 0, sizeof(float) * count);
}

inline float AudioPeak(float* samples, uint32_t count) {
  DCHECK(((uint64_t)samples & 0xf) == 0);
  int32_t i = 0;
  float ret = 0.f;
  if (count > 4) {
    __m128 peak = _mm_andnot_ps(_mm_set1_ps(-0.F), _mm_load_ps(samples));
    i = 4;
    for (; (i + 3) < count; i += 4) {
      __m128 val = _mm_load_ps(&samples[i]);
      peak = _mm_max_ps(peak, _mm_andnot_ps(_mm_set1_ps(-0.F), val));
    }
    float x4_mem[4] = {};
    _mm_storeu_ps(x4_mem, peak);
    ret = x4_mem[0];
    ret = fmaxf(ret, x4_mem[1]);
    ret = fmaxf(ret, x4_mem[2]);
    ret = fmaxf(ret, x4_mem[3]);
  }

  for (; i < count; i++) {
    ret = fmaxf(ret, samples[i]);
  }
  return ret;
}

inline float AudioPeakMinMax(float* samples, uint32_t count) {
  int32_t i = 0;
  float ret_max = -2.f;
  float ret_min = 2.f;
  if (count > 4) {
    __m128 peak_max = _mm_loadu_ps(samples);
    __m128 peak_min = _mm_loadu_ps(samples);
    i = 4;
    for (; (i + 3) < count; i += 4) {
      __m128 val = _mm_loadu_ps(&samples[i]);
      peak_max = _mm_max_ps(peak_max, val);
      peak_min = _mm_min_ps(peak_min, val);
    }
    float x4_mem[4] = {};
    _mm_storeu_ps(x4_mem, peak_max);
    ret_max = x4_mem[0];
    ret_max = fmaxf(ret_max, x4_mem[1]);
    ret_max = fmaxf(ret_max, x4_mem[2]);
    ret_max = fmaxf(ret_max, x4_mem[3]);

    if (ret_max < 0.f) {
      _mm_storeu_ps(x4_mem, peak_min);
      ret_min = x4_mem[0];
      ret_min = fminf(ret_min, x4_mem[1]);
      ret_min = fmaxf(ret_min, x4_mem[2]);
      ret_min = fmaxf(ret_min, x4_mem[3]);
      return ret_min;
    } else {
      return ret_max;
    }
  }
  return 0.f;
}
}  // namespace mediasdk
