#pragma once

namespace mediasdk {

enum class H264NaluType : uint32_t {
  kUnknown = 0,
  kSlice = 1,
  kSliceDpa = 2,
  kSliceDpb = 3,
  kSliceDpc = 4,
  kSliceIdr = 5,
  kSei = 6,
  kSps = 7,
  kPps = 8,
  kAud = 9,
  kF<PERSON><PERSON> = 12
};

enum class HevcNaluType : uint32_t {
  kTrailN = 0,
  kTrailR = 1,
  kTsaN = 2,
  kTsaR = 3,
  kStsaN = 4,
  kStsaR = 5,
  kRadlN = 6,
  kRadlR = 7,
  kRaslN = 8,
  kRaslR = 9,
  kVclN10 = 10,
  kVclR11 = 11,
  kVclN12 = 12,
  kVclR13 = 13,
  kVclN14 = 14,
  kVclR15 = 15,
  kBlaWLP = 16,
  kBlaWRadl = 17,
  kBlaNLP = 18,
  kIdrWRadl = 19,
  kIdrNLP = 20,
  kCraNut = 21,
  kIrapVcl22 = 22,
  kIrapVcl23 = 23,
  kRsvVcl24 = 24,
  kRsvVcl25 = 25,
  kRsvVcl26 = 26,
  kRsvVcl27 = 27,
  kRsvVcl28 = 28,
  kRsvVcl29 = 29,
  kRsvVcl30 = 30,
  kRsvVcl31 = 31,
  kVps = 32,
  kSps = 33,
  kPps = 34,
  kAud = 35,
  kEosNut = 36,
  kEobNut = 37,
  kFdNut = 38,
  kSeiPrefix = 39,
  kSeiSuffix = 40,
};

}  // namespace media
