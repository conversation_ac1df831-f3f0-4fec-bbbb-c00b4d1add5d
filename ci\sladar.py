import utils
import os
import shutil
import zipfile
import tempfile
import time
from config import CONFIG, Platform
from concurrent.futures import ThreadPoolExecutor


def zip_files(files, zip_file_path_name):
    try:
        with zipfile.ZipFile(zip_file_path_name, "w") as zipObj2:
            for file in files:
                zipObj2.write(
                    file,
                    os.path.split(file)[1],
                    compress_type=zipfile.ZIP_DEFLATED,
                    compresslevel=9,
                )
        return True
    except Exception as e:
        print("Failed to create zip file. Error: ", e)
        return False


def upload_file(curl_exe, file):
    if not utils.exec_cmd(
        R'{} -X POST https://slardar-us.tiktok-row.net/windbg_upload -F"aid={}" -F"file=@{}"'.format(
            curl_exe, 2021, file
        )
    ):
        return False
    return True


# The sladar file service is unstable, and in some cases, success can be achieved by trying several times.
def upload_file_try_multi_times(curl_exe, file, times=3, sleep=3):
    # Try to publish the pdb to slardar up to {times} times
    pdb_suc = False
    for attempt in range(times):
        print("Attempt {} to upload file to sladar [{}]".format(attempt + 1, file))
        if upload_file(curl_exe, file):
            pdb_suc = True
            break
        else:
            print("Attempt {} failed. Retrying...".format(attempt + 1))
            if attempt < times - 1:  # No need to sleep after the last attempt
                time.sleep(sleep)

    if not pdb_suc:
        print("All attempts have failed.")
        return False
    return True


def dump_syms(dump_sym_exe, pdb_file, pe_file, out_path):
    if not utils.exec_cmd(
        R'{dump_sym_exe} "{pe_file}" "{pdb_file}" > "{out_path}"'.format(
            dump_sym_exe=dump_sym_exe,
            pe_file=pe_file,
            pdb_file=pdb_file,
            out_path=out_path,
        )
    ):
        return False
    return True


def upload_task(curl_exe, dump_sym_exe, pdb_file, pe_file):
    print("upload pdb file to symbolicate [{}], pe_file[{}]".format(pdb_file, pe_file))

    temp_dir = tempfile.mkdtemp()
    try:
        base_name = os.path.splitext(os.path.basename(pdb_file))[0]
        # Upload the pdb and pe files
        zip_file_path_name = os.path.join(temp_dir, base_name + ".zip")
        if not zip_files([pdb_file, pe_file], zip_file_path_name):
            return False
        if not upload_file_try_multi_times(curl_exe, zip_file_path_name):
            print("Failed upload_file:", zip_file_path_name)
            return False

        # Dump symbols
        # `pdb_file` and `pe_file` need to be in the same directory
        pdb_file = utils.safe_copy(pdb_file, temp_dir)
        pe_file = utils.safe_copy(pe_file, temp_dir)
        if pdb_file is None or pe_file is None:
            return False
        sym_file_path = os.path.join(temp_dir, base_name + ".sym")
        if not dump_syms(dump_sym_exe, pdb_file, pe_file, sym_file_path):
            print("Failed dump_syms:", pdb_file, pe_file, sym_file_path)
            return False

        # Upload the sym file
        if not zip_files([sym_file_path], zip_file_path_name):
            return False
        if not upload_file_try_multi_times(curl_exe, zip_file_path_name):
            print("Failed upload_file:", zip_file_path_name)
            return False
    finally:
        shutil.rmtree(temp_dir)

    return True


def find_pe_with_pdb(pdb_file):
    search_path = [CONFIG.exec_dir(Platform.X64), CONFIG.exec_dir(Platform.WIN32)]
    # Get the base name of pdb_file without extension
    base_name = os.path.splitext(os.path.basename(pdb_file))[0]
    if len(base_name.split('.')) > 1:
        base_name = base_name.split('.')[0]

    # Search for files in search_path and search_path2
    extensions = ["exe", "dll", "node"]
    for path in search_path:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                file_base_name, ext = os.path.splitext(filename)
                if file_base_name == base_name and ext[1:] in extensions:
                    return os.path.join(dirpath, filename)

    # If no matching file is found, return None
    return None


def publish_pdb(pdb_path):
    with ThreadPoolExecutor() as executor:
        futures = []

        pdb_files = []
        for parent, dirs, files in os.walk(pdb_path):
            for f in files:
                if f.endswith(".pdb"):
                    pdb_files.append(os.path.join(parent, f))

        for pdb_file in pdb_files:
            pe_file = find_pe_with_pdb(pdb_file)
            if pe_file is None or not os.path.exists(pe_file):
                print("Warning find_pe_with_pdb:", pdb_file)
                continue
            future = executor.submit(
                upload_task, CONFIG.curl_path, CONFIG.dump_sym_path, pdb_file, pe_file
            )
            futures.append(future)

        # Wait for all tasks to complete
        results = [future.result() for future in futures]
        all_successful = all(results)
        return all_successful
