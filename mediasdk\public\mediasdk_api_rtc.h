#include <stdint.h>

#include "mediasdk_defines_rtc.h"
#include "mediasdk_export.h"
#include "mediasdk_rtc_event_observer.h"

namespace mediasdk {

#ifdef __cplusplus
extern "C" {
#endif  // __cplusplus

// Create RTC engine for the app specified by app_id. After the creation is
// complete, the OnEngineStart event callback will be triggered.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API CreateEngine(const char* app_id,
                                         const char* json_params,
                                         Closure closure);

// Destroy the RTC engine. Once the destruction is complete, the `OnEngineStop`
// event callback will be triggered.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API DestroyEngine(Closure closure);

// Register a object named MediaSDKRTCEventObserver to rtc components, When
// event happened it will notify Ensure observer is valid before you call the
// UnregisterWindowEventObserver
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
RegisterRTCEventObserver(MediaSDKRTCEventObserver* observer, Closure closure);

// Unregister observer to the rtc components
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
UnregisterRTCEventObserver(MediaSDKRTCEventObserver* observer, Closure closure);

// Set a custom `business_id` to identify the business scenario used by the
// current app. This must be called before joining the room.
// Asynchronous callback result, int, a value equal to zero indicates success,
// while a value less than zero indicates failure.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API SetBusinessId(const char* business_id,
                                          Closure closure);

// Add the user specified by `user_id` to the room associated with `room_id`.
// Authentication is required using a token.
// Asynchronous callback result, int, a value equal to zero indicates success
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API JoinRoom(const char* room_id,
                                     const char* user_id,
                                     const char* token,
                                     const char* json_params,
                                     Closure closure);

// Leave the RTC room, end the call, and release all resources related to the
// call. After the interface call is completed, the `OnLeaveChannel` event
// callback will be triggered.
// Asynchronous callback result, int, a value equal to zero indicates success,
// while a value less than zero indicates failure.
// This interface needs to be called after joining the rooms.
MEDIASDK_EXPORT void MS_API LeaveRoom(Closure closure);

// Publish the media stream of the type specified by `stream_index` within the
// current room, with `media_type` specifying the type of audio and video
// stream_index controlled by the media stream.
// `stream_index` and `media_type` are defined in the `mediasdk_defines_rtc.h`
// header file as `StreamIndex` and `MediaStreamType`, respectively.
// Asynchronous callback result, int, a return value of zero indicates
// success.
// This interface needs to be called after joining the rooms.
MEDIASDK_EXPORT void MS_API PublishStream(int stream_index,
                                          int media_type,
                                          Closure closure);

// Stop publishing the media stream of the type specified by `stream_index`
// within the current room, with `media_type` specifying the type of audio and
// video controlled by the media stream.
// `stream_index` and `media_type` are defined in the `mediasdk_defines_rtc.h`
// header file as `StreamIndex` and `MediaStreamType`, respectively.
// Asynchronous callback result, int, a return value of zero indicates
// success.
// This interface needs to be called after joining the rooms.
MEDIASDK_EXPORT void MS_API UnPublishStream(int stream_index,
                                            int media_type,
                                            Closure closure);

// Subscribe to the media stream of type `stream_index` from the remote user
// specified by `user_id`, with `media_type` specifying the type of audio and
// video.
// `stream_index` and `media_type` are defined in the `mediasdk_defines_rtc.h`
// header file as `StreamIndex` and `MediaStreamType`, respectively.
// Asynchronous callback result, int, a return value of zero indicates
// success.
// This interface needs to be called after joining the rooms.
MEDIASDK_EXPORT void MS_API SubscribeStream(const char* user_id,
                                            int stream_index,
                                            int media_type,
                                            Closure closure);

// Unsubscribe from the media stream of type `stream_index` from the remote user
// specified by `user_id`, with `media_type` specifying the type of audio and
// video.
// `stream_index` and `media_type` are defined in the `mediasdk_defines_rtc.h`
// header file as `StreamIndex` and `MediaStreamType`, respectively.
// Asynchronous callback result, int, a return value of zero indicates
// success.
// This interface needs to be called after joining the rooms.
MEDIASDK_EXPORT void MS_API UnSubscribeStream(const char* user_id,
                                              int stream_index,
                                              int media_type,
                                              Closure closure);

// Set whether the audio stream of the track specified by `stream_index` and
// `track_id` is output to the local RTC.
// Asynchronous callback result, int, a value equal to zero indicates success,
// while a value less than zero indicates failure.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API EnableLocalAudio(int stream_index,
                                             bool enable,
                                             int track_id,
                                             Closure closure);

// Set the audio quality level for the RTC.
// enum AudioProfileType {
//    kAudioProfileTypeDefault = 0,
//    kAudioProfileTypeFluent = 1,
//    kAudioProfileTypeStandard = 2,
//    kAudioProfileTypeHD = 3,
//    kAudioProfileTypeStandardStereo = 4,
//    kAudioProfileTypeHDMono = 5,
// };
// Asynchronous callback result, int, a value equal to zero indicates success,
// while a value less than zero indicates failure.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API SetAudioProfile(int audio_profile, Closure closure);

// Adjust the volume of all remote users' mixed audio that is played locally.
// The `volume` parameter represents the ratio of the playback volume to the
// original volume, ranging from [0, 400] and expressed as a percentage (%),
// with built-in overflow protection. This adjustment affects only the audio
// data's volume information and does not involve hardware volume control on the
// local device.
// Asynchronous callback result, int, a value equal to zero indicates success,
// while a value less than zero indicates failure.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API SetPlaybackVolume(int volume, Closure closure);

// Set the audio playback device to the one specified by `device_id`. If
// `device_id` is empty, the playback device will follow the system's default
// device.
// Asynchronous callback result, int, a value equal to zero indicates
// success, while a value less than zero indicates failure.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API SetAudioPlaybackDevice(const char* device_id,
                                                   Closure closure);

// Retrieve the ID of the current audio playback device.
// Asynchronous callback result, MSCallbackString
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API GetAudioPlaybackDevice(Closure closure);

// Enable audio information notifications, with `interval` specifying the
// notification interval.
// Asynchronous callback result, int, a value equal to zero indicates
// success, while a value less than zero indicates failure.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API EnableAudioPropertiesReport(int interval,
                                                        Closure closure);

// Set whether the video stream specified by `sink_id` for the media stream
// identified by `stream_index` is output to the local RTC.
// Asynchronous callback result, int, a value equal to zero indicates
// success, while a value less than zero indicates failure.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API EnableLocalVideo(int stream_index,
                                             bool enable,
                                             int sink_id,
                                             Closure closure);

// Update the cropping and scaling information for the video stream specified by
// `stream_index` that is output to the RTC.
// json_params sample:
// {
//   "framerate": 30,
//   "max_bitrate": 5000,
//   "min_bitrate": 1000,
//   "enable_simulcast": true,
//   "region": [0, 0, 1920, 1080],
//   "scale": 1.0,
//   "intermediate_output_scale":1.0
// }
// Asynchronous callback result, int, a value equal to zero indicates
// success, while a value less than zero indicates failure.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API UpdateCropAndScale(int stream_index,
                                               const char* json_params,
                                               Closure closure);

// Update which specified visuals should be filtered out from the video stream
// specified by `stream_index` when it is output to the RTC.
// json_params sample:
// {
//   "visual_ids": [
//     "id1",
//     "id2",
//     "id3"
//   ]
// }
// Asynchronous callback result, int, a value equal to zero indicates
// success, while a value less than zero indicates failure.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API UpdateExcludedVisuals(int stream_index,
                                                  const char* json_params,
                                                  Closure closure);

// Set the parameters for subscribing to the video stream of the remote user
// specified by `user_id`.
// json_params sample:
// {
//   "framerate": 30,
//   "resolution_height": 720,
//   "resolution_width": 1280
// }
// Asynchronous callback result, int, a value equal to zero indicates
// success, while a value less than zero indicates failure.
// This interface needs to be called after joining the rooms.
MEDIASDK_EXPORT void MS_API SetRemoteVideoConfig(const char* user_id,
                                                 const char* json_params,
                                                 Closure closure);

// Embed SEI (Supplemental Enhancement Information) in the media stream
// specified by `stream_index`. The `repeat_count` parameter specifies how many
// times to repeat sending from the current frame, while `single_sei_per_frame`
// specifies the sending mode.
// Asynchronous callback result, int, a value less than zero indicates failure,
// while a value greater than or equal to zero represents the number of SEI
// messages that will be added to the video frame.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API SendSEIMessage(int stream_index,
                                           const char* data,
                                           int length,
                                           int repeat_count,
                                           bool single_sei_per_frame,
                                           Closure closure);

// Start forwarding media streams across rooms, enabling media streams to be
// forwarded to multiple rooms. This is suitable for scenarios such as
// cross-room linking.
// json_params sample:
// {
//   "forward_config": [
//     {
//       "room_id": "room1",
//       "token": "token1"
//     },
//     {
//       "room_id": "room2",
//       "token": "token2"
//     }
//   ]
// }
// Asynchronous callback result, int, a value equal to zero indicates
// success, while a value less than zero indicates failure.
// This interface needs to be called after joining the rooms.
MEDIASDK_EXPORT void MS_API StartForwardStreamToRooms(const char* json_params,
                                                      Closure closure);

// Update the forwarding information for cross-room media streams.
// json_params sample:
// {
//   "forward_config": [
//     {
//       "room_id": "room1",
//       "token": "token1"
//     },
//     {
//       "room_id": "room2",
//       "token": "token2"
//     }
//   ]
// }
// Asynchronous callback result, int, a value equal to zero indicates
// success, while a value less than zero indicates failure.
// This interface needs to be called after joining the rooms.
MEDIASDK_EXPORT void MS_API UpdateForwardStreamToRooms(const char* json_params,
                                                       Closure closure);

// Stop cross-room media stream forwarding.
// Asynchronous callback result, int, a value equal to zero indicates
// success, while a value less than zero indicates failure.
// This interface needs to be called after joining the rooms.
MEDIASDK_EXPORT void MS_API StopForwardStreamToRooms(Closure closure);

// Add a new merge stream live task and configure the layout of images and video
// views, as well as the audio attributes for the merge.
// json_params sample:
// {
//   "transcodeMeta": {
//     "advancedConfig": "some_advanced_config",
//     "authInfo": "some_auth_info",
//     "transcode": {
//       "url": "http://example.com/transcode"
//     },
//     "audio": {
//       "channels": 2,
//       "sampleRate": 48000,
//       "bitRate": 64000,
//       "codec": "AAC",
//       "profile": "LC"
//     },
//     "video": {
//       "width": 1280,
//       "height": 720,
//       "fps": 30,
//       "gop": 60,
//       "bitRate": 5000000,
//       "bFrame": true,
//       "codec": "H264"
//     },
//     "syncControl": {
//       "syncClientVideoNeedMix": true,
//       "syncBaseUser": "base_user_id",
//       "syncQueueLengthMs": 1000,
//       "syncStream": true
//     },
//     "clientMix": {
//       "clientMixUseAudioMixer": true,
//       "clientMixVideoFormat": 1
//     },
//     "spatialConfig": {
//       "enableSpatialRender": true,
//       "audienceSpatialPosition": [1.0, 2.0, 3.0],
//       "audienceSpatialOrientation": {
//         "forward": [1.0, 0.0, 0.0],
//         "right": [0.0, 1.0, 0.0],
//         "up": [0.0, 0.0, 1.0]
//       }
//     },
//     "layout": {
//       "regions": [
//         {
//           "uid": "user1",
//           "roomID": "room1",
//           "x": 0.1,
//           "y": 0.1,
//           "w": 0.3,
//           "h": 0.3,
//           "alpha": 0.8,
//           "cornerRadius": 5,
//           "zorder": 1,
//           "contentControl": 0,
//           "renderMode": 1,
//           "local_user": false,
//           "screen": true,
//           "type": 1,
//           "spatialPosition": [0.0, 0.0, 0.0]
//         }
//       ],
//       "canvas": {
//         "bgnd": "#FFFFFF"
//       },
//       "app_data": "some_app_data"
//     }
//   },
//   "mixingType": 1,
//   "roomId": "room_id",
//   "userId": "user_id"
//   "custom_flv_metadata": {
//     "orientation": "vertical"
//    }
// }
// Asynchronous callback result, int, a value of zero indicates success, while
// a non-zero value indicates failure.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API StartLiveTranscoding(const char* task_id,
                                                 const char* json_params,
                                                 Closure closure);

// Update the parameters for the merge stream live task.
// The format of `json_params` is the same as that for the
// `StartLiveTranscoding` interface.
// Asynchronous callback result, int, a value of zero indicates success, while
// a non-zero value indicates failure.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API UpdateLiveTranscoding(const char* task_id,
                                                  const char* json_params,
                                                  Closure closure);

// Stop a single-stream live task or a merge stream live task using `task_id` to
// identify the task that needs to be stopped.
// Asynchronous callback result, int, a value of zero indicates success, while
// a non-zero value indicates failure.
// This interface needs to be called after the RTC engine is created.
MEDIASDK_EXPORT void MS_API StopLiveTranscoding(const char* task_id,
                                                Closure closure);

// Broadcast a text message to all other users in the room.
// Asynchronous callback result, int, a return value of zero indicates success.
// This interface needs to be called after joining the rooms.
MEDIASDK_EXPORT void MS_API SendRoomMessage(const char* data, Closure closure);

// Send a peer-to-peer text message to a specific user within the room.
// Asynchronous callback result, int, a return value of zero indicates success.
// This interface needs to be called after joining the rooms.
MEDIASDK_EXPORT void MS_API SendUserMessage(const char* user_id,
                                            const char* data,
                                            Closure closure);

MEDIASDK_EXPORT void MS_API SetLyraxLiveRoomId(const char* live_room_id,
                                               Closure closure);
#ifdef __cplusplus
}
#endif  // __cplusplus

}  // namespace mediasdk
