#pragma once

#include <DirectXMath.h>
#include <string.h>
#include <map>
#include <mutex>
#include "shader.h"
#include "transform.h"
using namespace DirectX;

namespace graphics {
class TextureShader : public Shader {
 public:
  struct VERTEXTYPE {
    XMFLOAT3 position;
    XMFLOAT2 texture;
    XMFLOAT4 color;
  };

  static inline const char* SHADER_ID_STRING = "texture_shader";

  static std::shared_ptr<Shader> CreateTextureShader(const char* type) {
    if (strcmp(type, SHADER_ID_STRING) == 0) {
      return std::make_shared<TextureShader>();
    }
    return nullptr;
  }

  static void EnumPluginsFunction(void* param, EnumPluginCallBack fpn) {
    fpn(param,
        ShaderItem{SHADER_ID_STRING, "shader for draw texture to graphics",
                   TextureShader::CreateTextureShader});
  }

  static bool CreateInputBuffer(
      const Microsoft::WRL::ComPtr<ID3D11Device>& device,
      Microsoft::WRL::ComPtr<ID3D11Buffer>& crop_buffer,
      Microsoft::WRL::ComPtr<ID3D11Buffer>& matrix_buffer,
      Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_buffer);

 public:
  bool Init(const std::shared_ptr<Device>&) override;

  void RenderBGRA(ID3D11ShaderResourceView* view,
                  Microsoft::WRL::ComPtr<ID3D11Buffer>& vertex_buffer,
                  Microsoft::WRL::ComPtr<ID3D11Buffer>& index_buffer,
                  Microsoft::WRL::ComPtr<ID3D11Buffer>& crop_buffer,
                  Microsoft::WRL::ComPtr<ID3D11Buffer>& matrix_buffer,
                  Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_buffer,
                  DXGI_FORMAT format,
                  TONEMAP_TYPE tonemap_type = UNCHARTED,
                  bool tone_mapping = true,
                  SAMPLE_TYPE sample_type = LINER);

  void RenderBGRAWithLuminanceMap(ID3D11ShaderResourceView* view,
                                  ID3D11ShaderResourceView* luminanceMapSRV,
                                  Microsoft::WRL::ComPtr<ID3D11Buffer>& vertex_buffer,
                                  Microsoft::WRL::ComPtr<ID3D11Buffer>& index_buffer,
                                  Microsoft::WRL::ComPtr<ID3D11Buffer>& crop_input,
                                  Microsoft::WRL::ComPtr<ID3D11Buffer>& matrix_buffer,
                                  Microsoft::WRL::ComPtr<ID3D11Buffer>& ps_buffer,
                                  DXGI_FORMAT format,
                                  TONEMAP_TYPE tonemap_type = UNCHARTED,
                                  bool tone_mapping = true,
                                  SAMPLE_TYPE sample_type = LINER);

  void Destroy() override;
  ~TextureShader() override;

 private:
  bool DoInit(const std::shared_ptr<Device>& ins);

 private:
  Microsoft::WRL::ComPtr<ID3D11Device> GetDevice_();

  ID3D11DeviceContext* GetContext_();

 private:
  bool try_init_ = false;
  bool init_suc_ = false;
  std::shared_ptr<Device> device_;
  ID3D11DeviceContext* context_;
  Microsoft::WRL::ComPtr<ID3D11VertexShader> vs_shader_;
  std::vector<TONEMAP_INFO> scrgb_ps_shaders_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> srgb_ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> srgb_gama_ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11PixelShader> y8_ps_shader_;
  Microsoft::WRL::ComPtr<ID3D11InputLayout> layout_;
  Microsoft::WRL::ComPtr<ID3D11SamplerState> sampler_;
  Microsoft::WRL::ComPtr<ID3D11SamplerState> sampler_state2_;
};
}  // namespace graphics
