#pragma once

#include "base/memory/weak_ptr.h"
#include "mediasdk/component.h"
#include "mediasdk/public/effect_platform/mediasdk_effect_platform.h"
#include "mediasdk/public/mediasdk_defines.h"
#include "mediasdk/public/mediasdk_effect_platform_event_observer.h"

namespace mediasdk {

class EffectPlatformController
    : public Component,
      public base::SupportsWeakPtr<EffectPlatformController> {
 public:
  static constexpr char kComponentName[] = "EffectPlatformController";

  virtual bool EPInitialize(const std::string& json_param) = 0;

  virtual bool EPUninitialize() = 0;

  virtual bool EPRegistEventObserver(MediaSDKEPEventObserver* observer) = 0;

  virtual bool EPUnregistEventObserver(MediaSDKEPEventObserver* observer) = 0;

  virtual bool EPUpdateConfig(const std::string& user_id,
                              const std::string& hardware_level) = 0;

  virtual bool EPLoadModels(const std::vector<std::string>& requirments,
                            const std::string& request_id,
                            const std::string& model_name) = 0;

  virtual bool UseFinder(const ep::Finder& finder) = 0;

  virtual bool IsLoaded() = 0;
};

};  // namespace mediasdk