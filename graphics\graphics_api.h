#pragma once

#include <cstdint>
#include <vector>
#include "adapter.h"
#include "graphics/graphics_export.h"
#include "mediasdk/public/mediasdk_string.hpp"

#ifdef __cplusplus
extern "C" {
#endif
namespace graphics {

GRAPHICS_EXPORT int32_t IsWICCanDecodeMetaFile(const wchar_t* fileName,
                                               uint32_t* puiWidth,
                                               uint32_t* puiHeight);

GRAPHICS_EXPORT bool EnumAdapterInfo(std::vector<GpuAdapterInfo>& infos);

}  // namespace graphics

#ifdef __cplusplus
}
#endif
