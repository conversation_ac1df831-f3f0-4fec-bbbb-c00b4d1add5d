#pragma once

#include <array>
#include <memory>
#include "bgra_to_yuv_graphics.h"
#include "bgra_to_yuv_shader.h"
#include "graphics.h"

namespace graphics {

class BGRAToYUVGraphicsImpl : public BGRAToYUVGraphics {
 public:
  explicit BGRAToYUVGraphicsImpl(Device& ins);

 public:
  bool Init(BGRAToYUVConvertConfig conf) override;

  bool BGRAToYUV(Texture&) override;

  bool BGRAToYUVToTarget(Texture&, ConvertResourceType& resource) override;

  std::shared_ptr<Texture> GetOutputTexture(int plane) override;

  std::shared_ptr<Texture> MoveOutputTexture(int plane) override;

  ~BGRAToYUVGraphicsImpl() override;

  void Destroy() override;

 private:
  bool TryUpdateShaderParameter(int32_t width, int32_t height);

  bool DoConvertByShader(
      bool i420,
      bool only_y,
      Texture& texture,
      BGRAToYUVGraphics::ConvertResourceType& planer_graphics);

  bool DoTextureConvert(Texture&,
                        BGRAToYUVGraphics::ConvertResourceType& planer_graphics,
                        bool planer_texture,
                        bool only_y = false,
                        bool i420 = false,
                        bool shared = false,
                        Texture* output_texture = nullptr);

  ID3D11Device* GetDevice_();

  ID3D11DeviceContext* GetContext_();

  ShaderManager* GetShaderManager_();

 private:
  std::unique_ptr<BGRAToYUVGraphics::ConvertResourceType> planer_graphics_;
  BGRAToYUVConvertConfig config_;
  Device& device_;
  BGRAToYUVShader::PS_CONSTANT_BUFFER ps_buffer_v_ = {};
  BGRAToYUVShader::VS_CONSTANT_BUFFER vs_buffer_v_ = {};
  Microsoft::WRL::ComPtr<ID3D11Buffer> vs_buffer_;
  Microsoft::WRL::ComPtr<ID3D11Buffer> ps_buffer_;
};

}  // namespace graphics
