#pragma once

#include <map>
#include <memory>
#include <string>
#include <vector>

namespace mediasdk {

class EndtoEndDelayCaptureConst;

class EndtoEndDelayCaptures {
 public:
  static std::shared_ptr<EndtoEndDelayCaptures> CreateShared();

  void SetVisualCaptureTimestmapNS(const char*, int64_t timestamp);

  std::shared_ptr<EndtoEndDelayCaptureConst> MoveToConst();

 private:
  std::map<const char*, int64_t> capture_ns_s_;
};

class EndtoEndDelayCaptureConst {
 public:
  EndtoEndDelayCaptureConst(
      const std::map<std::string, int64_t>& capture_timestamps);

  const std::map<std::string, int64_t>& GetAllTimestmaps();

  int64_t GetMinCaptureTimestampNS();

 private:
  const std::map<std::string, int64_t> capture_timestamps_;
};

}  // namespace mediasdk
