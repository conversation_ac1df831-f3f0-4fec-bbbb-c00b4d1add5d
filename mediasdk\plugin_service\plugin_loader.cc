#include "plugin_loader.h"

#include <Shlwapi.h>
#include "audio_encoder_source_factory.h"
#include "audio_filter_factory.h"
#include "audio_input_source_factory_impl.h"
#include "base/files/file_enumerator.h"
#include "base/path_service.h"
#include "base/strings/stringprintf.h"
#include "component_center.h"
#include "data_center/os_version.h"
#include "data_center/vqos_data.h"
#include "mediasdk/data_center/data_center.h"
#include "mediasdk/public/mediasdk_defines.h"
#include "plugin_black_list.h"
#include "stream_service_source_factory.h"
#include "task_server_delegate.h"
#include "time_helper.h"
#include "video_encoder_source_factory.h"
#include "visual_filter_factory.h"
#include "visual_source_factory_impl.h"

namespace {

// Only report the hidden points that take more than 100ms to load the plugin.
constexpr uint64_t kLoadThreshold = 100;

base::FilePath GetPluginPath() {
  base::FilePath base_file;
  if (base::PathService::Get(base::FILE_MODULE, &base_file)) {
    base::FilePath base_dir = base_file.DirName();
    base::FilePath plugin_dir = base_dir.Append(FILE_PATH_LITERAL("plugins"));
    return plugin_dir;
  }
  return base::FilePath();
}

std::vector<base::FilePath> GetPluginFileList(
    const base::FilePath& plugin_dir) {
  std::vector<base::FilePath> path_list;

  base::FileEnumerator enumerator(plugin_dir,
                                  /*recursive=*/false,
                                  base::FileEnumerator::FILES);
  for (base::FilePath file = enumerator.Next(); !file.empty();
       file = enumerator.Next()) {
    if (base::FilePath::CompareIgnoreCase(file.Extension(), L".dll") == 0) {
      if (plugin_black_list::IsInBlackList(file)) {
        continue;
      }
      path_list.push_back(file);
    }
  }

  return path_list;
}

std::string FailedListToString(
    const std::vector<std::pair<base::FilePath, base::NativeLibraryLoadError>>&
        list) {
  std::ostringstream oss;
  for (const auto& [path, error] : list) {
    oss << base::StringPrintf("%s, %s;", path.BaseName().AsUTF8Unsafe().c_str(),
                              error.ToString().c_str());
  }
  return oss.str();
}

}  // namespace

namespace mediasdk {

std::unique_ptr<PluginLoader> PluginLoader::Create(
    PluginLoaderDelegate* delegate) {
  DCHECK(delegate);

  auto loader = std::unique_ptr<PluginLoader>(new PluginLoader(delegate));
  return loader;
}

PluginLoader::PluginLoader(PluginLoaderDelegate* delegate)
    : delegate_(delegate) {
  // Statistics of plugin loading time
  start_ts_ms_ = milli_now();
}

PluginLoader::~PluginLoader() {
  // Stop all the threads
  for (auto& thread : init_threads_) {
    thread->Stop();
  }

  // Report all the plugin loaded event
  event_tracking_data::MediaSdkInitialize sdk_init;
  sdk_init.init_cost = std::max(milli_now() - start_ts_ms_, 0ll);
  sdk_init.init_result = static_cast<int>(SDKInitResult::kSuccess);
  sdk_init.os_version = os_version::OsVersion::GetOsVersion();
  sdk_init.event_type = static_cast<int>(SDKInitType::kPluginLoad);
  event_tracking_data::LegacyCpuField::FillField(sdk_init);
  event_tracking_data::LegacyGpuField::FillField(sdk_init);
  VqosData data;
  data.ReportInitialize(sdk_init);
}

void PluginLoader::Load() {
  load_start_time_ = milli_now();

  auto plugin_file_list = GetPluginFileList(GetPluginPath());

  std::vector<std::pair<const base::FilePath, std::shared_ptr<SourceFactory>>>
      factories;
  std::vector<std::pair<const base::FilePath, std::future<base::NativeLibrary>>>
      futures;

  for (const auto& file_path : plugin_file_list) {
    // Launch a task asynchronously for each plugin file
    futures.push_back(std::make_pair(
        file_path, std::async(std::launch::async, &PluginLoader::LoadPluginDll,
                              this, file_path)));
  }

  // Collect results from all futures
  for (auto& future : futures) {
    auto library = future.second.get();
    if (!library) {
      continue;
    }

    auto info = GetPluginInfo(library);
    if (!info) {
      base::UnloadNativeLibrary(library);
      continue;
    }

    auto fac = CreateSourceFactory(library, info);
    if (!fac) {
      LOG(ERROR) << "Failed to CreateSourceFactory: " << info->name.ToString();
      base::UnloadNativeLibrary(library);
      continue;
    }

    if (delegate_) {
      delegate_->OnPluginLoaded(fac);
    }

    factories.push_back(std::make_pair(future.first, fac));
  }

  // Initialize all plugin with the collected factories
  InitAllPlugins(std::move(factories));
}

void PluginLoader::ReportLoadEvent() {
  if (load_start_time_ > 0) {
    VqosData data;
    data.ReportPluginLoaderDuration(
        {static_cast<int>(milli_now() - load_start_time_), ""});
  }

  if (!load_failed_libraries_.empty()) {
    event_tracking_data::FatalError fatal_error;
    fatal_error.error_type =
        static_cast<int>(FATAL_ERROR_TYPE::kLoadLibraryError);
    fatal_error.error_msg = FailedListToString(load_failed_libraries_);
    VqosData data;
    data.ReportFatalError(fatal_error);
  }

  for (const auto& it : load_library_costs_) {
    if (it.second.load_cost > kLoadThreshold) {
      event_tracking_data::PluginLoadDuration load_duration{
          static_cast<int>(it.second.load_cost),
          it.first.BaseName().AsUTF8Unsafe()};
      VqosData data;
      data.ReportPluginLoadDuration(load_duration);
    }
    if (it.second.init_cost > kLoadThreshold) {
      event_tracking_data::PluginInitDuration init_duration{
          static_cast<int>(it.second.init_cost),
          it.first.BaseName().AsUTF8Unsafe()};
      VqosData data;
      data.ReportPluginInitDuration(init_duration);
    }
  }
}

base::NativeLibrary PluginLoader::LoadPluginDll(
    const base::FilePath& file_path) {
  const auto load_start_ts = milli_now();

  base::NativeLibraryLoadError load_error;
  base::NativeLibrary library = base::LoadNativeLibrary(file_path, &load_error);
  if (!library) {
    LOG(ERROR) << "Failed to load library: " << load_error.ToString()
               << ", path:" << file_path.BaseName();
    {
      std::lock_guard<std::mutex> lock(lock_load_failed_libraries_);
      load_failed_libraries_.emplace_back(
          std::make_pair(file_path, load_error));
    }
    return nullptr;
  }
  {
    std::lock_guard<std::mutex> lock(lock_load_library_costs_);
    if (load_library_costs_.count(file_path) > 0) {
      load_library_costs_[file_path].load_cost = milli_now() - load_start_ts;
    } else {
      load_library_costs_[file_path] = {
          static_cast<uint64_t>(milli_now() - load_start_ts), 0};
    }
  }

  return library;
}

std::shared_ptr<PluginInfo> PluginLoader::GetPluginInfo(
    base::NativeLibrary library) {
  typedef const PluginInfo* (*GetPluginInfoFunc)();

  GetPluginInfoFunc func = reinterpret_cast<GetPluginInfoFunc>(
      base::GetFunctionPointerFromNativeLibrary(library, "GetPluginInfo"));

  if (!func) {
    LOG(ERROR) << "Failed to get function pointer for GetPluginInfo";
    return nullptr;
  }

  const auto* info = func();
  if (info) {
    return std::make_shared<PluginInfo>(*info);
  }
  return nullptr;
}

std::shared_ptr<SourceFactory> PluginLoader::CreateSourceFactory(
    base::NativeLibrary library,
    std::shared_ptr<PluginInfo> info) {
  if (!info) {
    return nullptr;
  }

  std::shared_ptr<SourceFactory> factory;
  if (info->type == PluginType::kVisual) {
    factory = VisualSourceFactoryImpl::Create(library, info);
  } else if (info->type == PluginType::kAudio) {
    factory = AudioInputSourceFactoryImpl::Create(library, info);
  } else if (info->type == PluginType::kAudioFilter) {
    factory = AudioFilterFactory::Create(library, info);
  } else if (info->type == PluginType::kVisualFilter) {
    factory = VisualFilterFactory::Create(library, info);
  } else if (info->type == PluginType::kService) {
    factory = StreamServiceSourceFactory::Create(library, info);
  } else if (info->type == PluginType::kVideoEncoder) {
    factory = VideoEncoderSourceFactory::Create(
        library, info, delegate_ ? delegate_->GetPluginGlobalProxy() : nullptr);
  } else if (info->type == PluginType::kAudioEncoder) {
    factory = AudioEncoderSourceFactory::Create(library, info);
  }

  return factory;
}

void PluginLoader::InitAllPlugins(
    std::vector<std::pair<const base::FilePath,
                          std::shared_ptr<SourceFactory>>>&& factories) {
  for (auto& fac : factories) {
    if (!fac.second) {
      DCHECK(false);
      continue;
    }
    ++pending_plugins_[fac.second->GetType()];
  }
  for (auto& fac : factories) {
    if (!fac.second) {
      DCHECK(false);
      continue;
    }

    if (fac.second->NeedAsyncGlobalInit(
            delegate_ ? delegate_->GetPluginGlobalProxy() : nullptr)) {
      LOG(INFO) << "Plugin " << fac.second->GetName() << " NeedAsyncGlobalInit";
      // Execute asynchronous initialization
      auto thread = std::make_unique<base::Thread>(fac.second->GetName());
      thread->Start();
      thread->task_runner()->PostTaskAndReplyWithResult(
          FROM_HERE,
          base::BindOnce(&PluginLoader::InitPlugin, base::Unretained(this),
                         fac.first, fac.second),
          base::BindOnce(&PluginLoader::OnSourceFactoryGlobalInitResult,
                         AsWeakPtr(), fac.second));
      init_threads_.push_back(std::move(thread));

    } else {
      LOG(INFO) << "Plugin " << fac.second->GetName()
                << "do not NeedAsyncGlobalInit";
      OnSourceFactoryGlobalInitResult(fac.second,
                                      InitPlugin(fac.first, fac.second));
    }
  }
}

bool PluginLoader::InitPlugin(const base::FilePath& file_path,
                              std::shared_ptr<SourceFactory> fac) {
  auto start = milli_now();
  bool success = false;
  if (fac) {
    success = fac->PluginGlobalInit(
        delegate_ ? delegate_->GetPluginGlobalProxy() : nullptr);
    fac->SetInitialized(success);
  }
  if (success) {
    std::lock_guard<std::mutex> lock(lock_load_library_costs_);
    if (load_library_costs_.count(file_path) > 0) {
      load_library_costs_[file_path].init_cost = milli_now() - start;
    } else {
      load_library_costs_[file_path] = {
          0, static_cast<uint64_t>(milli_now() - start)};
    }
  }
  return success;
}

void PluginLoader::OnSourceFactoryGlobalInitResult(
    std::shared_ptr<SourceFactory> fac,
    bool success) {
  --pending_plugins_[fac->GetType()];
  if (!success && fac) {
    LOG(INFO) << "Failed to PluginGlobalInit: " << fac->GetName();
  }
  if (pending_plugins_[fac->GetType()] == 0) {
    if (delegate_) {
      delegate_->OnSubTypePluginsLoadFinished(fac->GetType());
    }
  }
  if (delegate_) {
    delegate_->OnPluginInitializeResult(fac);
  }

  CheckAllPluginsInitialized();
}

void PluginLoader::CheckAllPluginsInitialized() {
  if (delegate_) {
    int left = 0;
    std::for_each(pending_plugins_.begin(), pending_plugins_.end(),
                  [&left](auto& it) { left += it.second; });
    if (!left) {
      // Report the load event
      ReportLoadEvent();

      delegate_->OnLoadFinished();
    }
  }
}

}  // namespace mediasdk
