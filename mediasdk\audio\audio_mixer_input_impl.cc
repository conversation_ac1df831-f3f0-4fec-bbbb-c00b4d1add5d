#include "audio_mixer_input_impl.h"
#include <mediasdk/component_center.h>
#include <mediasdk/debug_helper.h>
#include <mediasdk/public/mediasdk_trace_event.h>
#include "aligned_audio_frame.h"
#include "mediasdk/notify_center.h"
#include "mediasdk/utils/time_helper.h"
using namespace mediasdk;

namespace {
constexpr int64_t kMaxAudioBufferSeconds = 20;
constexpr int64_t k20SecondsOfMS = 20000;

}  // namespace

namespace mediasdk {
AudioMixerInputImpl::~AudioMixerInputImpl() = default;

AudioMixerInputImpl::AudioMixerInputImpl() {
  target_format_ = kAudioOutputFormat;

  for (int i = 0; i < mix_buffer_.size() && i < kAudioOutputFormat.GetPlane();
       i++) {
    mix_buffer_[i].ResizeBufferTo(kAudioOutputFormat.GetSampleRate() * 2);
  }
}

TimeDurationFrame* AudioMixerInputImpl::GetAudioFrameDuration(
    const std::string_view& debug_str,
    const TimeDuration& ts) {
  if (!ConsumeInputBuffer(debug_str, ts)) {
    return nullptr;
  }
  auto ret = &output_audio_frame_duraion_;
  ret->duration = GetOutputDuration();
  AudioFrameData frame_data;
  frame_data.block_size = sizeof(float);
  frame_data.count = ts.GetSampleCnt();
  frame_data.buffer[0] = mix_output_buffer_->GetData(0);
  frame_data.buffer[1] = mix_output_buffer_->GetData(1);
  ret->frame.SetData(frame_data);
  ret->frame.SetCaptureTimeStampNS(ret->duration.GetBeginTimeStampNS());
  ret->frame.SetSampleRate(target_format_.GetSampleRate());
  DCHECK(ret->frame.GetCaptureTimeStampNS() ==
         ret->duration.GetBeginTimeStampNS());
  return ret;
}

void AudioMixerInputImpl::CheckAndReportErrorCnt(const std::string_view& str) {
  if (!last_check_mixer_error_cnt_ms_) {
    last_check_mixer_error_cnt_ms_ = mediasdk::low_precision_milli_now();
    return;
  }

  const auto now = mediasdk::low_precision_milli_now();
  if (now - last_check_mixer_error_cnt_ms_ > k20SecondsOfMS) {
    last_check_mixer_error_cnt_ms_ = now;
    int32_t too_late = 0;
    int32_t too_early = 0;

    GetErrorCnt(too_late, too_early);
    if (too_late || too_early) {
      auto* nc = com::GetNotifyCenter();
      if (nc) {
        nlohmann::json json_root;
        json_root["event_name"] = "OnAudioInputMixerError";
        json_root["too_late"] = too_late;
        json_root["too_early"] = too_early;
        nc->AudioEvent()->Notify(FROM_HERE,
                                 &MediaSDKAudioStatusObserver::OnAudioEvent,
                                 str, std::string(json_root.dump()));
      }
    }
  }
}

void AudioMixerInputImpl::OnData(const std::string_view& str,
                                 const AudioFrame& data,
                                 int64_t syn_offset) {
  CheckAndReportErrorCnt(str);
  DCHECK(data.GetData(0) && data.GetData(1));
  if (syn_offset != pre_offset_) {
    std::lock_guard<std::mutex> lock(lock_mix_buffer_);
    for (auto& mix : mix_buffer_) {
      mix.Clear();
    }
    output_ts_ = {};
    input_ts_ = 0;
    pre_offset_ = syn_offset;
  }
  AudioFrame offset_frame = data;
  if (pre_offset_ != 0) {
    offset_frame.SetCaptureTimeStampNS(offset_frame.GetCaptureTimeStampNS() +
                                       pre_offset_);
  }

  DCHECK(offset_frame.GetData(0) && offset_frame.GetData(1));

  AUDIO_MIX_TRACE_VALUE("mix_input_cnt", offset_frame.GetCount());
  // outputFrame is in target_format_
  BufferAudioFrame(str, offset_frame);
}

void AudioMixerInputImpl::PopFill(const TimeDuration& ts) {
  output_ts_ = ts;
  input_ts_ = ts.GetEndTimeStampNS();
  int64_t copy_cnt = ts.GetSampleCnt();
  for (int i = 0; i < mix_buffer_.size(); i++) {
    if (mix_buffer_[i].Cnt()) {
      DCHECK(copy_cnt == ts.GetSampleCnt());
      mix_buffer_[i].PopTo((float*)mix_output_buffer_->GetData(i), copy_cnt);
    }
  }
}

void AudioMixerInputImpl::PopMiddle(const TimeDuration& input_buffer_ts,
                                    const TimeDuration& target_ts) {
  output_ts_ = target_ts;
  input_ts_ = target_ts.GetEndTimeStampNS();
  int64_t copy_begin = NSDurationToAudioSamples(
      target_format_.GetSampleRate(),
      target_ts.GetBeginTimeStampNS() - input_buffer_ts.GetBeginTimeStampNS());
  int64_t copy_cnt = target_ts.GetSampleCnt();
  DCHECK(copy_cnt <= target_ts.GetSampleCnt());
  for (int i = 0; i < mix_buffer_.size(); i++) {
    if (mix_buffer_[i].Cnt()) {
      mix_buffer_[i].PopTo((float*)mix_output_buffer_->GetData(i), copy_begin,
                           copy_cnt);
    }
  }
}

void AudioMixerInputImpl::PopPart(const TimeDuration& input_buffer_ts,
                                  const TimeDuration& ts) {
  output_ts_.SetBeginTimeStampNS(input_buffer_ts.GetBeginTimeStampNS());
  output_ts_.SetEndTimeStampNS(ts.GetEndTimeStampNS());
  input_ts_ = ts.GetEndTimeStampNS();
  int32_t output_buffer_cnt = NSDurationToAudioSamples(
      target_format_.GetSampleRate(), output_ts_.GetNSDuration());
  output_ts_.SetSampleCnt(output_buffer_cnt);
  output_ts_.SetTimeBase(target_format_.GetSampleRate());
  DCHECK(output_buffer_cnt <= ts.GetSampleCnt());
  for (int i = 0; i < mix_buffer_.size(); i++) {
    if (mix_buffer_[i].Cnt()) {
      mix_buffer_[i].PopTo((float*)mix_output_buffer_->GetData(i),
                           output_buffer_cnt);
    }
  }
}

bool AudioMixerInputImpl::CheckCanConsumeInputBuffer(
    const std::string_view& debug_str,
    const mediasdk::TimeDuration& target_duration) const {
  std::lock_guard<std::mutex> lock(lock_mix_buffer_);

  const TimeDuration mix_duration = GetInputDuration();

  if (!mix_duration.GetBeginTimeStampNS()) {
    // empty, skip
    return true;
  }
  if (mix_duration.GetEndTimeStampNS() < target_duration.GetEndTimeStampNS()) {
    return false;
  }

  // we can try to get some audio from input buffer
  if (mix_duration.GetBeginTimeStampNS() ==
      target_duration.GetBeginTimeStampNS()) {
    return true;
  } else if (mix_duration.GetBeginTimeStampNS() <
             target_duration.GetBeginTimeStampNS()) {
    return true;
  } else if (mix_duration.GetBeginTimeStampNS() <
             target_duration.GetEndTimeStampNS()) {
    return true;
  } else {
    return true;
  }
  return true;
}

bool AudioMixerInputImpl::ConsumeInputBuffer(
    const std::string_view& debug_str,
    const TimeDuration& target_duration) {
  if (!mix_output_buffer_) {
    mix_output_buffer_ = CreateAlignedAudioFrame(
        kAudioOutputFormat, target_duration.GetSampleCnt());
  }
  output_ts_ = {};
  std::lock_guard<std::mutex> lock(lock_mix_buffer_);

  const TimeDuration mix_duration = GetInputDuration();
#ifdef DEBUG_AUDIO_MIX
  ScopedTask trace([this, &debug_str, &mix_duration]() {
    TimeDuration v = GetInputDuration();
    if (v == mix_duration)
      return;

    AUDIO_MIX_TRACE_DURATION(
        std::string(debug_str) +
            TypeToString(trace::AUDIO_DURATION_TYPE::BUFFER_CHANGE_FROM),
        mix_duration.GetBeginTimeStampNS(), mix_duration.GetNSDuration());
    AUDIO_MIX_TRACE_DURATION(
        std::string(debug_str) +
            TypeToString(trace::AUDIO_DURATION_TYPE::BUFFER_CHANGE_TO),
        v.GetBeginTimeStampNS(), v.GetNSDuration());
  });
#endif
  if (!mix_duration.GetBeginTimeStampNS()) {
    // empty, skip
    return true;
  }
  AUDIO_MIX_TRACE_VALUE(std::string(debug_str) + "_buff_cnt",
                        mix_buffer_[0].Cnt());
  AUDIO_MIX_TRACE_VALUE(std::string(debug_str) + "_begin_diff",
                        (mix_duration.GetBeginTimeStampNS() / 1000000 -
                         target_duration.GetBeginTimeStampNS() / 1000000));
  AUDIO_MIX_TRACE_VALUE(std::string(debug_str) + "_end_diff",
                        (mix_duration.GetEndTimeStampNS() / 1000000 -
                         target_duration.GetEndTimeStampNS() / 1000000));
  AUDIO_MIX_TRACE_VALUE(std::string(debug_str) + "_target_diff_begin",
                        (target_duration.GetEndTimeStampNS() / 1000000 -
                         target_duration.GetBeginTimeStampNS() / 1000000));
  if (mix_duration.GetEndTimeStampNS() < target_duration.GetEndTimeStampNS()) {
    // core audio need to wait for this audio input to produce enough audio
    // samples, or this audio input is not correctly working, need to skip
#ifdef DEBUG_AUDIO_MIX
    LOG(INFO) << "cur [" << mix_duration.GetEndTimeStampNS() << "]"
              << " target [" << target_duration.GetEndTimeStampNS() << "]"
              << " diff ["
              << target_duration.GetEndTimeStampNS() -
                     mix_duration.GetEndTimeStampNS()
              << "]";
#endif
    return false;
  }

  // we can try to get some audio from input buffer
  if (mix_duration.GetBeginTimeStampNS() ==
      target_duration.GetBeginTimeStampNS()) {
    // perfect, consume ts samples
    // mix     |----------------------------------|
    // target  |-------------------|
    //         |----------------------------------|
    AUDIO_MIX_TRACE_VALUE(
        std::string(debug_str) +
            trace::TypeToString(trace::AUDIO_INNER_EVENT_TYPE::PERFECT_CONSUME),
        target_duration.GetBeginTimeStampNS());
    PopFill(target_duration);
    return true;
  } else if (mix_duration.GetBeginTimeStampNS() <
             target_duration.GetBeginTimeStampNS()) {
    // mix     |----------------------------------|
    // target   |-------------------|
    //            |-------------------------------|
    AUDIO_MIX_TRACE_VALUE(
        std::string(debug_str) +
            trace::TypeToString(
                trace::AUDIO_INNER_EVENT_TYPE::INPUT_MIDDLLE_CONSUME),
        mix_duration.GetBeginTimeStampNS());

    PopMiddle(mix_duration, target_duration);
    return true;
  } else if (mix_duration.GetBeginTimeStampNS() <
             target_duration.GetEndTimeStampNS()) {
    // mix                       |----------------------------------|
    // target  |------------------|
    AUDIO_MIX_TRACE_VALUE(
        std::string(debug_str) +
            trace::TypeToString(
                trace::AUDIO_INNER_EVENT_TYPE::OUTPUT_MIDDLE_CONSUME),
        mix_duration.GetBeginTimeStampNS());
    PopPart(mix_duration, target_duration);
    return true;
  } else {
    // mix                       |----------------------------------|
    // target |------------------|
    //               |----------|
    // mix buffer is too new for target
    output_ts_ = {};
    return true;
  }
  DCHECK(false);
  return true;
}

int64_t AudioMixerInputImpl::GetLastUpdateNS() {
  if (!last_update_ts_)
    return std::numeric_limits<std::int64_t>::max();

  return last_update_ts_;
}

void AudioMixerInputImpl::GetErrorCnt(int32_t& too_late, int32_t& too_early) {
  too_late = error_cnt_too_late_;
  too_early = error_cnt_too_early_;
  error_cnt_too_early_ = 0;
  error_cnt_too_late_ = 0;
}

TimeDuration AudioMixerInputImpl::GetInputDuration() const {
  if (!input_ts_) {
    return {};
  } else {
    TimeDuration time = {};
    time.SetBeginTimeStampNS(input_ts_);
    time.SetEndTimeStampNS(time.GetBeginTimeStampNS() +
                           SamplesToNSDuration(target_format_.GetSampleRate(),
                                               mix_buffer_[0].Cnt()));
    time.SetSampleCnt(mix_buffer_[0].Cnt());
    time.SetTimeBase(target_format_.GetSampleRate());
    return time;
  }
}

TimeDuration AudioMixerInputImpl::GetOutputDuration() {
  return output_ts_;
}

void AudioMixerInputImpl::UpdateLastUpdateTS(int64_t ts) {
  last_update_ts_ = ts;
}

bool IsPerfect(const TimeDuration& input_buffer,
               const TimeDuration& audio_frame) {
  return audio_frame.GetBeginTimeStampNS() == input_buffer.GetEndTimeStampNS();
}

bool IsALittleEarly(const TimeDuration& input_buffer,
                    const TimeDuration& audio_frame) {
  if (audio_frame.GetBeginTimeStampNS() < input_buffer.GetEndTimeStampNS()) {
    if (input_buffer.GetEndTimeStampNS() - audio_frame.GetBeginTimeStampNS() <
        30000000ll) {
      return true;  // 30ms for a little early
    }
    return false;
  }
  return false;
}

bool IsALittleLate(const TimeDuration& input_buffer,
                   const TimeDuration& audio_frame) {
  if (audio_frame.GetBeginTimeStampNS() > input_buffer.GetEndTimeStampNS()) {
    if (audio_frame.GetBeginTimeStampNS() - input_buffer.GetEndTimeStampNS() <
        30000000ll) {
      return true;  // 30ms for a little late
    }
    return false;
  }
  return false;
}

bool IsTooLate(const TimeDuration& input_buffer,
               const TimeDuration& audio_frame) {
  if (audio_frame.GetBeginTimeStampNS() > input_buffer.GetEndTimeStampNS()) {
    return !IsALittleLate(input_buffer, audio_frame);
  }
  return false;
}

bool IsTooEarly(const TimeDuration& input_buffer,
                const TimeDuration& audio_frame) {
  if (audio_frame.GetBeginTimeStampNS() < input_buffer.GetEndTimeStampNS()) {
    return !IsALittleEarly(input_buffer, audio_frame);
  }
  return false;
}

void AudioMixerInputImpl::PushBack(const std::string_view& debug_str,
                                   const TimeDuration& mix_buffer_duration,
                                   const AudioFrame& apk) {
  const auto apk_ts = apk.GetTimeDuration();
  constexpr int64_t MAX_AUDIO_BUFFER_TS = 20000000000ll;  // 20s
  if (mix_buffer_duration.GetNSDuration() >= MAX_AUDIO_BUFFER_TS) {
    AUDIO_MIX_TRACE_VALUE(
        std::string(debug_str) +
            trace::TypeToString(
                trace::AUDIO_INNER_EVENT_TYPE::MIX_BUFFER_OVER_FLOW),
        mix_buffer_duration.GetBeginTimeStampNS());
    // need limit max audio input buffer length
    for (int i = 0; i < target_format_.GetPlane(); i++) {
      if (void* data = apk.GetData(i)) {
        mix_buffer_[i].Clear();  // remove early audio packet
      }
    }
    input_ts_ +=
        SamplesToNSDuration(target_format_.GetSampleRate(), apk.GetCount());
    return;
  } else {
    AUDIO_MIX_TRACE_VALUE(
        std::string(debug_str) +
            trace::TypeToString(
                trace::AUDIO_INNER_EVENT_TYPE::REALLY_PUSH_BACK),
        apk_ts.GetBeginTimeStampNS());
    if (!input_ts_) {
      input_ts_ = apk_ts.GetBeginTimeStampNS();
    }

    for (int i = 0; i < target_format_.GetPlane(); i++) {
      if (apk.GetData(i)) {
        mix_buffer_[i].Add((const float*)apk.GetData(i), apk.GetCount());
      } else {
        DCHECK(false);
      }
    }
    return;
  }
}

void AudioMixerInputImpl::ResetInputBuffer() {
  input_ts_ = 0;
  for (auto& buffer : mix_buffer_) {
    buffer.Clear();
  }
}

void AudioMixerInputImpl::BufferAudioFrame(const std::string_view& debug_str,
                                           const AudioFrame& frame) {
  UpdateLastUpdateTS(nano_now());
  auto frame_duration = frame.GetTimeDuration();
  AUDIO_MIX_TRACE_DURATION(
      std::string(debug_str) +
          trace::TypeToString(trace::AUDIO_DURATION_TYPE::FRAME),
      frame_duration.GetBeginTimeStampNS(), frame_duration.GetNSDuration());
  std::lock_guard<std::mutex> lock(lock_mix_buffer_);

  auto mix_duration = GetInputDuration();
#ifdef DEBUG_AUDIO_MIX
  ScopedTask trace([this, &debug_str, &mix_duration]() {
    TimeDuration v = GetInputDuration();
    if (v == mix_duration)
      return;

    AUDIO_MIX_TRACE_DURATION(
        std::string(debug_str) +
            trace::TypeToString(trace::AUDIO_DURATION_TYPE::BUFFER_CHANGE_FROM),
        mix_duration.GetBeginTimeStampNS(), mix_duration.GetNSDuration());
    AUDIO_MIX_TRACE_DURATION(
        std::string(debug_str) +
            trace::TypeToString(trace::AUDIO_DURATION_TYPE::BUFFER_CHANGE_TO),
        v.GetBeginTimeStampNS(), v.GetNSDuration());
  });
#endif
  int64_t diff = (frame_duration.GetBeginTimeStampNS() -
                  mix_duration.GetEndTimeStampNS()) /
                 1000000ll;
  if (input_ts_) {
    AUDIO_MIX_TRACE_VALUE(std::string(debug_str) + "_frame_buffer_diff", diff);
#ifdef DEBUG_AUDIO_MIX
    LOG(INFO) << "frame diff [" << diff << "]";
#endif
  }

  if (!input_ts_) {
    // handle first input
    AUDIO_MIX_TRACE_DURATION(
        std::string(debug_str) +
            TypeToString(trace::AUDIO_DURATION_TYPE::FIRST_FRAME),
        frame_duration.GetBeginTimeStampNS(), frame_duration.GetNSDuration());
    PushBack(debug_str, mix_duration, frame);
    return;
  }

  if (IsPerfect(mix_duration, frame_duration)) {
    PushBack(debug_str, mix_duration, frame);
  } else {
    if (IsALittleLate(mix_duration, frame_duration)) {
      AUDIO_MIX_TRACE_VALUE(
          std::string(debug_str) +
              trace::TypeToString(trace::AUDIO_INNER_EVENT_TYPE::A_LITTLE_LATE),
          frame_duration.GetBeginTimeStampNS());
      PushBack(debug_str, mix_duration, frame);
    } else if (IsALittleEarly(mix_duration, frame_duration)) {
      AUDIO_MIX_TRACE_VALUE(
          std::string(debug_str) +
              trace::TypeToString(
                  trace::AUDIO_INNER_EVENT_TYPE::A_LITTLE_EARLY),
          frame_duration.GetBeginTimeStampNS());
      PushBack(debug_str, mix_duration, frame);
    } else if (IsTooLate(mix_duration, frame_duration)) {
      AUDIO_MIX_TRACE_VALUE(
          std::string(debug_str) +
              trace::TypeToString(trace::AUDIO_INNER_EVENT_TYPE::TOO_LATE),
          frame_duration.GetBeginTimeStampNS());
      const auto now = mediasdk::low_precision_milli_now();
      if (now - last_log_ms_ > 200) {
        LOG(WARNING) << "audio too late[" << mix_duration.ToString() << "] ["
                     << frame_duration.ToString();
        last_log_ms_ = now;
      }
      ++error_cnt_too_late_;
      ResetInputBuffer();
    } else if (IsTooEarly(mix_duration, frame_duration)) {
      AUDIO_MIX_TRACE_VALUE(
          std::string(debug_str) +
              trace::TypeToString(trace::AUDIO_INNER_EVENT_TYPE::TOO_EARLY),
          frame_duration.GetBeginTimeStampNS());
      const auto now = mediasdk::low_precision_milli_now();
      if (now - last_log_ms_ > 200) {
        LOG(WARNING) << "audio too early[" << mix_duration.ToString() << "] ["
                     << frame_duration.ToString();
        last_log_ms_ = now;
      }
      ++error_cnt_too_early_;
      ResetInputBuffer();
    }
  }
  // limit buffer max size less than kMaxAudioBufferSeconds seconds
  if (mix_buffer_[0].Cnt() >
      kMaxAudioBufferSeconds * kAudioOutputFormat.GetSampleRate()) {
    LOG(WARNING) << "buffer too long, pop from begin [" << debug_str << "] ["
                 << input_ts_ << "]";

    for (auto& mix_buffer : mix_buffer_) {
      if (mix_buffer.Cnt()) {
        mix_buffer.PopTo(nullptr, frame.GetCount());
      }
    }
    input_ts_ += SamplesToNSDuration(kAudioOutputFormat.GetSampleRate(),
                                     frame.GetCount());
  }
}

void AudioMixerInputImpl::Trace(const std::string_view& str) {
#ifdef DEBUG_AUDIO_MIX
  LOG(INFO) << "[" << str << "]";
  LOG(INFO) << "\tinput ts[" << input_ts_ << "]";
  LOG(INFO) << "\tbuff cnt[" << mix_buffer_[0].Cnt() << "]";
  auto mix_duration = GetInputDuration();
  LOG(INFO) << "\tmix buffer[" << mix_duration.ToString() << "]";
#endif
}

std::shared_ptr<AudioMixerInput> CreateAudioMixerInput() {
  return std::make_shared<AudioMixerInputImpl>();
}

}  // namespace mediasdk