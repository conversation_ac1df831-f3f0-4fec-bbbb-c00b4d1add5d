#pragma once

#include <stdint.h>
#include <map>
#include <memory>
#include <mutex>
#include <string>

namespace mediasdk {

class VisualFpsStoreCalc {
 public:
  VisualFpsStoreCalc(const std::string& visual_id);

  virtual ~VisualFpsStoreCalc();

  std::string GetVisualId() { return visual_id_; }

  virtual float CalcFps() = 0;

 private:
  std::string visual_id_;
};

class VisualFpsStore {
 public:
  VisualFpsStore();

  ~VisualFpsStore();

  // Register a VisualFpsStoreCalc instance
  void RegisterVisualFpsCalc(VisualFpsStoreCalc* calc);

  // Unregister a VisualFpsStoreCalc instance
  void UnregisterVisualFpsCalc(VisualFpsStoreCalc* calc);

  // Get the visual FPS for a given visual ID
  std::pair<bool, float> GetVisualFps(const std::string& visual_id);

 private:
  std::mutex calc_map_mtx_;
  std::map<std::string, VisualFpsStoreCalc*> calc_map_;
};

}  // namespace mediasdk
