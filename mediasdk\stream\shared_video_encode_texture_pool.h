#pragma once

#include <memory>
#include <vector>

#include "graphics/device.h"
#include "shared_video_encode_texture.h"

namespace mediasdk {

class SharedVideoEncodeTexturePool
    : public std::enable_shared_from_this<SharedVideoEncodeTexturePool> {
 public:
  static std::shared_ptr<SharedVideoEncodeTexturePool> Create(
      uint32_t max_count,
      graphics::Device& device);

  ~SharedVideoEncodeTexturePool();
  std::shared_ptr<SharedVideoEncodeTexture> Borrow(uint32_t width,
                                                   uint32_t height);

 private:
  SharedVideoEncodeTexturePool(uint32_t max_count, graphics::Device& device);

  void ReturnInternal(SharedVideoEncodeTexture* texture);
  void ClearInternal();

  graphics::Device& device_;
  const uint32_t max_count_ = 0;
  uint32_t active_count_ = 0;
  uint32_t width_ = 0;
  uint32_t height_ = 0;
  std::vector<SharedVideoEncodeTexture*> pool_;
  scoped_refptr<base::SequencedTaskRunner> task_runner_;
};

}  // namespace mediasdk
