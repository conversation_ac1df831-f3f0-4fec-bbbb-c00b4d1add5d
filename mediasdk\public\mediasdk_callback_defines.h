#pragma once

#include <stdint.h>
#include "mediasdk_defines.h"
#include "mediasdk_export.h"

#pragma pack(push, 1)

namespace mediasdk {

struct ResultBoolFloat {
  bool success;
  float value;
};

struct ResultBoolBool {
  bool success;
  bool value;
};

struct ResultBoolInt32 {
  bool success;
  int32_t value;
};

struct ResultBoolUint32 {
  bool success;
  uint32_t value;
};

struct ResultBoolString {
  bool success;
  MediaSDKString value;
};

struct ResultBoolMSRect {
  bool success;
  MSRect value;
};

struct ResultBoolMSRectF {
  bool success;
  MSRectF value;
};

struct ResultBoolMSClipF {
  bool success;
  MSClipF value;
};

struct ResultBoolMSScaleF {
  bool success;
  MSScaleF value;
};

struct ResultBoolMSTranslateF {
  bool success;
  MSTranslateF value;
};

struct ResultBoolMSSizeF {
  bool success;
  MSSizeF value;
};

struct ResultBoolMSTransform {
  bool success;
  MSTransform value;
};

struct ResultBoolMSVisualFrame {
  bool success;
  MSVisualFrame frame;
};

struct PerformanceInfo {
  float cpu_usage;
  float cpu_global_usage;
  float memory_usage;
  float active_fps;
  float sdk_memory_usage;
  float total_mem;
  float cpu_global_utilization;
};

struct MonitorInfo {
  void* monitor_handle;
  int gpu_index;
  int left;
  int top;
  int right;
  int bottom;
  int refresh;
  bool hdr;
};

struct CpuInfo {
  MediaSDKString name;
  uint32_t processor_num;
  uint32_t clock_speed;
  double process_usage;
  double system_usage;
  double system_time;
};

struct MemoryInfo {
  int memory_size;
  int system_used_memory_size;
  int process_memory_size;
  int page_fault;
  float memory_usage;
};

struct DeviceInfo {
  MediaSDKString adapter_name;
  MediaSDKString pci_id;
  MediaSDKString driver_version;
  MediaSDKString luid;
  int64_t dedicated_memory_size;
  int64_t shared_memory_size;
  int64_t system_3d_usage;
  int64_t process_3d_usage;
  bool current_gpu;
};

struct EncoderStatisticInfo {
  MediaSDKString video_codec_name;
  double video_enc_fps;
  double video_encode_err_fps;
};

struct StatisticInfo {
  MediaSDKString video_codec_name;
  double video_enc_fps;
  double video_enc_bitrate;
  double audio_enc_bitrate;
  double video_encode_err_fps;
  double video_send_fps;
  double send_bitrate;
  double video_send_bitrate;
  double audio_send_bitrate;
  double total_send_bitrate;
  int64_t total_drops;
  int64_t total_video_packets;
  int64_t package_delay_ms;
};

struct EncoderTsInfo {
  int64_t video_in_pts;
  int64_t video_out_pts;
  int64_t video_out_dts;
  int64_t audio_in_pts;
  int64_t audio_out_pts;
  int64_t audio_out_dts;
};

struct RenderInfo {
  int64_t fps;
  double no_ready_fps;
  double present_ready_fps;
  uint64_t gpu_num;
};

struct EncoderBitrate {
  uint32_t bitrate;
  uint32_t error_code;
};

typedef MediaSDKArray<DeviceInfo> DeviceInfoArray;

enum MSLogSeverity {
  kMSLogSeverityVerbose = -1,
  kMSLogSeverityInfo = 0,
  kMSLogSeverityWarning = 1,
  kMSLogSeverityError = 2,
  kMSLogSeverityFatal = 3,
  kMSLogSeverityNumSeverities = 4,
};

enum kReconfigVideoEncoderErrorCode {
  kReconfigVideoEncoderErrorCodeSuccess = 0,
  kReconfigVideoEncoderErrorCodeInvalidCodecID = 1,
  kReconfigVideoEncoderErrorCodeInvalidBitrate = 2,
  kReconfigVideoEncoderErrorCodeReconfigFailed = 3,
};

typedef bool(MS_API* LogHandlerFunc)(int32_t log_severity, const char* log_msg);

typedef void(MS_API* Callback)(uint32_t call_id, void* result, void* ctx);

typedef struct {
  Callback cb;
  void* ctx;
  uint32_t call_id;
} Closure;

}  // namespace mediasdk

#pragma pack(pop)
