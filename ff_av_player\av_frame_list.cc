#include "av_frame_list.h"
#include <base/check.h>

int32_t AVFrameList::GetLeftFrameCnt() {
  std::lock_guard<std::mutex> lock(lock_frame_list_);
  return frame_list_.size();
}

void AVFrameList::PushFrame(AVFrame& frame,int32_t serial) {
  AVFrame* ref = av_frame_clone(&frame);
  std::lock_guard<std::mutex> lock(lock_frame_list_);
  frame_list_.push_back({ref, serial});
}

AVFrameWithSerial AVFrameList::PopFrame() {
  std::lock_guard<std::mutex> lock(lock_frame_list_);
  if (frame_list_.empty())
    return {};
  else {
    AVFrameWithSerial  ret = *(frame_list_.begin());
    DCHECK(ret.frame && "not impl");
    frame_list_.pop_front();
    return ret;
  }
}

int64_t AVFrameList::GetFirstFrameTS(const AVRational& time_base) {
  std::lock_guard<std::mutex> lock(lock_frame_list_);
  if (frame_list_.size()) {
    auto& begin = *frame_list_.begin();

   return av_q2d(time_base) * 1000ll * begin.frame->pts;
  } else {
    return -1;
  }
}

AVFrameList::~AVFrameList() {
  Clear();
}

void AVFrameList::Clear() {
  std::list<AVFrameWithSerial> list;
  {
    std::lock_guard<std::mutex> lock(lock_frame_list_);
    std::swap(list, frame_list_);
  }

  for (auto& frame : list) {
    av_frame_free(&frame.frame);
  }
  list.clear();
}