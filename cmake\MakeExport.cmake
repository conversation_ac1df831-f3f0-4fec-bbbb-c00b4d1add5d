
file(GLOB EXPORT_INCLUDE_FILES
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_api.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_api_alloc.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_api_audio.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_api_audio_filter.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_api_effect_platform.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_api_lyrax.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_api_rtc.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_api_visual.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_api_visual_filter.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_api_vqm.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_api_canvas.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_array.hpp"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_audio_status_observer.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_binary_data.hpp"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_callback_defines.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_defines.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_defines_audio.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_defines_canvas.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_defines_rtc.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_defines_stream_error_code.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_defines_video.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_defines_visual.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_defines_visual_filter.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_effect_platform_event_observer.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_export.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_global_event_observer.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_rtc_event_observer.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_stream_status_observer.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_string.hpp"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_video_frame_type.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_video_nalu_type.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_visual_event_observer.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_window_event_observer.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_canvas_item_event_observer.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_virtual_camera_event_observer.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/audio_encoder_config.hpp"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/video_encoder_config.hpp"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_nv_black_list.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_trace_config.h"
	"${CMAKE_SOURCE_DIR}/mediasdk/public/mediasdk_trace_event.h"
	)
	
file(GLOB EXPORT_JSON_INCLUDE_FILES
	"${CMAKE_SOURCE_DIR}/third_party/nlohmann/include/nlohmann/json.hpp"
	)

set(EXPORT_RTC_HELPER_FILES)
file(GLOB children LIST_DIRECTORIES true
	${CMAKE_SOURCE_DIR}/mediasdk/rtc/*helper.h)

foreach(child ${children})
	list(APPEND EXPORT_RTC_HELPER_FILES ${child})
endforeach()

set(EXPORT_PLUGINS_INCLUDE_FILES)
file(GLOB children LIST_DIRECTORIES true 
	${CMAKE_SOURCE_DIR}/plugins/visual_source/*
	${CMAKE_SOURCE_DIR}/plugins/audio_source/*
	${CMAKE_SOURCE_DIR}/plugins/audio_filter/*
	${CMAKE_SOURCE_DIR}/plugins/stream_service_source/*
	${CMAKE_SOURCE_DIR}/plugins/encoder_source/*
	${CMAKE_SOURCE_DIR}/plugins/visual_filter/*)
foreach(child ${children})
    if(IS_DIRECTORY ${child})
        file(GLOB HELPED_FILES "${child}/*helper.h")
        if(HELPED_FILES)
            list(APPEND EXPORT_PLUGINS_INCLUDE_FILES ${HELPED_FILES})
        endif()
    endif()
endforeach()

function(export_include datadest)
	set(EXPORT_DST_PATH_INCLUDE "${CMAKE_SOURCE_DIR}/bins/export/include/${datadest}")
	foreach(exportHFile ${EXPORT_INCLUDE_FILES})
		message(STATUS "copying ${exportHFile} to ${EXPORT_DST_PATH_INCLUDE}")
		file(COPY "${exportHFile}" DESTINATION "${EXPORT_DST_PATH_INCLUDE}/")
	endforeach(exportHFile ${EXPORT_INCLUDE_FILES})
	set(EXPORT_JSON_DST_PATH_INCLUDE "${CMAKE_SOURCE_DIR}/bins/export/include/${datadest}/nlohmann")
	foreach(exportHJsonFile ${EXPORT_JSON_INCLUDE_FILES})
		message(STATUS "copying ${exportHJsonFile} to ${EXPORT_JSON_DST_PATH_INCLUDE}")
		file(COPY "${exportHJsonFile}" DESTINATION "${EXPORT_JSON_DST_PATH_INCLUDE}/")
	endforeach(exportHJsonFile ${EXPORT_JSON_INCLUDE_FILES})
	set(EXPORT_RTC_HELPER_PATH_INCLUDE "${CMAKE_SOURCE_DIR}/bins/export/include/${datadest}/RTC")
	foreach(exportHRtcFile ${EXPORT_RTC_HELPER_FILES})
		message(STATUS "copying ${exportHRtcFile} to ${EXPORT_RTC_HELPER_PATH_INCLUDE}")
		file(COPY "${exportHRtcFile}" DESTINATION "${EXPORT_RTC_HELPER_PATH_INCLUDE}/")
	endforeach(exportHRtcFile ${EXPORT_RTC_HELPER_FILES})	
	set(EXPORT_PLUGINS_DST_PATH_INCLUDE "${CMAKE_SOURCE_DIR}/bins/export/include/${datadest}/plugins")
	foreach(exportHPLUGINSFile ${EXPORT_PLUGINS_INCLUDE_FILES})
		message(STATUS "copying ${exportHPLUGINSFile} to ${EXPORT_PLUGINS_DST_PATH_INCLUDE}")
		file(COPY "${exportHPLUGINSFile}" DESTINATION "${EXPORT_PLUGINS_DST_PATH_INCLUDE}/")
	endforeach(exportHPLUGINSFile ${EXPORT_PLUGINS_INCLUDE_FILES})
endfunction()
