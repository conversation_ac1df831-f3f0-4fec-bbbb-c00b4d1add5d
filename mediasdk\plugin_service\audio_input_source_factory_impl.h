
#pragma once

#include <memory>

#include <base/native_library.h>
#include "mediasdk/plugin_service/audio_input_source_factory.h"

namespace mediasdk {

class AudioInputSourceFactoryImpl : public AudioInputSourceFactory {
 public:
  static std::shared_ptr<AudioInputSourceFactory> Create(
      base::NativeLibrary library,
      std::shared_ptr<PluginInfo> info);

  AudioInputSourceFactoryImpl(base::NativeLibrary library,
                              std::shared_ptr<PluginInfo> info);

  ~AudioInputSourceFactoryImpl() override;

  std::shared_ptr<AudioInputSource> CreateSource(
      std::shared_ptr<AudioInputProxy> proxy,
      const std::string& json_params) override;

  void Destroy(std::shared_ptr<AudioInputSource> source) override;

  void DestroyAll() override { sources_.clear(); }

  MediaSDKStringData EnumAudioInput() override;

  MediaSDKStringData GetDefaultAudioInput() override;

  MediaSDKStringData GetDefaultAudioOutput() override;

  MediaSDKStringData EnumCaptureAudio() override;

  MediaSDKStringData EnumRenderAudio() override;

 private:
  bool Load();

 private:
  typedef AudioInputSource* (*CreateAudioSourceFunc)(AudioInputProxy*,
                                                     const char*);
  using DestroyAudioSourceFunc = void (*)(AudioInputSource*);
  typedef MediaSDKStringData (*EnumAudioInputDeviceFunc)();
  typedef MediaSDKStringData (*GetDefaultAudioInputDeviceFunc)();
  typedef MediaSDKStringData (*GetDefaultAudioOutDeviceFunc)();
  typedef MediaSDKStringData (*EnumCaptureAudioDeviceFunc)();
  typedef MediaSDKStringData (*EnumRenderAudioDeviceFunc)();

 private:
  static std::function<void(AudioInputSource*)> DestroyWithCheck(
      DestroyAudioSourceFunc);

 private:
  CreateAudioSourceFunc create_func_ = nullptr;
  DestroyAudioSourceFunc destroy_func_ = nullptr;
  EnumAudioInputDeviceFunc enum_input_device_func_ = nullptr;
  GetDefaultAudioInputDeviceFunc get_default_audio_input_device_func_ = nullptr;
  GetDefaultAudioOutDeviceFunc get_default_audio_output_device_func_ = nullptr;
  EnumCaptureAudioDeviceFunc enum_capture_audio_device_func_ = nullptr;
  EnumRenderAudioDeviceFunc enum_render_audio_device_func_ = nullptr;

  std::vector<std::shared_ptr<AudioInputSource>> sources_;
};

}  // namespace mediasdk
