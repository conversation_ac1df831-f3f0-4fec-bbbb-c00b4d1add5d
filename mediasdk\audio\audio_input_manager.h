
#pragma once

#include <cstdint>
#include <map>
#include <vector>

#include <audio/audio_format.h>
#include <audio/audio_input_change_observer.h>
#include <base/timer/timer.h>
#include <mediasdk/callback_utils.h>
#include <mediasdk/public/mediasdk_defines_audio.h>
#include "base/mediasdk/observer_list_thread_safe_weak.h"
#include "source_audio_input.h"

namespace mediasdk {

class AudioInput;
class AudioFilter;
class AudioTrack;
class AudioInputSource;
class LyraxAudioInput;
class LyraxAudioProcessor;

class AudioInputManager : public base::SupportsWeakPtr<AudioInputManager> {
 public:
  using TracksType = std::map<uint32_t, std::shared_ptr<AudioTrack>>;

  AudioInputManager();

  ~AudioInputManager();

  AudioFormat GetOutputFormat() const;

  void CreateAudioInput(const std::string& id,
                        const CreateAudioParams& params,
                        MSCallbackBool callback);

  void CreateLyraxAudioInput(const std::string& id,
                             const CreateAudioParams& params,
                             MSCallbackBool callback);

  ResultBoolBool AddAudioInputToTrack(const std::string& id,
                                      const uint32_t& track_id);

  void DestroyAudioInput(const std::string& id, MSCallbackBool callback);

  ResultBoolBool RemoveAudioInputFromTrack(const std::string& id,
                                           uint32_t track_id);

  void DestroyAllAudioInput(MSCallbackBool callback);

  bool IsAudioInputExist(const std::string& id);

  std::shared_ptr<AudioInput> GetAudioInput(const std::string& id);

  std::shared_ptr<AudioTrack> GetAudioTrack(uint32_t track_id);

  TracksType& GetTracksRef();

  std::vector<std::string> GetAudioInputIds();

  std::vector<std::shared_ptr<AudioInput>> GetAudioInputList();

  bool AddAudioInput(uint32_t track_id,
                     std::shared_ptr<AudioInput> audio_input);

  void RemoveAudioInput(const std::string& id);

  void DestroyAudioInputFromVisual(const std::string& id);

  void CheckAudioTrackExisted(uint32_t track_id);

  void GetAudioInputPerformance(const std::string& audio_input_id,
                                MSAudioPerformance& performance);

  MediaSDKString EnumAudioInputsInTrack(uint32_t track_id);

 private:
  // Detach and destroy related audio input source
  void DestroyAudioInputAndSource(const std::shared_ptr<AudioInput> audio_input,
                                  MSCallbackBool callback);
  void OnLyraxAudioInputCreated(
      MSCallbackBool callback,
      const CreateAudioParams& params,
      std::shared_ptr<LyraxAudioInput> audio_input,
      std::shared_ptr<LyraxAudioProcessor> audio_processor);

  void OnAudioInputSourceCreated(uint32_t track_id,
                                 const std::string& plugin_name,
                                 MSCallbackBool callback,
                                 std::shared_ptr<SourceAudioInput> audio_input,
                                 std::shared_ptr<AudioInputSource> source);

  void OnAudioInputSourceDestroyed(std::shared_ptr<AudioInput> audio_input,
                                   MSCallbackBool callback);

  void OnAllAudioInputSourceDestroyed(
      std::vector<std::shared_ptr<AudioInput>> audio_input_list,
      MSCallbackBool callback);

  void CreateAudioInputWithParam(std::shared_ptr<SourceAudioInput> audio_input,
                                 const CreateAudioParams& params,
                                 MSCallbackBool callback);

  void OnOutputAudioLayoutTimer();

  std::string GetLayoutString();

 private:
  std::map<uint32_t, std::shared_ptr<AudioTrack>> tracks_;
  AudioFormat output_format_;
  base::RepeatingTimer output_audio_layout_timer_;
  std::string pre_layout_str_;
};

}  // namespace mediasdk
