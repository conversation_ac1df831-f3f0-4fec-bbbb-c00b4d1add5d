#include "effect_platform_controller_impl.h"

#include "base/bind.h"
#include "base/logging.h"

#include "mediasdk/component_proxy.h"
#include "mediasdk/effect_platform/effect_sdk_loader.h"
#include "mediasdk/effect_platform/ep_engine.h"
#include "mediasdk/effect_platform/ep_helper.h"
#include "mediasdk/mediasdk_thread.h"

namespace mediasdk {

EffectPlatformControllerImpl::EffectPlatformControllerImpl() {}

EffectPlatformControllerImpl::~EffectPlatformControllerImpl() {}

bool EffectPlatformControllerImpl::Initialize() {
  LOG(INFO) << "[EffectPlatform] initialized.";
  return true;
}

void EffectPlatformControllerImpl::Uninitialize() {
  LOG(INFO) << "[EffectPlatform] begin to uninitialize.";
  EPUninitialize();
  LOG(INFO) << "[EffectPlatform] uninitialized.";
  return;
}

bool EffectPlatformControllerImpl::EPInitialize(const std::string& json_param) {
  DCHECK_CURRENTLY_ON(ThreadID::NOTIFY);
  LOG(INFO) << "[EffectPlatform] ep init param:" << json_param;

  if (!ep::EffectSdkLoader::GetInstance()->Load()) {
    LOG(ERROR) << "[EffectPlatform] ep init load lib failed";
    return false;
  }
  ep_engine_ = std::make_shared<ep::Engine>(this);
  if (!ep_engine_) {
    LOG(ERROR) << "[EffectPlatform] ep engine not found";
    return false;
  }
  auto ret = ep_engine_->Initialize(json_param);
  if (ret) {
    event_observer_ =
        scoped_refptr<base::ObserverListThreadSafe<MediaSDKEPEventObserver>>(
            new base::ObserverListThreadSafe<MediaSDKEPEventObserver>());
    LOG(INFO) << "[EffectPlatform] ep engine init success";
  } else {
    LOG(ERROR) << "[EffectPlatform] ep engine init failed";
  }

  return ret;
}

bool EffectPlatformControllerImpl::EPUninitialize() {
  DCHECK_CURRENTLY_ON(ThreadID::NOTIFY);

  if (ep_engine_) {
    ep_engine_->UnInitialize();
    ep_engine_.reset();
  }

  if (event_observer_) {
    event_observer_.reset();
  }

  // FE may call "EPUninitialize" while the process is running.
  // We don't free libraries in case of some null pointer related crashes
  //ep::EffectSdkLoader::GetInstance()->Clear();

  LOG(INFO) << "[EffectPlatform] ep engine uninitialized";
  return true;
}

bool EffectPlatformControllerImpl::EPRegistEventObserver(
    MediaSDKEPEventObserver* observer) {
  DCHECK_CURRENTLY_ON(ThreadID::NOTIFY);
  DCHECK(observer);
  DCHECK(event_observer_);

  if (!event_observer_) {
    LOG(ERROR) << "[EffectPlatform] need initialize first";
    return false;
  }

  if (!observer) {
    LOG(ERROR) << "[EffectPlatform] observer null, add failed";
    return false;
  }

  event_observer_->AddObserver(observer);
  LOG(INFO) << "[EffectPlatform] observer:" << std::hex << observer << " added";
  return true;
}

bool EffectPlatformControllerImpl::EPUnregistEventObserver(
    MediaSDKEPEventObserver* observer) {
  if (!event_observer_) {
    LOG(ERROR) << "[EffectPlatform] need initialize first";
    return false;
  }

  if (observer) {
    event_observer_->RemoveObserver(observer);
    LOG(INFO) << "[EffectPlatform] observer:" << std::hex << observer
              << " removed";
  } else {
    LOG(WARNING) << "[EffectPlatform] null observer";
  }
  return true;
}

bool EffectPlatformControllerImpl::EPUpdateConfig(
    const std::string& user_id,
    const std::string& hardware_level) {
  DCHECK_CURRENTLY_ON(ThreadID::NOTIFY);

  if (!ep_engine_) {
    LOG(ERROR) << "[EffectPlatform] ep engine not found for(" << user_id << ","
               << hardware_level << ")";
    return false;
  }
  ep_engine_->UpdateUrlParameter("user_id", user_id);
  ep_engine_->UpdateUrlParameter("ttls_hardware_level", hardware_level);

  LOG(INFO) << "[EffectPlatform] ep engine update(" << user_id << ","
            << hardware_level << ") success";
  return true;
}

bool EffectPlatformControllerImpl::EPLoadModels(
    const std::vector<std::string>& requirments,
    const std::string& request_id,
    const std::string& model_name) {
  DCHECK_CURRENTLY_ON(ThreadID::NOTIFY);

  if (!ep_engine_) {
    LOG(ERROR) << "[EffectPlatform] ep engine not found for(" << request_id
               << ", model_name:" << model_name << ")";
    return false;
  }
  ep_engine_->LoadModels(requirments, model_name, request_id);

  LOG(INFO) << "[EffectPlatform] load model for(" << request_id
            << ", model_name:" << model_name << ") success";
  return true;
}

bool EffectPlatformControllerImpl::UseFinder(const ep::Finder& finder) {
  if (!ep_engine_) {
    return false;
  }
  auto rf = ep_engine_->GetResourceFinder();
  return finder.SetFinder(reinterpret_cast<void*>(rf.finder),
                          rf.finder_releaser);
}

bool EffectPlatformControllerImpl::IsLoaded() {
  return ep::EffectSdkLoader::GetInstance()->IsLoaded();
}

void EffectPlatformControllerImpl::OnDownloadModelSuccess(
    const std::string& request_id) {
  DCHECK(event_observer_);

  if (!event_observer_) {
    LOG(ERROR) << "[EffectPlatform] model download success, but not observer";
    return;
  }
  LOG(INFO) << "[EffectPlatform] model download(" << request_id << ") success";
  event_observer_->Notify(FROM_HERE,
                          &MediaSDKEPEventObserver::OnDownloadModelSuccess,
                          MediaSDKString(request_id));
}

void EffectPlatformControllerImpl::OnDownloadModelError(
    const std::string& request_id,
    const std::string& error) {
  DCHECK(event_observer_);

  if (!event_observer_) {
    LOG(ERROR)
        << "[EffectPlatform] model download failed, but not observer for:"
        << request_id << ",error:" << error;
    return;
  }
  LOG(ERROR) << "[EffectPlatform] model download(" << request_id << "," << error
             << ") failed";

  event_observer_->Notify(FROM_HERE,
                          &MediaSDKEPEventObserver::OnDownloadModelError,
                          MediaSDKString(request_id), MediaSDKString(error));
}

void EffectPlatformControllerImpl::OnDownloadModelProgress(
    const std::string& request_id,
    int progress) {
  DCHECK(event_observer_);

  if (!event_observer_) {
    LOG(ERROR)
        << "[EffectPlatform] model download progress, but not observer, for("
        << request_id << "," << progress << ")";
    return;
  }
  LOG(INFO) << "[EffectPlatform] model download(" << request_id
            << ") progress:" << progress;
  event_observer_->Notify(FROM_HERE,
                          &MediaSDKEPEventObserver::OnDownloadModelProgress,
                          MediaSDKString(request_id), progress);
}

}  // namespace mediasdk
