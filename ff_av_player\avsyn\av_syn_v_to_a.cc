#include "av_syn_v_to_a.h"
#include <base/check.h>
#include <base/logging.h>

int64_t AVSynClock::GetCurrentClock() const {
  return global_clock_.NowMS();
}

int64_t AVSynClock::GetVideoClock() const {
  return video_clock_.NowMS();
}

void AVSynClock::Reset(int32_t serial) {
  serial_ = serial;
  audio_clock_.Reset();
  video_clock_.Reset();
  global_clock_.Reset();
}

void AVSynClock::UpdateAudioPTS(int32_t serial, int64_t ms_pts) {
  if (serial_ != serial) {
    LOG(WARNING) << "diff serial[" << serial_ << "] [" << serial << "]";
    return;
  }
  if (!global_clock_.IsStep()) {
    global_clock_.UpdateNano(ms_pts);
  }

  audio_clock_.UpdateNano(ms_pts);
}

void AVSynClock::UpdateVideoPTS(int32_t serial, int64_t ms_pts) {
  if (serial_ != serial) {
    LOG(WARNING) << "diff serial[" << serial_ << "] [" << serial << "]";
    return;
  }
  if (no_audio_ || global_clock_.IsStep()) {
    global_clock_.UpdateNano(ms_pts);
  }

  video_clock_.UpdateNano(ms_pts);
}

bool AVSynClock::IsVideoTooQuick(int64_t diff) {
  constexpr int64_t kTooQuickNano = 20ll;

  if (diff > kTooQuickNano) {
    return true;
  } else {
    return false;
  }
}

bool AVSynClock::GetVideoDiff(int64_t cur_pts,
                              int64_t next_pts,
                              int64_t& cur_diff,
                              int64_t* next_diff,
                              int64_t* last_render_pts_diff,
                              int64_t* render_diff) const {
  cur_diff = 0;
  if (AudioNotReady()) {
    return false;
  }

  int64_t global_ns = global_clock_.NowMS();
  if (AVClock::kNotRunning == global_ns) {
    return false;
  }
  cur_diff = (cur_pts - global_ns);
  if (next_diff) {
    *next_diff = (next_pts - global_ns);
  }
  if (render_diff) {
    auto last = video_clock_.LastUpdateTS();
    if (last) {
      *render_diff = (ms_now_internal() - video_clock_.LastUpdateTS());
    } else {
      *render_diff = 0;
    }
    if (last_render_pts_diff) {
      *last_render_pts_diff = cur_pts - video_clock_.LastPTS();
    }
  }
  return true;
}

bool AVSynClock::IsAudioTookQuick(int64_t ms_pts, int64_t* diff) const {
  if (no_video_)
    return false;
  if (video_pts_not_valid_)
    return false;

  constexpr int64_t kTooQuickNano = 40ll;
  if (VideoNotReady()) {
    return true;
  }

  int64_t global_ms = global_clock_.NowMS();
  if (ms_pts < global_ms) {
    return false;
  } else {
    if (ms_pts > (global_ms + kTooQuickNano)) {
      if (diff) {
        *diff = (ms_pts - (global_ms));
      }
      return true;
    }
    return false;
  }
}

bool AVSynClock::IsVideoTooSlow(int64_t diff) {
  constexpr int64_t kTooSlowNano = 50ll;
  if (diff < (-kTooSlowNano)) {
    return true;
  }
  return false;
}

void AVSynClock::Pause() {
  audio_clock_.Pause();
  video_clock_.Pause();
  global_clock_.Pause();
}

void AVSynClock::SetStep(bool step) {
  global_clock_.SetStep(step);
  audio_clock_.SetStep(step);
  video_clock_.SetStep(step);
}

bool AVSynClock::IsStep() const {
  return global_clock_.IsStep();
}

void AVSynClock::Resume() {
  if (no_audio_) {
    global_clock_.UpdateNano(video_clock_.NowMS());
    global_clock_.Resume();
  } else {
    global_clock_.UpdateNano(audio_clock_.NowMS());  // all resume to audio
    global_clock_.Resume();
  }
  video_clock_.Resume();
  audio_clock_.Resume();
}

bool AVSynClock::IsPause() const {
  return global_clock_.IsPause();
}

bool AVSynClock::AudioNotReady() const {
  if (no_audio_)
    return false;
  return audio_clock_.NowMS() == AVClock::kNotRunning;
}

bool AVSynClock::VideoNotReady() const {
  if (no_video_)
    return false;
  return video_clock_.NowMS() == AVClock::kNotRunning;
}

void AVSynClock::SetNoAudio() {
  no_audio_ = true;
  DCHECK(!no_video_);
}

void AVSynClock::SetNoVideo() {
  no_video_ = true;
  DCHECK(!no_audio_);
}

bool AVSynClock::IsNoVideo() const {
  return no_video_;
}

bool AVSynClock::IsNoAudio() const {
  return no_audio_;
}

void AVSynClock::SetVideoPtsNotValid() {
  video_pts_not_valid_ = true;
}

void AVSynClock::SetSpeed(float speed) {
  global_clock_.SetSpeed(speed);
}

float AVSynClock::GetSpeed() const {
  return global_clock_.GetSpeed();
}