#include "dshow_audio_only_capture.h"

#include <mutex>
#include <base/logging.h>
#include <base/strings/stringprintf.h>
#include <base/strings/utf_string_conversions.h>
#include "mediasdk/utils/time_helper.h"

namespace mediasdk {

// NullVideoRenderer 实现
const CLSID NullVideoRenderer::CLSID_NullVideoRenderer = {
    0x12345678, 0x1234, 0x1234, {0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0}
};

NullVideoRenderer::NullVideoRenderer(HRESULT* phr) 
    : CBaseRenderer(CLSID_NullVideoRenderer, L"Null Video Renderer", nullptr, phr) {
}

NullVideoRenderer::~NullVideoRenderer() = default;

HRESULT NullVideoRenderer::CheckMediaType(const CMediaType* pmtIn) {
  // 接受所有视频媒体类型
  if (pmtIn && pmtIn->majortype == MEDIATYPE_Video) {
    return S_OK;
  }
  return E_INVALIDARG;
}

HRESULT NullVideoRenderer::DoRenderSample(IMediaSample* pMediaSample) {
  // 直接丢弃视频数据，不做任何处理
  return S_OK;
}

// DShowAudioOnlyCapture 实现
DShowAudioOnlyCapture::DShowAudioOnlyCapture(AudioInputProxy* proxy)
    : proxy_(proxy), is_created_(false), is_running_(false), is_paused_(false),
      audio_sink_filter_(nullptr) {
}

DShowAudioOnlyCapture::~DShowAudioOnlyCapture() {
  Destroy();
}

bool DShowAudioOnlyCapture::Create(const std::string& device_id, const nlohmann::json& json_params) {
  std::lock_guard<std::mutex> lock(state_mutex_);
  
  if (is_created_) {
    LOG(WARNING) << "DShowAudioOnlyCapture already created";
    return true;
  }

  device_id_ = device_id;
  
  HRESULT hr = CreateAudioGraph(device_id, json_params);
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to create audio graph: 0x%X", hr);
    CleanupGraph();
    return false;
  }

  is_created_ = true;
  LOG(INFO) << "DShowAudioOnlyCapture created successfully for device: " << device_id;
  return true;
}

void DShowAudioOnlyCapture::Destroy() {
  std::lock_guard<std::mutex> lock(state_mutex_);
  
  if (!is_created_) {
    return;
  }

  Stop();
  CleanupGraph();
  is_created_ = false;
  
  LOG(INFO) << "DShowAudioOnlyCapture destroyed";
}

bool DShowAudioOnlyCapture::Start() {
  std::lock_guard<std::mutex> lock(state_mutex_);
  
  if (!is_created_ || is_running_) {
    return false;
  }

  if (!media_control_) {
    LOG(ERROR) << "Media control not available";
    return false;
  }

  HRESULT hr = media_control_->Run();
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to start audio capture: 0x%X", hr);
    return false;
  }

  is_running_ = true;
  is_paused_ = false;
  LOG(INFO) << "Audio capture started";
  return true;
}

bool DShowAudioOnlyCapture::Stop() {
  std::lock_guard<std::mutex> lock(state_mutex_);
  
  if (!is_running_) {
    return true;
  }

  if (media_control_) {
    HRESULT hr = media_control_->Stop();
    if (FAILED(hr)) {
      LOG(WARNING) << base::StringPrintf("Failed to stop media control: 0x%X", hr);
    }
  }

  is_running_ = false;
  is_paused_ = false;
  LOG(INFO) << "Audio capture stopped";
  return true;
}

bool DShowAudioOnlyCapture::Pause() {
  std::lock_guard<std::mutex> lock(state_mutex_);
  
  if (!is_running_ || is_paused_) {
    return false;
  }

  if (!media_control_) {
    return false;
  }

  HRESULT hr = media_control_->Pause();
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to pause audio capture: 0x%X", hr);
    return false;
  }

  is_paused_ = true;
  LOG(INFO) << "Audio capture paused";
  return true;
}

bool DShowAudioOnlyCapture::Resume() {
  std::lock_guard<std::mutex> lock(state_mutex_);
  
  if (!is_paused_) {
    return false;
  }

  return Start();  // 重新启动
}

bool DShowAudioOnlyCapture::IsRunning() const {
  std::lock_guard<std::mutex> lock(state_mutex_);
  return is_running_;
}

bool DShowAudioOnlyCapture::IsPaused() const {
  std::lock_guard<std::mutex> lock(state_mutex_);
  return is_paused_;
}

void DShowAudioOnlyCapture::OnAudioFrameReceive(BYTE* buffer, LONG buffer_size, int64_t timestamp) {
  if (!proxy_ || !buffer || buffer_size <= 0) {
    return;
  }

  // 将音频数据传递给代理
  // 这里需要根据实际的 AudioInputProxy 接口进行调用
  // proxy_->OnAudioData(buffer, buffer_size, timestamp);
  
  // 临时日志，实际使用时应该调用代理的音频数据回调
  static int frame_count = 0;
  if (++frame_count % 100 == 0) {  // 每100帧打印一次日志
    LOG(INFO) << base::StringPrintf("Received audio frame: size=%d, timestamp=%lld", 
                                    buffer_size, timestamp);
  }
}

void DShowAudioOnlyCapture::OnAudioFormatChange(AM_MEDIA_TYPE* media_type) {
  if (!media_type) {
    return;
  }

  LOG(INFO) << "Audio format changed";
  
  // 处理音频格式变化
  if (media_type->formattype == FORMAT_WaveFormatEx) {
    WAVEFORMATEX* wave_format = reinterpret_cast<WAVEFORMATEX*>(media_type->pbFormat);
    if (wave_format) {
      LOG(INFO) << base::StringPrintf(
          "New audio format: %d Hz, %d channels, %d bits",
          wave_format->nSamplesPerSec, wave_format->nChannels, wave_format->wBitsPerSample);
    }
  }
}

HRESULT DShowAudioOnlyCapture::CreateAudioGraph(const std::string& device_id, const nlohmann::json& json_params) {
  HRESULT hr = S_OK;

  // 创建图形构建器
  hr = CoCreateInstance(CLSID_FilterGraph, nullptr, CLSCTX_INPROC_SERVER,
                        IID_PPV_ARGS(&graph_builder_));
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to create filter graph: 0x%X", hr);
    return hr;
  }

  // 创建捕获图形构建器
  hr = CoCreateInstance(CLSID_CaptureGraphBuilder2, nullptr, CLSCTX_INPROC_SERVER,
                        IID_PPV_ARGS(&capture_builder_));
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to create capture graph builder: 0x%X", hr);
    return hr;
  }

  // 设置过滤器图形
  hr = capture_builder_->SetFiltergraph(graph_builder_.Get());
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to set filter graph: 0x%X", hr);
    return hr;
  }

  // 获取媒体控制接口
  hr = graph_builder_->QueryInterface(IID_PPV_ARGS(&media_control_));
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to get media control: 0x%X", hr);
    return hr;
  }

  // 获取媒体事件接口
  hr = graph_builder_->QueryInterface(IID_PPV_ARGS(&media_event_));
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to get media event: 0x%X", hr);
    return hr;
  }

  // 查找音频捕获设备
  hr = FindAudioCaptureDevice(device_id);
  if (FAILED(hr)) {
    LOG(ERROR) << "Failed to find audio capture device";
    return hr;
  }

  // 创建音频接收过滤器
  audio_sink_filter_ = CreateAudioFilter(this, &hr);
  if (!audio_sink_filter_ || FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to create audio sink filter: 0x%X", hr);
    return hr;
  }

  // 添加过滤器到图形
  hr = graph_builder_->AddFilter(audio_capture_filter_.Get(), L"Audio Capture");
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to add audio capture filter: 0x%X", hr);
    return hr;
  }

  hr = graph_builder_->AddFilter(audio_sink_filter_, L"Audio Sink");
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to add audio sink filter: 0x%X", hr);
    return hr;
  }

  // 连接音频过滤器
  hr = ConnectAudioFilters();
  if (FAILED(hr)) {
    LOG(ERROR) << "Failed to connect audio filters";
    return hr;
  }

  // 如果是复合设备，处理视频部分（用于丢弃）
  if (IsCompositeAudioVideoDevice(device_id)) {
    hr = HandleCompositeDevice(device_id);
    if (FAILED(hr)) {
      LOG(WARNING) << "Failed to handle composite device, continuing with audio only";
      // 不返回错误，因为音频部分可能仍然工作
    }
  }

  return S_OK;
}

HRESULT DShowAudioOnlyCapture::FindAudioCaptureDevice(const std::string& device_id) {
  std::wstring wide_device_id = base::UTF8ToWide(device_id);

  // 首先尝试在音频输入设备类别中查找
  std::vector<DShowDeviceName> audio_devices;
  if (DShowEnumDevice(audio_devices, CLSID_AudioInputDeviceCategory)) {
    for (const auto& device : audio_devices) {
      if (device.id == wide_device_id || device.name == wide_device_id) {
        audio_capture_filter_ = GetDeviceBaseFilter(device, CLSID_AudioInputDeviceCategory);
        if (audio_capture_filter_) {
          LOG(INFO) << "Found audio device in audio category: " << base::WideToUTF8(device.name);
          return S_OK;
        }
      }
    }
  }

  // 如果在音频类别中没找到，尝试在视频类别中查找（复合设备）
  std::vector<DShowDeviceName> video_devices;
  if (DShowEnumDevice(video_devices, CLSID_VideoInputDeviceCategory)) {
    for (const auto& device : video_devices) {
      if (device.id == wide_device_id || device.name == wide_device_id) {
        audio_capture_filter_ = GetDeviceBaseFilter(device, CLSID_VideoInputDeviceCategory);
        if (audio_capture_filter_) {
          LOG(INFO) << "Found audio device in video category: " << base::WideToUTF8(device.name);
          return S_OK;
        }
      }
    }
  }

  LOG(ERROR) << "Audio capture device not found: " << device_id;
  return E_FAIL;
}

HRESULT DShowAudioOnlyCapture::ConnectAudioFilters() {
  if (!audio_capture_filter_ || !audio_sink_filter_) {
    return E_POINTER;
  }

  // 查找音频捕获输出引脚
  audio_capture_output_pin_ = FindPinByCategoryAndStreamMajorType(
      audio_capture_filter_.Get(), PINDIR_OUTPUT, PIN_CATEGORY_CAPTURE, MEDIATYPE_Audio);

  if (!audio_capture_output_pin_) {
    LOG(ERROR) << "Failed to find audio capture output pin";
    return E_FAIL;
  }

  // 获取音频接收输入引脚
  audio_sink_input_pin_ = audio_sink_filter_->GetPin(0);
  if (!audio_sink_input_pin_) {
    LOG(ERROR) << "Failed to get audio sink input pin";
    return E_FAIL;
  }

  // 连接音频引脚
  HRESULT hr = graph_builder_->Connect(audio_capture_output_pin_.Get(), audio_sink_input_pin_.Get());
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to connect audio pins: 0x%X", hr);
    return hr;
  }

  LOG(INFO) << "Audio filters connected successfully";
  return S_OK;
}

bool DShowAudioOnlyCapture::IsCompositeAudioVideoDevice(const std::string& device_id) {
  // 检查设备是否同时支持音频和视频
  std::wstring wide_device_id = base::UTF8ToWide(device_id);

  // 如果设备在视频类别中找到，且有音频引脚，则认为是复合设备
  std::vector<DShowDeviceName> video_devices;
  if (DShowEnumDevice(video_devices, CLSID_VideoInputDeviceCategory)) {
    for (const auto& device : video_devices) {
      if (device.id == wide_device_id || device.name == wide_device_id) {
        auto filter = GetDeviceBaseFilter(device, CLSID_VideoInputDeviceCategory);
        if (filter) {
          // 检查是否有音频引脚
          auto audio_pin = FindPinByCategoryAndStreamMajorType(
              filter.Get(), PINDIR_OUTPUT, PIN_CATEGORY_CAPTURE, MEDIATYPE_Audio);
          if (audio_pin) {
            LOG(INFO) << "Device is composite audio/video: " << base::WideToUTF8(device.name);
            return true;
          }
        }
      }
    }
  }

  return false;
}

HRESULT DShowAudioOnlyCapture::HandleCompositeDevice(const std::string& device_id) {
  // 对于复合设备，我们需要处理视频部分以确保音频正常工作
  // 但是我们会丢弃所有视频数据

  std::wstring wide_device_id = base::UTF8ToWide(device_id);

  // 查找视频捕获过滤器（可能与音频是同一个过滤器）
  std::vector<DShowDeviceName> video_devices;
  if (!DShowEnumDevice(video_devices, CLSID_VideoInputDeviceCategory)) {
    return E_FAIL;
  }

  for (const auto& device : video_devices) {
    if (device.id == wide_device_id || device.name == wide_device_id) {
      video_capture_filter_ = GetDeviceBaseFilter(device, CLSID_VideoInputDeviceCategory);
      break;
    }
  }

  if (!video_capture_filter_) {
    LOG(WARNING) << "Video capture filter not found for composite device";
    return E_FAIL;
  }

  // 如果视频过滤器与音频过滤器不同，添加到图形中
  if (video_capture_filter_.Get() != audio_capture_filter_.Get()) {
    HRESULT hr = graph_builder_->AddFilter(video_capture_filter_.Get(), L"Video Capture");
    if (FAILED(hr)) {
      LOG(ERROR) << base::StringPrintf("Failed to add video capture filter: 0x%X", hr);
      return hr;
    }
  }

  // 创建空渲染器来丢弃视频数据
  HRESULT hr = S_OK;
  null_renderer_ = new NullVideoRenderer(&hr);
  if (!null_renderer_ || FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to create null video renderer: 0x%X", hr);
    return hr;
  }

  hr = graph_builder_->AddFilter(null_renderer_.Get(), L"Null Video Renderer");
  if (FAILED(hr)) {
    LOG(ERROR) << base::StringPrintf("Failed to add null video renderer: 0x%X", hr);
    return hr;
  }

  // 查找视频输出引脚并连接到空渲染器
  auto video_output_pin = FindPinByCategoryAndStreamMajorType(
      video_capture_filter_.Get(), PINDIR_OUTPUT, PIN_CATEGORY_CAPTURE, MEDIATYPE_Video);

  if (video_output_pin) {
    auto null_renderer_input_pin = FindPinByDirection(null_renderer_.Get(), PINDIR_INPUT);
    if (null_renderer_input_pin) {
      hr = graph_builder_->Connect(video_output_pin.Get(), null_renderer_input_pin.Get());
      if (SUCCEEDED(hr)) {
        LOG(INFO) << "Video pin connected to null renderer (video data will be discarded)";
      } else {
        LOG(WARNING) << base::StringPrintf("Failed to connect video pin to null renderer: 0x%X", hr);
      }
    }
  }

  return S_OK;
}

void DShowAudioOnlyCapture::CleanupGraph() {
  if (media_control_) {
    media_control_->Stop();
    media_control_.Reset();
  }

  if (media_event_) {
    media_event_.Reset();
  }

  if (audio_sink_filter_) {
    DestroyAudioFilter(audio_sink_filter_);
    audio_sink_filter_ = nullptr;
  }

  audio_capture_output_pin_.Reset();
  audio_sink_input_pin_.Reset();
  audio_capture_filter_.Reset();
  video_capture_filter_.Reset();
  null_renderer_.Reset();

  if (capture_builder_) {
    capture_builder_.Reset();
  }

  if (graph_builder_) {
    graph_builder_.Reset();
  }
}

// 工厂函数
std::unique_ptr<DShowAudioOnlyCapture> CreateDShowAudioOnlyCapture(AudioInputProxy* proxy) {
  return std::make_unique<DShowAudioOnlyCapture>(proxy);
}

}  // namespace mediasdk
