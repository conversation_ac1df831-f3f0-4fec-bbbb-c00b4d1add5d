#pragma once

#include <cstdint>

#include <audio/audio_format.h>
#include <audio/audio_frame.h>
#include <base/memory/aligned_memory.h>

namespace mediasdk {
inline std::shared_ptr<AudioFrame> CreateAlignedAudioFrame(
    const AudioFormat& format,
    const int32_t sample_cnt) {
  AudioFrameData frame_data;
  frame_data.block_size = format.GetBlockSize();
  frame_data.count = sample_cnt;
  frame_data.channel_count = format.GetChannel();
  for (int i = 0; i < frame_data.channel_count; i++) {
    frame_data.buffer[i] = static_cast<uint8_t*>(
        base::AlignedAlloc(frame_data.block_size * frame_data.count, 64));
    if (frame_data.buffer[i] == nullptr) {
      throw std::bad_alloc();
    }
  }
  frame_data.buffer_deallocator = [](const AudioFrameDataBuffer& data_buffer,
                                     void* user_data) {
    for (auto* buffer : data_buffer) {
      base::AlignedFree(buffer);
    }
  };
  auto ret = std::make_shared<AudioFrame>();
  ret->SetSampleRate(format.GetSampleRate());
  ret->SetData(frame_data);

  return ret;
}

inline std::shared_ptr<AudioFrame> DeepCopyAudioFrame(const AudioFormat& format,
                                                      const AudioFrame& frame) {
  const auto block_size = frame.GetBlockSize();
  const auto sample_count = frame.GetCount();

  auto ret = CreateAlignedAudioFrame(format, sample_count);

  DCHECK(format.GetBlockSize() == frame.GetBlockSize());
  for (int i = 0; i < format.GetChannel(); i++) {
    if (frame.GetData(i) && ret->GetData(i)) {
      memcpy(ret->GetData(i), frame.GetData(i), block_size * sample_count);
    }
  }
  ret->SetCaptureTimeStampNS(frame.GetCaptureTimeStampNS());
  ret->SetPts(frame.GetPts());
  ret->SetSampleRate(frame.GetSampleRate());
  return ret;
}

}  // namespace mediasdk
