#include "pdh_wrapper.h"
#include <PdhMsg.h>
#include <base/time/time.h>
#include <string>
#include "time_helper.h"

namespace {
constexpr int kBufferSize = 0x10000;

constexpr int kReverseSize = 0x1000;

constexpr char kGpuEngineStr[] = "\\GPU Engine(*)\\Utilization Percentage";

constexpr char kGpuDedicatedStr[] = "\\GPU Process Memory(*)\\Dedicated Usage";

constexpr int64_t kCollectDuration = 5000;

bool ParseLUID(const std::string_view& name, LUID& luid) {
  const auto start_pos = name.find("_luid_0x");
  if (start_pos == std::string::npos) {
    return false;
  }
  if (const auto end_pos = name.find("_phys_0"); end_pos == std::string::npos) {
    return false;
  }
  sscanf_s(name.data() + start_pos, "_luid_0x%lX_0x%lX_phys_0", &luid.HighPart,
           &luid.LowPart);
  return true;
}

bool IsMemoryName(const std::string_view& name) {
  // pid_%d_luid_0x0000000_0x%X_phys_0
  if (name.empty()) {
    return false;
  }
  if (const auto pos = name.find("_phys_0"); pos == std::string::npos) {
    return false;
  }
  if (const auto pos = name.find("_luid_0x"); pos == std::string::npos) {
    return false;
  }
  if (name[name.size() - 1] != '0' && name[name.size() - 2] != '_') {
    return false;
  }
  return true;
}

gpu_collector::COLLECT_TYPE ParseEngineType(const std::string_view& name) {
  if (name.empty()) {
    return gpu_collector::COLLECT_TYPE::DEFAULT;
  }
  if (const auto pos = name.find("_engtype_3D"); pos != std::string::npos) {
    return gpu_collector::COLLECT_TYPE::ENGINE_3D;
  }
  if (const auto pos = name.find("_engtype_Cuda"); pos != std::string::npos) {
    return gpu_collector::COLLECT_TYPE::ENGINE_CUDA;
  }
  if (const auto pos = name.find("_engtype_Copy"); pos != std::string::npos) {
    return gpu_collector::COLLECT_TYPE::ENGINE_COPY;
  }
  if (const auto pos = name.find("_engtype_VideoDecode");
      pos != std::string::npos) {
    return gpu_collector::COLLECT_TYPE::ENGINE_DECODE;
  }
  if (const auto pos = name.find("_engtype_VideoEncode");
      pos != std::string::npos) {
    return gpu_collector::COLLECT_TYPE::ENGINE_ENCODE;
  }
  if (const auto pos = name.find("_engtype_VideoProcessing");
      pos != std::string::npos) {
    return gpu_collector::COLLECT_TYPE::ENGINE_PROCESSING;
  }
  return gpu_collector::COLLECT_TYPE::DEFAULT;
}

int ParsePid(const std::string_view& name) {
  // pid_%d_luid_0x0000000_0x%X_phys_0
  if (name.empty()) {
    return 0;
  }
  const auto pid_pos = name.find("pid_");
  if (pid_pos == std::string::npos) {
    return 0;
  }
  const auto luid_pos = name.find("_luid");
  if (luid_pos == std::string::npos) {
    return 0;
  }
  int pid = 0;
  sscanf_s(name.data() + pid_pos, "pid_%d_luid", &pid);
  return pid;
}

int ParseEngineIndex(const std::string_view& name) {
  // pid_%d_luid_0x00000000_0x%X_phys_0_eng_%d_engtype_3D
  if (name.empty()) {
    return 0;
  }
  const auto eng_pos = name.find("eng_");
  if (eng_pos == std::string::npos) {
    return 0;
  }
  const auto engtype_pos = name.find("_engtype");
  if (engtype_pos == std::string::npos) {
    return 0;
  }
  int index = 0;
  sscanf_s(name.data() + eng_pos, "eng_%d_engtype", &index);
  return index;
}
}  // namespace

namespace gpu_collector {

PdhWrapper::PdhWrapper()
    : initialized_(Initialize()),
      last_ts_(mediasdk::low_precision_milli_now()) {}

PdhWrapper::~PdhWrapper() {
  if (hquery_) {
    PdhCloseQuery(hquery_);
    hquery_ = nullptr;
  }
  if (hmem_query_) {
    PdhCloseQuery(hmem_query_);
    hmem_query_ = nullptr;
  }
  hcounter_ = nullptr;
  hmem_counter_ = nullptr;
  buffer_ = std::vector<char>();
  mem_buffer_ = std::vector<char>();
  initialized_ = false;
}

bool PdhWrapper::Initialize() {
  buffer_.resize(kBufferSize);
  mem_buffer_.resize(kBufferSize);
  PDH_STATUS status = ERROR_SUCCESS;

  do {
    status = ::PdhOpenQuery(NULL, NULL, &hquery_);
    if (status != ERROR_SUCCESS) {
      break;
    }

    status = ::PdhOpenQuery(NULL, NULL, &hmem_query_);
    if (status != ERROR_SUCCESS) {
      break;
    }

    status = ::PdhAddCounterA(hquery_, kGpuEngineStr, NULL, &hcounter_);
    if (status != ERROR_SUCCESS) {
      break;
    }

    status =
        ::PdhAddCounterA(hmem_query_, kGpuDedicatedStr, NULL, &hmem_counter_);
    if (status != ERROR_SUCCESS) {
      break;
    }

    status = ::PdhCollectQueryData(hquery_);
    if (status != ERROR_SUCCESS) {
      break;
    }

    status = ::PdhCollectQueryData(hmem_query_);
    if (status != ERROR_SUCCESS) {
      break;
    }
  } while (false);

  return status == ERROR_SUCCESS;
}

bool PdhWrapper::CollectData(std::vector<GpuCollectItem>& collect_items) {
  if (!initialized_) {
    return false;
  }
  {
    std::lock_guard<std::mutex> lck(collect_mutex_);
    const auto now = mediasdk::low_precision_milli_now();
    if (now - last_ts_ < kCollectDuration) {
      return true;
    }
    last_ts_ = now;
  }

  PDH_STATUS status = ERROR_SUCCESS;
  {
    DWORD buffer_size = buffer_.size();
    auto* buffer =
        reinterpret_cast<PPDH_FMT_COUNTERVALUE_ITEM_A>(buffer_.data());

    status = ::PdhCollectQueryData(hquery_);
    if (status != ERROR_SUCCESS) {
      return false;
    }

    status = ::PdhGetFormattedCounterArrayA(hcounter_, PDH_FMT_DOUBLE,
                                            &buffer_size, &item_count_, buffer);
    if (status == PDH_MORE_DATA) {
      buffer_size += kReverseSize;
      buffer_.resize(buffer_size);
      buffer = reinterpret_cast<PPDH_FMT_COUNTERVALUE_ITEM_A>(buffer_.data());
      status = ::PdhGetFormattedCounterArrayA(
          hcounter_, PDH_FMT_DOUBLE, &buffer_size, &item_count_, buffer);
    }
  }
  {
    DWORD buffer_size = mem_buffer_.size();
    auto* buffer =
        reinterpret_cast<PPDH_FMT_COUNTERVALUE_ITEM_A>(mem_buffer_.data());

    status = ::PdhCollectQueryData(hmem_query_);
    if (status != ERROR_SUCCESS) {
      return false;
    }

    status = ::PdhGetFormattedCounterArrayA(
        hmem_counter_, PDH_FMT_DOUBLE, &buffer_size, &mem_item_count_, buffer);
    if (status == PDH_MORE_DATA) {
      buffer_size += kReverseSize;
      mem_buffer_.resize(buffer_size);
      buffer =
          reinterpret_cast<PPDH_FMT_COUNTERVALUE_ITEM_A>(mem_buffer_.data());
      status = ::PdhGetFormattedCounterArrayA(hmem_counter_, PDH_FMT_DOUBLE,
                                              &buffer_size, &mem_item_count_,
                                              buffer);
    }
  }

  return (status == ERROR_SUCCESS) && ParseBuffer(collect_items);
}

bool PdhWrapper::ParseBuffer(std::vector<GpuCollectItem>& collect_items) {
  collect_items.clear();
  if (!initialized_) {
    return false;
  }
  if (item_count_ <= 0 || buffer_.empty()) {
    return false;
  }
  if (mem_item_count_ <= 0 || mem_buffer_.empty()) {
    return false;
  }
  {
    const auto* items_buffer =
        reinterpret_cast<PPDH_FMT_COUNTERVALUE_ITEM_A>(buffer_.data());
    for (int i = 0; i < static_cast<int>(item_count_); i++) {
      const auto& item = items_buffer[i];
      if (!item.szName) {
        continue;
      }
      std::string_view name = item.szName;
      if (name.empty()) {
        continue;
      }
      LUID luid = {0, 0};
      if (!ParseLUID(name, luid)) {
        continue;
      }

      if (const auto type = ParseEngineType(name); type != DEFAULT) {
        GpuCollectItem collect_item;
        collect_item.collect_type = type;
        collect_item.pid = ParsePid(name);
        collect_item.device_info =
            GpuDeviceInfo{static_cast<uint32_t>(luid.LowPart),
                          static_cast<uint32_t>(luid.HighPart)};
        collect_item.value = item.FmtValue.doubleValue;
        collect_item.engine_index = ParseEngineIndex(name);
        collect_items.emplace_back(collect_item);
      }
    }
  }
  {
    const auto* items_buffer =
        reinterpret_cast<PPDH_FMT_COUNTERVALUE_ITEM_A>(mem_buffer_.data());
    for (int i = 0; i < static_cast<int>(mem_item_count_); i++) {
      const auto& item = items_buffer[i];
      if (!item.szName) {
        continue;
      }
      std::string_view name = item.szName;
      if (name.empty()) {
        continue;
      }
      LUID luid = {0, 0};
      if (!ParseLUID(name, luid)) {
        continue;
      }
      if (IsMemoryName(name)) {
        GpuCollectItem collect_item;
        collect_item.collect_type = DEDICATE;
        collect_item.pid = ParsePid(name);
        collect_item.device_info =
            GpuDeviceInfo{static_cast<uint32_t>(luid.LowPart),
                          static_cast<uint32_t>(luid.HighPart)};
        collect_item.value = item.FmtValue.doubleValue;
        collect_items.emplace_back(collect_item);
      }
    }
  }
  return true;
}
}  // namespace gpu_collector