#pragma once

#include "mediasdk_callback_defines.h"
#include "mediasdk_export.h"

#ifdef __cplusplus
extern "C" {
#endif

namespace mediasdk {

// Create a filter for the specified visual.
// Set the filter properties using 'json_params'.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API CreateAudioFilter(const char* audio_filter_id,
                                              const char* audio_filter_name,
                                              const char* audio_input_id,
                                              const char* json_params,
                                              Closure closure);

// Destroy a specified filter based on the filter id.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API DestroyAudioFilter(const char* audio_filter_id,
                                               const char* audio_input_id,
                                               Closure closure);

// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetAudioFilterEnable(const char* audio_filter_id,
                                                 const char* audio_input_id,
                                                 bool enable,
                                                 Closure closure);

// Asynchronous callback result, ResultBoolBool(exec if success, result)
MEDIASDK_EXPORT void MS_API IsAudioFilterEnable(const char* audio_filter_id,
                                                const char* audio_input_id,
                                                Closure closure);

// Set the filter properties using 'json_params'.
// Asynchronous callback result, bool
MEDIASDK_EXPORT void SetAudioFilterProperty(const char* audio_filter_id,
                                            const char* audio_input_id,
                                            const char* key,
                                            const char* value,
                                            Closure closure);

// Get the filter properties.
// Asynchronous callback result, ResultBoolString(exec if success, result)
MEDIASDK_EXPORT void GetAudioFilterProperty(const char* audio_filter_id,
                                            const char* audio_input_id,
                                            const char* key,
                                            Closure closure);

// Make the filter perform certain actions.
// Asynchronous callback result, ResultBoolString(exec if success, result)
MEDIASDK_EXPORT void AudioFilterAction(const char* audio_input_id,
                                       const char* audio_filter_id,
                                       const char* action,
                                       const char* param,
                                       Closure closure);
}  // namespace mediasdk

#ifdef __cplusplus
}
#endif

#define MS_AUDIO_FILTER mediasdk::audio_filter
