import json
from urllib import request
from config import CONFIG


def __get_tenant_access_token():
    APP_ID = CONFIG.get_feihui_app_id
    APP_SECRET = CONFIG.get_feihui_app_secret
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
    headers = {"Content-Type": "application/json"}
    req_body = {"app_id": APP_ID, "app_secret": APP_SECRET}

    data = bytes(json.dumps(req_body), encoding="utf8")
    req = request.Request(url=url, data=data, headers=headers, method="POST")
    try:
        response = request.urlopen(req)
    except Exception as e:
        print(e.read().decode())
        return ""

    rsp_body = response.read().decode("utf-8")
    rsp_dict = json.loads(rsp_body)
    code = rsp_dict.get("code", -1)
    if code != 0:
        print("get tenant_access_token error, code =", code)
        return ""
    return rsp_dict.get("tenant_access_token", "")


def report(msg_str):
    chat_id = CONFIG.get_feihui_chat_id
    token = __get_tenant_access_token()
    url = "https://open.feishu.cn/open-apis/message/v4/send/"
    headers = {
        "Authorization": "Bearer " + token,
        "Content-Type": "application/json; charset=utf-8",
    }
    req_body = {"chat_id": chat_id, "msg_type": "text", "content": {"text": msg_str}}

    data = bytes(json.dumps(req_body), encoding="utf8")
    req = request.Request(url=url, data=data, headers=headers, method="POST")

    try:
        response = request.urlopen(req)
        rsp_body = response.read().decode("utf-8")
        rsp_dict = json.loads(rsp_body)
        print(" send robot mes response: ", rsp_dict, "")
    except Exception as e:
        print("----------Failed to send data to robot, %s ----------" % str(e))


def basic_info_message(at_user):
    msg_str = "job_id: " + CONFIG.args.job_id + "\r\n"
    msg_str += "branch: " + CONFIG.args.branch + "\r\n"
    msg_str += "version: " + CONFIG.args.version + "\r\n"
    msg_str += "release: " + ("true" if CONFIG.args.release else "false") + "\r\n"
    if CONFIG.extra_cmake_params:
        msg_str += "extra_cmake_params: " + str(CONFIG.extra_cmake_params) + "\r\n"
    msg_str += CONFIG.job_log_url + "\r\n"
    msg_str += '<at user_id="' + at_user + '"></at> '
    return msg_str


def report_start(at_user):
    msg_str = "Start packaging ...\r\n"
    msg_str += basic_info_message(at_user)
    report(msg_str)


def report_failure(at_user, exit_str, exit_code):
    msg_str = "Packaging failed ...\r\n"
    msg_str += "exit_code:{exit_code}: exit_str:{exit_str}\r\n".format(
        exit_code=exit_code, exit_str=exit_str
    )
    msg_str += basic_info_message(at_user)
    report(msg_str)


def report_success(at_user, check_mes, tos_download_url, enable_platform):
    msg_str = "【Test package】 MEDIASDK_TT_2024: "
    if enable_platform == "ALL":
        msg_str += "32&64"
    elif "32" in enable_platform:
        msg_str += "32 only"
    elif "64" in enable_platform:
        msg_str += "64 only"
    msg_str += "\r\n"

    msg_str += basic_info_message(at_user) + "\r\n"
    msg_str += "jenkins download url " + CONFIG.jenkins_download_url + "\r\n"
    msg_str += "tos download url  " + tos_download_url + "\r\n"
    msg_str += "check whether the package size meets expectations" + "\r\n"
    msg_str += check_mes

    report(msg_str)
