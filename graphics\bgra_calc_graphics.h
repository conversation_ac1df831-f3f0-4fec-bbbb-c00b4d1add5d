#pragma once
#include "texture.h"

namespace graphics {
class GRAPHICS_EXPORT BGRACalcGraphics {
 public:
  enum CalcType { KCalcAlphaMask };

  virtual bool AlphaMask(std::shared_ptr<Texture> texture,
                         std::shared_ptr<Texture> mask,
                         CalcType type,
                         const DirectX::XMFLOAT2 texutre_move = {},
                         const DirectX::XMFLOAT2 mask_move = {}) = 0;
  virtual std::shared_ptr<Texture> GetOutputTexture() = 0;
  virtual ~BGRACalcGraphics(){};
};

GRAPHICS_EXPORT std::shared_ptr<BGRACalcGraphics> CreateBGRACalcGraphics(
    Device& ins);
}  // namespace graphics
